openapi: 3.0.3
info:
  title: WTMS API
  description: 工作任务管理系统API接口文档
  version: 1.0.0
  contact:
    name: WTMS团队
    email: <EMAIL>
  license:
    name: MIT
    url: https://opensource.org/licenses/MIT

servers:
  - url: http://localhost:55557/api/v1
    description: 开发环境
  - url: https://wtms.example.com/api/v1
    description: 生产环境

security:
  - BearerAuth: []

paths:
  # 认证接口
  /auth/login:
    post:
      tags:
        - 用户认证
      summary: 用户登录
      description: 用户登录获取访问令牌
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/LoginRequest'
            example:
              username: admin
              password: admin123456
              rememberMe: false
      responses:
        '200':
          description: 登录成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/LoginResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'

  /auth/register:
    post:
      tags:
        - 用户认证
      summary: 用户注册
      description: 注册新用户账户
      security: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/RegisterRequest'
      responses:
        '201':
          description: 注册成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '409':
          $ref: '#/components/responses/Conflict'

  /auth/logout:
    post:
      tags:
        - 用户认证
      summary: 用户登出
      description: 用户登出并使令牌失效
      responses:
        '200':
          description: 登出成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'

  # 用户管理接口
  /users:
    get:
      tags:
        - 用户管理
      summary: 获取用户列表
      description: 分页获取用户列表
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/SizeParam'
        - name: keyword
          in: query
          description: 搜索关键词
          schema:
            type: string
        - name: departmentId
          in: query
          description: 部门ID
          schema:
            type: string
        - name: status
          in: query
          description: 用户状态
          schema:
            $ref: '#/components/schemas/UserStatus'
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/PageResult'

    post:
      tags:
        - 用户管理
      summary: 创建用户
      description: 创建新用户
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUserRequest'
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          $ref: '#/components/responses/Forbidden'

  /users/{userId}:
    get:
      tags:
        - 用户管理
      summary: 获取用户详情
      description: 根据用户ID获取用户详细信息
      parameters:
        - name: userId
          in: path
          required: true
          description: 用户ID
          schema:
            type: string
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/UserDetail'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags:
        - 用户管理
      summary: 更新用户
      description: 更新用户信息
      parameters:
        - name: userId
          in: path
          required: true
          description: 用户ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateUserRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/User'
        '400':
          $ref: '#/components/responses/BadRequest'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags:
        - 用户管理
      summary: 删除用户
      description: 删除指定用户
      parameters:
        - name: userId
          in: path
          required: true
          description: 用户ID
          schema:
            type: string
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'

  # 任务管理接口
  /tasks:
    get:
      tags:
        - 任务管理
      summary: 获取任务列表
      description: 分页获取任务列表
      parameters:
        - $ref: '#/components/parameters/PageParam'
        - $ref: '#/components/parameters/SizeParam'
        - name: keyword
          in: query
          description: 搜索关键词
          schema:
            type: string
        - name: status
          in: query
          description: 任务状态
          schema:
            $ref: '#/components/schemas/TaskStatus'
        - name: priority
          in: query
          description: 优先级
          schema:
            $ref: '#/components/schemas/TaskPriority'
        - name: assigneeId
          in: query
          description: 负责人ID
          schema:
            type: string
        - name: creatorId
          in: query
          description: 创建人ID
          schema:
            type: string
        - name: startDate
          in: query
          description: 开始日期
          schema:
            type: string
            format: date
        - name: endDate
          in: query
          description: 结束日期
          schema:
            type: string
            format: date
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        allOf:
                          - $ref: '#/components/schemas/PageResult'
                          - type: object
                            properties:
                              records:
                                type: array
                                items:
                                  $ref: '#/components/schemas/Task'

    post:
      tags:
        - 任务管理
      summary: 创建任务
      description: 创建新任务
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateTaskRequest'
      responses:
        '201':
          description: 创建成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Task'
        '400':
          $ref: '#/components/responses/BadRequest'

  /tasks/{taskId}:
    get:
      tags:
        - 任务管理
      summary: 获取任务详情
      description: 根据任务ID获取任务详细信息
      parameters:
        - name: taskId
          in: path
          required: true
          description: 任务ID
          schema:
            type: string
      responses:
        '200':
          description: 查询成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/TaskDetail'
        '404':
          $ref: '#/components/responses/NotFound'

    put:
      tags:
        - 任务管理
      summary: 更新任务
      description: 更新任务信息
      parameters:
        - name: taskId
          in: path
          required: true
          description: 任务ID
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateTaskRequest'
      responses:
        '200':
          description: 更新成功
          content:
            application/json:
              schema:
                allOf:
                  - $ref: '#/components/schemas/ApiResponse'
                  - type: object
                    properties:
                      data:
                        $ref: '#/components/schemas/Task'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'

    delete:
      tags:
        - 任务管理
      summary: 删除任务
      description: 删除指定任务
      parameters:
        - name: taskId
          in: path
          required: true
          description: 任务ID
          schema:
            type: string
      responses:
        '200':
          description: 删除成功
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApiResponse'
        '404':
          $ref: '#/components/responses/NotFound'

components:
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT

  parameters:
    PageParam:
      name: page
      in: query
      description: 页码
      schema:
        type: integer
        minimum: 1
        default: 1
    SizeParam:
      name: size
      in: query
      description: 每页大小
      schema:
        type: integer
        minimum: 1
        maximum: 100
        default: 10

  schemas:
    # 通用响应结构
    ApiResponse:
      type: object
      properties:
        code:
          type: integer
          description: 响应码
          example: 200
        message:
          type: string
          description: 响应消息
          example: 操作成功
        data:
          type: object
          description: 响应数据
        timestamp:
          type: string
          format: date-time
          description: 响应时间
          example: 2024-01-01T12:00:00Z

    # 分页结果
    PageResult:
      type: object
      properties:
        records:
          type: array
          items:
            type: object
          description: 数据列表
        total:
          type: integer
          description: 总记录数
          example: 100
        size:
          type: integer
          description: 每页大小
          example: 10
        current:
          type: integer
          description: 当前页码
          example: 1
        pages:
          type: integer
          description: 总页数
          example: 10

    # 登录请求
    LoginRequest:
      type: object
      required:
        - username
        - password
      properties:
        username:
          type: string
          description: 用户名
          example: admin
        password:
          type: string
          description: 密码
          example: admin123456
        rememberMe:
          type: boolean
          description: 记住登录状态
          default: false

    # 登录响应
    LoginResponse:
      type: object
      properties:
        token:
          type: string
          description: 访问令牌
        refreshToken:
          type: string
          description: 刷新令牌
        expiresIn:
          type: integer
          description: 令牌有效期（秒）
          example: 86400
        user:
          $ref: '#/components/schemas/User'

    # 注册请求
    RegisterRequest:
      type: object
      required:
        - username
        - password
        - fullName
        - email
      properties:
        username:
          type: string
          description: 用户名
          minLength: 3
          maxLength: 20
        password:
          type: string
          description: 密码
          minLength: 8
          maxLength: 20
        fullName:
          type: string
          description: 姓名
        email:
          type: string
          format: email
          description: 邮箱
        phone:
          type: string
          description: 手机号
        departmentId:
          type: string
          description: 部门ID

    # 用户信息
    User:
      type: object
      properties:
        id:
          type: string
          description: 用户ID
        username:
          type: string
          description: 用户名
        fullName:
          type: string
          description: 姓名
        email:
          type: string
          description: 邮箱
        phone:
          type: string
          description: 手机号
        avatar:
          type: string
          description: 头像URL
        status:
          $ref: '#/components/schemas/UserStatus'
        roles:
          type: array
          items:
            type: string
          description: 角色列表
        permissions:
          type: array
          items:
            type: string
          description: 权限列表
        lastLoginAt:
          type: string
          format: date-time
          description: 最后登录时间
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间

    # 用户详情
    UserDetail:
      allOf:
        - $ref: '#/components/schemas/User'
        - type: object
          properties:
            department:
              $ref: '#/components/schemas/Department'

    # 部门信息
    Department:
      type: object
      properties:
        id:
          type: string
          description: 部门ID
        name:
          type: string
          description: 部门名称
        parentId:
          type: string
          description: 父部门ID
        level:
          type: integer
          description: 部门层级

    # 创建用户请求
    CreateUserRequest:
      type: object
      required:
        - username
        - password
        - fullName
        - email
      properties:
        username:
          type: string
          description: 用户名
        password:
          type: string
          description: 密码
        fullName:
          type: string
          description: 姓名
        email:
          type: string
          description: 邮箱
        phone:
          type: string
          description: 手机号
        departmentId:
          type: string
          description: 部门ID
        roleIds:
          type: array
          items:
            type: string
          description: 角色ID列表
        status:
          $ref: '#/components/schemas/UserStatus'

    # 更新用户请求
    UpdateUserRequest:
      type: object
      properties:
        fullName:
          type: string
          description: 姓名
        email:
          type: string
          description: 邮箱
        phone:
          type: string
          description: 手机号
        departmentId:
          type: string
          description: 部门ID
        roleIds:
          type: array
          items:
            type: string
          description: 角色ID列表
        status:
          $ref: '#/components/schemas/UserStatus'

    # 任务信息
    Task:
      type: object
      properties:
        id:
          type: string
          description: 任务ID
        title:
          type: string
          description: 任务标题
        description:
          type: string
          description: 任务描述
        status:
          $ref: '#/components/schemas/TaskStatus'
        priority:
          $ref: '#/components/schemas/TaskPriority'
        type:
          type: string
          description: 任务类型
        creator:
          $ref: '#/components/schemas/User'
        assignee:
          $ref: '#/components/schemas/User'
        participants:
          type: array
          items:
            $ref: '#/components/schemas/User'
          description: 参与者列表
        tags:
          type: array
          items:
            type: string
          description: 标签列表
        estimatedHours:
          type: number
          description: 预估工时
        actualHours:
          type: number
          description: 实际工时
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: 进度百分比
        startDate:
          type: string
          format: date
          description: 开始日期
        dueDate:
          type: string
          format: date
          description: 截止日期
        createdAt:
          type: string
          format: date-time
          description: 创建时间
        updatedAt:
          type: string
          format: date-time
          description: 更新时间

    # 任务详情
    TaskDetail:
      allOf:
        - $ref: '#/components/schemas/Task'
        - type: object
          properties:
            attachments:
              type: array
              items:
                $ref: '#/components/schemas/Attachment'
              description: 附件列表
            comments:
              type: array
              items:
                $ref: '#/components/schemas/Comment'
              description: 评论列表

    # 附件信息
    Attachment:
      type: object
      properties:
        id:
          type: string
          description: 附件ID
        fileName:
          type: string
          description: 文件名
        fileSize:
          type: integer
          description: 文件大小（字节）
        fileUrl:
          type: string
          description: 文件URL
        uploadedBy:
          type: string
          description: 上传者
        uploadedAt:
          type: string
          format: date-time
          description: 上传时间

    # 评论信息
    Comment:
      type: object
      properties:
        id:
          type: string
          description: 评论ID
        content:
          type: string
          description: 评论内容
        author:
          $ref: '#/components/schemas/User'
        createdAt:
          type: string
          format: date-time
          description: 创建时间

    # 创建任务请求
    CreateTaskRequest:
      type: object
      required:
        - title
      properties:
        title:
          type: string
          description: 任务标题
        description:
          type: string
          description: 任务描述
        priority:
          $ref: '#/components/schemas/TaskPriority'
        type:
          type: string
          description: 任务类型
        assigneeId:
          type: string
          description: 负责人ID
        participantIds:
          type: array
          items:
            type: string
          description: 参与者ID列表
        tags:
          type: array
          items:
            type: string
          description: 标签列表
        estimatedHours:
          type: number
          description: 预估工时
        startDate:
          type: string
          format: date
          description: 开始日期
        dueDate:
          type: string
          format: date
          description: 截止日期
        attachmentIds:
          type: array
          items:
            type: string
          description: 附件ID列表

    # 更新任务请求
    UpdateTaskRequest:
      type: object
      properties:
        title:
          type: string
          description: 任务标题
        description:
          type: string
          description: 任务描述
        priority:
          $ref: '#/components/schemas/TaskPriority'
        type:
          type: string
          description: 任务类型
        assigneeId:
          type: string
          description: 负责人ID
        participantIds:
          type: array
          items:
            type: string
          description: 参与者ID列表
        tags:
          type: array
          items:
            type: string
          description: 标签列表
        estimatedHours:
          type: number
          description: 预估工时
        actualHours:
          type: number
          description: 实际工时
        progress:
          type: integer
          minimum: 0
          maximum: 100
          description: 进度百分比
        startDate:
          type: string
          format: date
          description: 开始日期
        dueDate:
          type: string
          format: date
          description: 截止日期

    # 枚举类型
    UserStatus:
      type: string
      enum:
        - ACTIVE
        - INACTIVE
        - LOCKED
        - EXPIRED
      description: 用户状态

    TaskStatus:
      type: string
      enum:
        - TODO
        - IN_PROGRESS
        - REVIEW
        - DONE
        - CANCELLED
      description: 任务状态

    TaskPriority:
      type: string
      enum:
        - LOW
        - MEDIUM
        - HIGH
        - URGENT
      description: 任务优先级

  responses:
    BadRequest:
      description: 请求参数错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            code: 400
            message: 请求参数错误
            data: null
            timestamp: 2024-01-01T12:00:00Z

    Unauthorized:
      description: 未授权访问
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            code: 401
            message: 未授权访问
            data: null
            timestamp: 2024-01-01T12:00:00Z

    Forbidden:
      description: 权限不足
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            code: 403
            message: 权限不足
            data: null
            timestamp: 2024-01-01T12:00:00Z

    NotFound:
      description: 资源不存在
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            code: 404
            message: 资源不存在
            data: null
            timestamp: 2024-01-01T12:00:00Z

    Conflict:
      description: 资源冲突
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            code: 409
            message: 资源冲突
            data: null
            timestamp: 2024-01-01T12:00:00Z

    InternalServerError:
      description: 服务器内部错误
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ApiResponse'
          example:
            code: 500
            message: 服务器内部错误
            data: null
            timestamp: 2024-01-01T12:00:00Z
