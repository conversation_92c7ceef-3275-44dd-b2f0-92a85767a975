package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作流连接线实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("workflow_edges")
@Schema(description = "工作流连接线实体")
public class WorkflowEdge implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "连接线ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "工作流定义ID")
    @TableField("workflow_definition_id")
    private String workflowDefinitionId;

    @Schema(description = "连接线编码")
    @TableField("edge_code")
    private String edgeCode;

    @Schema(description = "连接线名称")
    @TableField("edge_name")
    private String edgeName;

    @Schema(description = "源节点ID")
    @TableField("source_node_id")
    private String sourceNodeId;

    @Schema(description = "目标节点ID")
    @TableField("target_node_id")
    private String targetNodeId;

    @Schema(description = "连接线类型")
    @TableField("edge_type")
    private String edgeType;

    @Schema(description = "条件表达式")
    @TableField("condition_expression")
    private String conditionExpression;

    @Schema(description = "连接线配置JSON")
    @TableField("config_json")
    private String configJson;

    @Schema(description = "路径点JSON")
    @TableField("path_points")
    private String pathPoints;

    @Schema(description = "是否默认路径")
    @TableField("is_default")
    private Boolean isDefault;

    @Schema(description = "是否启用")
    @TableField("is_enabled")
    private Boolean isEnabled;

    @Schema(description = "排序号")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "工作流定义信息")
    @TableField(exist = false)
    private WorkflowDefinition workflowDefinition;

    @Schema(description = "源节点信息")
    @TableField(exist = false)
    private WorkflowNode sourceNode;

    @Schema(description = "目标节点信息")
    @TableField(exist = false)
    private WorkflowNode targetNode;

    /**
     * 连接线类型枚举
     */
    public enum EdgeType {
        SEQUENCE_FLOW("sequence_flow", "顺序流"),
        CONDITIONAL_FLOW("conditional_flow", "条件流"),
        DEFAULT_FLOW("default_flow", "默认流"),
        MESSAGE_FLOW("message_flow", "消息流"),
        ASSOCIATION("association", "关联"),
        DATA_ASSOCIATION("data_association", "数据关联");

        private final String code;
        private final String description;

        EdgeType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static EdgeType fromCode(String code) {
            for (EdgeType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return SEQUENCE_FLOW;
        }
    }

    /**
     * 检查是否为默认路径
     */
    public boolean isDefault() {
        return Boolean.TRUE.equals(this.isDefault);
    }

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.isEnabled);
    }

    /**
     * 获取连接线类型描述
     */
    public String getEdgeTypeText() {
        return EdgeType.fromCode(this.edgeType).getDescription();
    }

    /**
     * 检查是否有条件表达式
     */
    public boolean hasCondition() {
        return conditionExpression != null && !conditionExpression.trim().isEmpty();
    }

    /**
     * 检查是否为条件流
     */
    public boolean isConditionalFlow() {
        return EdgeType.CONDITIONAL_FLOW.getCode().equals(this.edgeType);
    }

    /**
     * 检查是否为默认流
     */
    public boolean isDefaultFlow() {
        return EdgeType.DEFAULT_FLOW.getCode().equals(this.edgeType);
    }

    /**
     * 检查是否为顺序流
     */
    public boolean isSequenceFlow() {
        return EdgeType.SEQUENCE_FLOW.getCode().equals(this.edgeType);
    }

    /**
     * 获取连接线标识
     */
    public String getEdgeIdentifier() {
        return String.format("%s -> %s", sourceNodeId, targetNodeId);
    }
}
