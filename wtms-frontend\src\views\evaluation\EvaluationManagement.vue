<template>
  <div class="evaluation-management">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">质量评价管理</h1>
          <p class="page-description">管理任务质量评价，查看评价统计和分析</p>
        </div>
        <div class="header-right">
          <el-button type="primary" @click="handleCreateEvaluation">
            <el-icon><Plus /></el-icon>
            新建评价
          </el-button>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-section">
      <el-col :span="6">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-item">
            <div class="stats-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-value">{{ stats.totalEvaluations }}</div>
              <div class="stats-label">总评价数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-item">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-value">{{ stats.pendingEvaluations }}</div>
              <div class="stats-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-item">
            <div class="stats-icon published">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-value">{{ stats.publishedEvaluations }}</div>
              <div class="stats-label">已发布</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="stats-card" shadow="hover">
          <div class="stats-item">
            <div class="stats-icon average">
              <el-icon><Star /></el-icon>
            </div>
            <div class="stats-content">
              <div class="stats-value">{{ stats.averageScore?.toFixed(1) }}</div>
              <div class="stats-label">平均评分</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 快捷操作 -->
    <el-card class="quick-actions" shadow="never">
      <template #header>
        <span>快捷操作</span>
      </template>
      <div class="actions-grid">
        <div class="action-item" @click="handleViewMyEvaluations">
          <el-icon><User /></el-icon>
          <span>我的评价</span>
        </div>
        <div class="action-item" @click="handleViewPendingReviews">
          <el-icon><View /></el-icon>
          <span>待审核评价</span>
        </div>
        <div class="action-item" @click="handleViewOverdueEvaluations">
          <el-icon><Warning /></el-icon>
          <span>超期评价</span>
        </div>
        <div class="action-item" @click="handleViewStatistics">
          <el-icon><DataAnalysis /></el-icon>
          <span>统计分析</span>
        </div>
        <div class="action-item" @click="handleExportReport">
          <el-icon><Download /></el-icon>
          <span>导出报告</span>
        </div>
        <div class="action-item" @click="handleBatchOperations">
          <el-icon><Operation /></el-icon>
          <span>批量操作</span>
        </div>
      </div>
    </el-card>

    <!-- 标签页 -->
    <el-card class="content-card" shadow="never">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="评价列表" name="list">
          <EvaluationList ref="evaluationListRef" />
        </el-tab-pane>
        <el-tab-pane label="统计分析" name="statistics">
          <StatisticsDashboard />
        </el-tab-pane>
        <el-tab-pane label="质量排名" name="ranking">
          <QualityRanking />
        </el-tab-pane>
        <el-tab-pane label="评价模板" name="templates">
          <EvaluationTemplates />
        </el-tab-pane>
      </el-tabs>
    </el-card>

    <!-- 评价表单对话框 -->
    <el-dialog
      v-model="showEvaluationForm"
      title="新建评价"
      width="80%"
      :close-on-click-modal="false"
    >
      <EvaluationForm
        v-model="showEvaluationForm"
        @success="handleEvaluationSuccess"
      />
    </el-dialog>

    <!-- 批量操作对话框 -->
    <el-dialog
      v-model="showBatchDialog"
      title="批量操作"
      width="600px"
    >
      <div class="batch-operations">
        <el-form :model="batchForm" label-width="120px">
          <el-form-item label="操作类型">
            <el-select v-model="batchForm.operation" placeholder="请选择操作类型" style="width: 100%">
              <el-option label="批量提交" value="submit" />
              <el-option label="批量审核" value="review" />
              <el-option label="批量发布" value="publish" />
              <el-option label="批量归档" value="archive" />
              <el-option label="批量删除" value="delete" />
            </el-select>
          </el-form-item>
          <el-form-item label="筛选条件">
            <el-checkbox-group v-model="batchForm.filters">
              <el-checkbox label="draft">草稿状态</el-checkbox>
              <el-checkbox label="submitted">已提交</el-checkbox>
              <el-checkbox label="reviewed">已审核</el-checkbox>
              <el-checkbox label="overdue">超期评价</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
          <el-form-item label="确认操作">
            <el-checkbox v-model="batchForm.confirmed">
              我确认要执行此批量操作
            </el-checkbox>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showBatchDialog = false">取消</el-button>
        <el-button 
          type="primary" 
          :disabled="!batchForm.confirmed"
          @click="handleExecuteBatch"
        >
          执行操作
        </el-button>
      </template>
    </el-dialog>

    <!-- 导出对话框 -->
    <el-dialog
      v-model="showExportDialog"
      title="导出报告"
      width="500px"
    >
      <div class="export-options">
        <el-form :model="exportForm" label-width="120px">
          <el-form-item label="报告类型">
            <el-select v-model="exportForm.type" placeholder="请选择报告类型" style="width: 100%">
              <el-option label="评价汇总报告" value="summary" />
              <el-option label="详细评价报告" value="detailed" />
              <el-option label="统计分析报告" value="statistics" />
              <el-option label="质量趋势报告" value="trend" />
            </el-select>
          </el-form-item>
          <el-form-item label="时间范围">
            <el-date-picker
              v-model="exportForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              style="width: 100%"
            />
          </el-form-item>
          <el-form-item label="导出格式">
            <el-radio-group v-model="exportForm.format">
              <el-radio label="excel">Excel</el-radio>
              <el-radio label="pdf">PDF</el-radio>
              <el-radio label="csv">CSV</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <el-button @click="showExportDialog = false">取消</el-button>
        <el-button type="primary" @click="handleExecuteExport">
          导出
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Document, Clock, Check, Star, User, View, Warning, 
  DataAnalysis, Download, Operation 
} from '@element-plus/icons-vue'
import { useRouter } from 'vue-router'
import EvaluationList from '@/components/TaskEvaluation/EvaluationList.vue'
import EvaluationForm from '@/components/TaskEvaluation/EvaluationForm.vue'
import StatisticsDashboard from '@/components/QualityStatistics/StatisticsDashboard.vue'
import QualityRanking from '@/components/QualityStatistics/QualityRanking.vue'
import EvaluationTemplates from '@/components/TaskEvaluation/EvaluationTemplates.vue'
import { 
  getTaskEvaluationStatistics,
  countEvaluationsByStatus,
  getPendingEvaluations,
  getOverdueEvaluations
} from '@/api/evaluation'
import { useUserStore } from '@/stores/user'

// 响应式数据
const router = useRouter()
const userStore = useUserStore()
const activeTab = ref('list')
const showEvaluationForm = ref(false)
const showBatchDialog = ref(false)
const showExportDialog = ref(false)
const evaluationListRef = ref()

const stats = reactive({
  totalEvaluations: 0,
  pendingEvaluations: 0,
  publishedEvaluations: 0,
  averageScore: 0
})

const batchForm = reactive({
  operation: '',
  filters: [],
  confirmed: false
})

const exportForm = reactive({
  type: 'summary',
  dateRange: [],
  format: 'excel'
})

// 方法
const loadStatistics = async () => {
  try {
    const [statsResponse, pendingCount, publishedCount] = await Promise.all([
      getTaskEvaluationStatistics(),
      countEvaluationsByStatus('submitted'),
      countEvaluationsByStatus('published')
    ])
    
    const statisticsData = statsResponse.data
    stats.totalEvaluations = statisticsData.totalCount || 0
    stats.pendingEvaluations = pendingCount.data || 0
    stats.publishedEvaluations = publishedCount.data || 0
    stats.averageScore = statisticsData.averageScore || 0
  } catch (error) {
    console.error('Failed to load statistics:', error)
  }
}

const handleCreateEvaluation = () => {
  showEvaluationForm.value = true
}

const handleEvaluationSuccess = () => {
  loadStatistics()
  evaluationListRef.value?.loadEvaluations()
}

const handleTabChange = (tabName: string) => {
  activeTab.value = tabName
}

const handleViewMyEvaluations = () => {
  activeTab.value = 'list'
  // 设置筛选条件为当前用户的评价
  evaluationListRef.value?.setFilter('evaluatorId', userStore.userInfo?.id)
}

const handleViewPendingReviews = () => {
  activeTab.value = 'list'
  evaluationListRef.value?.setFilter('status', 'submitted')
}

const handleViewOverdueEvaluations = async () => {
  try {
    const response = await getOverdueEvaluations()
    if (response.data.length === 0) {
      ElMessage.info('暂无超期评价')
      return
    }
    activeTab.value = 'list'
    evaluationListRef.value?.setOverdueFilter()
  } catch (error) {
    console.error('Failed to load overdue evaluations:', error)
    ElMessage.error('加载超期评价失败')
  }
}

const handleViewStatistics = () => {
  activeTab.value = 'statistics'
}

const handleExportReport = () => {
  showExportDialog.value = true
}

const handleBatchOperations = () => {
  showBatchDialog.value = true
}

const handleExecuteBatch = async () => {
  try {
    await ElMessageBox.confirm(
      `确定要执行批量${batchForm.operation}操作吗？此操作不可撤销。`,
      '确认批量操作',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    // 执行批量操作逻辑
    ElMessage.success('批量操作执行成功')
    showBatchDialog.value = false
    
    // 重置表单
    Object.assign(batchForm, {
      operation: '',
      filters: [],
      confirmed: false
    })
    
    // 刷新数据
    loadStatistics()
    evaluationListRef.value?.loadEvaluations()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to execute batch operation:', error)
      ElMessage.error('批量操作执行失败')
    }
  }
}

const handleExecuteExport = async () => {
  try {
    // 执行导出逻辑
    ElMessage.success('报告导出成功')
    showExportDialog.value = false
    
    // 重置表单
    Object.assign(exportForm, {
      type: 'summary',
      dateRange: [],
      format: 'excel'
    })
  } catch (error) {
    console.error('Failed to export report:', error)
    ElMessage.error('报告导出失败')
  }
}

// 生命周期
onMounted(() => {
  loadStatistics()
})
</script>

<style scoped>
.evaluation-management {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.page-header {
  margin-bottom: 20px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: white;
  padding: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px 0;
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.page-description {
  margin: 0;
  color: #606266;
  font-size: 14px;
}

.header-right {
  display: flex;
  gap: 12px;
}

.stats-section {
  margin-bottom: 20px;
}

.stats-card {
  height: 100px;
}

.stats-item {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 16px;
}

.stats-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.stats-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stats-icon.pending {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stats-icon.published {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.stats-icon.average {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.stats-content {
  flex: 1;
}

.stats-value {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.stats-label {
  font-size: 14px;
  color: #909399;
}

.quick-actions {
  margin-bottom: 20px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
}

.action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 20px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.action-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
  transform: translateY(-2px);
}

.action-item .el-icon {
  font-size: 24px;
  color: #409eff;
}

.action-item span {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.content-card {
  background: white;
  border-radius: 8px;
}

.batch-operations,
.export-options {
  padding: 20px 0;
}

:deep(.el-tabs__header) {
  margin-bottom: 20px;
}

:deep(.el-tabs__item) {
  font-size: 16px;
  font-weight: 500;
}

:deep(.el-tabs__content) {
  padding: 0;
}
</style>
