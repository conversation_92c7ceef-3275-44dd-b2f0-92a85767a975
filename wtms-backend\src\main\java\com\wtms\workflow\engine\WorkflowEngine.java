package com.wtms.workflow.engine;

import com.wtms.entity.WorkflowInstance;
import com.wtms.entity.WorkflowTask;
import com.wtms.workflow.model.WorkflowContext;
import com.wtms.workflow.model.WorkflowExecutionResult;

import java.util.List;
import java.util.Map;

/**
 * 工作流引擎接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface WorkflowEngine {

    /**
     * 启动工作流实例
     *
     * @param definitionId 工作流定义ID
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param variables 初始变量
     * @param starterId 启动者ID
     * @return 工作流实例
     */
    WorkflowInstance startProcess(String definitionId, String businessId, String businessType, 
                                 Map<String, Object> variables, String starterId);

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @param variables 任务变量
     * @param userId 用户ID
     * @return 执行结果
     */
    WorkflowExecutionResult completeTask(String taskId, Map<String, Object> variables, String userId);

    /**
     * 认领任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean claimTask(String taskId, String userId);

    /**
     * 取消认领任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean unclaimTask(String taskId, String userId);

    /**
     * 委托任务
     *
     * @param taskId 任务ID
     * @param delegateId 委托给的用户ID
     * @param userId 当前用户ID
     * @return 是否成功
     */
    boolean delegateTask(String taskId, String delegateId, String userId);

    /**
     * 分配任务
     *
     * @param taskId 任务ID
     * @param assigneeId 分配给的用户ID
     * @param userId 当前用户ID
     * @return 是否成功
     */
    boolean assignTask(String taskId, String assigneeId, String userId);

    /**
     * 暂停工作流实例
     *
     * @param instanceId 实例ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean suspendInstance(String instanceId, String userId);

    /**
     * 恢复工作流实例
     *
     * @param instanceId 实例ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean resumeInstance(String instanceId, String userId);

    /**
     * 终止工作流实例
     *
     * @param instanceId 实例ID
     * @param reason 终止原因
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean terminateInstance(String instanceId, String reason, String userId);

    /**
     * 取消工作流实例
     *
     * @param instanceId 实例ID
     * @param reason 取消原因
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean cancelInstance(String instanceId, String reason, String userId);

    /**
     * 跳转到指定节点
     *
     * @param instanceId 实例ID
     * @param targetNodeId 目标节点ID
     * @param variables 变量
     * @param userId 用户ID
     * @return 执行结果
     */
    WorkflowExecutionResult jumpToNode(String instanceId, String targetNodeId, 
                                      Map<String, Object> variables, String userId);

    /**
     * 回退到上一个节点
     *
     * @param instanceId 实例ID
     * @param variables 变量
     * @param userId 用户ID
     * @return 执行结果
     */
    WorkflowExecutionResult rollbackToPreviousNode(String instanceId, Map<String, Object> variables, String userId);

    /**
     * 获取用户的待办任务
     *
     * @param userId 用户ID
     * @return 待办任务列表
     */
    List<WorkflowTask> getUserTodoTasks(String userId);

    /**
     * 获取用户的候选任务
     *
     * @param userId 用户ID
     * @return 候选任务列表
     */
    List<WorkflowTask> getUserCandidateTasks(String userId);

    /**
     * 获取工作流实例的当前任务
     *
     * @param instanceId 实例ID
     * @return 当前任务列表
     */
    List<WorkflowTask> getInstanceCurrentTasks(String instanceId);

    /**
     * 获取工作流实例的历史任务
     *
     * @param instanceId 实例ID
     * @return 历史任务列表
     */
    List<WorkflowTask> getInstanceHistoryTasks(String instanceId);

    /**
     * 获取工作流实例的执行路径
     *
     * @param instanceId 实例ID
     * @return 执行路径
     */
    List<String> getInstanceExecutionPath(String instanceId);

    /**
     * 获取工作流实例的下一个可能节点
     *
     * @param instanceId 实例ID
     * @return 下一个可能节点列表
     */
    List<String> getInstanceNextPossibleNodes(String instanceId);

    /**
     * 检查用户是否可以完成任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否可以完成
     */
    boolean canCompleteTask(String taskId, String userId);

    /**
     * 检查用户是否可以认领任务
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 是否可以认领
     */
    boolean canClaimTask(String taskId, String userId);

    /**
     * 检查工作流实例是否可以暂停
     *
     * @param instanceId 实例ID
     * @return 是否可以暂停
     */
    boolean canSuspendInstance(String instanceId);

    /**
     * 检查工作流实例是否可以终止
     *
     * @param instanceId 实例ID
     * @return 是否可以终止
     */
    boolean canTerminateInstance(String instanceId);

    /**
     * 验证工作流定义
     *
     * @param definitionId 工作流定义ID
     * @return 验证结果
     */
    WorkflowExecutionResult validateDefinition(String definitionId);

    /**
     * 执行工作流节点
     *
     * @param context 工作流上下文
     * @return 执行结果
     */
    WorkflowExecutionResult executeNode(WorkflowContext context);

    /**
     * 计算条件表达式
     *
     * @param expression 条件表达式
     * @param variables 变量
     * @return 计算结果
     */
    boolean evaluateCondition(String expression, Map<String, Object> variables);

    /**
     * 获取工作流引擎统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getEngineStatistics();

    /**
     * 清理已完成的工作流实例
     *
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanupCompletedInstances(int days);

    /**
     * 处理超期任务
     *
     * @return 处理数量
     */
    int handleOverdueTasks();

    /**
     * 重启失败的工作流实例
     *
     * @param instanceId 实例ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean restartFailedInstance(String instanceId, String userId);
}
