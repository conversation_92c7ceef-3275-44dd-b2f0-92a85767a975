package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wtms.entity.Permission;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Set;

/**
 * 权限Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface PermissionMapper extends BaseMapper<Permission> {

    /**
     * 查询所有权限（树形结构）
     */
    List<Permission> selectPermissionTree();

    /**
     * 根据父ID查询子权限
     */
    @Select("SELECT * FROM permissions WHERE parent_id = #{parentId} AND deleted_at IS NULL ORDER BY sort_order ASC, created_at ASC")
    List<Permission> selectByParentId(@Param("parentId") String parentId);

    /**
     * 查询根权限
     */
    @Select("SELECT * FROM permissions WHERE parent_id IS NULL AND deleted_at IS NULL ORDER BY sort_order ASC, created_at ASC")
    List<Permission> selectRootPermissions();

    /**
     * 根据权限编码查询权限
     */
    @Select("SELECT * FROM permissions WHERE code = #{code} AND deleted_at IS NULL")
    Permission selectByCode(@Param("code") String code);

    /**
     * 根据权限类型查询权限
     */
    @Select("SELECT * FROM permissions WHERE type = #{type} AND is_enabled = TRUE AND deleted_at IS NULL ORDER BY sort_order ASC")
    List<Permission> selectByType(@Param("type") String type);

    /**
     * 根据权限分组查询权限
     */
    @Select("SELECT * FROM permissions WHERE group_name = #{groupName} AND is_enabled = TRUE AND deleted_at IS NULL ORDER BY sort_order ASC")
    List<Permission> selectByGroup(@Param("groupName") String groupName);

    /**
     * 根据用户ID查询用户权限
     */
    List<Permission> selectPermissionsByUserId(@Param("userId") String userId);

    /**
     * 根据角色ID查询角色权限
     */
    List<Permission> selectPermissionsByRoleId(@Param("roleId") String roleId);

    /**
     * 根据用户ID查询用户权限编码
     */
    Set<String> selectPermissionCodesByUserId(@Param("userId") String userId);

    /**
     * 根据角色ID查询角色权限编码
     */
    Set<String> selectPermissionCodesByRoleId(@Param("roleId") String roleId);

    /**
     * 根据用户ID查询菜单权限
     */
    List<Permission> selectMenuPermissionsByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID查询按钮权限
     */
    Set<String> selectButtonPermissionsByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID查询API权限
     */
    Set<String> selectApiPermissionsByUserId(@Param("userId") String userId);

    /**
     * 根据用户ID查询数据权限
     */
    Set<String> selectDataPermissionsByUserId(@Param("userId") String userId);

    /**
     * 检查权限编码是否存在
     */
    @Select("SELECT COUNT(*) FROM permissions WHERE code = #{code} AND deleted_at IS NULL")
    int countByCode(@Param("code") String code);

    /**
     * 检查权限名称是否存在
     */
    @Select("SELECT COUNT(*) FROM permissions WHERE name = #{name} AND deleted_at IS NULL")
    int countByName(@Param("name") String name);

    /**
     * 检查是否存在子权限
     */
    @Select("SELECT COUNT(*) FROM permissions WHERE parent_id = #{parentId} AND deleted_at IS NULL")
    int countChildren(@Param("parentId") String parentId);

    /**
     * 获取权限的最大层级
     */
    @Select("SELECT COALESCE(MAX(level), 0) FROM permissions WHERE deleted_at IS NULL")
    int getMaxLevel();

    /**
     * 获取同级权限的最大排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) FROM permissions WHERE parent_id = #{parentId} AND deleted_at IS NULL")
    int getMaxSortOrder(@Param("parentId") String parentId);

    /**
     * 批量插入权限
     */
    int batchInsert(@Param("permissions") List<Permission> permissions);

    /**
     * 批量更新权限状态
     */
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("isEnabled") Boolean isEnabled);

    /**
     * 根据资源和操作查询权限
     */
    @Select("SELECT * FROM permissions WHERE resource = #{resource} AND action = #{action} AND deleted_at IS NULL")
    Permission selectByResourceAndAction(@Param("resource") String resource, @Param("action") String action);

    /**
     * 查询启用的权限
     */
    @Select("SELECT * FROM permissions WHERE is_enabled = TRUE AND deleted_at IS NULL ORDER BY sort_order ASC, created_at ASC")
    List<Permission> selectEnabledPermissions();

    /**
     * 查询系统权限
     */
    @Select("SELECT * FROM permissions WHERE is_system = TRUE AND deleted_at IS NULL ORDER BY sort_order ASC, created_at ASC")
    List<Permission> selectSystemPermissions();

    /**
     * 查询非系统权限
     */
    @Select("SELECT * FROM permissions WHERE is_system = FALSE AND deleted_at IS NULL ORDER BY sort_order ASC, created_at ASC")
    List<Permission> selectNonSystemPermissions();
}
