package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wtms.entity.EvaluationDimension;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.util.List;

/**
 * 评价维度Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface EvaluationDimensionMapper extends BaseMapper<EvaluationDimension> {

    /**
     * 根据任务评价ID查询维度
     */
    @Select("SELECT * FROM evaluation_dimensions WHERE task_evaluation_id = #{taskEvaluationId} AND deleted_at IS NULL ORDER BY sort_order ASC, created_at ASC")
    List<EvaluationDimension> selectByTaskEvaluationId(@Param("taskEvaluationId") String taskEvaluationId);

    /**
     * 根据维度编码查询维度
     */
    @Select("SELECT * FROM evaluation_dimensions WHERE dimension_code = #{dimensionCode} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<EvaluationDimension> selectByDimensionCode(@Param("dimensionCode") String dimensionCode);

    /**
     * 根据维度名称查询维度
     */
    @Select("SELECT * FROM evaluation_dimensions WHERE dimension_name = #{dimensionName} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<EvaluationDimension> selectByDimensionName(@Param("dimensionName") String dimensionName);

    /**
     * 查询启用的维度
     */
    @Select("SELECT * FROM evaluation_dimensions WHERE is_enabled = TRUE AND deleted_at IS NULL ORDER BY sort_order ASC, created_at ASC")
    List<EvaluationDimension> selectEnabledDimensions();

    /**
     * 查询必填维度
     */
    @Select("SELECT * FROM evaluation_dimensions WHERE is_required = TRUE AND deleted_at IS NULL ORDER BY sort_order ASC, created_at ASC")
    List<EvaluationDimension> selectRequiredDimensions();

    /**
     * 根据任务评价ID和维度编码查询维度
     */
    @Select("SELECT * FROM evaluation_dimensions WHERE task_evaluation_id = #{taskEvaluationId} AND dimension_code = #{dimensionCode} AND deleted_at IS NULL")
    EvaluationDimension selectByTaskEvaluationAndCode(@Param("taskEvaluationId") String taskEvaluationId, 
                                                     @Param("dimensionCode") String dimensionCode);

    /**
     * 统计维度数量
     */
    @Select("SELECT COUNT(*) FROM evaluation_dimensions WHERE deleted_at IS NULL")
    int countAll();

    /**
     * 根据任务评价ID统计维度数量
     */
    @Select("SELECT COUNT(*) FROM evaluation_dimensions WHERE task_evaluation_id = #{taskEvaluationId} AND deleted_at IS NULL")
    int countByTaskEvaluationId(@Param("taskEvaluationId") String taskEvaluationId);

    /**
     * 统计已评分维度数量
     */
    @Select("SELECT COUNT(*) FROM evaluation_dimensions WHERE task_evaluation_id = #{taskEvaluationId} AND score IS NOT NULL AND deleted_at IS NULL")
    int countScoredDimensions(@Param("taskEvaluationId") String taskEvaluationId);

    /**
     * 统计必填维度数量
     */
    @Select("SELECT COUNT(*) FROM evaluation_dimensions WHERE task_evaluation_id = #{taskEvaluationId} AND is_required = TRUE AND deleted_at IS NULL")
    int countRequiredDimensions(@Param("taskEvaluationId") String taskEvaluationId);

    /**
     * 统计已完成必填维度数量
     */
    @Select("SELECT COUNT(*) FROM evaluation_dimensions WHERE task_evaluation_id = #{taskEvaluationId} AND is_required = TRUE AND score IS NOT NULL AND deleted_at IS NULL")
    int countCompletedRequiredDimensions(@Param("taskEvaluationId") String taskEvaluationId);

    /**
     * 启用维度
     */
    @Update("UPDATE evaluation_dimensions SET is_enabled = TRUE, updated_at = NOW() WHERE id = #{dimensionId}")
    int enableDimension(@Param("dimensionId") String dimensionId);

    /**
     * 禁用维度
     */
    @Update("UPDATE evaluation_dimensions SET is_enabled = FALSE, updated_at = NOW() WHERE id = #{dimensionId}")
    int disableDimension(@Param("dimensionId") String dimensionId);

    /**
     * 设置为必填
     */
    @Update("UPDATE evaluation_dimensions SET is_required = TRUE, updated_at = NOW() WHERE id = #{dimensionId}")
    int setRequired(@Param("dimensionId") String dimensionId);

    /**
     * 取消必填
     */
    @Update("UPDATE evaluation_dimensions SET is_required = FALSE, updated_at = NOW() WHERE id = #{dimensionId}")
    int unsetRequired(@Param("dimensionId") String dimensionId);

    /**
     * 更新维度评分
     */
    @Update("UPDATE evaluation_dimensions SET score = #{score}, updated_at = NOW() WHERE id = #{dimensionId}")
    int updateScore(@Param("dimensionId") String dimensionId, @Param("score") BigDecimal score);

    /**
     * 批量删除维度
     */
    int batchDeleteDimensions(@Param("dimensionIds") List<String> dimensionIds);

    /**
     * 批量更新维度状态
     */
    int batchUpdateStatus(@Param("dimensionIds") List<String> dimensionIds, @Param("isEnabled") Boolean isEnabled);

    /**
     * 计算任务评价的加权总分
     */
    @Select("SELECT SUM(score * weight) / SUM(weight) FROM evaluation_dimensions WHERE task_evaluation_id = #{taskEvaluationId} AND score IS NOT NULL AND weight IS NOT NULL AND deleted_at IS NULL")
    BigDecimal calculateWeightedScore(@Param("taskEvaluationId") String taskEvaluationId);

    /**
     * 计算任务评价的平均分
     */
    @Select("SELECT AVG(score) FROM evaluation_dimensions WHERE task_evaluation_id = #{taskEvaluationId} AND score IS NOT NULL AND deleted_at IS NULL")
    BigDecimal calculateAverageScore(@Param("taskEvaluationId") String taskEvaluationId);

    /**
     * 查询维度统计信息
     */
    Object selectDimensionStatistics();

    /**
     * 查询维度使用频率统计
     */
    List<Object> selectDimensionUsageStatistics();

    /**
     * 查询维度评分分布统计
     */
    List<Object> selectDimensionScoreDistribution(@Param("dimensionCode") String dimensionCode);

    /**
     * 查询维度平均分统计
     */
    List<Object> selectDimensionAverageScores();

    /**
     * 查询最高分维度
     */
    @Select("SELECT * FROM evaluation_dimensions WHERE score IS NOT NULL AND deleted_at IS NULL ORDER BY score DESC LIMIT #{limit}")
    List<EvaluationDimension> selectHighestScoreDimensions(@Param("limit") Integer limit);

    /**
     * 查询最低分维度
     */
    @Select("SELECT * FROM evaluation_dimensions WHERE score IS NOT NULL AND deleted_at IS NULL ORDER BY score ASC LIMIT #{limit}")
    List<EvaluationDimension> selectLowestScoreDimensions(@Param("limit") Integer limit);

    /**
     * 查询未评分维度
     */
    @Select("SELECT * FROM evaluation_dimensions WHERE score IS NULL AND deleted_at IS NULL ORDER BY created_at ASC")
    List<EvaluationDimension> selectUnscoredDimensions();

    /**
     * 查询维度完成率
     */
    Object selectDimensionCompletionRate(@Param("taskEvaluationId") String taskEvaluationId);

    /**
     * 检查维度编码是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM evaluation_dimensions WHERE dimension_code = #{dimensionCode} AND deleted_at IS NULL")
    boolean existsByDimensionCode(@Param("dimensionCode") String dimensionCode);

    /**
     * 检查维度名称是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM evaluation_dimensions WHERE dimension_name = #{dimensionName} AND task_evaluation_id = #{taskEvaluationId} AND deleted_at IS NULL")
    boolean existsByDimensionName(@Param("dimensionName") String dimensionName, @Param("taskEvaluationId") String taskEvaluationId);

    /**
     * 获取下一个排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) + 1 FROM evaluation_dimensions WHERE task_evaluation_id = #{taskEvaluationId} AND deleted_at IS NULL")
    Integer getNextSortOrder(@Param("taskEvaluationId") String taskEvaluationId);

    /**
     * 重新排序维度
     */
    int reorderDimensions(@Param("taskEvaluationId") String taskEvaluationId, @Param("dimensionOrders") List<Object> dimensionOrders);

    /**
     * 复制维度到其他评价
     */
    int copyDimensionsToEvaluation(@Param("sourceEvaluationId") String sourceEvaluationId, 
                                  @Param("targetEvaluationId") String targetEvaluationId);

    /**
     * 查询维度模板
     */
    List<EvaluationDimension> selectDimensionTemplates(@Param("templateType") String templateType);

    /**
     * 应用维度模板
     */
    int applyDimensionTemplate(@Param("taskEvaluationId") String taskEvaluationId, 
                              @Param("templateType") String templateType);
}
