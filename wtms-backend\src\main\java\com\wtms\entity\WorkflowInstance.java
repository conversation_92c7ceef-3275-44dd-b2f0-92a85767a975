package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 工作流实例实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("workflow_instances")
@Schema(description = "工作流实例实体")
public class WorkflowInstance implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "实例ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "工作流定义ID")
    @TableField("workflow_definition_id")
    private String workflowDefinitionId;

    @Schema(description = "关联业务ID")
    @TableField("business_id")
    private String businessId;

    @Schema(description = "业务类型")
    @TableField("business_type")
    private String businessType;

    @Schema(description = "实例名称")
    @TableField("instance_name")
    private String instanceName;

    @Schema(description = "实例状态")
    @TableField("status")
    private String status;

    @Schema(description = "当前节点ID")
    @TableField("current_node_id")
    private String currentNodeId;

    @Schema(description = "启动者ID")
    @TableField("starter_id")
    private String starterId;

    @Schema(description = "启动时间")
    @TableField("started_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startedAt;

    @Schema(description = "结束时间")
    @TableField("ended_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime endedAt;

    @Schema(description = "暂停时间")
    @TableField("suspended_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime suspendedAt;

    @Schema(description = "实例变量JSON")
    @TableField("variables_json")
    private String variablesJson;

    @Schema(description = "优先级")
    @TableField("priority")
    private Integer priority;

    @Schema(description = "到期时间")
    @TableField("due_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueDate;

    @Schema(description = "父实例ID")
    @TableField("parent_instance_id")
    private String parentInstanceId;

    @Schema(description = "根实例ID")
    @TableField("root_instance_id")
    private String rootInstanceId;

    @Schema(description = "租户ID")
    @TableField("tenant_id")
    private String tenantId;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "工作流定义信息")
    @TableField(exist = false)
    private WorkflowDefinition workflowDefinition;

    @Schema(description = "当前节点信息")
    @TableField(exist = false)
    private WorkflowNode currentNode;

    @Schema(description = "启动者信息")
    @TableField(exist = false)
    private User starter;

    @Schema(description = "任务实例列表")
    @TableField(exist = false)
    private List<WorkflowTask> tasks;

    @Schema(description = "子实例列表")
    @TableField(exist = false)
    private List<WorkflowInstance> childInstances;

    @Schema(description = "执行历史列表")
    @TableField(exist = false)
    private List<WorkflowHistory> histories;

    /**
     * 实例状态枚举
     */
    public enum Status {
        RUNNING("running", "运行中"),
        COMPLETED("completed", "已完成"),
        SUSPENDED("suspended", "已暂停"),
        TERMINATED("terminated", "已终止"),
        CANCELLED("cancelled", "已取消"),
        ERROR("error", "错误"),
        PENDING("pending", "等待中");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return RUNNING;
        }
    }

    /**
     * 业务类型枚举
     */
    public enum BusinessType {
        TASK("task", "任务"),
        PROJECT("project", "项目"),
        APPROVAL("approval", "审批"),
        REQUEST("request", "申请"),
        ORDER("order", "订单"),
        CUSTOM("custom", "自定义");

        private final String code;
        private final String description;

        BusinessType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static BusinessType fromCode(String code) {
            for (BusinessType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return CUSTOM;
        }
    }

    /**
     * 检查实例是否运行中
     */
    public boolean isRunning() {
        return Status.RUNNING.getCode().equals(this.status);
    }

    /**
     * 检查实例是否已完成
     */
    public boolean isCompleted() {
        return Status.COMPLETED.getCode().equals(this.status);
    }

    /**
     * 检查实例是否已暂停
     */
    public boolean isSuspended() {
        return Status.SUSPENDED.getCode().equals(this.status);
    }

    /**
     * 检查实例是否已终止
     */
    public boolean isTerminated() {
        return Status.TERMINATED.getCode().equals(this.status);
    }

    /**
     * 检查实例是否已取消
     */
    public boolean isCancelled() {
        return Status.CANCELLED.getCode().equals(this.status);
    }

    /**
     * 检查实例是否有错误
     */
    public boolean hasError() {
        return Status.ERROR.getCode().equals(this.status);
    }

    /**
     * 检查实例是否活跃
     */
    public boolean isActive() {
        return isRunning() || Status.PENDING.getCode().equals(this.status);
    }

    /**
     * 检查实例是否已结束
     */
    public boolean isEnded() {
        return isCompleted() || isTerminated() || isCancelled() || hasError();
    }

    /**
     * 获取状态描述
     */
    public String getStatusText() {
        return Status.fromCode(this.status).getDescription();
    }

    /**
     * 获取业务类型描述
     */
    public String getBusinessTypeText() {
        return BusinessType.fromCode(this.businessType).getDescription();
    }

    /**
     * 计算执行时长（毫秒）
     */
    public Long getDuration() {
        if (startedAt == null) {
            return null;
        }
        LocalDateTime endTime = endedAt != null ? endedAt : LocalDateTime.now();
        return java.time.Duration.between(startedAt, endTime).toMillis();
    }

    /**
     * 检查是否超期
     */
    public boolean isOverdue() {
        return dueDate != null && LocalDateTime.now().isAfter(dueDate) && isActive();
    }
}
