# WTMS 数据库初始化脚本

## 📋 概述

本目录包含WTMS（工作任务管理系统）的完整数据库初始化脚本，用于创建数据表结构并初始化基础数据。

## 📁 文件结构

```
sql/
├── README.md                 # 本说明文档
├── 00_execute_all.sql       # 完整执行脚本（MySQL命令行使用）
├── 01_create_tables.sql     # 数据表结构创建脚本
├── 02_init_data.sql         # 基础数据初始化脚本
├── execute_init.py          # Python执行脚本（推荐使用）
└── verify_database.py       # 数据库验证脚本
```

## 🚀 快速开始

### 方法一：使用Python执行脚本（推荐）

```bash
# 1. 确保已安装pymysql
pip install pymysql

# 2. 执行数据库初始化
python wtms-backend/sql/execute_init.py

# 3. 验证数据库（可选）
python wtms-backend/sql/verify_database.py
```

### 方法二：使用MySQL命令行

```bash
# 1. 连接到MySQL服务器
mysql -h localhost -P 3308 -u root -p

# 2. 执行完整初始化脚本
source /path/to/wtms-backend/sql/00_execute_all.sql;
```

### 方法三：分步执行

```bash
# 1. 连接到MySQL并创建数据库
mysql -h localhost -P 3308 -u root -p
CREATE DATABASE IF NOT EXISTS wtms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE wtms;

# 2. 创建数据表
source /path/to/wtms-backend/sql/01_create_tables.sql;

# 3. 初始化基础数据
source /path/to/wtms-backend/sql/02_init_data.sql;
```

## 📊 数据库结构

### 核心数据表

| 表名 | 说明 | 记录数 |
|------|------|--------|
| `users` | 用户表 | 8 |
| `departments` | 部门表 | 8 |
| `roles` | 角色表 | 8 |
| `permissions` | 权限表 | 45+ |
| `user_roles` | 用户角色关联表 | 8 |
| `role_permissions` | 角色权限关联表 | 100+ |
| `task_categories` | 任务分类表 | 11 |
| `tasks` | 任务表 | 8 |
| `task_comments` | 任务评论表 | 8 |

### 数据库特性

- **字符集**: utf8mb4
- **排序规则**: utf8mb4_unicode_ci
- **存储引擎**: InnoDB
- **外键约束**: 已启用
- **软删除**: 支持（deleted_at字段）
- **自动时间戳**: 支持（created_at, updated_at）

## 👥 默认用户账户

| 用户名 | 密码 | 角色 | 部门 | 说明 |
|--------|------|------|------|------|
| admin | admin123 | 超级管理员 | 总经理办公室 | 系统超级管理员 |
| pm001 | admin123 | 项目经理 | 产品部 | 项目经理 |
| dev001 | admin123 | 开发者 | 前端开发组 | 前端开发 |
| dev002 | admin123 | 开发者 | 后端开发组 | 后端开发 |
| test001 | admin123 | 测试员 | 测试部 | 测试工程师 |
| product001 | admin123 | 产品经理 | 产品部 | 产品经理 |
| op001 | admin123 | 运营人员 | 运营部 | 运营专员 |
| user001 | admin123 | 普通用户 | 运营部 | 普通用户 |

## 🔐 权限体系

### 角色权限矩阵

| 角色 | 系统管理 | 用户管理 | 角色管理 | 权限管理 | 任务管理 | 项目管理 | 报表管理 |
|------|----------|----------|----------|----------|----------|----------|----------|
| 超级管理员 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| 系统管理员 | ✅ | ✅ | ✅ | 部分 | ✅ | ✅ | ✅ |
| 项目经理 | ❌ | 查看 | ❌ | ❌ | ✅ | ✅ | ✅ |
| 开发者 | ❌ | 查看 | ❌ | ❌ | 部分 | 查看 | ❌ |
| 测试员 | ❌ | 查看 | ❌ | ❌ | 部分 | 查看 | ❌ |
| 产品经理 | ❌ | 查看 | ❌ | ❌ | 部分 | ✅ | 查看 |
| 运营人员 | ❌ | 查看 | ❌ | ❌ | 部分 | 查看 | 查看 |
| 普通用户 | ❌ | ❌ | ❌ | ❌ | 查看 | 查看 | ❌ |

### 权限分组

- **system**: 系统管理权限
- **user**: 用户管理权限
- **role**: 角色管理权限
- **permission**: 权限管理权限
- **task**: 任务管理权限
- **project**: 项目管理权限
- **report**: 报表管理权限

## 🏗️ 数据表设计说明

### 用户管理模块

- **users**: 用户基本信息，支持软删除
- **departments**: 部门信息，支持层级结构
- **roles**: 角色定义，区分系统角色和自定义角色
- **permissions**: 权限定义，支持层级结构和权限表达式
- **user_roles**: 用户角色关联，支持角色过期时间
- **role_permissions**: 角色权限关联

### 任务管理模块

- **task_categories**: 任务分类，支持层级结构
- **tasks**: 任务主表，包含完整的任务生命周期信息
- **task_comments**: 任务评论，支持回复和附件

## 🔧 配置要求

### MySQL版本要求
- MySQL 8.0+ （推荐）
- MySQL 5.7+ （最低要求）

### 配置参数
```sql
-- 推荐的MySQL配置
character_set_server = utf8mb4
collation_server = utf8mb4_unicode_ci
innodb_file_format = Barracuda
innodb_file_per_table = ON
innodb_large_prefix = ON
```

## 🧪 验证安装

执行完初始化脚本后，可以通过以下方式验证：

### 1. 检查表结构
```sql
-- 查看所有表
SHOW TABLES;

-- 查看表结构
DESCRIBE users;
DESCRIBE tasks;
```

### 2. 检查数据
```sql
-- 检查用户数据
SELECT username, full_name, status FROM users;

-- 检查权限数据
SELECT COUNT(*) as permission_count FROM permissions;

-- 检查角色权限关联
SELECT r.name, COUNT(rp.permission_id) as permission_count 
FROM roles r 
LEFT JOIN role_permissions rp ON r.id = rp.role_id 
GROUP BY r.id, r.name;
```

### 3. 测试登录
使用默认管理员账户测试系统登录：
- 用户名: `admin`
- 密码: `admin123`

## 🚨 注意事项

1. **备份重要**: 在生产环境执行前，请务必备份现有数据
2. **权限检查**: 确保MySQL用户具有CREATE、DROP、INSERT等权限
3. **字符集**: 确保数据库和表使用utf8mb4字符集
4. **外键约束**: 脚本会创建外键约束，删除数据时需注意依赖关系
5. **密码安全**: 生产环境请及时修改默认密码

## 🔄 更新和维护

### 数据库迁移
如需更新数据库结构，建议：
1. 创建新的迁移脚本
2. 在测试环境验证
3. 备份生产数据
4. 执行迁移脚本

### 数据备份
```bash
# 备份整个数据库
mysqldump -h localhost -P 3308 -u root -p wtms > wtms_backup_$(date +%Y%m%d_%H%M%S).sql

# 仅备份结构
mysqldump -h localhost -P 3308 -u root -p --no-data wtms > wtms_structure.sql

# 仅备份数据
mysqldump -h localhost -P 3308 -u root -p --no-create-info wtms > wtms_data.sql
```

## 📞 技术支持

如遇到问题，请检查：
1. MySQL服务是否正常运行
2. 连接参数是否正确
3. 用户权限是否充足
4. 字符集设置是否正确

---

**版本**: 1.0.0  
**更新时间**: 2025-07-24  
**兼容性**: MySQL 8.0+, Spring Boot 2.7.18, MyBatis Plus 3.5.4+
