/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElLink: typeof import('element-plus/es')['ElLink']
    EvaluationDetail: typeof import('./src/components/TaskEvaluation/EvaluationDetail.vue')['default']
    EvaluationForm: typeof import('./src/components/TaskEvaluation/EvaluationForm.vue')['default']
    EvaluationList: typeof import('./src/components/TaskEvaluation/EvaluationList.vue')['default']
    EvaluationTemplates: typeof import('./src/components/TaskEvaluation/EvaluationTemplates.vue')['default']
    QualityRanking: typeof import('./src/components/QualityStatistics/QualityRanking.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    StatisticsDashboard: typeof import('./src/components/QualityStatistics/StatisticsDashboard.vue')['default']
    TaskComment: typeof import('./src/components/TaskComment/index.vue')['default']
    TemplateEditor: typeof import('./src/components/TaskEvaluation/TemplateEditor.vue')['default']
    WorkflowDesigner: typeof import('./src/components/WorkflowDesigner/index.vue')['default']
  }
}
