name: WTMS CI/CD Pipeline

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

env:
  JAVA_VERSION: '17'
  NODE_VERSION: '18'
  MYSQL_VERSION: '8.0'
  REDIS_VERSION: '7.0'

jobs:
  # 代码质量检查
  code-quality:
    runs-on: ubuntu-latest
    name: 代码质量检查
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: 设置Java环境
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: wtms-frontend/package-lock.json

    - name: 缓存Maven依赖
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: 后端代码检查
      run: |
        cd wtms-backend
        ./mvnw checkstyle:check
        ./mvnw spotbugs:check

    - name: 前端代码检查
      run: |
        cd wtms-frontend
        npm ci
        npm run lint
        npm run type-check

  # 单元测试
  unit-tests:
    runs-on: ubuntu-latest
    name: 单元测试
    needs: code-quality
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test123456
          MYSQL_DATABASE: wtms_test
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:7.0
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Java环境
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: wtms-frontend/package-lock.json

    - name: 缓存Maven依赖
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}
        restore-keys: ${{ runner.os }}-m2

    - name: 等待MySQL启动
      run: |
        while ! mysqladmin ping -h"127.0.0.1" -P3306 -uroot -ptest123456 --silent; do
          sleep 1
        done

    - name: 初始化测试数据库
      run: |
        mysql -h127.0.0.1 -P3306 -uroot -ptest123456 wtms_test < database/schema.sql

    - name: 运行后端单元测试
      run: |
        cd wtms-backend
        ./mvnw clean test
      env:
        SPRING_PROFILES_ACTIVE: test
        SPRING_DATASOURCE_URL: *************************************
        SPRING_DATASOURCE_USERNAME: root
        SPRING_DATASOURCE_PASSWORD: test123456
        SPRING_REDIS_HOST: localhost
        SPRING_REDIS_PORT: 6379

    - name: 运行前端单元测试
      run: |
        cd wtms-frontend
        npm ci
        npm run test:unit

    - name: 生成测试覆盖率报告
      run: |
        cd wtms-backend
        ./mvnw jacoco:report

    - name: 上传覆盖率报告到Codecov
      uses: codecov/codecov-action@v3
      with:
        files: ./wtms-backend/target/site/jacoco/jacoco.xml,./wtms-frontend/coverage/lcov.info
        flags: unittests
        name: codecov-umbrella

  # 集成测试
  integration-tests:
    runs-on: ubuntu-latest
    name: 集成测试
    needs: unit-tests
    
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test123456
          MYSQL_DATABASE: wtms_test
        ports:
          - 3306:3306
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:7.0
        ports:
          - 6379:6379
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Java环境
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: 设置Node.js环境
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: wtms-frontend/package-lock.json

    - name: 初始化测试数据库
      run: |
        mysql -h127.0.0.1 -P3306 -uroot -ptest123456 wtms_test < database/schema.sql
        mysql -h127.0.0.1 -P3306 -uroot -ptest123456 wtms_test < database/test-data.sql

    - name: 构建后端应用
      run: |
        cd wtms-backend
        ./mvnw clean package -DskipTests

    - name: 构建前端应用
      run: |
        cd wtms-frontend
        npm ci
        npm run build

    - name: 启动应用服务
      run: |
        cd wtms-backend
        java -jar target/wtms-backend-*.jar &
        echo $! > app.pid
        sleep 30
      env:
        SPRING_PROFILES_ACTIVE: test
        SPRING_DATASOURCE_URL: *************************************
        SPRING_DATASOURCE_USERNAME: root
        SPRING_DATASOURCE_PASSWORD: test123456
        SPRING_REDIS_HOST: localhost
        SPRING_REDIS_PORT: 6379

    - name: 运行API集成测试
      run: |
        cd wtms-backend
        ./mvnw test -Dtest="**/*IntegrationTest"

    - name: 运行E2E测试
      run: |
        cd wtms-frontend
        npm run test:e2e:headless
      env:
        CYPRESS_BASE_URL: http://localhost:8080

    - name: 停止应用服务
      if: always()
      run: |
        if [ -f wtms-backend/app.pid ]; then
          kill $(cat wtms-backend/app.pid) || true
        fi

  # 构建Docker镜像
  build-docker:
    runs-on: ubuntu-latest
    name: 构建Docker镜像
    needs: integration-tests
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: 登录Docker Hub
      uses: docker/login-action@v3
      with:
        username: ${{ secrets.DOCKER_USERNAME }}
        password: ${{ secrets.DOCKER_PASSWORD }}

    - name: 构建并推送后端镜像
      uses: docker/build-push-action@v5
      with:
        context: ./wtms-backend
        push: true
        tags: |
          wtms/backend:latest
          wtms/backend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

    - name: 构建并推送前端镜像
      uses: docker/build-push-action@v5
      with:
        context: ./wtms-frontend
        push: true
        tags: |
          wtms/frontend:latest
          wtms/frontend:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max

  # 安全扫描
  security-scan:
    runs-on: ubuntu-latest
    name: 安全扫描
    needs: code-quality
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 运行Trivy漏洞扫描
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: 上传Trivy扫描结果
      uses: github/codeql-action/upload-sarif@v2
      if: always()
      with:
        sarif_file: 'trivy-results.sarif'

    - name: 运行CodeQL分析
      uses: github/codeql-action/analyze@v2
      with:
        languages: java, javascript

  # 性能测试
  performance-test:
    runs-on: ubuntu-latest
    name: 性能测试
    needs: integration-tests
    if: github.ref == 'refs/heads/main'
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 启动测试环境
      run: |
        docker-compose -f docker-compose.test.yml up -d
        sleep 60

    - name: 运行JMeter性能测试
      run: |
        docker run --rm -v $(pwd)/docs/testing/performance:/tests \
          justb4/jmeter:latest \
          -n -t /tests/jmeter-test-plan.jmx \
          -l /tests/results.jtl \
          -j /tests/jmeter.log

    - name: 上传性能测试报告
      uses: actions/upload-artifact@v3
      with:
        name: performance-test-results
        path: docs/testing/performance/results.jtl

    - name: 清理测试环境
      if: always()
      run: |
        docker-compose -f docker-compose.test.yml down

  # 部署到测试环境
  deploy-staging:
    runs-on: ubuntu-latest
    name: 部署到测试环境
    needs: [build-docker, security-scan]
    if: github.ref == 'refs/heads/develop'
    environment: staging
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 部署到测试环境
      run: |
        echo "部署到测试环境..."
        # 这里添加实际的部署脚本
        # 例如：kubectl apply -f k8s/staging/
        # 或者：docker-compose -f docker-compose.staging.yml up -d

  # 部署到生产环境
  deploy-production:
    runs-on: ubuntu-latest
    name: 部署到生产环境
    needs: [build-docker, security-scan, performance-test]
    if: github.ref == 'refs/heads/main'
    environment: production
    
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 部署到生产环境
      run: |
        echo "部署到生产环境..."
        # 这里添加实际的部署脚本
        # 例如：kubectl apply -f k8s/production/
        # 或者：docker-compose -f docker-compose.prod.yml up -d

  # 通知
  notify:
    runs-on: ubuntu-latest
    name: 构建通知
    needs: [deploy-staging, deploy-production]
    if: always()
    
    steps:
    - name: 发送成功通知
      if: ${{ needs.deploy-staging.result == 'success' || needs.deploy-production.result == 'success' }}
      run: |
        echo "部署成功通知"
        # 发送成功通知到Slack、钉钉等

    - name: 发送失败通知
      if: ${{ needs.deploy-staging.result == 'failure' || needs.deploy-production.result == 'failure' }}
      run: |
        echo "部署失败通知"
        # 发送失败通知到Slack、钉钉等
