<template>
  <div class="evaluation-list">
    <!-- 搜索和筛选 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.keyword"
            placeholder="搜索评价内容..."
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item label="评价类型">
          <el-select v-model="searchForm.evaluationType" placeholder="请选择" clearable style="width: 150px">
            <el-option label="自评" value="self" />
            <el-option label="同事评价" value="peer" />
            <el-option label="上级评价" value="supervisor" />
            <el-option label="下级评价" value="subordinate" />
            <el-option label="客户评价" value="customer" />
          </el-select>
        </el-form-item>
        <el-form-item label="状态">
          <el-select v-model="searchForm.status" placeholder="请选择" clearable style="width: 120px">
            <el-option label="草稿" value="draft" />
            <el-option label="已提交" value="submitted" />
            <el-option label="已审核" value="reviewed" />
            <el-option label="已发布" value="published" />
            <el-option label="已归档" value="archived" />
            <el-option label="已拒绝" value="rejected" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 操作栏 -->
    <el-card class="action-card" shadow="never">
      <div class="action-bar">
        <div class="action-left">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新建评价
          </el-button>
          <el-button 
            type="danger" 
            :disabled="!selectedEvaluations.length"
            @click="handleBatchDelete"
          >
            <el-icon><Delete /></el-icon>
            批量删除
          </el-button>
        </div>
        <div class="action-right">
          <el-button-group>
            <el-button :type="viewMode === 'table' ? 'primary' : ''" @click="viewMode = 'table'">
              <el-icon><List /></el-icon>
            </el-button>
            <el-button :type="viewMode === 'card' ? 'primary' : ''" @click="viewMode = 'card'">
              <el-icon><Grid /></el-icon>
            </el-button>
          </el-button-group>
        </div>
      </div>
    </el-card>

    <!-- 评价列表 - 表格视图 -->
    <el-card v-if="viewMode === 'table'" class="list-card" shadow="never">
      <el-table
        v-loading="loading"
        :data="evaluationList"
        @selection-change="handleSelectionChange"
        stripe
      >
        <el-table-column type="selection" width="55" />
        <el-table-column label="任务" min-width="200">
          <template #default="{ row }">
            <div class="task-info">
              <div class="task-title">{{ row.task?.title }}</div>
              <div class="task-desc">{{ row.task?.description }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="被评价者" width="120">
          <template #default="{ row }">
            <div class="user-info">
              <el-avatar :size="32" :src="row.evaluatee?.avatar">
                {{ row.evaluatee?.fullName?.charAt(0) }}
              </el-avatar>
              <span class="user-name">{{ row.evaluatee?.fullName }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="评价类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getEvaluationTypeTagType(row.evaluationType)" size="small">
              {{ getEvaluationTypeText(row.evaluationType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="总体评分" width="120">
          <template #default="{ row }">
            <div class="score-display">
              <el-progress
                :percentage="row.overallScore"
                :color="getScoreColor(row.overallScore)"
                :stroke-width="8"
                text-inside
              />
              <div class="score-grade">{{ getScoreGrade(row.overallScore) }}</div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template #default="{ row }">
            {{ formatDateTime(row.createdAt) }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button size="small" @click="handleView(row)">
              <el-icon><View /></el-icon>
              查看
            </el-button>
            <el-button 
              v-if="canEdit(row)"
              size="small" 
              type="primary" 
              @click="handleEdit(row)"
            >
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-dropdown @command="(command) => handleAction(command, row)">
              <el-button size="small">
                更多<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item v-if="row.status === 'draft'" command="submit">提交</el-dropdown-item>
                  <el-dropdown-item v-if="row.status === 'submitted'" command="review">审核</el-dropdown-item>
                  <el-dropdown-item v-if="row.status === 'reviewed'" command="publish">发布</el-dropdown-item>
                  <el-dropdown-item v-if="row.status === 'published'" command="archive">归档</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 评价列表 - 卡片视图 -->
    <div v-else class="card-view">
      <el-row :gutter="20">
        <el-col v-for="evaluation in evaluationList" :key="evaluation.id" :span="8">
          <el-card class="evaluation-card" shadow="hover">
            <template #header>
              <div class="card-header">
                <div class="task-title">{{ evaluation.task?.title }}</div>
                <el-tag :type="getStatusTagType(evaluation.status)" size="small">
                  {{ getStatusText(evaluation.status) }}
                </el-tag>
              </div>
            </template>
            
            <div class="card-content">
              <div class="evaluatee-info">
                <el-avatar :size="40" :src="evaluation.evaluatee?.avatar">
                  {{ evaluation.evaluatee?.fullName?.charAt(0) }}
                </el-avatar>
                <div class="evaluatee-details">
                  <div class="evaluatee-name">{{ evaluation.evaluatee?.fullName }}</div>
                  <div class="evaluation-type">{{ getEvaluationTypeText(evaluation.evaluationType) }}</div>
                </div>
              </div>
              
              <div class="score-section">
                <div class="overall-score">
                  <span class="score-label">总体评分</span>
                  <div class="score-value">
                    <span class="score-number">{{ evaluation.overallScore }}</span>
                    <span class="score-grade">{{ getScoreGrade(evaluation.overallScore) }}</span>
                  </div>
                </div>
                
                <div class="dimension-scores">
                  <div class="dimension-item">
                    <span>质量</span>
                    <el-progress :percentage="evaluation.qualityScore" :stroke-width="4" :show-text="false" />
                  </div>
                  <div class="dimension-item">
                    <span>效率</span>
                    <el-progress :percentage="evaluation.efficiencyScore" :stroke-width="4" :show-text="false" />
                  </div>
                  <div class="dimension-item">
                    <span>沟通</span>
                    <el-progress :percentage="evaluation.communicationScore" :stroke-width="4" :show-text="false" />
                  </div>
                </div>
              </div>
              
              <div class="evaluation-content">
                <p>{{ evaluation.evaluationContent.substring(0, 100) }}{{ evaluation.evaluationContent.length > 100 ? '...' : '' }}</p>
              </div>
              
              <div class="card-footer">
                <div class="create-time">{{ formatDateTime(evaluation.createdAt) }}</div>
                <div class="card-actions">
                  <el-button size="small" @click="handleView(evaluation)">查看</el-button>
                  <el-button v-if="canEdit(evaluation)" size="small" type="primary" @click="handleEdit(evaluation)">编辑</el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 分页 -->
    <div class="pagination-wrapper">
      <el-pagination
        v-model:current-page="pagination.page"
        v-model:page-size="pagination.size"
        :total="pagination.total"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
      />
    </div>

    <!-- 评价表单对话框 -->
    <el-dialog
      v-model="showEvaluationForm"
      :title="formMode === 'create' ? '新建评价' : '编辑评价'"
      width="80%"
      :close-on-click-modal="false"
    >
      <EvaluationForm
        v-model="showEvaluationForm"
        :evaluation-id="currentEvaluationId"
        :task-id="currentTaskId"
        :evaluatee-id="currentEvaluateeId"
        @success="handleFormSuccess"
      />
    </el-dialog>

    <!-- 评价详情对话框 -->
    <el-dialog
      v-model="showEvaluationDetail"
      title="评价详情"
      width="70%"
    >
      <EvaluationDetail
        v-if="showEvaluationDetail"
        :evaluation-id="currentEvaluationId"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Search, Refresh, Plus, Delete, List, Grid, View, Edit, ArrowDown 
} from '@element-plus/icons-vue'
import { 
  searchTaskEvaluations, 
  deleteTaskEvaluation, 
  batchDeleteTaskEvaluations,
  submitTaskEvaluation,
  reviewTaskEvaluation,
  publishTaskEvaluation,
  archiveTaskEvaluation,
  type TaskEvaluation 
} from '@/api/evaluation'
import EvaluationForm from './EvaluationForm.vue'
import EvaluationDetail from './EvaluationDetail.vue'
import { useUserStore } from '@/stores/user'

// 响应式数据
const userStore = useUserStore()
const loading = ref(false)
const viewMode = ref<'table' | 'card'>('table')
const evaluationList = ref<TaskEvaluation[]>([])
const selectedEvaluations = ref<TaskEvaluation[]>([])
const showEvaluationForm = ref(false)
const showEvaluationDetail = ref(false)
const formMode = ref<'create' | 'edit'>('create')
const currentEvaluationId = ref<string>()
const currentTaskId = ref<string>()
const currentEvaluateeId = ref<string>()

const searchForm = reactive({
  keyword: '',
  evaluationType: '',
  status: ''
})

const pagination = reactive({
  page: 1,
  size: 20,
  total: 0
})

// 方法
const loadEvaluations = async () => {
  loading.value = true
  try {
    const response = await searchTaskEvaluations({
      ...searchForm,
      page: pagination.page,
      size: pagination.size
    })
    evaluationList.value = response.data.records
    pagination.total = response.data.total
  } catch (error) {
    console.error('Failed to load evaluations:', error)
    ElMessage.error('加载评价列表失败')
  } finally {
    loading.value = false
  }
}

const handleSearch = () => {
  pagination.page = 1
  loadEvaluations()
}

const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    evaluationType: '',
    status: ''
  })
  handleSearch()
}

const handleCreate = () => {
  formMode.value = 'create'
  currentEvaluationId.value = undefined
  currentTaskId.value = undefined
  currentEvaluateeId.value = undefined
  showEvaluationForm.value = true
}

const handleEdit = (evaluation: TaskEvaluation) => {
  formMode.value = 'edit'
  currentEvaluationId.value = evaluation.id
  currentTaskId.value = evaluation.taskId
  currentEvaluateeId.value = evaluation.evaluateeId
  showEvaluationForm.value = true
}

const handleView = (evaluation: TaskEvaluation) => {
  currentEvaluationId.value = evaluation.id
  showEvaluationDetail.value = true
}

const handleSelectionChange = (selections: TaskEvaluation[]) => {
  selectedEvaluations.value = selections
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要删除选中的评价吗？此操作不可恢复。', '确认删除', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    const evaluationIds = selectedEvaluations.value.map(item => item.id)
    await batchDeleteTaskEvaluations(evaluationIds)
    
    ElMessage.success('批量删除成功')
    loadEvaluations()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to batch delete evaluations:', error)
      ElMessage.error('批量删除失败')
    }
  }
}

const handleAction = async (command: string, evaluation: TaskEvaluation) => {
  try {
    switch (command) {
      case 'submit':
        await submitTaskEvaluation(evaluation.id)
        ElMessage.success('评价提交成功')
        break
      case 'review':
        await reviewTaskEvaluation(evaluation.id)
        ElMessage.success('评价审核成功')
        break
      case 'publish':
        await publishTaskEvaluation(evaluation.id)
        ElMessage.success('评价发布成功')
        break
      case 'archive':
        await archiveTaskEvaluation(evaluation.id)
        ElMessage.success('评价归档成功')
        break
      case 'delete':
        await ElMessageBox.confirm('确定要删除此评价吗？此操作不可恢复。', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        await deleteTaskEvaluation(evaluation.id)
        ElMessage.success('删除成功')
        break
    }
    loadEvaluations()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to perform action:', error)
      ElMessage.error('操作失败')
    }
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  pagination.page = 1
  loadEvaluations()
}

const handleCurrentChange = (page: number) => {
  pagination.page = page
  loadEvaluations()
}

const handleFormSuccess = () => {
  loadEvaluations()
}

// 工具方法
const getEvaluationTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    self: '自评',
    peer: '同事评价',
    supervisor: '上级评价',
    subordinate: '下级评价',
    customer: '客户评价'
  }
  return typeMap[type] || type
}

const getEvaluationTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    self: 'info',
    peer: '',
    supervisor: 'success',
    subordinate: 'warning',
    customer: 'danger'
  }
  return typeMap[type] || ''
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    submitted: '已提交',
    reviewed: '已审核',
    published: '已发布',
    archived: '已归档',
    rejected: '已拒绝'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'info',
    submitted: 'warning',
    reviewed: 'primary',
    published: 'success',
    archived: '',
    rejected: 'danger'
  }
  return statusMap[status] || ''
}

const getScoreGrade = (score: number) => {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '中等'
  if (score >= 60) return '及格'
  return '不及格'
}

const getScoreColor = (score: number) => {
  if (score >= 90) return '#67c23a'
  if (score >= 80) return '#409eff'
  if (score >= 70) return '#e6a23c'
  if (score >= 60) return '#f56c6c'
  return '#909399'
}

const canEdit = (evaluation: TaskEvaluation) => {
  return evaluation.status === 'draft' && evaluation.evaluatorId === userStore.userInfo?.id
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

// 生命周期
onMounted(() => {
  loadEvaluations()
})
</script>

<style scoped>
.evaluation-list {
  padding: 20px;
}

.search-card,
.action-card,
.list-card {
  margin-bottom: 20px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.action-left {
  display: flex;
  gap: 12px;
}

.task-info {
  padding: 4px 0;
}

.task-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.task-desc {
  font-size: 12px;
  color: #909399;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-size: 14px;
  color: #303133;
}

.score-display {
  text-align: center;
}

.score-grade {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.card-view {
  margin-bottom: 20px;
}

.evaluation-card {
  margin-bottom: 20px;
  height: 400px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-content {
  height: 300px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.evaluatee-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.evaluatee-details {
  flex: 1;
}

.evaluatee-name {
  font-weight: 500;
  color: #303133;
}

.evaluation-type {
  font-size: 12px;
  color: #909399;
}

.score-section {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  padding: 12px;
}

.overall-score {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.score-label {
  font-size: 14px;
  color: #606266;
}

.score-value {
  display: flex;
  align-items: baseline;
  gap: 4px;
}

.score-number {
  font-size: 24px;
  font-weight: 600;
  color: #409eff;
}

.score-grade {
  font-size: 12px;
  color: #909399;
}

.dimension-scores {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.dimension-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dimension-item span {
  width: 40px;
  font-size: 12px;
  color: #606266;
}

.evaluation-content {
  flex: 1;
  overflow: hidden;
}

.evaluation-content p {
  margin: 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.card-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 12px;
  border-top: 1px solid #ebeef5;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.pagination-wrapper {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
