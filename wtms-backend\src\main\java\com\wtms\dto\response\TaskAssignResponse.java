package com.wtms.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务分配响应DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "任务分配响应")
public class TaskAssignResponse {

    @Schema(description = "分配ID")
    private String assignId;

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "任务标题")
    private String taskTitle;

    @Schema(description = "分配类型")
    private String assignType;

    @Schema(description = "分配者信息")
    private AssignerInfo assigner;

    @Schema(description = "被分配者列表")
    private List<AssigneeInfo> assignees;

    @Schema(description = "分配状态")
    private String status;

    @Schema(description = "分配备注")
    private String comment;

    @Schema(description = "分配时间")
    private LocalDateTime assignedAt;

    @Schema(description = "接受时间")
    private LocalDateTime acceptedAt;

    @Schema(description = "截止时间")
    private LocalDateTime deadline;

    /**
     * 分配者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "分配者信息")
    public static class AssignerInfo {
        @Schema(description = "用户ID")
        private String id;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "姓名")
        private String fullName;

        @Schema(description = "头像")
        private String avatar;
    }

    /**
     * 被分配者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "被分配者信息")
    public static class AssigneeInfo {
        @Schema(description = "目标类型")
        private String targetType;

        @Schema(description = "目标ID")
        private String targetId;

        @Schema(description = "目标名称")
        private String targetName;

        @Schema(description = "头像")
        private String avatar;

        @Schema(description = "分配状态")
        private String status;

        @Schema(description = "接受时间")
        private LocalDateTime acceptedAt;

        @Schema(description = "拒绝原因")
        private String rejectReason;
    }
}
