-- =====================================================
-- WTMS 开发用户权限提升脚本
-- 版本: 1.0.0
-- 创建时间: 2025-07-24
-- 说明: 为开发用户分配超级管理员权限，便于开发调试
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 为开发用户分配超级管理员角色
-- =====================================================

-- 检查开发用户是否存在
SELECT 
    '检查开发用户状态' as action,
    u.username,
    u.full_name,
    u.status,
    r.name as current_role
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = 1
LEFT JOIN roles r ON ur.role_id = r.id
WHERE u.username IN ('dev001', 'dev002');

-- 删除开发用户现有的角色分配
DELETE FROM user_roles 
WHERE user_id IN (
    SELECT id FROM users WHERE username IN ('dev001', 'dev002')
);

-- 为开发用户分配超级管理员角色
INSERT INTO `user_roles` (`id`, `user_id`, `role_id`, `assigned_by`, `is_active`) 
SELECT 
    CONCAT('ur-dev-', ROW_NUMBER() OVER (ORDER BY u.id)) as id,
    u.id as user_id,
    r.id as role_id,
    'user-001' as assigned_by,
    1 as is_active
FROM users u
CROSS JOIN roles r
WHERE u.username IN ('dev001', 'dev002')
AND r.code = 'SUPER_ADMIN';

-- =====================================================
-- 2. 更新开发配置白名单
-- =====================================================

-- 显示当前开发用户的权限状态
SELECT 
    '开发用户权限状态' as info,
    u.username as '用户名',
    u.full_name as '姓名',
    r.name as '角色',
    r.code as '角色编码',
    COUNT(DISTINCT p.id) as '权限数量'
FROM users u
JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = 1
JOIN roles r ON ur.role_id = r.id
LEFT JOIN role_permissions rp ON r.id = rp.role_id
LEFT JOIN permissions p ON rp.permission_id = p.id AND p.is_enabled = 1
WHERE u.username IN ('admin', 'dev001', 'dev002')
GROUP BY u.id, u.username, u.full_name, r.name, r.code
ORDER BY u.username;

-- =====================================================
-- 3. 验证权限分配结果
-- =====================================================

-- 验证超级管理员用户列表
SELECT 
    '超级管理员用户列表' as info,
    u.username as '用户名',
    u.full_name as '姓名',
    u.email as '邮箱',
    u.status as '状态',
    r.name as '角色名称'
FROM users u
JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = 1
JOIN roles r ON ur.role_id = r.id
WHERE r.code = 'SUPER_ADMIN'
ORDER BY u.username;

-- 验证开发用户权限数量
SELECT 
    '开发用户权限验证' as check_item,
    u.username,
    COUNT(DISTINCT p.id) as permission_count,
    CASE 
        WHEN COUNT(DISTINCT p.id) >= 45 THEN '✓ 权限充足' 
        ELSE '✗ 权限不足' 
    END as status
FROM users u
JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = 1
JOIN roles r ON ur.role_id = r.id
JOIN role_permissions rp ON r.id = rp.role_id
JOIN permissions p ON rp.permission_id = p.id AND p.is_enabled = 1
WHERE u.username IN ('dev001', 'dev002')
GROUP BY u.id, u.username;

-- =====================================================
-- 4. 创建开发环境测试数据
-- =====================================================

-- 添加一些测试任务，方便开发调试
INSERT INTO `tasks` (`id`, `task_code`, `title`, `description`, `category_id`, `status`, `priority`, `difficulty_level`, `estimated_hours`, `creator_id`, `assignee_id`, `planned_start_date`, `planned_end_date`) VALUES
('task-dev-001', 'DEV-001', '权限系统测试', '测试权限系统的各项功能', 'cat-102', 'in_progress', 4, 3, 8.00, 'user-003', 'user-003', NOW(), DATE_ADD(NOW(), INTERVAL 2 DAY)),
('task-dev-002', 'DEV-002', '前端权限组件开发', '开发前端权限控制组件', 'cat-101', 'pending', 3, 2, 12.00, 'user-004', 'user-003', NOW(), DATE_ADD(NOW(), INTERVAL 3 DAY)),
('task-dev-003', 'DEV-003', 'API权限验证', '验证所有API接口的权限控制', 'cat-102', 'pending', 5, 4, 16.00, 'user-003', 'user-004', NOW(), DATE_ADD(NOW(), INTERVAL 5 DAY));

-- 添加开发任务评论
INSERT INTO `task_comments` (`id`, `task_id`, `user_id`, `content`, `comment_type`) VALUES
('comment-dev-001', 'task-dev-001', 'user-003', '开始权限系统功能测试，重点验证角色权限分配', 'comment'),
('comment-dev-002', 'task-dev-002', 'user-003', '前端权限组件需要支持动态权限控制', 'comment'),
('comment-dev-003', 'task-dev-003', 'user-004', 'API权限验证需要覆盖所有Controller接口', 'comment');

-- =====================================================
-- 5. 显示完成信息
-- =====================================================

SELECT 
    '🎉 开发用户权限提升完成！' as message,
    '开发用户现在拥有超级管理员权限' as info,
    '可以访问所有系统功能进行开发调试' as note;

-- 显示开发用户登录信息
SELECT 
    '开发用户登录信息' as info,
    username as '用户名',
    'admin123' as '密码',
    full_name as '姓名',
    email as '邮箱'
FROM users 
WHERE username IN ('dev001', 'dev002')
ORDER BY username;

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
