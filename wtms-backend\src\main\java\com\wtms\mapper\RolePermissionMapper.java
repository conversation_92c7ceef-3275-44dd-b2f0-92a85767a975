package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wtms.entity.RolePermission;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色权限关联Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface RolePermissionMapper extends BaseMapper<RolePermission> {

    /**
     * 根据角色ID查询权限关联
     */
    @Select("SELECT * FROM role_permissions WHERE role_id = #{roleId}")
    List<RolePermission> selectByRoleId(@Param("roleId") String roleId);

    /**
     * 根据权限ID查询角色关联
     */
    @Select("SELECT * FROM role_permissions WHERE permission_id = #{permissionId}")
    List<RolePermission> selectByPermissionId(@Param("permissionId") String permissionId);

    /**
     * 根据角色ID和权限ID查询关联
     */
    @Select("SELECT * FROM role_permissions WHERE role_id = #{roleId} AND permission_id = #{permissionId}")
    RolePermission selectByRoleIdAndPermissionId(@Param("roleId") String roleId, @Param("permissionId") String permissionId);

    /**
     * 根据角色ID查询有效的权限关联
     */
    @Select("SELECT * FROM role_permissions WHERE role_id = #{roleId} AND is_enabled = TRUE " +
            "AND (expires_at IS NULL OR expires_at > NOW())")
    List<RolePermission> selectValidByRoleId(@Param("roleId") String roleId);

    /**
     * 根据权限ID查询有效的角色关联
     */
    @Select("SELECT * FROM role_permissions WHERE permission_id = #{permissionId} AND is_enabled = TRUE " +
            "AND (expires_at IS NULL OR expires_at > NOW())")
    List<RolePermission> selectValidByPermissionId(@Param("permissionId") String permissionId);

    /**
     * 删除角色的所有权限关联
     */
    @Delete("DELETE FROM role_permissions WHERE role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") String roleId);

    /**
     * 删除权限的所有角色关联
     */
    @Delete("DELETE FROM role_permissions WHERE permission_id = #{permissionId}")
    int deleteByPermissionId(@Param("permissionId") String permissionId);

    /**
     * 删除指定角色和权限的关联
     */
    @Delete("DELETE FROM role_permissions WHERE role_id = #{roleId} AND permission_id = #{permissionId}")
    int deleteByRoleIdAndPermissionId(@Param("roleId") String roleId, @Param("permissionId") String permissionId);

    /**
     * 批量插入角色权限关联
     */
    int batchInsert(@Param("rolePermissions") List<RolePermission> rolePermissions);

    /**
     * 批量删除角色权限关联
     */
    int batchDeleteByRoleIdAndPermissionIds(@Param("roleId") String roleId, @Param("permissionIds") List<String> permissionIds);

    /**
     * 检查角色权限关联是否存在
     */
    @Select("SELECT COUNT(*) FROM role_permissions WHERE role_id = #{roleId} AND permission_id = #{permissionId}")
    int countByRoleIdAndPermissionId(@Param("roleId") String roleId, @Param("permissionId") String permissionId);

    /**
     * 统计角色的权限数量
     */
    @Select("SELECT COUNT(*) FROM role_permissions WHERE role_id = #{roleId} AND is_enabled = TRUE")
    int countPermissionsByRoleId(@Param("roleId") String roleId);

    /**
     * 统计权限的角色数量
     */
    @Select("SELECT COUNT(*) FROM role_permissions WHERE permission_id = #{permissionId} AND is_enabled = TRUE")
    int countRolesByPermissionId(@Param("permissionId") String permissionId);

    /**
     * 查询过期的权限关联
     */
    @Select("SELECT * FROM role_permissions WHERE expires_at IS NOT NULL AND expires_at <= NOW()")
    List<RolePermission> selectExpiredRolePermissions();

    /**
     * 更新过期的权限关联状态
     */
    int updateExpiredRolePermissions();
}
