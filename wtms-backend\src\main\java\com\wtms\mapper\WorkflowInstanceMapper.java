package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.entity.WorkflowInstance;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工作流实例Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface WorkflowInstanceMapper extends BaseMapper<WorkflowInstance> {

    /**
     * 根据工作流定义ID查询实例
     */
    @Select("SELECT * FROM workflow_instances WHERE workflow_definition_id = #{definitionId} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<WorkflowInstance> selectByDefinitionId(@Param("definitionId") String definitionId);

    /**
     * 根据业务ID查询实例
     */
    @Select("SELECT * FROM workflow_instances WHERE business_id = #{businessId} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<WorkflowInstance> selectByBusinessId(@Param("businessId") String businessId);

    /**
     * 根据业务ID和业务类型查询实例
     */
    @Select("SELECT * FROM workflow_instances WHERE business_id = #{businessId} AND business_type = #{businessType} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<WorkflowInstance> selectByBusinessIdAndType(@Param("businessId") String businessId, @Param("businessType") String businessType);

    /**
     * 根据状态查询实例
     */
    @Select("SELECT * FROM workflow_instances WHERE status = #{status} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<WorkflowInstance> selectByStatus(@Param("status") String status);

    /**
     * 根据启动者查询实例
     */
    IPage<WorkflowInstance> selectByStarterId(Page<WorkflowInstance> page, @Param("starterId") String starterId, 
                                             @Param("status") String status);

    /**
     * 查询活跃实例
     */
    @Select("SELECT * FROM workflow_instances WHERE status IN ('running', 'pending') AND deleted_at IS NULL ORDER BY created_at DESC")
    List<WorkflowInstance> selectActiveInstances();

    /**
     * 查询已完成实例
     */
    @Select("SELECT * FROM workflow_instances WHERE status = 'completed' AND deleted_at IS NULL ORDER BY ended_at DESC")
    List<WorkflowInstance> selectCompletedInstances();

    /**
     * 查询超期实例
     */
    @Select("SELECT * FROM workflow_instances WHERE due_date IS NOT NULL AND due_date < NOW() AND status IN ('running', 'pending') AND deleted_at IS NULL ORDER BY due_date ASC")
    List<WorkflowInstance> selectOverdueInstances();

    /**
     * 根据父实例ID查询子实例
     */
    @Select("SELECT * FROM workflow_instances WHERE parent_instance_id = #{parentInstanceId} AND deleted_at IS NULL ORDER BY created_at ASC")
    List<WorkflowInstance> selectByParentInstanceId(@Param("parentInstanceId") String parentInstanceId);

    /**
     * 根据根实例ID查询所有相关实例
     */
    @Select("SELECT * FROM workflow_instances WHERE root_instance_id = #{rootInstanceId} AND deleted_at IS NULL ORDER BY created_at ASC")
    List<WorkflowInstance> selectByRootInstanceId(@Param("rootInstanceId") String rootInstanceId);

    /**
     * 搜索工作流实例
     */
    IPage<WorkflowInstance> searchWorkflowInstances(Page<WorkflowInstance> page, @Param("keyword") String keyword,
                                                   @Param("definitionId") String definitionId, @Param("status") String status,
                                                   @Param("businessType") String businessType, @Param("starterId") String starterId);

    /**
     * 统计实例数量
     */
    @Select("SELECT COUNT(*) FROM workflow_instances WHERE deleted_at IS NULL")
    int countAll();

    /**
     * 根据状态统计实例数量
     */
    @Select("SELECT COUNT(*) FROM workflow_instances WHERE status = #{status} AND deleted_at IS NULL")
    int countByStatus(@Param("status") String status);

    /**
     * 根据工作流定义统计实例数量
     */
    @Select("SELECT COUNT(*) FROM workflow_instances WHERE workflow_definition_id = #{definitionId} AND deleted_at IS NULL")
    int countByDefinitionId(@Param("definitionId") String definitionId);

    /**
     * 根据启动者统计实例数量
     */
    @Select("SELECT COUNT(*) FROM workflow_instances WHERE starter_id = #{starterId} AND deleted_at IS NULL")
    int countByStarterId(@Param("starterId") String starterId);

    /**
     * 根据业务类型统计实例数量
     */
    @Select("SELECT COUNT(*) FROM workflow_instances WHERE business_type = #{businessType} AND deleted_at IS NULL")
    int countByBusinessType(@Param("businessType") String businessType);

    /**
     * 启动实例
     */
    @Update("UPDATE workflow_instances SET status = 'running', started_at = NOW(), updated_at = NOW() WHERE id = #{instanceId}")
    int startInstance(@Param("instanceId") String instanceId);

    /**
     * 完成实例
     */
    @Update("UPDATE workflow_instances SET status = 'completed', ended_at = NOW(), updated_at = NOW() WHERE id = #{instanceId}")
    int completeInstance(@Param("instanceId") String instanceId);

    /**
     * 暂停实例
     */
    @Update("UPDATE workflow_instances SET status = 'suspended', suspended_at = NOW(), updated_at = NOW() WHERE id = #{instanceId}")
    int suspendInstance(@Param("instanceId") String instanceId);

    /**
     * 恢复实例
     */
    @Update("UPDATE workflow_instances SET status = 'running', suspended_at = NULL, updated_at = NOW() WHERE id = #{instanceId}")
    int resumeInstance(@Param("instanceId") String instanceId);

    /**
     * 终止实例
     */
    @Update("UPDATE workflow_instances SET status = 'terminated', ended_at = NOW(), updated_at = NOW() WHERE id = #{instanceId}")
    int terminateInstance(@Param("instanceId") String instanceId);

    /**
     * 取消实例
     */
    @Update("UPDATE workflow_instances SET status = 'cancelled', ended_at = NOW(), updated_at = NOW() WHERE id = #{instanceId}")
    int cancelInstance(@Param("instanceId") String instanceId);

    /**
     * 更新当前节点
     */
    @Update("UPDATE workflow_instances SET current_node_id = #{nodeId}, updated_at = NOW() WHERE id = #{instanceId}")
    int updateCurrentNode(@Param("instanceId") String instanceId, @Param("nodeId") String nodeId);

    /**
     * 更新实例变量
     */
    @Update("UPDATE workflow_instances SET variables_json = #{variablesJson}, updated_at = NOW() WHERE id = #{instanceId}")
    int updateVariables(@Param("instanceId") String instanceId, @Param("variablesJson") String variablesJson);

    /**
     * 批量删除实例
     */
    int batchDeleteInstances(@Param("instanceIds") List<String> instanceIds);

    /**
     * 批量更新实例状态
     */
    int batchUpdateStatus(@Param("instanceIds") List<String> instanceIds, @Param("status") String status);

    /**
     * 查询实例统计信息
     */
    Object selectInstanceStatistics();

    /**
     * 查询状态统计
     */
    List<Object> selectStatusStatistics();

    /**
     * 查询业务类型统计
     */
    List<Object> selectBusinessTypeStatistics();

    /**
     * 查询启动者统计
     */
    List<Object> selectStarterStatistics();

    /**
     * 查询工作流定义统计
     */
    List<Object> selectDefinitionStatistics();

    /**
     * 查询执行时长统计
     */
    List<Object> selectDurationStatistics(@Param("days") Integer days);

    /**
     * 查询每日启动统计
     */
    List<Object> selectDailyStartStatistics(@Param("days") Integer days);

    /**
     * 查询每日完成统计
     */
    List<Object> selectDailyCompletionStatistics(@Param("days") Integer days);

    /**
     * 查询超期实例统计
     */
    Object selectOverdueStatistics();

    /**
     * 查询性能统计
     */
    Object selectPerformanceStatistics(@Param("definitionId") String definitionId, @Param("days") Integer days);

    /**
     * 清理已完成的实例
     */
    @Update("UPDATE workflow_instances SET deleted_at = NOW() WHERE status IN ('completed', 'terminated', 'cancelled') AND ended_at < #{beforeDate}")
    int cleanupCompletedInstances(@Param("beforeDate") LocalDateTime beforeDate);

    /**
     * 查询长时间运行的实例
     */
    @Select("SELECT * FROM workflow_instances WHERE status = 'running' AND started_at < #{beforeDate} AND deleted_at IS NULL ORDER BY started_at ASC")
    List<WorkflowInstance> selectLongRunningInstances(@Param("beforeDate") LocalDateTime beforeDate);

    /**
     * 查询最近启动的实例
     */
    @Select("SELECT * FROM workflow_instances WHERE deleted_at IS NULL ORDER BY started_at DESC LIMIT #{limit}")
    List<WorkflowInstance> selectRecentStartedInstances(@Param("limit") Integer limit);

    /**
     * 查询最近完成的实例
     */
    @Select("SELECT * FROM workflow_instances WHERE status = 'completed' AND deleted_at IS NULL ORDER BY ended_at DESC LIMIT #{limit}")
    List<WorkflowInstance> selectRecentCompletedInstances(@Param("limit") Integer limit);
}
