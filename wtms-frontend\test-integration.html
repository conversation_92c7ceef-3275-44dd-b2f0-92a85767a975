<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WTMS前后端集成测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-family: monospace;
        }
        .success {
            background-color: #f0f9ff;
            border: 1px solid #0ea5e9;
            color: #0c4a6e;
        }
        .error {
            background-color: #fef2f2;
            border: 1px solid #ef4444;
            color: #991b1b;
        }
        .info {
            background-color: #f8fafc;
            border: 1px solid #64748b;
            color: #334155;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #10b981; }
        .status-offline { background-color: #ef4444; }
        .status-unknown { background-color: #6b7280; }
    </style>
</head>
<body>
    <h1>🚀 WTMS前后端集成测试</h1>
    
    <div class="test-container">
        <h2>📊 服务状态监控</h2>
        <div id="service-status">
            <p><span id="frontend-status" class="status-indicator status-unknown"></span>前端服务 (http://localhost:33335)</p>
            <p><span id="backend-status" class="status-indicator status-unknown"></span>后端服务 (http://localhost:55557)</p>
        </div>
        <button onclick="checkServiceStatus()">检查服务状态</button>
    </div>

    <div class="test-container">
        <h2>🔐 用户认证测试</h2>
        <div>
            <button onclick="testLogin()">测试登录 (admin/admin123456)</button>
            <button onclick="testInvalidLogin()">测试错误登录</button>
            <button onclick="testUserProfile()">获取用户信息</button>
        </div>
        <div id="auth-results"></div>
    </div>

    <div class="test-container">
        <h2>📋 任务管理测试</h2>
        <div>
            <button onclick="testTaskList()">获取任务列表</button>
            <button onclick="testTaskDetail()">获取任务详情</button>
            <button onclick="testCreateTask()">创建新任务</button>
            <button onclick="testTaskComments()">获取任务评论</button>
        </div>
        <div id="task-results"></div>
    </div>

    <div class="test-container">
        <h2>🔄 完整流程测试</h2>
        <div>
            <button onclick="runFullTest()">运行完整测试流程</button>
            <button onclick="clearResults()">清空结果</button>
        </div>
        <div id="full-test-results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:55557/api/v1';
        let authToken = null;

        // 工具函数
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        function clearResults() {
            ['auth-results', 'task-results', 'full-test-results'].forEach(id => {
                document.getElementById(id).innerHTML = '';
            });
        }

        // API调用函数
        async function apiCall(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    'Origin': 'http://localhost:33335',
                    ...options.headers
                },
                ...options
            };

            if (authToken && !config.headers.Authorization) {
                config.headers.Authorization = `Bearer ${authToken}`;
            }

            try {
                const response = await fetch(url, config);
                const data = await response.json();
                return { success: response.ok, status: response.status, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // 服务状态检查
        async function checkServiceStatus() {
            // 检查前端服务
            try {
                const frontendResponse = await fetch('http://localhost:33335');
                document.getElementById('frontend-status').className = 
                    'status-indicator ' + (frontendResponse.ok ? 'status-online' : 'status-offline');
            } catch {
                document.getElementById('frontend-status').className = 'status-indicator status-offline';
            }

            // 检查后端服务
            const result = await apiCall('/health');
            document.getElementById('backend-status').className = 
                'status-indicator ' + (result.success ? 'status-online' : 'status-offline');
            
            if (result.success) {
                addResult('full-test-results', `✅ 后端服务正常: ${result.data.data.service} v${result.data.data.version}`, 'success');
            } else {
                addResult('full-test-results', `❌ 后端服务异常: ${result.error || '连接失败'}`, 'error');
            }
        }

        // 认证测试
        async function testLogin() {
            addResult('auth-results', '🔍 测试登录...', 'info');
            
            const result = await apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'admin',
                    password: 'admin123456'
                })
            });

            if (result.success && result.data.success) {
                authToken = result.data.data.token;
                addResult('auth-results', `✅ 登录成功! 用户: ${result.data.data.user.fullName}`, 'success');
                addResult('auth-results', `🎫 Token: ${authToken.substring(0, 20)}...`, 'info');
                addResult('auth-results', `👤 角色: ${result.data.data.user.roles.join(', ')}`, 'info');
            } else {
                addResult('auth-results', `❌ 登录失败: ${result.data?.message || result.error}`, 'error');
            }
        }

        async function testInvalidLogin() {
            addResult('auth-results', '🔍 测试错误登录...', 'info');
            
            const result = await apiCall('/auth/login', {
                method: 'POST',
                body: JSON.stringify({
                    username: 'admin',
                    password: 'wrongpassword'
                })
            });

            if (!result.success || !result.data.success) {
                addResult('auth-results', `✅ 错误登录正确被拒绝: ${result.data?.message || result.error}`, 'success');
            } else {
                addResult('auth-results', `❌ 错误登录应该被拒绝`, 'error');
            }
        }

        async function testUserProfile() {
            if (!authToken) {
                addResult('auth-results', '⚠️ 请先登录', 'error');
                return;
            }

            addResult('auth-results', '🔍 获取用户信息...', 'info');
            
            const result = await apiCall('/user/profile');

            if (result.success && result.data.success) {
                const user = result.data.data;
                addResult('auth-results', `✅ 获取用户信息成功: ${user.fullName} (${user.email})`, 'success');
                addResult('auth-results', `🔑 权限数量: ${user.permissions.length}`, 'info');
            } else {
                addResult('auth-results', `❌ 获取用户信息失败: ${result.data?.message || result.error}`, 'error');
            }
        }

        // 任务管理测试
        async function testTaskList() {
            addResult('task-results', '🔍 获取任务列表...', 'info');
            
            const result = await apiCall('/tasks?page=1&size=10');

            if (result.success && result.data.success) {
                const tasks = result.data.data.records;
                addResult('task-results', `✅ 获取任务列表成功: ${tasks.length} 个任务`, 'success');
                if (tasks.length > 0) {
                    addResult('task-results', `📋 第一个任务: ${tasks[0].title} (${tasks[0].status})`, 'info');
                }
            } else {
                addResult('task-results', `❌ 获取任务列表失败: ${result.data?.message || result.error}`, 'error');
            }
        }

        async function testTaskDetail() {
            addResult('task-results', '🔍 获取任务详情...', 'info');
            
            const result = await apiCall('/tasks/1');

            if (result.success && result.data.success) {
                const task = result.data.data;
                addResult('task-results', `✅ 获取任务详情成功: ${task.title}`, 'success');
                addResult('task-results', `📊 进度: ${task.progress}% | 优先级: ${task.priority}`, 'info');
            } else {
                addResult('task-results', `❌ 获取任务详情失败: ${result.data?.message || result.error}`, 'error');
            }
        }

        async function testCreateTask() {
            addResult('task-results', '🔍 创建新任务...', 'info');
            
            const result = await apiCall('/tasks', {
                method: 'POST',
                body: JSON.stringify({
                    title: '集成测试任务',
                    description: '这是通过前后端集成测试创建的任务',
                    priority: 'HIGH'
                })
            });

            if (result.success && result.data.success) {
                const task = result.data.data;
                addResult('task-results', `✅ 创建任务成功: ${task.title} (ID: ${task.id})`, 'success');
            } else {
                addResult('task-results', `❌ 创建任务失败: ${result.data?.message || result.error}`, 'error');
            }
        }

        async function testTaskComments() {
            addResult('task-results', '🔍 获取任务评论...', 'info');
            
            const result = await apiCall('/tasks/1/comments');

            if (result.success && result.data.success) {
                const comments = result.data.data;
                addResult('task-results', `✅ 获取任务评论成功: ${comments.length} 条评论`, 'success');
                if (comments.length > 0) {
                    addResult('task-results', `💬 第一条评论: ${comments[0].content}`, 'info');
                }
            } else {
                addResult('task-results', `❌ 获取任务评论失败: ${result.data?.message || result.error}`, 'error');
            }
        }

        // 完整流程测试
        async function runFullTest() {
            clearResults();
            addResult('full-test-results', '🚀 开始完整集成测试...', 'info');
            
            // 1. 检查服务状态
            await checkServiceStatus();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            // 2. 测试认证流程
            addResult('full-test-results', '📝 测试认证流程...', 'info');
            await testLogin();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            if (authToken) {
                await testUserProfile();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                // 3. 测试任务管理
                addResult('full-test-results', '📋 测试任务管理...', 'info');
                await testTaskList();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testTaskDetail();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testCreateTask();
                await new Promise(resolve => setTimeout(resolve, 500));
                
                await testTaskComments();
                
                addResult('full-test-results', '🎉 完整集成测试完成！', 'success');
            } else {
                addResult('full-test-results', '❌ 认证失败，无法继续测试', 'error');
            }
        }

        // 页面加载时检查服务状态
        window.onload = function() {
            checkServiceStatus();
        };
    </script>
</body>
</html>
