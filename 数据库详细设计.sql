-- WTMS 工作任务管理平台数据库设计
-- 数据库: MySQL 8.0+
-- 字符集: UTF8MB4
-- 排序规则: utf8mb4_unicode_ci

-- 创建数据库
CREATE DATABASE IF NOT EXISTS wtms_db
CHARACTER SET utf8mb4
COLLATE utf8mb4_unicode_ci;

USE wtms_db;

-- ================================
-- 1. 用户管理模块
-- ================================

-- 部门表
CREATE TABLE departments (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL COMMENT '部门编码',
    description TEXT,
    parent_id CHAR(36),
    manager_id CHAR(36) COMMENT '延迟外键约束',
    level INT DEFAULT 1 COMMENT '部门层级',
    sort_order INT DEFAULT 0,
    status VARCHAR(20) DEFAULT 'active' COMMENT 'active, inactive',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 角色表
CREATE TABLE roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(50) NOT NULL,
    code VARCHAR(20) UNIQUE NOT NULL,
    description TEXT,
    permissions JSONB DEFAULT '[]', -- 权限列表
    is_system BOOLEAN DEFAULT FALSE, -- 是否系统角色
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 用户表
CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    phone VARCHAR(20),
    password_hash VARCHAR(255) NOT NULL,
    salt VARCHAR(50) NOT NULL,
    full_name VARCHAR(100) NOT NULL,
    avatar_url VARCHAR(500),
    department_id UUID REFERENCES departments(id),
    role_id UUID REFERENCES roles(id),
    employee_id VARCHAR(50) UNIQUE, -- 工号
    hire_date DATE,
    status VARCHAR(20) DEFAULT 'active', -- active, inactive, locked
    last_login_at TIMESTAMP WITH TIME ZONE,
    login_count INTEGER DEFAULT 0,
    settings JSONB DEFAULT '{}', -- 用户设置
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 添加部门管理者外键约束
ALTER TABLE departments ADD CONSTRAINT fk_departments_manager 
    FOREIGN KEY (manager_id) REFERENCES users(id);

-- 用户技能表
CREATE TABLE user_skills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    skill_id UUID REFERENCES skills(id) ON DELETE CASCADE,
    level INTEGER CHECK (level >= 1 AND level <= 5), -- 技能等级 1-5
    certified BOOLEAN DEFAULT FALSE, -- 是否认证
    certified_at TIMESTAMP WITH TIME ZONE,
    certified_by UUID REFERENCES users(id),
    notes TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, skill_id)
);

-- ================================
-- 2. 技能管理模块
-- ================================

-- 技能分类表
CREATE TABLE skill_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES skill_categories(id),
    icon VARCHAR(50),
    color VARCHAR(7), -- 颜色代码
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 技能表
CREATE TABLE skills (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    category_id UUID REFERENCES skill_categories(id),
    level_definitions JSONB, -- 各等级定义
    assessment_criteria JSONB, -- 评估标准
    related_skills UUID[], -- 相关技能ID数组
    is_core BOOLEAN DEFAULT FALSE, -- 是否核心技能
    status VARCHAR(20) DEFAULT 'active',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================
-- 3. 任务管理模块
-- ================================

-- 任务分类表
CREATE TABLE task_categories (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    parent_id UUID REFERENCES task_categories(id),
    color VARCHAR(7),
    icon VARCHAR(50),
    naming_template VARCHAR(200), -- 命名模板
    default_workflow_id UUID, -- 默认工作流
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 任务表
CREATE TABLE tasks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(200) NOT NULL,
    description TEXT,
    task_code VARCHAR(100) UNIQUE NOT NULL, -- 任务编号
    category_id UUID REFERENCES task_categories(id),
    parent_id UUID REFERENCES tasks(id), -- 父任务（子任务支持）
    
    -- 基本信息
    priority INTEGER DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
    difficulty_level INTEGER DEFAULT 1 CHECK (difficulty_level >= 1 AND difficulty_level <= 5),
    business_value INTEGER DEFAULT 3 CHECK (business_value >= 1 AND business_value <= 5),
    
    -- 时间管理
    estimated_hours DECIMAL(8,2),
    actual_hours DECIMAL(8,2),
    planned_start_date TIMESTAMP WITH TIME ZONE,
    planned_end_date TIMESTAMP WITH TIME ZONE,
    actual_start_date TIMESTAMP WITH TIME ZONE,
    actual_end_date TIMESTAMP WITH TIME ZONE,
    due_date TIMESTAMP WITH TIME ZONE,
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'draft', -- draft, pending, in_progress, review, testing, completed, paused, cancelled, archived
    progress DECIMAL(5,2) DEFAULT 0 CHECK (progress >= 0 AND progress <= 100),
    
    -- 人员分配
    creator_id UUID REFERENCES users(id) NOT NULL,
    assignee_id UUID REFERENCES users(id),
    reviewer_id UUID REFERENCES users(id),
    
    -- 扩展信息
    tags JSONB DEFAULT '[]', -- 标签数组
    custom_fields JSONB DEFAULT '{}', -- 自定义字段
    attachments JSONB DEFAULT '[]', -- 附件信息
    
    -- 质量评估
    quality_score DECIMAL(3,1), -- 质量得分
    completion_quality JSONB, -- 完成质量详情
    
    -- 审计信息
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    deleted_at TIMESTAMP WITH TIME ZONE -- 软删除
);

-- 任务描述版本表
CREATE TABLE task_descriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    content TEXT NOT NULL, -- Markdown格式内容
    structured_fields JSONB DEFAULT '{}', -- 结构化字段
    version INTEGER NOT NULL,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(task_id, version)
);

-- 任务依赖关系表
CREATE TABLE task_dependencies (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    predecessor_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    successor_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    dependency_type VARCHAR(2) DEFAULT 'FS', -- FS, SS, FF, SF
    lag_days INTEGER DEFAULT 0, -- 延迟天数
    description TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(predecessor_id, successor_id),
    CHECK (predecessor_id != successor_id) -- 防止自依赖
);

-- 任务技能需求表
CREATE TABLE task_skill_requirements (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    skill_id UUID REFERENCES skills(id) ON DELETE CASCADE,
    required_level INTEGER CHECK (required_level >= 1 AND required_level <= 5),
    is_mandatory BOOLEAN DEFAULT TRUE, -- 是否必需
    weight DECIMAL(3,2) DEFAULT 1.0, -- 权重
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(task_id, skill_id)
);

-- 任务评论表
CREATE TABLE task_comments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    parent_id UUID REFERENCES task_comments(id), -- 支持回复
    author_id UUID REFERENCES users(id),
    content TEXT NOT NULL,
    comment_type VARCHAR(20) DEFAULT 'comment', -- comment, system, status_change
    attachments JSONB DEFAULT '[]',
    is_internal BOOLEAN DEFAULT FALSE, -- 是否内部评论
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================
-- 4. 工作流管理模块
-- ================================

-- 工作流定义表
CREATE TABLE workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    category VARCHAR(50),
    definition JSONB NOT NULL, -- 工作流定义JSON
    version INTEGER DEFAULT 1,
    status VARCHAR(20) DEFAULT 'draft', -- draft, active, archived
    is_template BOOLEAN DEFAULT FALSE, -- 是否模板
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 工作流实例表
CREATE TABLE workflow_instances (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_id UUID REFERENCES workflows(id),
    task_id UUID REFERENCES tasks(id),
    name VARCHAR(100),
    current_step VARCHAR(100),
    status VARCHAR(20) DEFAULT 'running', -- running, completed, failed, cancelled
    variables JSONB DEFAULT '{}', -- 流程变量
    execution_log JSONB DEFAULT '[]', -- 执行日志
    started_by UUID REFERENCES users(id),
    started_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    completed_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT
);

-- 工作流步骤执行记录表
CREATE TABLE workflow_step_executions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    instance_id UUID REFERENCES workflow_instances(id) ON DELETE CASCADE,
    step_name VARCHAR(100) NOT NULL,
    step_type VARCHAR(50) NOT NULL, -- task, decision, parallel, etc.
    status VARCHAR(20) DEFAULT 'pending', -- pending, running, completed, failed, skipped
    assignee_id UUID REFERENCES users(id),
    input_data JSONB,
    output_data JSONB,
    started_at TIMESTAMP WITH TIME ZONE,
    completed_at TIMESTAMP WITH TIME ZONE,
    duration_seconds INTEGER,
    error_message TEXT
);

-- 审批记录表
CREATE TABLE approvals (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    workflow_instance_id UUID REFERENCES workflow_instances(id),
    step_execution_id UUID REFERENCES workflow_step_executions(id),
    task_id UUID REFERENCES tasks(id),
    approver_id UUID REFERENCES users(id),
    action VARCHAR(20) NOT NULL, -- approve, reject, delegate, request_change
    comment TEXT,
    attachments JSONB DEFAULT '[]',
    delegated_to UUID REFERENCES users(id), -- 委托给谁
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================
-- 5. 质量评价模块
-- ================================

-- 评价模板表
CREATE TABLE evaluation_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(100) NOT NULL,
    description TEXT,
    task_category_id UUID REFERENCES task_categories(id),
    dimensions JSONB NOT NULL, -- 评价维度定义
    scoring_method VARCHAR(20) DEFAULT 'weighted', -- weighted, average, custom
    passing_score DECIMAL(3,1) DEFAULT 60.0,
    is_default BOOLEAN DEFAULT FALSE,
    created_by UUID REFERENCES users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 任务评价表
CREATE TABLE task_evaluations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    task_id UUID REFERENCES tasks(id) ON DELETE CASCADE,
    template_id UUID REFERENCES evaluation_templates(id),
    evaluator_id UUID REFERENCES users(id),
    evaluation_type VARCHAR(20) NOT NULL, -- self, peer, supervisor, expert
    scores JSONB NOT NULL, -- 各维度得分
    overall_score DECIMAL(3,1),
    comments TEXT,
    strengths TEXT, -- 优点
    improvements TEXT, -- 改进建议
    status VARCHAR(20) DEFAULT 'draft', -- draft, submitted, approved
    submitted_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================
-- 6. 系统配置和日志模块
-- ================================

-- 系统配置表
CREATE TABLE system_configs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    key VARCHAR(100) UNIQUE NOT NULL,
    value JSONB,
    description TEXT,
    category VARCHAR(50),
    is_public BOOLEAN DEFAULT FALSE, -- 是否公开配置
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 操作日志表
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    action VARCHAR(50) NOT NULL, -- create, update, delete, login, logout, etc.
    resource_type VARCHAR(50), -- task, user, workflow, etc.
    resource_id UUID,
    old_values JSONB,
    new_values JSONB,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- 通知表
CREATE TABLE notifications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    recipient_id UUID REFERENCES users(id) ON DELETE CASCADE,
    title VARCHAR(200) NOT NULL,
    content TEXT,
    type VARCHAR(50) NOT NULL, -- task_assigned, task_completed, approval_required, etc.
    related_resource_type VARCHAR(50),
    related_resource_id UUID,
    channels JSONB DEFAULT '["web"]', -- web, email, sms, push
    status VARCHAR(20) DEFAULT 'unread', -- unread, read, archived
    priority INTEGER DEFAULT 3 CHECK (priority >= 1 AND priority <= 5),
    scheduled_at TIMESTAMP WITH TIME ZONE,
    sent_at TIMESTAMP WITH TIME ZONE,
    read_at TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- ================================
-- 索引创建
-- ================================

-- 用户表索引
CREATE INDEX idx_users_department ON users(department_id);
CREATE INDEX idx_users_role ON users(role_id);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_email ON users(email);

-- 任务表索引
CREATE INDEX idx_tasks_assignee ON tasks(assignee_id);
CREATE INDEX idx_tasks_creator ON tasks(creator_id);
CREATE INDEX idx_tasks_status ON tasks(status);
CREATE INDEX idx_tasks_category ON tasks(category_id);
CREATE INDEX idx_tasks_due_date ON tasks(due_date);
CREATE INDEX idx_tasks_priority ON tasks(priority);
CREATE INDEX idx_tasks_created_at ON tasks(created_at);
CREATE INDEX idx_tasks_parent ON tasks(parent_id);
CREATE INDEX idx_tasks_code ON tasks(task_code);

-- 全文搜索索引
CREATE INDEX idx_tasks_title_gin ON tasks USING gin(to_tsvector('english', title));
CREATE INDEX idx_tasks_description_gin ON tasks USING gin(to_tsvector('english', description));

-- 工作流索引
CREATE INDEX idx_workflow_instances_task ON workflow_instances(task_id);
CREATE INDEX idx_workflow_instances_status ON workflow_instances(status);
CREATE INDEX idx_workflow_instances_workflow ON workflow_instances(workflow_id);

-- 评价表索引
CREATE INDEX idx_task_evaluations_task ON task_evaluations(task_id);
CREATE INDEX idx_task_evaluations_evaluator ON task_evaluations(evaluator_id);
CREATE INDEX idx_task_evaluations_type ON task_evaluations(evaluation_type);

-- 通知表索引
CREATE INDEX idx_notifications_recipient ON notifications(recipient_id);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_created_at ON notifications(created_at);

-- 审计日志索引
CREATE INDEX idx_audit_logs_user ON audit_logs(user_id);
CREATE INDEX idx_audit_logs_resource ON audit_logs(resource_type, resource_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);

-- ================================
-- 触发器和函数
-- ================================

-- 更新时间戳触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表添加更新时间戳触发器
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_tasks_updated_at BEFORE UPDATE ON tasks
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_workflows_updated_at BEFORE UPDATE ON workflows
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- 任务状态变更审计触发器
CREATE OR REPLACE FUNCTION audit_task_status_change()
RETURNS TRIGGER AS $$
BEGIN
    IF OLD.status != NEW.status THEN
        INSERT INTO audit_logs (user_id, action, resource_type, resource_id, old_values, new_values)
        VALUES (
            NEW.assignee_id,
            'status_change',
            'task',
            NEW.id,
            jsonb_build_object('status', OLD.status),
            jsonb_build_object('status', NEW.status)
        );
    END IF;
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER audit_task_status_change_trigger
    AFTER UPDATE ON tasks
    FOR EACH ROW
    EXECUTE FUNCTION audit_task_status_change();

-- ================================
-- 初始数据插入
-- ================================

-- 插入系统角色
INSERT INTO roles (name, code, description, permissions, is_system) VALUES
('系统管理员', 'ADMIN', '系统管理员，拥有所有权限', '["*"]', true),
('项目经理', 'PM', '项目经理，负责项目管理', '["project.*", "task.*", "workflow.*"]', true),
('开发人员', 'DEVELOPER', '开发人员，负责任务执行', '["task.read", "task.update", "task.comment"]', true),
('测试人员', 'TESTER', '测试人员，负责质量保证', '["task.read", "task.evaluate", "task.comment"]', true);

-- 插入默认部门
INSERT INTO departments (name, code, description) VALUES
('技术部', 'TECH', '技术开发部门'),
('产品部', 'PRODUCT', '产品管理部门'),
('运营部', 'OPERATION', '运营管理部门');

-- 插入系统配置
INSERT INTO system_configs (key, value, description, category) VALUES
('task.default_priority', '3', '默认任务优先级', 'task'),
('task.auto_assign', 'false', '是否自动分配任务', 'task'),
('notification.email_enabled', 'true', '是否启用邮件通知', 'notification'),
('workflow.max_parallel_tasks', '10', '最大并行任务数', 'workflow');
