package com.wtms.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wtms.common.result.Result;
import com.wtms.dto.request.CreateCommentRequest;
import com.wtms.dto.request.UpdateCommentRequest;
import com.wtms.dto.response.CommentResponse;
import com.wtms.entity.TaskComment;
import com.wtms.service.TaskCommentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 任务评论控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/task-comments")
@Tag(name = "任务评论管理", description = "任务评论相关接口")
public class TaskCommentController {

    @Autowired
    private TaskCommentService taskCommentService;

    @PostMapping
    @Operation(summary = "创建评论", description = "创建新的任务评论")
    @PreAuthorize("hasAuthority('task:comment') or hasRole('USER')")
    public Result<TaskComment> createComment(@Valid @RequestBody CreateCommentRequest request) {
        log.info("Creating comment for task: {}", request.getTaskId());
        
        TaskComment comment = taskCommentService.createComment(request);
        return Result.success("评论创建成功", comment);
    }

    @PutMapping("/{commentId}")
    @Operation(summary = "更新评论", description = "更新指定评论")
    @PreAuthorize("hasAuthority('task:comment:edit') or @taskCommentService.canEditComment(#commentId, authentication.principal.id)")
    public Result<TaskComment> updateComment(
            @Parameter(description = "评论ID", required = true)
            @PathVariable @NotBlank(message = "评论ID不能为空") String commentId,
            @Valid @RequestBody UpdateCommentRequest request) {
        
        log.info("Updating comment: {}", commentId);
        
        TaskComment comment = taskCommentService.updateComment(commentId, request);
        return Result.success("评论更新成功", comment);
    }

    @DeleteMapping("/{commentId}")
    @Operation(summary = "删除评论", description = "删除指定评论")
    @PreAuthorize("hasAuthority('task:comment:delete') or @taskCommentService.canDeleteComment(#commentId, authentication.principal.id)")
    public Result<String> deleteComment(
            @Parameter(description = "评论ID", required = true)
            @PathVariable @NotBlank(message = "评论ID不能为空") String commentId) {
        
        log.info("Deleting comment: {}", commentId);
        
        taskCommentService.deleteComment(commentId);
        return Result.success("评论删除成功");
    }

    @GetMapping("/{commentId}")
    @Operation(summary = "获取评论详情", description = "根据ID获取评论详细信息")
    @PreAuthorize("hasAuthority('task:comment:view') or hasRole('USER')")
    public Result<TaskComment> getComment(
            @Parameter(description = "评论ID", required = true)
            @PathVariable @NotBlank(message = "评论ID不能为空") String commentId) {
        
        TaskComment comment = taskCommentService.getCommentById(commentId);
        return Result.success(comment);
    }

    @GetMapping("/tasks/{taskId}/tree")
    @Operation(summary = "获取任务评论树", description = "获取指定任务的评论树结构")
    @PreAuthorize("hasAuthority('task:comment:view') or hasRole('USER')")
    public Result<List<CommentResponse>> getTaskCommentTree(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        List<CommentResponse> commentTree = taskCommentService.getTaskCommentTree(taskId);
        return Result.success(commentTree);
    }

    @GetMapping("/tasks/{taskId}")
    @Operation(summary = "分页获取任务评论", description = "分页获取指定任务的评论列表")
    @PreAuthorize("hasAuthority('task:comment:view') or hasRole('USER')")
    public Result<IPage<CommentResponse>> getTaskComments(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "评论类型")
            @RequestParam(required = false) String commentType,
            @Parameter(description = "状态")
            @RequestParam(required = false) String status,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<CommentResponse> comments = taskCommentService.getTaskComments(taskId, commentType, status, page, size);
        return Result.success(comments);
    }

    @GetMapping("/users/{userId}")
    @Operation(summary = "获取用户评论", description = "分页获取指定用户的评论列表")
    @PreAuthorize("hasAuthority('task:comment:view') or #userId == authentication.principal.id or hasRole('ADMIN')")
    public Result<IPage<CommentResponse>> getUserComments(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId,
            @Parameter(description = "状态")
            @RequestParam(required = false) String status,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<CommentResponse> comments = taskCommentService.getUserComments(userId, status, page, size);
        return Result.success(comments);
    }

    @GetMapping("/{parentId}/children")
    @Operation(summary = "获取子评论", description = "获取指定评论的子评论列表")
    @PreAuthorize("hasAuthority('task:comment:view') or hasRole('USER')")
    public Result<List<CommentResponse>> getChildComments(
            @Parameter(description = "父评论ID", required = true)
            @PathVariable @NotBlank(message = "父评论ID不能为空") String parentId) {
        
        List<CommentResponse> children = taskCommentService.getChildComments(parentId);
        return Result.success(children);
    }

    @GetMapping("/tasks/{taskId}/root")
    @Operation(summary = "获取根评论", description = "获取指定任务的根评论列表")
    @PreAuthorize("hasAuthority('task:comment:view') or hasRole('USER')")
    public Result<List<CommentResponse>> getRootComments(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        List<CommentResponse> rootComments = taskCommentService.getRootComments(taskId);
        return Result.success(rootComments);
    }

    @GetMapping("/tasks/{taskId}/pinned")
    @Operation(summary = "获取置顶评论", description = "获取指定任务的置顶评论列表")
    @PreAuthorize("hasAuthority('task:comment:view') or hasRole('USER')")
    public Result<List<CommentResponse>> getPinnedComments(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        List<CommentResponse> pinnedComments = taskCommentService.getPinnedComments(taskId);
        return Result.success(pinnedComments);
    }

    @GetMapping("/tasks/{taskId}/latest")
    @Operation(summary = "获取最新评论", description = "获取指定任务的最新评论列表")
    @PreAuthorize("hasAuthority('task:comment:view') or hasRole('USER')")
    public Result<List<CommentResponse>> getLatestComments(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "数量限制", example = "5")
            @RequestParam(defaultValue = "5") Integer limit) {
        
        List<CommentResponse> latestComments = taskCommentService.getLatestComments(taskId, limit);
        return Result.success(latestComments);
    }

    @GetMapping("/tasks/{taskId}/popular")
    @Operation(summary = "获取热门评论", description = "获取指定任务的热门评论列表")
    @PreAuthorize("hasAuthority('task:comment:view') or hasRole('USER')")
    public Result<List<CommentResponse>> getPopularComments(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "数量限制", example = "5")
            @RequestParam(defaultValue = "5") Integer limit) {
        
        List<CommentResponse> popularComments = taskCommentService.getPopularComments(taskId, limit);
        return Result.success(popularComments);
    }

    @GetMapping("/search")
    @Operation(summary = "搜索评论", description = "根据条件搜索评论")
    @PreAuthorize("hasAuthority('task:comment:view') or hasRole('USER')")
    public Result<IPage<CommentResponse>> searchComments(
            @Parameter(description = "任务ID")
            @RequestParam(required = false) String taskId,
            @Parameter(description = "关键词")
            @RequestParam(required = false) String keyword,
            @Parameter(description = "评论者ID")
            @RequestParam(required = false) String commenterId,
            @Parameter(description = "评论类型")
            @RequestParam(required = false) String commentType,
            @Parameter(description = "状态")
            @RequestParam(required = false) String status,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "10")
            @RequestParam(defaultValue = "10") Integer size) {
        
        IPage<CommentResponse> searchResult = taskCommentService.searchComments(
                taskId, keyword, commenterId, commentType, status, page, size);
        return Result.success(searchResult);
    }

    @PostMapping("/{commentId}/like")
    @Operation(summary = "点赞评论", description = "为指定评论点赞")
    @PreAuthorize("hasAuthority('task:comment:like') or hasRole('USER')")
    public Result<String> likeComment(
            @Parameter(description = "评论ID", required = true)
            @PathVariable @NotBlank(message = "评论ID不能为空") String commentId) {
        
        log.info("Liking comment: {}", commentId);
        
        // TODO: 获取当前用户ID
        String userId = "current-user-id";
        taskCommentService.likeComment(commentId, userId);
        return Result.success("点赞成功");
    }

    @DeleteMapping("/{commentId}/like")
    @Operation(summary = "取消点赞评论", description = "取消对指定评论的点赞")
    @PreAuthorize("hasAuthority('task:comment:like') or hasRole('USER')")
    public Result<String> unlikeComment(
            @Parameter(description = "评论ID", required = true)
            @PathVariable @NotBlank(message = "评论ID不能为空") String commentId) {
        
        log.info("Unliking comment: {}", commentId);
        
        // TODO: 获取当前用户ID
        String userId = "current-user-id";
        taskCommentService.unlikeComment(commentId, userId);
        return Result.success("取消点赞成功");
    }

    @PutMapping("/{commentId}/pin")
    @Operation(summary = "置顶评论", description = "置顶指定评论")
    @PreAuthorize("hasAuthority('task:comment:pin') or hasRole('ADMIN')")
    public Result<String> pinComment(
            @Parameter(description = "评论ID", required = true)
            @PathVariable @NotBlank(message = "评论ID不能为空") String commentId) {
        
        log.info("Pinning comment: {}", commentId);
        
        taskCommentService.pinComment(commentId);
        return Result.success("置顶成功");
    }

    @DeleteMapping("/{commentId}/pin")
    @Operation(summary = "取消置顶评论", description = "取消置顶指定评论")
    @PreAuthorize("hasAuthority('task:comment:pin') or hasRole('ADMIN')")
    public Result<String> unpinComment(
            @Parameter(description = "评论ID", required = true)
            @PathVariable @NotBlank(message = "评论ID不能为空") String commentId) {
        
        log.info("Unpinning comment: {}", commentId);
        
        taskCommentService.unpinComment(commentId);
        return Result.success("取消置顶成功");
    }

    @PutMapping("/{commentId}/hide")
    @Operation(summary = "隐藏评论", description = "隐藏指定评论")
    @PreAuthorize("hasAuthority('task:comment:moderate') or hasRole('ADMIN')")
    public Result<String> hideComment(
            @Parameter(description = "评论ID", required = true)
            @PathVariable @NotBlank(message = "评论ID不能为空") String commentId) {
        
        log.info("Hiding comment: {}", commentId);
        
        taskCommentService.hideComment(commentId);
        return Result.success("隐藏成功");
    }

    @PutMapping("/{commentId}/show")
    @Operation(summary = "显示评论", description = "显示指定评论")
    @PreAuthorize("hasAuthority('task:comment:moderate') or hasRole('ADMIN')")
    public Result<String> showComment(
            @Parameter(description = "评论ID", required = true)
            @PathVariable @NotBlank(message = "评论ID不能为空") String commentId) {
        
        log.info("Showing comment: {}", commentId);
        
        taskCommentService.showComment(commentId);
        return Result.success("显示成功");
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除评论", description = "批量删除多个评论")
    @PreAuthorize("hasAuthority('task:comment:delete') or hasRole('ADMIN')")
    public Result<String> batchDeleteComments(
            @Parameter(description = "评论ID列表", required = true)
            @RequestBody @NotEmpty(message = "评论ID列表不能为空") List<String> commentIds) {
        
        log.info("Batch deleting comments: {}", commentIds);
        
        taskCommentService.batchDeleteComments(commentIds);
        return Result.success("批量删除成功");
    }

    @PutMapping("/batch/status")
    @Operation(summary = "批量更新评论状态", description = "批量更新多个评论的状态")
    @PreAuthorize("hasAuthority('task:comment:moderate') or hasRole('ADMIN')")
    public Result<String> batchUpdateStatus(
            @Parameter(description = "评论ID列表", required = true)
            @RequestParam @NotEmpty(message = "评论ID列表不能为空") List<String> commentIds,
            @Parameter(description = "状态", required = true)
            @RequestParam @NotBlank(message = "状态不能为空") String status) {
        
        log.info("Batch updating comment status: {} -> {}", commentIds, status);
        
        taskCommentService.batchUpdateStatus(commentIds, status);
        return Result.success("批量更新状态成功");
    }

    @GetMapping("/tasks/{taskId}/statistics")
    @Operation(summary = "获取评论统计", description = "获取指定任务的评论统计信息")
    @PreAuthorize("hasAuthority('task:comment:statistics') or hasRole('ADMIN')")
    public Result<Object> getCommentStatistics(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        Object statistics = taskCommentService.getCommentStatistics(taskId);
        return Result.success(statistics);
    }

    @GetMapping("/tasks/{taskId}/activity")
    @Operation(summary = "获取评论活跃度统计", description = "获取指定任务的评论活跃度统计")
    @PreAuthorize("hasAuthority('task:comment:statistics') or hasRole('ADMIN')")
    public Result<List<Object>> getCommentActivityStats(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "天数", example = "7")
            @RequestParam(defaultValue = "7") Integer days) {
        
        List<Object> activityStats = taskCommentService.getCommentActivityStats(taskId, days);
        return Result.success(activityStats);
    }
}
