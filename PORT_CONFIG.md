# WTMS 端口配置规范

## 🔧 标准端口配置

### 应用服务端口
- **后端服务端口**: `55557`
  - Spring Boot 应用: `http://localhost:55557`
  - API 基础路径: `http://localhost:55557/api`
  
- **前端服务端口**: `33335`
  - Vue.js 开发服务器: `http://localhost:33335`
  - 生产环境前端: `http://localhost:33335`

### 基础服务端口
- **MySQL 数据库**: `3308` (映射到容器内部3306)
- **Redis 缓存**: `6379`
- **phpMyAdmin**: `8081`
- **Redis Commander**: `8082`

## 📋 配置文件检查清单

### 后端配置
- [ ] `wtms-backend/src/main/resources/application.yml` - server.port: 55557
- [ ] `wtms-backend/src/main/resources/application-dev.yml` - server.port: 55557
- [ ] `wtms-backend/src/main/resources/application-prod.yml` - server.port: 55557

### 前端配置
- [ ] `wtms-frontend/.env` - VITE_API_BASE_URL=http://localhost:55557/api
- [ ] `wtms-frontend/.env.development` - VITE_API_BASE_URL=http://localhost:55557/api
- [ ] `wtms-frontend/.env.production` - VITE_API_BASE_URL=http://localhost:55557/api
- [ ] `wtms-frontend/vite.config.ts` - server.port: 33335
- [ ] `wtms-frontend/vite.config.ts` - proxy target: http://localhost:55557

### Docker配置
- [ ] `docker-compose.yml` - 应用服务端口映射
- [ ] `docker-compose.dev.yml` - 开发环境端口映射

## 🚀 服务启动顺序

### 开发环境启动顺序
1. **启动基础服务** (MySQL + Redis)
   ```bash
   docker-compose -f docker-compose.dev.yml up -d mysql-dev redis-dev
   ```

2. **启动后端服务** (端口 55557)
   ```bash
   cd wtms-backend
   mvn spring-boot:run
   ```

3. **启动前端服务** (端口 33335)
   ```bash
   cd wtms-frontend
   npm run dev
   ```

4. **验证服务状态**
   - 后端健康检查: `http://localhost:55557/actuator/health`
   - 前端应用: `http://localhost:33335`
   - API文档: `http://localhost:55557/swagger-ui.html`

### 生产环境启动顺序
1. **构建应用**
   ```bash
   # 构建后端
   cd wtms-backend && mvn clean package -DskipTests
   
   # 构建前端
   cd wtms-frontend && npm run build
   ```

2. **启动所有服务**
   ```bash
   docker-compose up -d --build
   ```

## ⚠️ 重要注意事项

### 严格遵守的规则
1. **绝对不要**随意更改端口号
2. **绝对不要**因为端口冲突而使用其他端口
3. **绝对不要**在不同测试场景中使用不同端口
4. **绝对不要**在配置文件中使用动态端口分配

### 端口冲突处理
如果遇到端口冲突，应该：
1. **查找占用端口的进程**
   ```bash
   # Windows
   netstat -ano | findstr :55557
   netstat -ano | findstr :33335
   
   # Linux/Mac
   lsof -i :55557
   lsof -i :33335
   ```

2. **停止占用端口的进程**
   ```bash
   # Windows
   taskkill /PID <PID> /F
   
   # Linux/Mac
   kill -9 <PID>
   ```

3. **重新启动服务**

### 配置验证
在每次启动服务前，请验证：
- [ ] 后端配置文件中的端口是 55557
- [ ] 前端配置文件中的端口是 33335
- [ ] 前端API配置指向 http://localhost:55557/api
- [ ] 没有其他进程占用这些端口

## 🔍 故障排查

### 常见问题
1. **前端无法访问后端API**
   - 检查后端是否在 55557 端口启动
   - 检查前端API配置是否正确
   - 检查跨域配置

2. **端口被占用**
   - 使用上述命令查找并停止占用进程
   - 不要更改端口，而是释放端口

3. **Docker服务无法启动**
   - 检查Docker是否正常运行
   - 检查端口映射配置
   - 查看Docker日志

### 验证命令
```bash
# 检查端口监听状态
netstat -an | grep :55557  # 后端端口
netstat -an | grep :33335  # 前端端口

# 测试API连接
curl http://localhost:55557/actuator/health

# 测试前端访问
curl http://localhost:33335
```

---

**记住：端口配置的一致性是项目稳定运行的基础！**
