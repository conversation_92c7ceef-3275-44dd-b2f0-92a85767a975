package com.wtms.service;

import com.wtms.config.DevelopmentConfig;
import com.wtms.entity.User;
import com.wtms.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.core.env.Environment;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

/**
 * 开发环境权限服务
 * 用于在开发环境下提供权限覆盖功能
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class DevelopmentPermissionService {

    @Autowired
    private DevelopmentConfig developmentConfig;

    @Autowired
    private Environment environment;

    @Autowired
    private UserMapper userMapper;

    /**
     * 检查是否为开发环境
     */
    public boolean isDevelopmentEnvironment() {
        String[] activeProfiles = environment.getActiveProfiles();
        for (String profile : activeProfiles) {
            if ("dev".equals(profile) || "development".equals(profile) || "local".equals(profile)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否启用开发模式
     */
    public boolean isDevelopmentModeEnabled() {
        return isDevelopmentEnvironment() && developmentConfig.isDevelopmentMode();
    }

    /**
     * 检查是否应该覆盖权限
     */
    public boolean shouldOverridePermissions() {
        if (!isDevelopmentModeEnabled()) {
            return false;
        }

        String username = getCurrentUsername();
        if (username == null) {
            return false;
        }

        boolean shouldOverride = developmentConfig.shouldOverridePermissions(username);
        
        if (shouldOverride) {
            log.debug("Development mode: overriding permissions for user {}", username);
        }
        
        return shouldOverride;
    }

    /**
     * 检查是否应该覆盖角色
     */
    public boolean shouldOverrideRoles() {
        if (!isDevelopmentModeEnabled()) {
            return false;
        }

        if (!developmentConfig.getPermissionOverride().isOverrideRoles()) {
            return false;
        }

        String username = getCurrentUsername();
        if (username == null) {
            return false;
        }

        boolean shouldOverride = developmentConfig.shouldOverridePermissions(username);
        
        if (shouldOverride) {
            log.debug("Development mode: overriding roles for user {}", username);
        }
        
        return shouldOverride;
    }

    /**
     * 检查用户是否应该被视为超级管理员
     */
    public boolean shouldTreatAsSuperAdmin(String userId) {
        if (!isDevelopmentModeEnabled()) {
            return false;
        }

        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        boolean shouldTreat = developmentConfig.shouldOverridePermissions(user.getUsername());
        
        if (shouldTreat) {
            log.debug("Development mode: treating user {} as super admin", user.getUsername());
        }
        
        return shouldTreat;
    }

    /**
     * 检查用户是否应该被视为超级管理员（通过用户名）
     */
    public boolean shouldTreatAsSuperAdmin(String username, boolean isUsername) {
        if (!isDevelopmentModeEnabled()) {
            return false;
        }

        if (!isUsername) {
            return shouldTreatAsSuperAdmin(username); // 如果不是用户名，则按用户ID处理
        }

        boolean shouldTreat = developmentConfig.shouldOverridePermissions(username);
        
        if (shouldTreat) {
            log.debug("Development mode: treating user {} as super admin", username);
        }
        
        return shouldTreat;
    }

    /**
     * 获取当前用户名
     */
    private String getCurrentUsername() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails) {
            return ((UserDetails) principal).getUsername();
        }
        
        return null;
    }

    /**
     * 获取当前用户ID
     */
    public String getCurrentUserId() {
        String username = getCurrentUsername();
        if (username == null) {
            return null;
        }

        User user = userMapper.findByUsernameWithRole(username);
        return user != null ? user.getId() : null;
    }

    /**
     * 记录权限覆盖日志
     */
    public void logPermissionOverride(String action, String resource, String username) {
        if (isDevelopmentModeEnabled()) {
            log.info("Development mode permission override: user={}, action={}, resource={}", 
                    username, action, resource);
        }
    }

    /**
     * 记录角色覆盖日志
     */
    public void logRoleOverride(String role, String username) {
        if (isDevelopmentModeEnabled()) {
            log.info("Development mode role override: user={}, role={}", username, role);
        }
    }

    /**
     * 获取开发环境配置信息
     */
    public DevelopmentConfig.PermissionOverride getPermissionOverrideConfig() {
        return developmentConfig.getPermissionOverride();
    }

    /**
     * 获取超级管理员配置信息
     */
    public DevelopmentConfig.SuperAdmin getSuperAdminConfig() {
        return developmentConfig.getSuperAdmin();
    }

    /**
     * 检查是否需要自动创建超级管理员
     */
    public boolean shouldAutoCreateSuperAdmin() {
        return isDevelopmentModeEnabled() && developmentConfig.getSuperAdmin().isAutoCreate();
    }
}
