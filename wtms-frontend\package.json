{"name": "wtms-frontend", "version": "1.0.0", "description": "WTMS工作任务管理平台前端", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "build:check": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "lint:check": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "vue-tsc --noEmit", "code-quality": "npm run lint:check && npm run format:check && npm run type-check", "audit": "npm audit --audit-level=moderate", "audit:fix": "npm audit fix", "security:scan": "npm run audit && npm run snyk:test", "snyk:test": "snyk test --severity-threshold=medium", "snyk:monitor": "snyk monitor", "snyk:wizard": "snyk wizard"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.5", "pinia": "^2.1.7", "element-plus": "^2.4.4", "@element-plus/icons-vue": "^2.3.1", "axios": "^1.6.2", "echarts": "^5.4.3", "fabric": "^5.3.0", "konva": "^9.2.0", "vue-konva": "^3.0.2", "dayjs": "^1.11.10", "lodash-es": "^4.17.21", "nprogress": "^0.2.0", "js-cookie": "^3.0.5"}, "devDependencies": {"@types/node": "^20.10.5", "@types/lodash-es": "^4.17.12", "@types/nprogress": "^0.2.3", "@types/js-cookie": "^3.0.6", "@typescript-eslint/eslint-plugin": "^6.14.0", "@typescript-eslint/parser": "^6.14.0", "@vitejs/plugin-vue": "^4.5.2", "@vue/eslint-config-prettier": "^8.0.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/tsconfig": "^0.5.1", "eslint": "^8.56.0", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.1.1", "sass": "^1.69.5", "snyk": "^1.1266.0", "typescript": "~5.2.0", "unplugin-auto-import": "^0.17.2", "unplugin-vue-components": "^0.26.0", "vite": "^5.0.8", "vue-tsc": "^1.8.25"}, "engines": {"node": ">=18.0.0"}}