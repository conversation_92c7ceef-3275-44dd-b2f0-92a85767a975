import request from '@/utils/request'

/**
 * 质量评价相关接口
 */

// 任务评价类型定义
export interface TaskEvaluation {
  id: string
  taskId: string
  evaluatorId: string
  evaluateeId: string
  evaluationType: 'self' | 'peer' | 'supervisor' | 'subordinate' | 'customer' | 'system'
  evaluationStage: 'planning' | 'execution' | 'completion' | 'review' | 'final'
  overallScore: number
  qualityScore?: number
  efficiencyScore?: number
  communicationScore?: number
  innovationScore?: number
  teamworkScore?: number
  evaluationContent: string
  strengths?: string
  improvements?: string
  evaluationTags?: string
  isAnonymous: boolean
  isPublic: boolean
  status: 'draft' | 'submitted' | 'reviewed' | 'published' | 'archived' | 'rejected'
  weight?: number
  deadline?: string
  createdAt: string
  updatedAt: string
  task?: {
    id: string
    title: string
    description: string
  }
  evaluator?: {
    id: string
    username: string
    fullName: string
  }
  evaluatee?: {
    id: string
    username: string
    fullName: string
  }
  dimensions?: EvaluationDimension[]
}

// 评价维度类型定义
export interface EvaluationDimension {
  id: string
  taskEvaluationId: string
  dimensionName: string
  dimensionCode: string
  description?: string
  score: number
  maxScore: number
  minScore: number
  weight: number
  evaluationContent?: string
  evaluationCriteria?: string
  isRequired: boolean
  isEnabled: boolean
  sortOrder: number
  createdAt: string
  updatedAt: string
}

// 质量统计类型定义
export interface QualityStatistics {
  id: string
  targetId: string
  targetType: 'user' | 'task' | 'project' | 'department' | 'team' | 'organization'
  periodType: 'daily' | 'weekly' | 'monthly' | 'quarterly' | 'yearly' | 'custom'
  periodStart: string
  periodEnd: string
  totalEvaluations: number
  completedEvaluations: number
  avgOverallScore: number
  avgQualityScore: number
  avgEfficiencyScore: number
  avgCommunicationScore: number
  avgInnovationScore: number
  avgTeamworkScore: number
  maxScore: number
  minScore: number
  scoreStdDev: number
  excellentCount: number
  goodCount: number
  averageCount: number
  passCount: number
  failCount: number
  qualityTrend: 'improving' | 'stable' | 'declining' | 'unknown'
  improvementSuggestions: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
  createdAt: string
  updatedAt: string
}

// 创建评价请求
export interface CreateEvaluationRequest {
  taskId: string
  evaluateeId: string
  evaluationType: string
  evaluationStage?: string
  overallScore: number
  qualityScore?: number
  efficiencyScore?: number
  communicationScore?: number
  innovationScore?: number
  teamworkScore?: number
  evaluationContent: string
  strengths?: string
  improvements?: string
  evaluationTags?: string
  isAnonymous?: boolean
  isPublic?: boolean
  weight?: number
  deadline?: string
  dimensions?: {
    dimensionName: string
    dimensionCode?: string
    description?: string
    score: number
    maxScore?: number
    minScore?: number
    weight?: number
    evaluationContent?: string
    evaluationCriteria?: string
    isRequired?: boolean
    sortOrder?: number
  }[]
  attachmentIds?: string[]
}

/**
 * 任务评价管理接口
 */
export function createTaskEvaluation(data: CreateEvaluationRequest) {
  return request.post<TaskEvaluation>('/api/v1/task-evaluations', data)
}

export function updateTaskEvaluation(evaluationId: string, data: CreateEvaluationRequest) {
  return request.put<TaskEvaluation>(`/api/v1/task-evaluations/${evaluationId}`, data)
}

export function deleteTaskEvaluation(evaluationId: string) {
  return request.delete(`/api/v1/task-evaluations/${evaluationId}`)
}

export function getTaskEvaluation(evaluationId: string) {
  return request.get<TaskEvaluation>(`/api/v1/task-evaluations/${evaluationId}`)
}

export function getEvaluationsByTask(taskId: string) {
  return request.get<TaskEvaluation[]>(`/api/v1/task-evaluations/task/${taskId}`)
}

export function getEvaluationsByEvaluator(evaluatorId: string, params?: {
  status?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: TaskEvaluation[]
    total: number
    size: number
    current: number
    pages: number
  }>(`/api/v1/task-evaluations/evaluator/${evaluatorId}`, { params })
}

export function getEvaluationsByEvaluatee(evaluateeId: string, params?: {
  status?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: TaskEvaluation[]
    total: number
    size: number
    current: number
    pages: number
  }>(`/api/v1/task-evaluations/evaluatee/${evaluateeId}`, { params })
}

export function getEvaluationsByType(evaluationType: string) {
  return request.get<TaskEvaluation[]>(`/api/v1/task-evaluations/type/${evaluationType}`)
}

export function getEvaluationsByStage(evaluationStage: string) {
  return request.get<TaskEvaluation[]>(`/api/v1/task-evaluations/stage/${evaluationStage}`)
}

export function getEvaluationsByStatus(status: string) {
  return request.get<TaskEvaluation[]>(`/api/v1/task-evaluations/status/${status}`)
}

export function getOverdueEvaluations() {
  return request.get<TaskEvaluation[]>('/api/v1/task-evaluations/overdue')
}

export function getPendingEvaluations() {
  return request.get<TaskEvaluation[]>('/api/v1/task-evaluations/pending')
}

export function getPublishedEvaluations() {
  return request.get<TaskEvaluation[]>('/api/v1/task-evaluations/published')
}

export function searchTaskEvaluations(params: {
  keyword?: string
  taskId?: string
  evaluatorId?: string
  evaluateeId?: string
  evaluationType?: string
  status?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: TaskEvaluation[]
    total: number
    size: number
    current: number
    pages: number
  }>('/api/v1/task-evaluations/search', { params })
}

export function submitTaskEvaluation(evaluationId: string) {
  return request.put(`/api/v1/task-evaluations/${evaluationId}/submit`)
}

export function reviewTaskEvaluation(evaluationId: string) {
  return request.put(`/api/v1/task-evaluations/${evaluationId}/review`)
}

export function publishTaskEvaluation(evaluationId: string) {
  return request.put(`/api/v1/task-evaluations/${evaluationId}/publish`)
}

export function archiveTaskEvaluation(evaluationId: string) {
  return request.put(`/api/v1/task-evaluations/${evaluationId}/archive`)
}

export function rejectTaskEvaluation(evaluationId: string, reason: string) {
  return request.put(`/api/v1/task-evaluations/${evaluationId}/reject`, null, {
    params: { reason }
  })
}

export function batchDeleteTaskEvaluations(evaluationIds: string[]) {
  return request.delete('/api/v1/task-evaluations/batch', { data: evaluationIds })
}

export function batchUpdateEvaluationStatus(evaluationIds: string[], status: string) {
  return request.put('/api/v1/task-evaluations/batch/status', null, {
    params: { evaluationIds, status }
  })
}

export function calculateTaskAverageScore(taskId: string) {
  return request.get<number>(`/api/v1/task-evaluations/task/${taskId}/average-score`)
}

export function calculateUserAverageScore(evaluateeId: string) {
  return request.get<number>(`/api/v1/task-evaluations/user/${evaluateeId}/average-score`)
}

export function calculateUserDimensionScores(evaluateeId: string) {
  return request.get<Record<string, number>>(`/api/v1/task-evaluations/user/${evaluateeId}/dimension-scores`)
}

export function getTaskEvaluationStatistics() {
  return request.get('/api/v1/task-evaluations/statistics')
}

export function getHighScoreEvaluations(minScore = 90, limit = 10) {
  return request.get<TaskEvaluation[]>('/api/v1/task-evaluations/high-score', {
    params: { minScore, limit }
  })
}

export function getLowScoreEvaluations(maxScore = 60, limit = 10) {
  return request.get<TaskEvaluation[]>('/api/v1/task-evaluations/low-score', {
    params: { maxScore, limit }
  })
}

export function getRecentEvaluations(limit = 10) {
  return request.get<TaskEvaluation[]>('/api/v1/task-evaluations/recent', {
    params: { limit }
  })
}

export function getAttentionRequiredEvaluations(limit = 10) {
  return request.get<TaskEvaluation[]>('/api/v1/task-evaluations/attention-required', {
    params: { limit }
  })
}

export function cleanupExpiredDrafts(days = 30) {
  return request.post<number>('/api/v1/task-evaluations/cleanup-expired', null, {
    params: { days }
  })
}

export function canEvaluateTask(taskId: string, evaluateeId: string) {
  return request.get<boolean>('/api/v1/task-evaluations/can-evaluate', {
    params: { taskId, evaluateeId }
  })
}

/**
 * 质量统计管理接口
 */
export function createQualityStatistics(data: QualityStatistics) {
  return request.post<QualityStatistics>('/api/v1/quality-statistics', data)
}

export function updateQualityStatistics(statisticsId: string, data: QualityStatistics) {
  return request.put<QualityStatistics>(`/api/v1/quality-statistics/${statisticsId}`, data)
}

export function deleteQualityStatistics(statisticsId: string) {
  return request.delete(`/api/v1/quality-statistics/${statisticsId}`)
}

export function getQualityStatistics(statisticsId: string) {
  return request.get<QualityStatistics>(`/api/v1/quality-statistics/${statisticsId}`)
}

export function getStatisticsByTarget(targetId: string) {
  return request.get<QualityStatistics[]>(`/api/v1/quality-statistics/target/${targetId}`)
}

export function getStatisticsByTargetType(targetType: string) {
  return request.get<QualityStatistics[]>(`/api/v1/quality-statistics/target-type/${targetType}`)
}

export function getStatisticsByPeriodType(periodType: string) {
  return request.get<QualityStatistics[]>(`/api/v1/quality-statistics/period-type/${periodType}`)
}

export function getLatestQualityStatistics(targetId: string, targetType: string) {
  return request.get<QualityStatistics>('/api/v1/quality-statistics/latest', {
    params: { targetId, targetType }
  })
}

export function getStatisticsByTimeRange(startTime: string, endTime: string) {
  return request.get<QualityStatistics[]>('/api/v1/quality-statistics/time-range', {
    params: { startTime, endTime }
  })
}

export function searchQualityStatistics(params: {
  targetId?: string
  targetType?: string
  periodType?: string
  startTime?: string
  endTime?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: QualityStatistics[]
    total: number
    size: number
    current: number
    pages: number
  }>('/api/v1/quality-statistics/search', { params })
}

export function generateUserStatistics(userId: string, periodType: string, periodStart: string, periodEnd: string) {
  return request.post<boolean>('/api/v1/quality-statistics/generate/user', null, {
    params: { userId, periodType, periodStart, periodEnd }
  })
}

export function generateTaskStatistics(taskId: string, periodType: string, periodStart: string, periodEnd: string) {
  return request.post<boolean>('/api/v1/quality-statistics/generate/task', null, {
    params: { taskId, periodType, periodStart, periodEnd }
  })
}

export function batchGenerateStatistics(targetType: string, periodType: string, periodStart: string, periodEnd: string) {
  return request.post<number>('/api/v1/quality-statistics/generate/batch', null, {
    params: { targetType, periodType, periodStart, periodEnd }
  })
}

export function getQualityTrend(targetId: string, targetType: string, periodType: string, periods = 12) {
  return request.get<QualityStatistics[]>('/api/v1/quality-statistics/trend', {
    params: { targetId, targetType, periodType, periods }
  })
}

export function getQualityRanking(targetType: string, periodType: string, periodStart: string, periodEnd: string, limit = 20) {
  return request.get<QualityStatistics[]>('/api/v1/quality-statistics/ranking', {
    params: { targetType, periodType, periodStart, periodEnd, limit }
  })
}

export function getQualityComparison(targetIds: string[], targetType: string, periodType: string, periodStart: string, periodEnd: string) {
  return request.get<QualityStatistics[]>('/api/v1/quality-statistics/comparison', {
    params: { targetIds, targetType, periodType, periodStart, periodEnd }
  })
}

export function getQualityDistribution(targetType: string, periodType: string, periodStart: string, periodEnd: string) {
  return request.get('/api/v1/quality-statistics/distribution', {
    params: { targetType, periodType, periodStart, periodEnd }
  })
}

export function getQualityImprovementStatistics(targetType: string, periods = 6) {
  return request.get('/api/v1/quality-statistics/improvement', {
    params: { targetType, periods }
  })
}

export function getQualityWarningStatistics(targetType: string, threshold = 70.0) {
  return request.get<QualityStatistics[]>('/api/v1/quality-statistics/warning', {
    params: { targetType, threshold }
  })
}

export function getExcellentQualityStatistics(minScore = 90.0, limit = 20) {
  return request.get<QualityStatistics[]>('/api/v1/quality-statistics/excellent', {
    params: { minScore, limit }
  })
}

export function getImprovementNeededStatistics(maxScore = 60.0, limit = 20) {
  return request.get<QualityStatistics[]>('/api/v1/quality-statistics/improvement-needed', {
    params: { maxScore, limit }
  })
}

export function getQualityStatisticsOverview() {
  return request.get('/api/v1/quality-statistics/overview')
}

export function cleanupExpiredStatistics(days = 90) {
  return request.post<number>('/api/v1/quality-statistics/cleanup-expired', null, {
    params: { days }
  })
}

export function autoGeneratePeriodicStatistics(periodType: string) {
  return request.post<number>('/api/v1/quality-statistics/auto-generate', null, {
    params: { periodType }
  })
}
