<template>
  <div class="evaluation-form">
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="default"
    >
      <!-- 基本信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>基本信息</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="任务" prop="taskId">
              <el-select
                v-model="form.taskId"
                placeholder="请选择任务"
                filterable
                remote
                :remote-method="searchTasks"
                :loading="taskLoading"
                style="width: 100%"
                @change="handleTaskChange"
              >
                <el-option
                  v-for="task in taskOptions"
                  :key="task.id"
                  :label="task.title"
                  :value="task.id"
                >
                  <div class="task-option">
                    <div class="task-title">{{ task.title }}</div>
                    <div class="task-desc">{{ task.description }}</div>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="被评价者" prop="evaluateeId">
              <el-select
                v-model="form.evaluateeId"
                placeholder="请选择被评价者"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="user in evaluateeOptions"
                  :key="user.id"
                  :label="user.fullName"
                  :value="user.id"
                >
                  <div class="user-option">
                    <el-avatar :size="24" :src="user.avatar">{{ user.fullName?.charAt(0) }}</el-avatar>
                    <span class="user-name">{{ user.fullName }}</span>
                    <span class="user-role">{{ user.roleName }}</span>
                  </div>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="评价类型" prop="evaluationType">
              <el-select v-model="form.evaluationType" placeholder="请选择评价类型" style="width: 100%">
                <el-option label="自评" value="self" />
                <el-option label="同事评价" value="peer" />
                <el-option label="上级评价" value="supervisor" />
                <el-option label="下级评价" value="subordinate" />
                <el-option label="客户评价" value="customer" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="评价阶段" prop="evaluationStage">
              <el-select v-model="form.evaluationStage" placeholder="请选择评价阶段" style="width: 100%">
                <el-option label="计划阶段" value="planning" />
                <el-option label="执行阶段" value="execution" />
                <el-option label="完成阶段" value="completion" />
                <el-option label="回顾阶段" value="review" />
                <el-option label="最终评价" value="final" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="评价截止时间">
              <el-date-picker
                v-model="form.deadline"
                type="datetime"
                placeholder="请选择截止时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="权重">
              <el-input-number
                v-model="form.weight"
                :min="0.1"
                :max="10"
                :step="0.1"
                :precision="1"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item>
              <el-checkbox v-model="form.isAnonymous">匿名评价</el-checkbox>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item>
              <el-checkbox v-model="form.isPublic">公开评价</el-checkbox>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 评分维度 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Star /></el-icon>
            <span>评分维度</span>
          </div>
        </template>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="总体评分" prop="overallScore">
              <div class="score-input">
                <el-slider
                  v-model="form.overallScore"
                  :min="0"
                  :max="100"
                  :step="1"
                  show-input
                  :show-input-controls="false"
                />
                <div class="score-grade">{{ getScoreGrade(form.overallScore) }}</div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="质量评分">
              <div class="score-input">
                <el-slider
                  v-model="form.qualityScore"
                  :min="0"
                  :max="100"
                  :step="1"
                  show-input
                  :show-input-controls="false"
                />
                <div class="score-grade">{{ getScoreGrade(form.qualityScore) }}</div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="效率评分">
              <div class="score-input">
                <el-slider
                  v-model="form.efficiencyScore"
                  :min="0"
                  :max="100"
                  :step="1"
                  show-input
                  :show-input-controls="false"
                />
                <div class="score-grade">{{ getScoreGrade(form.efficiencyScore) }}</div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="沟通评分">
              <div class="score-input">
                <el-slider
                  v-model="form.communicationScore"
                  :min="0"
                  :max="100"
                  :step="1"
                  show-input
                  :show-input-controls="false"
                />
                <div class="score-grade">{{ getScoreGrade(form.communicationScore) }}</div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="创新评分">
              <div class="score-input">
                <el-slider
                  v-model="form.innovationScore"
                  :min="0"
                  :max="100"
                  :step="1"
                  show-input
                  :show-input-controls="false"
                />
                <div class="score-grade">{{ getScoreGrade(form.innovationScore) }}</div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="团队协作评分">
              <div class="score-input">
                <el-slider
                  v-model="form.teamworkScore"
                  :min="0"
                  :max="100"
                  :step="1"
                  show-input
                  :show-input-controls="false"
                />
                <div class="score-grade">{{ getScoreGrade(form.teamworkScore) }}</div>
              </div>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>

      <!-- 评价内容 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><EditPen /></el-icon>
            <span>评价内容</span>
          </div>
        </template>

        <el-form-item label="评价内容" prop="evaluationContent">
          <el-input
            v-model="form.evaluationContent"
            type="textarea"
            :rows="4"
            placeholder="请详细描述您的评价..."
            maxlength="2000"
            show-word-limit
          />
        </el-form-item>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="优点描述">
              <el-input
                v-model="form.strengths"
                type="textarea"
                :rows="3"
                placeholder="请描述被评价者的优点..."
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="改进建议">
              <el-input
                v-model="form.improvements"
                type="textarea"
                :rows="3"
                placeholder="请提出改进建议..."
                maxlength="1000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="评价标签">
          <el-tag
            v-for="tag in evaluationTags"
            :key="tag"
            closable
            @close="removeTag(tag)"
            style="margin-right: 8px; margin-bottom: 8px;"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="tagInputVisible"
            ref="tagInputRef"
            v-model="tagInputValue"
            size="small"
            style="width: 120px;"
            @keyup.enter="addTag"
            @blur="addTag"
          />
          <el-button
            v-else
            size="small"
            @click="showTagInput"
          >
            + 添加标签
          </el-button>
        </el-form-item>
      </el-card>

      <!-- 操作按钮 -->
      <div class="form-actions">
        <el-button @click="handleCancel">取消</el-button>
        <el-button @click="handleSaveDraft">保存草稿</el-button>
        <el-button type="primary" @click="handleSubmit">提交评价</el-button>
      </div>
    </el-form>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, nextTick, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Document, Star, EditPen } from '@element-plus/icons-vue'
import { createTaskEvaluation, updateTaskEvaluation, type CreateEvaluationRequest } from '@/api/evaluation'
import { searchTasks } from '@/api/task'
import { searchUsers } from '@/api/user'

// Props
interface Props {
  evaluationId?: string
  taskId?: string
  evaluateeId?: string
  modelValue?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  modelValue: false
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: boolean]
  'success': [evaluation: any]
}>()

// 响应式数据
const formRef = ref()
const tagInputRef = ref()
const taskLoading = ref(false)
const taskOptions = ref<any[]>([])
const evaluateeOptions = ref<any[]>([])
const tagInputVisible = ref(false)
const tagInputValue = ref('')

const form = reactive<CreateEvaluationRequest>({
  taskId: props.taskId || '',
  evaluateeId: props.evaluateeId || '',
  evaluationType: 'peer',
  evaluationStage: 'final',
  overallScore: 80,
  qualityScore: 80,
  efficiencyScore: 80,
  communicationScore: 80,
  innovationScore: 80,
  teamworkScore: 80,
  evaluationContent: '',
  strengths: '',
  improvements: '',
  evaluationTags: '',
  isAnonymous: false,
  isPublic: true,
  weight: 1.0
})

const rules = {
  taskId: [{ required: true, message: '请选择任务', trigger: 'change' }],
  evaluateeId: [{ required: true, message: '请选择被评价者', trigger: 'change' }],
  evaluationType: [{ required: true, message: '请选择评价类型', trigger: 'change' }],
  overallScore: [{ required: true, message: '请输入总体评分', trigger: 'blur' }],
  evaluationContent: [{ required: true, message: '请输入评价内容', trigger: 'blur' }]
}

// 计算属性
const evaluationTags = computed(() => {
  return form.evaluationTags ? form.evaluationTags.split(',').filter(tag => tag.trim()) : []
})

// 方法
const getScoreGrade = (score: number | undefined) => {
  if (!score) return ''
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '中等'
  if (score >= 60) return '及格'
  return '不及格'
}

const searchTasks = async (query: string) => {
  if (!query) return
  
  taskLoading.value = true
  try {
    const response = await searchTasks({
      keyword: query,
      page: 1,
      size: 20
    })
    taskOptions.value = response.data.records
  } catch (error) {
    console.error('Failed to search tasks:', error)
  } finally {
    taskLoading.value = false
  }
}

const handleTaskChange = async (taskId: string) => {
  // 根据任务获取相关用户
  try {
    const response = await searchUsers({
      keyword: '',
      page: 1,
      size: 50
    })
    evaluateeOptions.value = response.data.records
  } catch (error) {
    console.error('Failed to load users:', error)
  }
}

const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value?.focus()
  })
}

const addTag = () => {
  const tag = tagInputValue.value.trim()
  if (tag && !evaluationTags.value.includes(tag)) {
    const newTags = [...evaluationTags.value, tag]
    form.evaluationTags = newTags.join(',')
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

const removeTag = (tag: string) => {
  const newTags = evaluationTags.value.filter(t => t !== tag)
  form.evaluationTags = newTags.join(',')
}

const handleCancel = () => {
  emit('update:modelValue', false)
}

const handleSaveDraft = async () => {
  try {
    if (props.evaluationId) {
      await updateTaskEvaluation(props.evaluationId, form)
    } else {
      await createTaskEvaluation(form)
    }
    ElMessage.success('草稿保存成功')
  } catch (error) {
    console.error('Failed to save draft:', error)
    ElMessage.error('草稿保存失败')
  }
}

const handleSubmit = async () => {
  try {
    await formRef.value?.validate()
    
    await ElMessageBox.confirm('确定要提交此评价吗？提交后将无法修改。', '确认提交', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    let evaluation
    if (props.evaluationId) {
      evaluation = await updateTaskEvaluation(props.evaluationId, form)
    } else {
      evaluation = await createTaskEvaluation(form)
    }
    
    ElMessage.success('评价提交成功')
    emit('success', evaluation.data)
    emit('update:modelValue', false)
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to submit evaluation:', error)
      ElMessage.error('评价提交失败')
    }
  }
}

// 生命周期
onMounted(() => {
  if (props.taskId) {
    handleTaskChange(props.taskId)
  }
})
</script>

<style scoped>
.evaluation-form {
  max-width: 1000px;
  margin: 0 auto;
}

.form-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.task-option {
  padding: 4px 0;
}

.task-title {
  font-weight: 500;
  color: #303133;
}

.task-desc {
  font-size: 12px;
  color: #909399;
  margin-top: 2px;
}

.user-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-weight: 500;
  color: #303133;
}

.user-role {
  font-size: 12px;
  color: #909399;
}

.score-input {
  display: flex;
  align-items: center;
  gap: 16px;
}

.score-grade {
  min-width: 60px;
  font-size: 14px;
  font-weight: 500;
  color: #409EFF;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #EBEEF5;
}
</style>
