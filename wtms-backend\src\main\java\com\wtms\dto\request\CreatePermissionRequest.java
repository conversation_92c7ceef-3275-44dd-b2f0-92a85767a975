package com.wtms.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 创建权限请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "创建权限请求")
public class CreatePermissionRequest {

    @Schema(description = "权限编码", example = "user:create")
    @NotBlank(message = "权限编码不能为空")
    private String code;

    @Schema(description = "权限名称", example = "创建用户")
    @NotBlank(message = "权限名称不能为空")
    private String name;

    @Schema(description = "权限描述", example = "允许创建新用户")
    private String description;

    @Schema(description = "权限类型", example = "api", allowableValues = {"menu", "button", "api", "data"})
    @NotBlank(message = "权限类型不能为空")
    private String type;

    @Schema(description = "权限分组", example = "user")
    private String groupName;

    @Schema(description = "父权限ID")
    private String parentId;

    @Schema(description = "权限路径", example = "/system/user")
    private String path;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "资源标识", example = "user")
    private String resource;

    @Schema(description = "操作标识", example = "create")
    private String action;

    @Schema(description = "权限表达式", example = "hasRole('ADMIN') or hasPermission('user:create')")
    private String expression;

    @Schema(description = "是否启用", example = "true")
    @NotNull(message = "是否启用不能为空")
    private Boolean isEnabled = true;

    @Schema(description = "是否系统权限", example = "false")
    private Boolean isSystem = false;
}
