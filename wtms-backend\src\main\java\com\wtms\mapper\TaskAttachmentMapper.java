package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.entity.TaskAttachment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 任务附件Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface TaskAttachmentMapper extends BaseMapper<TaskAttachment> {

    /**
     * 根据任务ID查询附件
     */
    @Select("SELECT * FROM task_attachments WHERE task_id = #{taskId} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskAttachment> selectByTaskId(@Param("taskId") String taskId);

    /**
     * 根据评论ID查询附件
     */
    @Select("SELECT * FROM task_attachments WHERE comment_id = #{commentId} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskAttachment> selectByCommentId(@Param("commentId") String commentId);

    /**
     * 根据上传者ID查询附件
     */
    IPage<TaskAttachment> selectByUploaderId(Page<TaskAttachment> page, @Param("uploaderId") String uploaderId, 
                                           @Param("fileType") String fileType, @Param("status") String status);

    /**
     * 根据文件类型查询附件
     */
    @Select("SELECT * FROM task_attachments WHERE task_id = #{taskId} AND file_type = #{fileType} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskAttachment> selectByTaskIdAndFileType(@Param("taskId") String taskId, @Param("fileType") String fileType);

    /**
     * 根据文件MD5查询附件
     */
    @Select("SELECT * FROM task_attachments WHERE file_md5 = #{fileMd5} AND deleted_at IS NULL")
    List<TaskAttachment> selectByFileMd5(@Param("fileMd5") String fileMd5);

    /**
     * 查询公开附件
     */
    @Select("SELECT * FROM task_attachments WHERE task_id = #{taskId} AND is_public = TRUE AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskAttachment> selectPublicAttachments(@Param("taskId") String taskId);

    /**
     * 查询私有附件
     */
    @Select("SELECT * FROM task_attachments WHERE task_id = #{taskId} AND is_public = FALSE AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskAttachment> selectPrivateAttachments(@Param("taskId") String taskId);

    /**
     * 统计任务附件数量
     */
    @Select("SELECT COUNT(*) FROM task_attachments WHERE task_id = #{taskId} AND deleted_at IS NULL")
    int countByTaskId(@Param("taskId") String taskId);

    /**
     * 统计用户上传附件数量
     */
    @Select("SELECT COUNT(*) FROM task_attachments WHERE uploader_id = #{uploaderId} AND deleted_at IS NULL")
    int countByUploaderId(@Param("uploaderId") String uploaderId);

    /**
     * 统计附件总大小
     */
    @Select("SELECT COALESCE(SUM(file_size), 0) FROM task_attachments WHERE task_id = #{taskId} AND deleted_at IS NULL")
    long sumFileSizeByTaskId(@Param("taskId") String taskId);

    /**
     * 统计用户上传附件总大小
     */
    @Select("SELECT COALESCE(SUM(file_size), 0) FROM task_attachments WHERE uploader_id = #{uploaderId} AND deleted_at IS NULL")
    long sumFileSizeByUploaderId(@Param("uploaderId") String uploaderId);

    /**
     * 更新下载次数
     */
    @Update("UPDATE task_attachments SET download_count = download_count + 1, updated_at = NOW() WHERE id = #{attachmentId}")
    int incrementDownloadCount(@Param("attachmentId") String attachmentId);

    /**
     * 更新附件状态
     */
    @Update("UPDATE task_attachments SET status = #{status}, updated_at = NOW() WHERE id = #{attachmentId}")
    int updateStatus(@Param("attachmentId") String attachmentId, @Param("status") String status);

    /**
     * 批量删除附件
     */
    int batchDeleteAttachments(@Param("attachmentIds") List<String> attachmentIds);

    /**
     * 批量更新附件状态
     */
    int batchUpdateStatus(@Param("attachmentIds") List<String> attachmentIds, @Param("status") String status);

    /**
     * 查询过期附件
     */
    @Select("SELECT * FROM task_attachments WHERE expires_at IS NOT NULL AND expires_at <= NOW() AND status != 'expired'")
    List<TaskAttachment> selectExpiredAttachments();

    /**
     * 更新过期附件状态
     */
    @Update("UPDATE task_attachments SET status = 'expired', updated_at = NOW() WHERE expires_at IS NOT NULL AND expires_at <= NOW() AND status != 'expired'")
    int updateExpiredAttachments();

    /**
     * 查询大文件附件
     */
    @Select("SELECT * FROM task_attachments WHERE file_size > #{minSize} AND deleted_at IS NULL ORDER BY file_size DESC LIMIT #{limit}")
    List<TaskAttachment> selectLargeFiles(@Param("minSize") Long minSize, @Param("limit") Integer limit);

    /**
     * 搜索附件
     */
    IPage<TaskAttachment> searchAttachments(Page<TaskAttachment> page, @Param("taskId") String taskId,
                                          @Param("keyword") String keyword, @Param("fileType") String fileType,
                                          @Param("uploaderId") String uploaderId, @Param("status") String status);

    /**
     * 查询重复文件
     */
    List<TaskAttachment> selectDuplicateFiles(@Param("fileMd5") String fileMd5);

    /**
     * 查询附件统计信息
     */
    Object selectAttachmentStatistics(@Param("taskId") String taskId);

    /**
     * 查询文件类型统计
     */
    List<Object> selectFileTypeStatistics(@Param("taskId") String taskId);

    /**
     * 查询上传者统计
     */
    List<Object> selectUploaderStatistics(@Param("taskId") String taskId);
}
