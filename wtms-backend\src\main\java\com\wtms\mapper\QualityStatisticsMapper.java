package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.entity.QualityStatistics;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 质量统计Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface QualityStatisticsMapper extends BaseMapper<QualityStatistics> {

    /**
     * 根据统计对象ID查询统计
     */
    @Select("SELECT * FROM quality_statistics WHERE target_id = #{targetId} AND deleted_at IS NULL ORDER BY period_start DESC")
    List<QualityStatistics> selectByTargetId(@Param("targetId") String targetId);

    /**
     * 根据统计对象类型查询统计
     */
    @Select("SELECT * FROM quality_statistics WHERE target_type = #{targetType} AND deleted_at IS NULL ORDER BY period_start DESC")
    List<QualityStatistics> selectByTargetType(@Param("targetType") String targetType);

    /**
     * 根据统计周期查询统计
     */
    @Select("SELECT * FROM quality_statistics WHERE period_type = #{periodType} AND deleted_at IS NULL ORDER BY period_start DESC")
    List<QualityStatistics> selectByPeriodType(@Param("periodType") String periodType);

    /**
     * 根据统计对象和周期查询统计
     */
    @Select("SELECT * FROM quality_statistics WHERE target_id = #{targetId} AND period_type = #{periodType} AND deleted_at IS NULL ORDER BY period_start DESC")
    List<QualityStatistics> selectByTargetAndPeriod(@Param("targetId") String targetId, @Param("periodType") String periodType);

    /**
     * 根据时间范围查询统计
     */
    @Select("SELECT * FROM quality_statistics WHERE period_start >= #{startTime} AND period_end <= #{endTime} AND deleted_at IS NULL ORDER BY period_start DESC")
    List<QualityStatistics> selectByTimeRange(@Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 根据统计对象和时间范围查询统计
     */
    @Select("SELECT * FROM quality_statistics WHERE target_id = #{targetId} AND period_start >= #{startTime} AND period_end <= #{endTime} AND deleted_at IS NULL ORDER BY period_start DESC")
    List<QualityStatistics> selectByTargetAndTimeRange(@Param("targetId") String targetId, 
                                                       @Param("startTime") LocalDateTime startTime, 
                                                       @Param("endTime") LocalDateTime endTime);

    /**
     * 查询最新统计
     */
    @Select("SELECT * FROM quality_statistics WHERE target_id = #{targetId} AND target_type = #{targetType} AND deleted_at IS NULL ORDER BY period_end DESC LIMIT 1")
    QualityStatistics selectLatestStatistics(@Param("targetId") String targetId, @Param("targetType") String targetType);

    /**
     * 查询指定周期的统计
     */
    @Select("SELECT * FROM quality_statistics WHERE target_id = #{targetId} AND period_type = #{periodType} AND period_start = #{periodStart} AND deleted_at IS NULL")
    QualityStatistics selectByTargetAndPeriodStart(@Param("targetId") String targetId, 
                                                   @Param("periodType") String periodType, 
                                                   @Param("periodStart") LocalDateTime periodStart);

    /**
     * 搜索质量统计
     */
    IPage<QualityStatistics> searchStatistics(Page<QualityStatistics> page, @Param("targetId") String targetId,
                                              @Param("targetType") String targetType, @Param("periodType") String periodType,
                                              @Param("startTime") LocalDateTime startTime, @Param("endTime") LocalDateTime endTime);

    /**
     * 统计记录数量
     */
    @Select("SELECT COUNT(*) FROM quality_statistics WHERE deleted_at IS NULL")
    int countAll();

    /**
     * 根据统计对象类型统计数量
     */
    @Select("SELECT COUNT(*) FROM quality_statistics WHERE target_type = #{targetType} AND deleted_at IS NULL")
    int countByTargetType(@Param("targetType") String targetType);

    /**
     * 根据统计周期统计数量
     */
    @Select("SELECT COUNT(*) FROM quality_statistics WHERE period_type = #{periodType} AND deleted_at IS NULL")
    int countByPeriodType(@Param("periodType") String periodType);

    /**
     * 根据统计对象统计数量
     */
    @Select("SELECT COUNT(*) FROM quality_statistics WHERE target_id = #{targetId} AND deleted_at IS NULL")
    int countByTargetId(@Param("targetId") String targetId);

    /**
     * 更新统计状态
     */
    @Update("UPDATE quality_statistics SET status = #{status}, updated_at = NOW() WHERE id = #{statisticsId}")
    int updateStatus(@Param("statisticsId") String statisticsId, @Param("status") String status);

    /**
     * 批量删除统计
     */
    int batchDeleteStatistics(@Param("statisticsIds") List<String> statisticsIds);

    /**
     * 批量更新统计状态
     */
    int batchUpdateStatus(@Param("statisticsIds") List<String> statisticsIds, @Param("status") String status);

    /**
     * 生成用户质量统计
     */
    int generateUserStatistics(@Param("userId") String userId, @Param("periodType") String periodType,
                              @Param("periodStart") LocalDateTime periodStart, @Param("periodEnd") LocalDateTime periodEnd);

    /**
     * 生成任务质量统计
     */
    int generateTaskStatistics(@Param("taskId") String taskId, @Param("periodType") String periodType,
                              @Param("periodStart") LocalDateTime periodStart, @Param("periodEnd") LocalDateTime periodEnd);

    /**
     * 生成部门质量统计
     */
    int generateDepartmentStatistics(@Param("departmentId") String departmentId, @Param("periodType") String periodType,
                                    @Param("periodStart") LocalDateTime periodStart, @Param("periodEnd") LocalDateTime periodEnd);

    /**
     * 生成组织质量统计
     */
    int generateOrganizationStatistics(@Param("organizationId") String organizationId, @Param("periodType") String periodType,
                                      @Param("periodStart") LocalDateTime periodStart, @Param("periodEnd") LocalDateTime periodEnd);

    /**
     * 查询质量趋势
     */
    List<QualityStatistics> selectQualityTrend(@Param("targetId") String targetId, @Param("targetType") String targetType,
                                              @Param("periodType") String periodType, @Param("periods") Integer periods);

    /**
     * 查询质量排名
     */
    List<QualityStatistics> selectQualityRanking(@Param("targetType") String targetType, @Param("periodType") String periodType,
                                                 @Param("periodStart") LocalDateTime periodStart, @Param("periodEnd") LocalDateTime periodEnd,
                                                 @Param("limit") Integer limit);

    /**
     * 查询质量对比
     */
    List<QualityStatistics> selectQualityComparison(@Param("targetIds") List<String> targetIds, @Param("targetType") String targetType,
                                                    @Param("periodType") String periodType, @Param("periodStart") LocalDateTime periodStart,
                                                    @Param("periodEnd") LocalDateTime periodEnd);

    /**
     * 查询质量分布
     */
    List<Object> selectQualityDistribution(@Param("targetType") String targetType, @Param("periodType") String periodType,
                                          @Param("periodStart") LocalDateTime periodStart, @Param("periodEnd") LocalDateTime periodEnd);

    /**
     * 查询质量改进统计
     */
    List<Object> selectQualityImprovementStatistics(@Param("targetType") String targetType, @Param("periods") Integer periods);

    /**
     * 查询质量预警统计
     */
    List<QualityStatistics> selectQualityWarningStatistics(@Param("targetType") String targetType, @Param("threshold") Double threshold);

    /**
     * 查询优秀质量统计
     */
    @Select("SELECT * FROM quality_statistics WHERE avg_overall_score >= #{minScore} AND deleted_at IS NULL ORDER BY avg_overall_score DESC LIMIT #{limit}")
    List<QualityStatistics> selectExcellentQualityStatistics(@Param("minScore") Double minScore, @Param("limit") Integer limit);

    /**
     * 查询需要改进的质量统计
     */
    @Select("SELECT * FROM quality_statistics WHERE avg_overall_score <= #{maxScore} AND deleted_at IS NULL ORDER BY avg_overall_score ASC LIMIT #{limit}")
    List<QualityStatistics> selectImprovementNeededStatistics(@Param("maxScore") Double maxScore, @Param("limit") Integer limit);

    /**
     * 查询最新统计记录
     */
    @Select("SELECT * FROM quality_statistics WHERE deleted_at IS NULL ORDER BY created_at DESC LIMIT #{limit}")
    List<QualityStatistics> selectRecentStatistics(@Param("limit") Integer limit);

    /**
     * 查询统计概览
     */
    Object selectStatisticsOverview();

    /**
     * 查询统计汇总
     */
    Object selectStatisticsSummary(@Param("targetType") String targetType, @Param("periodType") String periodType,
                                  @Param("periodStart") LocalDateTime periodStart, @Param("periodEnd") LocalDateTime periodEnd);

    /**
     * 清理过期统计
     */
    @Update("UPDATE quality_statistics SET deleted_at = NOW() WHERE period_end < #{beforeDate}")
    int cleanupExpiredStatistics(@Param("beforeDate") LocalDateTime beforeDate);

    /**
     * 检查统计是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM quality_statistics WHERE target_id = #{targetId} AND period_type = #{periodType} AND period_start = #{periodStart} AND deleted_at IS NULL")
    boolean existsByTargetAndPeriod(@Param("targetId") String targetId, @Param("periodType") String periodType, @Param("periodStart") LocalDateTime periodStart);

    /**
     * 获取统计周期列表
     */
    @Select("SELECT DISTINCT period_type FROM quality_statistics WHERE deleted_at IS NULL ORDER BY period_type")
    List<String> selectDistinctPeriodTypes();

    /**
     * 获取统计对象类型列表
     */
    @Select("SELECT DISTINCT target_type FROM quality_statistics WHERE deleted_at IS NULL ORDER BY target_type")
    List<String> selectDistinctTargetTypes();

    /**
     * 查询统计时间范围
     */
    Object selectStatisticsTimeRange(@Param("targetId") String targetId, @Param("targetType") String targetType);

    /**
     * 查询质量波动统计
     */
    List<Object> selectQualityVolatilityStatistics(@Param("targetId") String targetId, @Param("periods") Integer periods);

    /**
     * 查询质量稳定性统计
     */
    Object selectQualityStabilityStatistics(@Param("targetId") String targetId, @Param("periods") Integer periods);
}
