<template>
  <div class="statistics-dashboard">
    <!-- 统计概览 -->
    <el-row :gutter="20" class="overview-section">
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-item">
            <div class="overview-icon total">
              <el-icon><Document /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-value">{{ overview.totalEvaluations || 0 }}</div>
              <div class="overview-label">总评价数</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-item">
            <div class="overview-icon average">
              <el-icon><Star /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-value">{{ overview.averageScore?.toFixed(1) || 0 }}</div>
              <div class="overview-label">平均评分</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-item">
            <div class="overview-icon excellent">
              <el-icon><Trophy /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-value">{{ overview.excellentCount || 0 }}</div>
              <div class="overview-label">优秀评价</div>
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="overview-card" shadow="hover">
          <div class="overview-item">
            <div class="overview-icon improvement">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="overview-content">
              <div class="overview-value">{{ overview.improvementNeeded || 0 }}</div>
              <div class="overview-label">需改进</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="charts-section">
      <!-- 评分趋势图 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="chart-header">
              <span>评分趋势</span>
              <el-select v-model="trendPeriod" size="small" style="width: 120px" @change="loadTrendData">
                <el-option label="最近7天" value="7" />
                <el-option label="最近30天" value="30" />
                <el-option label="最近90天" value="90" />
              </el-select>
            </div>
          </template>
          <div ref="trendChartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 评分分布图 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span>评分分布</span>
          </template>
          <div ref="distributionChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" class="charts-section">
      <!-- 评价类型统计 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <span>评价类型统计</span>
          </template>
          <div ref="typeChartRef" class="chart-container"></div>
        </el-card>
      </el-col>

      <!-- 维度评分雷达图 -->
      <el-col :span="12">
        <el-card class="chart-card" shadow="never">
          <template #header>
            <div class="chart-header">
              <span>维度评分分析</span>
              <el-select v-model="selectedUser" size="small" style="width: 150px" @change="loadRadarData">
                <el-option label="全部用户" value="" />
                <el-option
                  v-for="user in userOptions"
                  :key="user.id"
                  :label="user.fullName"
                  :value="user.id"
                />
              </el-select>
            </div>
          </template>
          <div ref="radarChartRef" class="chart-container"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 排行榜和最新评价 -->
    <el-row :gutter="20" class="data-section">
      <!-- 评分排行榜 -->
      <el-col :span="12">
        <el-card class="data-card" shadow="never">
          <template #header>
            <div class="chart-header">
              <span>评分排行榜</span>
              <el-select v-model="rankingType" size="small" style="width: 120px" @change="loadRankingData">
                <el-option label="用户排名" value="user" />
                <el-option label="任务排名" value="task" />
                <el-option label="部门排名" value="department" />
              </el-select>
            </div>
          </template>
          <div class="ranking-list">
            <div
              v-for="(item, index) in rankingData"
              :key="item.id"
              class="ranking-item"
            >
              <div class="ranking-number">
                <el-icon v-if="index === 0" class="gold"><Trophy /></el-icon>
                <el-icon v-else-if="index === 1" class="silver"><Medal /></el-icon>
                <el-icon v-else-if="index === 2" class="bronze"><Medal /></el-icon>
                <span v-else class="number">{{ index + 1 }}</span>
              </div>
              <div class="ranking-info">
                <div class="ranking-name">{{ item.name }}</div>
                <div class="ranking-desc">{{ item.description }}</div>
              </div>
              <div class="ranking-score">
                <div class="score-value">{{ item.score?.toFixed(1) }}</div>
                <div class="score-count">{{ item.count }}次评价</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 最新评价 -->
      <el-col :span="12">
        <el-card class="data-card" shadow="never">
          <template #header>
            <span>最新评价</span>
          </template>
          <div class="recent-evaluations">
            <div
              v-for="evaluation in recentEvaluations"
              :key="evaluation.id"
              class="evaluation-item"
              @click="handleViewEvaluation(evaluation)"
            >
              <div class="evaluation-header">
                <div class="task-title">{{ evaluation.task?.title }}</div>
                <div class="evaluation-time">{{ formatRelativeTime(evaluation.createdAt) }}</div>
              </div>
              <div class="evaluation-content">
                <div class="evaluatee-info">
                  <el-avatar :size="24" :src="evaluation.evaluatee?.avatar">
                    {{ evaluation.evaluatee?.fullName?.charAt(0) }}
                  </el-avatar>
                  <span class="evaluatee-name">{{ evaluation.evaluatee?.fullName }}</span>
                </div>
                <div class="evaluation-score">
                  <el-rate
                    :model-value="evaluation.overallScore / 20"
                    disabled
                    show-score
                    text-color="#ff9900"
                    score-template="{value}"
                  />
                </div>
              </div>
              <div class="evaluation-text">
                {{ evaluation.evaluationContent.substring(0, 80) }}
                {{ evaluation.evaluationContent.length > 80 ? '...' : '' }}
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 质量预警 -->
    <el-card v-if="warningData.length" class="warning-section" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><Warning /></el-icon>
          <span>质量预警</span>
        </div>
      </template>
      <div class="warning-list">
        <el-alert
          v-for="warning in warningData"
          :key="warning.id"
          :title="warning.title"
          :description="warning.description"
          type="warning"
          :closable="false"
          class="warning-item"
        >
          <template #default>
            <div class="warning-content">
              <div class="warning-info">
                <div class="warning-title">{{ warning.title }}</div>
                <div class="warning-desc">{{ warning.description }}</div>
              </div>
              <div class="warning-actions">
                <el-button size="small" @click="handleWarningAction(warning)">
                  查看详情
                </el-button>
              </div>
            </div>
          </template>
        </el-alert>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Document, Star, Trophy, Warning, Medal 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import {
  getQualityStatisticsOverview,
  getQualityTrend,
  getQualityDistribution,
  getQualityRanking,
  getQualityWarningStatistics,
  getRecentEvaluations,
  getTaskEvaluationStatistics,
  calculateUserDimensionScores
} from '@/api/evaluation'
import { searchUsers } from '@/api/user'

// 响应式数据
const overview = reactive({
  totalEvaluations: 0,
  averageScore: 0,
  excellentCount: 0,
  improvementNeeded: 0
})

const trendPeriod = ref('30')
const selectedUser = ref('')
const rankingType = ref('user')
const userOptions = ref<any[]>([])
const rankingData = ref<any[]>([])
const recentEvaluations = ref<any[]>([])
const warningData = ref<any[]>([])

// 图表引用
const trendChartRef = ref()
const distributionChartRef = ref()
const typeChartRef = ref()
const radarChartRef = ref()

// 图表实例
let trendChart: echarts.ECharts
let distributionChart: echarts.ECharts
let typeChart: echarts.ECharts
let radarChart: echarts.ECharts

// 方法
const loadOverviewData = async () => {
  try {
    const response = await getQualityStatisticsOverview()
    Object.assign(overview, response.data)
  } catch (error) {
    console.error('Failed to load overview data:', error)
  }
}

const loadTrendData = async () => {
  try {
    const response = await getQualityTrend('', 'user', 'daily', parseInt(trendPeriod.value))
    const data = response.data
    
    const dates = data.map((item: any) => item.periodStart.split(' ')[0])
    const scores = data.map((item: any) => item.avgOverallScore)
    
    const option = {
      title: {
        text: '评分趋势',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis',
        formatter: '{b}<br/>平均评分: {c}'
      },
      xAxis: {
        type: 'category',
        data: dates,
        axisLabel: { fontSize: 12 }
      },
      yAxis: {
        type: 'value',
        min: 0,
        max: 100,
        axisLabel: { fontSize: 12 }
      },
      series: [{
        data: scores,
        type: 'line',
        smooth: true,
        lineStyle: { color: '#409EFF' },
        itemStyle: { color: '#409EFF' },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 158, 255, 0.3)' },
            { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
          ])
        }
      }]
    }
    
    trendChart.setOption(option)
  } catch (error) {
    console.error('Failed to load trend data:', error)
  }
}

const loadDistributionData = async () => {
  try {
    const response = await getQualityDistribution('user', 'monthly', '', '')
    const data = response.data
    
    const option = {
      title: {
        text: '评分分布',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      series: [{
        name: '评分分布',
        type: 'pie',
        radius: '60%',
        data: [
          { value: data.excellent || 0, name: '优秀(90-100)', itemStyle: { color: '#67C23A' } },
          { value: data.good || 0, name: '良好(80-89)', itemStyle: { color: '#409EFF' } },
          { value: data.average || 0, name: '中等(70-79)', itemStyle: { color: '#E6A23C' } },
          { value: data.pass || 0, name: '及格(60-69)', itemStyle: { color: '#F56C6C' } },
          { value: data.fail || 0, name: '不及格(<60)', itemStyle: { color: '#909399' } }
        ],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }]
    }
    
    distributionChart.setOption(option)
  } catch (error) {
    console.error('Failed to load distribution data:', error)
  }
}

const loadTypeStatistics = async () => {
  try {
    const response = await getTaskEvaluationStatistics()
    const typeStats = response.data.typeStatistics || []
    
    const option = {
      title: {
        text: '评价类型统计',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {
        trigger: 'axis',
        axisPointer: { type: 'shadow' }
      },
      xAxis: {
        type: 'category',
        data: typeStats.map((item: any) => item.typeName),
        axisLabel: { fontSize: 12 }
      },
      yAxis: {
        type: 'value',
        axisLabel: { fontSize: 12 }
      },
      series: [{
        data: typeStats.map((item: any) => item.count),
        type: 'bar',
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: '#409EFF' },
            { offset: 1, color: '#67C23A' }
          ])
        }
      }]
    }
    
    typeChart.setOption(option)
  } catch (error) {
    console.error('Failed to load type statistics:', error)
  }
}

const loadRadarData = async () => {
  try {
    let radarData = [85, 82, 88, 75, 90] // 默认平均评分

    if (selectedUser.value) {
      // 如果选择了特定用户，获取该用户的维度评分
      try {
        const response = await calculateUserDimensionScores(selectedUser.value)
        const scores = response.data
        radarData = [
          scores.qualityScore || 0,
          scores.efficiencyScore || 0,
          scores.communicationScore || 0,
          scores.innovationScore || 0,
          scores.teamworkScore || 0
        ]
      } catch (error) {
        console.error('Failed to load user dimension scores:', error)
      }
    }

    const option = {
      title: {
        text: '维度评分分析',
        left: 'center',
        textStyle: { fontSize: 14 }
      },
      tooltip: {},
      radar: {
        indicator: [
          { name: '质量', max: 100 },
          { name: '效率', max: 100 },
          { name: '沟通', max: 100 },
          { name: '创新', max: 100 },
          { name: '团队协作', max: 100 }
        ]
      },
      series: [{
        name: '维度评分',
        type: 'radar',
        data: [{
          value: radarData,
          name: selectedUser.value ? '个人评分' : '平均评分',
          itemStyle: { color: '#409EFF' },
          areaStyle: { color: 'rgba(64, 158, 255, 0.3)' }
        }]
      }]
    }

    radarChart.setOption(option)
  } catch (error) {
    console.error('Failed to load radar data:', error)
  }
}

const loadRankingData = async () => {
  try {
    const response = await getQualityRanking(rankingType.value, 'monthly', '', '', 10)
    rankingData.value = response.data.map((item: any) => ({
      id: item.targetId,
      name: item.targetName || `${rankingType.value}_${item.targetId}`,
      description: `${rankingType.value === 'user' ? '用户' : rankingType.value === 'task' ? '任务' : '部门'}`,
      score: item.avgOverallScore,
      count: item.totalEvaluations
    }))
  } catch (error) {
    console.error('Failed to load ranking data:', error)
  }
}

const loadRecentEvaluations = async () => {
  try {
    const response = await getRecentEvaluations(10)
    recentEvaluations.value = response.data
  } catch (error) {
    console.error('Failed to load recent evaluations:', error)
  }
}

const loadWarningData = async () => {
  try {
    const response = await getQualityWarningStatistics('user', 70)
    warningData.value = response.data.map((item: any) => ({
      id: item.id,
      title: `${item.targetName || item.targetId} 评分偏低`,
      description: `平均评分: ${item.avgOverallScore?.toFixed(1)}，建议关注并提供改进支持`
    }))
  } catch (error) {
    console.error('Failed to load warning data:', error)
  }
}

const loadUserOptions = async () => {
  try {
    const response = await searchUsers({ keyword: '', page: 1, size: 50 })
    userOptions.value = response.data.records
  } catch (error) {
    console.error('Failed to load users:', error)
  }
}

const initCharts = () => {
  nextTick(() => {
    trendChart = echarts.init(trendChartRef.value)
    distributionChart = echarts.init(distributionChartRef.value)
    typeChart = echarts.init(typeChartRef.value)
    radarChart = echarts.init(radarChartRef.value)
    
    // 响应式调整
    window.addEventListener('resize', () => {
      trendChart?.resize()
      distributionChart?.resize()
      typeChart?.resize()
      radarChart?.resize()
    })
  })
}

const handleViewEvaluation = (evaluation: any) => {
  // 跳转到评价详情页面
  ElMessage.info('跳转到评价详情页面')
}

const handleWarningAction = (warning: any) => {
  // 处理预警操作
  ElMessage.info('查看预警详情')
}

const formatRelativeTime = (dateTime: string) => {
  const now = new Date()
  const time = new Date(dateTime)
  const diff = now.getTime() - time.getTime()
  
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) return `${minutes}分钟前`
  if (hours < 24) return `${hours}小时前`
  return `${days}天前`
}

// 生命周期
onMounted(async () => {
  initCharts()
  await Promise.all([
    loadOverviewData(),
    loadUserOptions(),
    loadTrendData(),
    loadDistributionData(),
    loadTypeStatistics(),
    loadRadarData(),
    loadRankingData(),
    loadRecentEvaluations(),
    loadWarningData()
  ])
})
</script>

<style scoped>
.statistics-dashboard {
  padding: 20px;
}

.overview-section,
.charts-section,
.data-section {
  margin-bottom: 20px;
}

.overview-card {
  height: 120px;
}

.overview-item {
  display: flex;
  align-items: center;
  height: 100%;
  gap: 16px;
}

.overview-icon {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.overview-icon.total {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.overview-icon.average {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.overview-icon.excellent {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.overview-icon.improvement {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.overview-content {
  flex: 1;
}

.overview-value {
  font-size: 28px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.overview-label {
  font-size: 14px;
  color: #909399;
}

.chart-card,
.data-card {
  height: 400px;
}

.chart-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chart-container {
  height: 320px;
}

.ranking-list {
  height: 320px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
  gap: 16px;
}

.ranking-number {
  width: 40px;
  text-align: center;
}

.ranking-number .gold {
  color: #ffd700;
  font-size: 20px;
}

.ranking-number .silver {
  color: #c0c0c0;
  font-size: 20px;
}

.ranking-number .bronze {
  color: #cd7f32;
  font-size: 20px;
}

.ranking-number .number {
  font-size: 16px;
  font-weight: 600;
  color: #606266;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.ranking-desc {
  font-size: 12px;
  color: #909399;
}

.ranking-score {
  text-align: right;
}

.score-value {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.score-count {
  font-size: 12px;
  color: #909399;
}

.recent-evaluations {
  height: 320px;
  overflow-y: auto;
}

.evaluation-item {
  padding: 12px;
  border-bottom: 1px solid #ebeef5;
  cursor: pointer;
  transition: background-color 0.3s;
}

.evaluation-item:hover {
  background-color: #f5f7fa;
}

.evaluation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.task-title {
  font-weight: 500;
  color: #303133;
}

.evaluation-time {
  font-size: 12px;
  color: #909399;
}

.evaluation-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.evaluatee-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.evaluatee-name {
  font-size: 14px;
  color: #606266;
}

.evaluation-text {
  font-size: 12px;
  color: #909399;
  line-height: 1.4;
}

.warning-section {
  margin-top: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.warning-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.warning-item {
  border-radius: 8px;
}

.warning-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.warning-info {
  flex: 1;
}

.warning-title {
  font-weight: 500;
  margin-bottom: 4px;
}

.warning-desc {
  font-size: 14px;
  color: #606266;
}

.warning-actions {
  margin-left: 16px;
}
</style>
