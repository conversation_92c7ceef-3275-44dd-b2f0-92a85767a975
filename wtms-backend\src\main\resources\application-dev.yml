# 开发环境配置
spring:
  datasource:
    url: **********************************************************************************************************************************************
    username: root
    password: ankaixin.docker.mysql

  redis:
    host: localhost
    port: 6379
    password:
    database: 0



# 日志配置
logging:
  level:
    root: info
    com.wtms: debug
    org.springframework.security: debug
    org.springframework.web: debug
    com.baomidou.mybatisplus: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n"

# MyBatis Plus配置
mybatis-plus:
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl

# 开发环境特殊配置
debug: false
