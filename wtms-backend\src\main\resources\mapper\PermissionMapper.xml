<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtms.mapper.PermissionMapper">

    <!-- 权限结果映射 -->
    <resultMap id="PermissionResultMap" type="com.wtms.entity.Permission">
        <id column="id" property="id"/>
        <result column="code" property="code"/>
        <result column="name" property="name"/>
        <result column="description" property="description"/>
        <result column="type" property="type"/>
        <result column="group_name" property="groupName"/>
        <result column="parent_id" property="parentId"/>
        <result column="path" property="path"/>
        <result column="level" property="level"/>
        <result column="sort_order" property="sortOrder"/>
        <result column="resource" property="resource"/>
        <result column="action" property="action"/>
        <result column="expression" property="expression"/>
        <result column="is_enabled" property="isEnabled"/>
        <result column="is_system" property="isSystem"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="deleted_at" property="deletedAt"/>
    </resultMap>

    <!-- 权限树结构映射 -->
    <resultMap id="PermissionTreeResultMap" type="com.wtms.entity.Permission" extends="PermissionResultMap">
        <collection property="children" ofType="com.wtms.entity.Permission" 
                   column="id" select="selectByParentId"/>
    </resultMap>

    <!-- 查询所有权限（树形结构） -->
    <select id="selectPermissionTree" resultMap="PermissionTreeResultMap">
        SELECT * FROM permissions 
        WHERE parent_id IS NULL AND deleted_at IS NULL 
        ORDER BY sort_order ASC, created_at ASC
    </select>

    <!-- 根据用户ID查询用户权限 -->
    <select id="selectPermissionsByUserId" resultMap="PermissionResultMap">
        SELECT DISTINCT p.* FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        INNER JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND p.is_enabled = TRUE
          AND p.deleted_at IS NULL
          AND rp.is_enabled = TRUE
          AND (rp.expires_at IS NULL OR rp.expires_at > NOW())
          AND ur.is_enabled = TRUE
          AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
        ORDER BY p.sort_order ASC, p.created_at ASC
    </select>

    <!-- 根据角色ID查询角色权限 -->
    <select id="selectPermissionsByRoleId" resultMap="PermissionResultMap">
        SELECT p.* FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
          AND p.is_enabled = TRUE
          AND p.deleted_at IS NULL
          AND rp.is_enabled = TRUE
          AND (rp.expires_at IS NULL OR rp.expires_at > NOW())
        ORDER BY p.sort_order ASC, p.created_at ASC
    </select>

    <!-- 根据用户ID查询用户权限编码 -->
    <select id="selectPermissionCodesByUserId" resultType="string">
        SELECT DISTINCT p.code FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        INNER JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND p.is_enabled = TRUE
          AND p.deleted_at IS NULL
          AND rp.is_enabled = TRUE
          AND (rp.expires_at IS NULL OR rp.expires_at > NOW())
          AND ur.is_enabled = TRUE
          AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    </select>

    <!-- 根据角色ID查询角色权限编码 -->
    <select id="selectPermissionCodesByRoleId" resultType="string">
        SELECT p.code FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        WHERE rp.role_id = #{roleId}
          AND p.is_enabled = TRUE
          AND p.deleted_at IS NULL
          AND rp.is_enabled = TRUE
          AND (rp.expires_at IS NULL OR rp.expires_at > NOW())
    </select>

    <!-- 根据用户ID查询菜单权限 -->
    <select id="selectMenuPermissionsByUserId" resultMap="PermissionTreeResultMap">
        SELECT DISTINCT p.* FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        INNER JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND p.type = 'menu'
          AND p.is_enabled = TRUE
          AND p.deleted_at IS NULL
          AND rp.is_enabled = TRUE
          AND (rp.expires_at IS NULL OR rp.expires_at > NOW())
          AND ur.is_enabled = TRUE
          AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
        ORDER BY p.sort_order ASC, p.created_at ASC
    </select>

    <!-- 根据用户ID查询按钮权限 -->
    <select id="selectButtonPermissionsByUserId" resultType="string">
        SELECT DISTINCT p.code FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        INNER JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND p.type = 'button'
          AND p.is_enabled = TRUE
          AND p.deleted_at IS NULL
          AND rp.is_enabled = TRUE
          AND (rp.expires_at IS NULL OR rp.expires_at > NOW())
          AND ur.is_enabled = TRUE
          AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    </select>

    <!-- 根据用户ID查询API权限 -->
    <select id="selectApiPermissionsByUserId" resultType="string">
        SELECT DISTINCT p.code FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        INNER JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND p.type = 'api'
          AND p.is_enabled = TRUE
          AND p.deleted_at IS NULL
          AND rp.is_enabled = TRUE
          AND (rp.expires_at IS NULL OR rp.expires_at > NOW())
          AND ur.is_enabled = TRUE
          AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    </select>

    <!-- 根据用户ID查询数据权限 -->
    <select id="selectDataPermissionsByUserId" resultType="string">
        SELECT DISTINCT p.code FROM permissions p
        INNER JOIN role_permissions rp ON p.id = rp.permission_id
        INNER JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = #{userId}
          AND p.type = 'data'
          AND p.is_enabled = TRUE
          AND p.deleted_at IS NULL
          AND rp.is_enabled = TRUE
          AND (rp.expires_at IS NULL OR rp.expires_at > NOW())
          AND ur.is_enabled = TRUE
          AND (ur.expires_at IS NULL OR ur.expires_at > NOW())
    </select>

    <!-- 批量插入权限 -->
    <insert id="batchInsert">
        INSERT INTO permissions (id, code, name, description, type, group_name, parent_id, path, level, 
                                sort_order, resource, action, expression, is_enabled, is_system, created_at, updated_at)
        VALUES
        <foreach collection="permissions" item="item" separator=",">
            (#{item.id}, #{item.code}, #{item.name}, #{item.description}, #{item.type}, #{item.groupName}, 
             #{item.parentId}, #{item.path}, #{item.level}, #{item.sortOrder}, #{item.resource}, #{item.action}, 
             #{item.expression}, #{item.isEnabled}, #{item.isSystem}, NOW(), NOW())
        </foreach>
    </insert>

    <!-- 批量更新权限状态 -->
    <update id="batchUpdateStatus">
        UPDATE permissions SET is_enabled = #{isEnabled}, updated_at = NOW()
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
