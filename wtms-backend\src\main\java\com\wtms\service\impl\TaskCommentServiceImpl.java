package com.wtms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.common.exception.BusinessException;
import com.wtms.common.result.ResultCode;
import com.wtms.dto.request.CreateCommentRequest;
import com.wtms.dto.request.UpdateCommentRequest;
import com.wtms.dto.response.CommentResponse;
import com.wtms.entity.Task;
import com.wtms.entity.TaskComment;
import com.wtms.entity.User;
import com.wtms.mapper.TaskCommentMapper;
import com.wtms.mapper.TaskMapper;
import com.wtms.mapper.UserMapper;
import com.wtms.service.TaskCommentService;
import com.wtms.service.UserRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务评论服务实现
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class TaskCommentServiceImpl implements TaskCommentService {

    @Autowired
    private TaskCommentMapper taskCommentMapper;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserRoleService userRoleService;

    @Override
    @CacheEvict(value = "taskComments", key = "#request.taskId")
    public TaskComment createComment(CreateCommentRequest request) {
        log.info("Creating comment for task: {}", request.getTaskId());

        // 验证任务是否存在
        Task task = taskMapper.selectById(request.getTaskId());
        if (task == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "任务不存在");
        }

        // 验证父评论是否存在
        TaskComment parentComment = null;
        if (StrUtil.isNotBlank(request.getParentId())) {
            parentComment = taskCommentMapper.selectById(request.getParentId());
            if (parentComment == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "父评论不存在");
            }
            if (!parentComment.getTaskId().equals(request.getTaskId())) {
                throw new BusinessException(ResultCode.INVALID_PARAMETER.getCode(), "父评论不属于当前任务");
            }
        }

        // 获取当前用户
        String currentUserId = getCurrentUserId();
        if (currentUserId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        TaskComment comment = new TaskComment();
        BeanUtils.copyProperties(request, comment);
        comment.setCommenterId(currentUserId);

        // 设置评论层级和路径
        if (parentComment != null) {
            comment.setLevel(parentComment.getLevel() + 1);
            comment.setPath(parentComment.getPath() + "/" + comment.getId());
        } else {
            comment.setLevel(1);
            comment.setPath("/" + comment.getId());
        }

        // 设置默认值
        comment.setStatus(TaskComment.Status.ACTIVE.getCode());
        comment.setLikeCount(0);
        comment.setReplyCount(0);
        comment.setCreatedAt(LocalDateTime.now());
        comment.setUpdatedAt(LocalDateTime.now());

        // 设置IP地址和用户代理（可以从请求中获取）
        // comment.setIpAddress(getClientIpAddress());
        // comment.setUserAgent(getUserAgent());

        taskCommentMapper.insert(comment);

        // 更新父评论的回复数量
        if (parentComment != null) {
            int replyCount = taskCommentMapper.countChildren(parentComment.getId());
            taskCommentMapper.updateReplyCount(parentComment.getId(), replyCount);
        }

        log.info("Comment created successfully: {}", comment.getId());
        return comment;
    }

    @Override
    @CacheEvict(value = "taskComments", allEntries = true)
    public TaskComment updateComment(String commentId, UpdateCommentRequest request) {
        log.info("Updating comment: {}", commentId);

        TaskComment comment = getCommentById(commentId);
        String currentUserId = getCurrentUserId();

        // 检查权限
        if (!canEditComment(commentId, currentUserId)) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        // 更新评论信息
        if (StrUtil.isNotBlank(request.getContent())) {
            comment.setContent(request.getContent());
        }
        if (request.getIsPinned() != null) {
            comment.setIsPinned(request.getIsPinned());
        }
        if (request.getIsPrivate() != null) {
            comment.setIsPrivate(request.getIsPrivate());
        }

        comment.setUpdatedAt(LocalDateTime.now());
        taskCommentMapper.updateById(comment);

        log.info("Comment updated successfully: {}", commentId);
        return comment;
    }

    @Override
    @CacheEvict(value = "taskComments", allEntries = true)
    public void deleteComment(String commentId) {
        log.info("Deleting comment: {}", commentId);

        TaskComment comment = getCommentById(commentId);
        String currentUserId = getCurrentUserId();

        // 检查权限
        if (!canDeleteComment(commentId, currentUserId)) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        // 检查是否有子评论
        int childrenCount = taskCommentMapper.countChildren(commentId);
        if (childrenCount > 0) {
            throw new BusinessException(ResultCode.OPERATION_NOT_ALLOWED.getCode(), "存在子评论，不允许删除");
        }

        // 软删除
        comment.setDeletedAt(LocalDateTime.now());
        taskCommentMapper.updateById(comment);

        // 更新父评论的回复数量
        if (StrUtil.isNotBlank(comment.getParentId())) {
            int replyCount = taskCommentMapper.countChildren(comment.getParentId());
            taskCommentMapper.updateReplyCount(comment.getParentId(), replyCount);
        }

        log.info("Comment deleted successfully: {}", commentId);
    }

    @Override
    @Cacheable(value = "taskComments", key = "#commentId")
    public TaskComment getCommentById(String commentId) {
        TaskComment comment = taskCommentMapper.selectById(commentId);
        if (comment == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "评论不存在");
        }
        return comment;
    }

    @Override
    @Cacheable(value = "taskComments", key = "'tree:' + #taskId")
    public List<CommentResponse> getTaskCommentTree(String taskId) {
        List<TaskComment> comments = taskCommentMapper.selectCommentsByTaskId(taskId);
        return CommentResponse.buildTree(comments);
    }

    @Override
    public IPage<CommentResponse> getTaskComments(String taskId, String commentType, String status, Integer page, Integer size) {
        Page<TaskComment> pageParam = new Page<>(page, size);
        IPage<TaskComment> commentPage = taskCommentMapper.selectCommentsByTaskIdWithPage(pageParam, taskId, commentType, status);
        
        // 转换为响应DTO
        IPage<CommentResponse> responsePage = new Page<>(page, size, commentPage.getTotal());
        List<CommentResponse> responseList = commentPage.getRecords().stream()
                .map(CommentResponse::from)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);
        
        return responsePage;
    }

    @Override
    public IPage<CommentResponse> getUserComments(String commenterId, String status, Integer page, Integer size) {
        Page<TaskComment> pageParam = new Page<>(page, size);
        IPage<TaskComment> commentPage = taskCommentMapper.selectCommentsByCommenterId(pageParam, commenterId, status);
        
        // 转换为响应DTO
        IPage<CommentResponse> responsePage = new Page<>(page, size, commentPage.getTotal());
        List<CommentResponse> responseList = commentPage.getRecords().stream()
                .map(CommentResponse::from)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);
        
        return responsePage;
    }

    @Override
    public List<CommentResponse> getChildComments(String parentId) {
        List<TaskComment> comments = taskCommentMapper.selectByParentId(parentId);
        return comments.stream()
                .map(CommentResponse::from)
                .collect(Collectors.toList());
    }

    @Override
    public List<CommentResponse> getRootComments(String taskId) {
        List<TaskComment> comments = taskCommentMapper.selectRootComments(taskId);
        return comments.stream()
                .map(CommentResponse::from)
                .collect(Collectors.toList());
    }

    @Override
    public List<CommentResponse> getPinnedComments(String taskId) {
        List<TaskComment> comments = taskCommentMapper.selectPinnedComments(taskId);
        return comments.stream()
                .map(CommentResponse::from)
                .collect(Collectors.toList());
    }

    @Override
    public List<CommentResponse> getLatestComments(String taskId, Integer limit) {
        List<TaskComment> comments = taskCommentMapper.selectLatestComments(taskId, limit);
        return comments.stream()
                .map(CommentResponse::from)
                .collect(Collectors.toList());
    }

    @Override
    public List<CommentResponse> getPopularComments(String taskId, Integer limit) {
        List<TaskComment> comments = taskCommentMapper.selectPopularComments(taskId, limit);
        return comments.stream()
                .map(CommentResponse::from)
                .collect(Collectors.toList());
    }

    @Override
    public IPage<CommentResponse> searchComments(String taskId, String keyword, String commenterId, 
                                                String commentType, String status, Integer page, Integer size) {
        Page<TaskComment> pageParam = new Page<>(page, size);
        IPage<TaskComment> commentPage = taskCommentMapper.searchComments(pageParam, taskId, keyword, commenterId, commentType, status);
        
        // 转换为响应DTO
        IPage<CommentResponse> responsePage = new Page<>(page, size, commentPage.getTotal());
        List<CommentResponse> responseList = commentPage.getRecords().stream()
                .map(CommentResponse::from)
                .collect(Collectors.toList());
        responsePage.setRecords(responseList);
        
        return responsePage;
    }

    @Override
    @CacheEvict(value = "taskComments", allEntries = true)
    public void likeComment(String commentId, String userId) {
        // TODO: 实现点赞逻辑，需要创建点赞表
        TaskComment comment = getCommentById(commentId);
        int newLikeCount = comment.getLikeCount() + 1;
        taskCommentMapper.updateLikeCount(commentId, newLikeCount);
    }

    @Override
    @CacheEvict(value = "taskComments", allEntries = true)
    public void unlikeComment(String commentId, String userId) {
        // TODO: 实现取消点赞逻辑
        TaskComment comment = getCommentById(commentId);
        int newLikeCount = Math.max(0, comment.getLikeCount() - 1);
        taskCommentMapper.updateLikeCount(commentId, newLikeCount);
    }

    @Override
    @CacheEvict(value = "taskComments", allEntries = true)
    public void pinComment(String commentId) {
        taskCommentMapper.pinComment(commentId);
    }

    @Override
    @CacheEvict(value = "taskComments", allEntries = true)
    public void unpinComment(String commentId) {
        taskCommentMapper.unpinComment(commentId);
    }

    @Override
    @CacheEvict(value = "taskComments", allEntries = true)
    public void hideComment(String commentId) {
        taskCommentMapper.hideComment(commentId);
    }

    @Override
    @CacheEvict(value = "taskComments", allEntries = true)
    public void showComment(String commentId) {
        taskCommentMapper.showComment(commentId);
    }

    @Override
    @CacheEvict(value = "taskComments", allEntries = true)
    public void batchDeleteComments(List<String> commentIds) {
        taskCommentMapper.batchDeleteComments(commentIds);
    }

    @Override
    @CacheEvict(value = "taskComments", allEntries = true)
    public void batchUpdateStatus(List<String> commentIds, String status) {
        taskCommentMapper.batchUpdateStatus(commentIds, status);
    }

    @Override
    public int countTaskComments(String taskId) {
        return taskCommentMapper.countByTaskId(taskId);
    }

    @Override
    public int countUserComments(String commenterId) {
        return taskCommentMapper.countByCommenterId(commenterId);
    }

    @Override
    public boolean canEditComment(String commentId, String userId) {
        TaskComment comment = getCommentById(commentId);
        
        // 评论者可以编辑自己的评论
        if (comment.getCommenterId().equals(userId)) {
            return true;
        }
        
        // 管理员可以编辑所有评论
        return userRoleService.isAdmin(userId);
    }

    @Override
    public boolean canDeleteComment(String commentId, String userId) {
        TaskComment comment = getCommentById(commentId);
        
        // 评论者可以删除自己的评论
        if (comment.getCommenterId().equals(userId)) {
            return true;
        }
        
        // 管理员可以删除所有评论
        return userRoleService.isAdmin(userId);
    }

    @Override
    public boolean isCommentLiked(String commentId, String userId) {
        // TODO: 实现点赞状态检查逻辑
        return false;
    }

    @Override
    public Object getCommentStatistics(String taskId) {
        return taskCommentMapper.selectCommentStatistics(taskId);
    }

    @Override
    public List<Object> getCommentActivityStats(String taskId, Integer days) {
        return taskCommentMapper.selectCommentActivityStats(taskId, days);
    }

    @Override
    public TaskComment createSystemComment(String taskId, String content, String commentType) {
        CreateCommentRequest request = new CreateCommentRequest();
        request.setTaskId(taskId);
        request.setContent(content);
        request.setCommentType(commentType);
        request.setIsPrivate(false);
        request.setIsPinned(false);
        
        return createComment(request);
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails) {
            String username = ((UserDetails) principal).getUsername();
            User user = userMapper.findByUsernameWithRole(username);
            return user != null ? user.getId() : null;
        }
        return null;
    }
}
