package com.wtms.controller;

import com.wtms.common.result.Result;
import com.wtms.dto.request.CreatePermissionRequest;
import com.wtms.dto.request.UpdatePermissionRequest;
import com.wtms.dto.response.PermissionTreeResponse;
import com.wtms.dto.response.UserPermissionResponse;
import com.wtms.entity.Permission;
import com.wtms.service.PermissionService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;
import java.util.Set;

/**
 * 权限管理控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/permissions")
@Tag(name = "权限管理", description = "权限管理相关接口")
public class PermissionController {

    @Autowired
    private PermissionService permissionService;

    @PostMapping
    @Operation(summary = "创建权限", description = "创建新的权限")
    @PreAuthorize("hasAuthority('permission:create') or hasRole('ADMIN')")
    public Result<Permission> createPermission(@Valid @RequestBody CreatePermissionRequest request) {
        log.info("Creating permission: {}", request.getCode());
        
        Permission permission = permissionService.createPermission(request);
        return Result.success("权限创建成功", permission);
    }

    @PutMapping("/{permissionId}")
    @Operation(summary = "更新权限", description = "更新指定权限信息")
    @PreAuthorize("hasAuthority('permission:update') or hasRole('ADMIN')")
    public Result<Permission> updatePermission(
            @Parameter(description = "权限ID", required = true)
            @PathVariable @NotBlank(message = "权限ID不能为空") String permissionId,
            @Valid @RequestBody UpdatePermissionRequest request) {
        
        log.info("Updating permission: {}", permissionId);
        
        Permission permission = permissionService.updatePermission(permissionId, request);
        return Result.success("权限更新成功", permission);
    }

    @DeleteMapping("/{permissionId}")
    @Operation(summary = "删除权限", description = "删除指定权限")
    @PreAuthorize("hasAuthority('permission:delete') or hasRole('ADMIN')")
    public Result<String> deletePermission(
            @Parameter(description = "权限ID", required = true)
            @PathVariable @NotBlank(message = "权限ID不能为空") String permissionId) {
        
        log.info("Deleting permission: {}", permissionId);
        
        permissionService.deletePermission(permissionId);
        return Result.success("权限删除成功");
    }

    @GetMapping("/{permissionId}")
    @Operation(summary = "获取权限详情", description = "根据ID获取权限详细信息")
    @PreAuthorize("hasAuthority('permission:view') or hasRole('ADMIN')")
    public Result<Permission> getPermission(
            @Parameter(description = "权限ID", required = true)
            @PathVariable @NotBlank(message = "权限ID不能为空") String permissionId) {
        
        Permission permission = permissionService.getPermissionById(permissionId);
        return Result.success(permission);
    }

    @GetMapping("/code/{code}")
    @Operation(summary = "根据编码获取权限", description = "根据权限编码获取权限信息")
    @PreAuthorize("hasAuthority('permission:view') or hasRole('ADMIN')")
    public Result<Permission> getPermissionByCode(
            @Parameter(description = "权限编码", required = true)
            @PathVariable @NotBlank(message = "权限编码不能为空") String code) {
        
        Permission permission = permissionService.getPermissionByCode(code);
        return Result.success(permission);
    }

    @GetMapping("/tree")
    @Operation(summary = "获取权限树", description = "获取完整的权限树结构")
    @PreAuthorize("hasAuthority('permission:view') or hasRole('ADMIN')")
    public Result<List<PermissionTreeResponse>> getPermissionTree() {
        List<PermissionTreeResponse> tree = permissionService.getPermissionTree();
        return Result.success(tree);
    }

    @GetMapping("/type/{type}")
    @Operation(summary = "根据类型获取权限", description = "根据权限类型获取权限列表")
    @PreAuthorize("hasAuthority('permission:view') or hasRole('ADMIN')")
    public Result<List<Permission>> getPermissionsByType(
            @Parameter(description = "权限类型", required = true)
            @PathVariable @NotBlank(message = "权限类型不能为空") String type) {
        
        List<Permission> permissions = permissionService.getPermissionsByType(type);
        return Result.success(permissions);
    }

    @GetMapping("/group/{groupName}")
    @Operation(summary = "根据分组获取权限", description = "根据权限分组获取权限列表")
    @PreAuthorize("hasAuthority('permission:view') or hasRole('ADMIN')")
    public Result<List<Permission>> getPermissionsByGroup(
            @Parameter(description = "权限分组", required = true)
            @PathVariable @NotBlank(message = "权限分组不能为空") String groupName) {
        
        List<Permission> permissions = permissionService.getPermissionsByGroup(groupName);
        return Result.success(permissions);
    }

    @GetMapping("/enabled")
    @Operation(summary = "获取启用的权限", description = "获取所有启用状态的权限")
    @PreAuthorize("hasAuthority('permission:view') or hasRole('ADMIN')")
    public Result<List<Permission>> getEnabledPermissions() {
        List<Permission> permissions = permissionService.getEnabledPermissions();
        return Result.success(permissions);
    }

    @GetMapping("/system")
    @Operation(summary = "获取系统权限", description = "获取所有系统权限")
    @PreAuthorize("hasAuthority('permission:view') or hasRole('ADMIN')")
    public Result<List<Permission>> getSystemPermissions() {
        List<Permission> permissions = permissionService.getSystemPermissions();
        return Result.success(permissions);
    }

    @PutMapping("/{permissionId}/enable")
    @Operation(summary = "启用权限", description = "启用指定权限")
    @PreAuthorize("hasAuthority('permission:update') or hasRole('ADMIN')")
    public Result<String> enablePermission(
            @Parameter(description = "权限ID", required = true)
            @PathVariable @NotBlank(message = "权限ID不能为空") String permissionId) {
        
        log.info("Enabling permission: {}", permissionId);
        
        permissionService.enablePermission(permissionId);
        return Result.success("权限启用成功");
    }

    @PutMapping("/{permissionId}/disable")
    @Operation(summary = "禁用权限", description = "禁用指定权限")
    @PreAuthorize("hasAuthority('permission:update') or hasRole('ADMIN')")
    public Result<String> disablePermission(
            @Parameter(description = "权限ID", required = true)
            @PathVariable @NotBlank(message = "权限ID不能为空") String permissionId) {
        
        log.info("Disabling permission: {}", permissionId);
        
        permissionService.disablePermission(permissionId);
        return Result.success("权限禁用成功");
    }

    @PutMapping("/batch/enable")
    @Operation(summary = "批量启用权限", description = "批量启用多个权限")
    @PreAuthorize("hasAuthority('permission:update') or hasRole('ADMIN')")
    public Result<String> batchEnablePermissions(
            @Parameter(description = "权限ID列表", required = true)
            @RequestBody @NotEmpty(message = "权限ID列表不能为空") List<String> permissionIds) {
        
        log.info("Batch enabling permissions: {}", permissionIds);
        
        permissionService.batchEnablePermissions(permissionIds);
        return Result.success("权限批量启用成功");
    }

    @PutMapping("/batch/disable")
    @Operation(summary = "批量禁用权限", description = "批量禁用多个权限")
    @PreAuthorize("hasAuthority('permission:update') or hasRole('ADMIN')")
    public Result<String> batchDisablePermissions(
            @Parameter(description = "权限ID列表", required = true)
            @RequestBody @NotEmpty(message = "权限ID列表不能为空") List<String> permissionIds) {
        
        log.info("Batch disabling permissions: {}", permissionIds);
        
        permissionService.batchDisablePermissions(permissionIds);
        return Result.success("权限批量禁用成功");
    }

    @GetMapping("/users/{userId}")
    @Operation(summary = "获取用户权限", description = "获取指定用户的完整权限信息")
    @PreAuthorize("hasAuthority('permission:view') or hasRole('ADMIN') or #userId == authentication.principal.id")
    public Result<UserPermissionResponse> getUserPermissions(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId) {
        
        UserPermissionResponse permissions = permissionService.getUserPermissions(userId);
        return Result.success(permissions);
    }

    @GetMapping("/users/{userId}/codes")
    @Operation(summary = "获取用户权限编码", description = "获取指定用户的权限编码列表")
    @PreAuthorize("hasAuthority('permission:view') or hasRole('ADMIN') or #userId == authentication.principal.id")
    public Result<Set<String>> getUserPermissionCodes(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId) {
        
        Set<String> permissionCodes = permissionService.getUserPermissionCodes(userId);
        return Result.success(permissionCodes);
    }

    @GetMapping("/roles/{roleId}")
    @Operation(summary = "获取角色权限", description = "获取指定角色的权限列表")
    @PreAuthorize("hasAuthority('permission:view') or hasRole('ADMIN')")
    public Result<List<Permission>> getRolePermissions(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotBlank(message = "角色ID不能为空") String roleId) {
        
        List<Permission> permissions = permissionService.getRolePermissions(roleId);
        return Result.success(permissions);
    }

    @GetMapping("/roles/{roleId}/codes")
    @Operation(summary = "获取角色权限编码", description = "获取指定角色的权限编码列表")
    @PreAuthorize("hasAuthority('permission:view') or hasRole('ADMIN')")
    public Result<Set<String>> getRolePermissionCodes(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotBlank(message = "角色ID不能为空") String roleId) {
        
        Set<String> permissionCodes = permissionService.getRolePermissionCodes(roleId);
        return Result.success(permissionCodes);
    }

    @PostMapping("/init")
    @Operation(summary = "初始化系统权限", description = "初始化系统默认权限")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<String> initSystemPermissions() {
        log.info("Initializing system permissions");
        
        permissionService.initSystemPermissions();
        return Result.success("系统权限初始化成功");
    }

    @PostMapping("/refresh-cache")
    @Operation(summary = "刷新权限缓存", description = "刷新权限相关缓存")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<String> refreshPermissionCache() {
        log.info("Refreshing permission cache");
        
        permissionService.refreshPermissionCache();
        return Result.success("权限缓存刷新成功");
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取权限统计", description = "获取权限统计信息")
    @PreAuthorize("hasAuthority('permission:statistics') or hasRole('ADMIN')")
    public Result<Object> getPermissionStatistics() {
        Object statistics = permissionService.getPermissionStatistics();
        return Result.success(statistics);
    }
}
