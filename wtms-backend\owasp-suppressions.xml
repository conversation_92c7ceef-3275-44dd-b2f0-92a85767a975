<?xml version="1.0" encoding="UTF-8"?>
<suppressions xmlns="https://jeremylong.github.io/DependencyCheck/dependency-suppression.1.3.xsd">
    
    <!-- 
        OWASP Dependency Check 抑制配置文件
        用于抑制已知的误报或已接受的风险
        
        配置说明：
        - filePath: 文件路径匹配模式
        - sha1: 文件SHA1哈希值
        - cve: 要抑制的CVE编号
        - cwe: 要抑制的CWE编号
        - cvssBelow: 抑制CVSS分数低于指定值的漏洞
        - until: 抑制的截止日期
    -->
    
    <!-- Spring Boot 相关抑制 -->
    <suppress>
        <notes>
            Spring Boot 2.7.18 - 已知的低风险漏洞，计划在下次版本升级时解决
        </notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/.*@2\.7\.18$</packageUrl>
        <cvssBelow>5.0</cvssBelow>
        <until>2024-12-31</until>
    </suppress>
    
    <!-- MyBatis Plus 相关抑制 -->
    <suppress>
        <notes>
            MyBatis Plus 3.5.4 - 误报，实际使用中不存在安全风险
        </notes>
        <packageUrl regex="true">^pkg:maven/com\.baomidou/mybatis-plus.*@3\.5\.4$</packageUrl>
        <cve>CVE-2022-25647</cve>
    </suppress>
    
    <!-- Jackson 相关抑制 -->
    <suppress>
        <notes>
            Jackson 反序列化漏洞 - 项目中未使用不安全的反序列化功能
        </notes>
        <packageUrl regex="true">^pkg:maven/com\.fasterxml\.jackson\.core/.*$</packageUrl>
        <cve>CVE-2022-42003</cve>
        <cve>CVE-2022-42004</cve>
    </suppress>
    
    <!-- Tomcat 相关抑制 -->
    <suppress>
        <notes>
            Tomcat 内嵌版本 - Spring Boot管理的版本，低风险
        </notes>
        <packageUrl regex="true">^pkg:maven/org\.apache\.tomcat\.embed/.*$</packageUrl>
        <cvssBelow>4.0</cvssBelow>
    </suppress>
    
    <!-- Logback 相关抑制 -->
    <suppress>
        <notes>
            Logback JNDI 漏洞 - 项目中未启用JNDI功能
        </notes>
        <packageUrl regex="true">^pkg:maven/ch\.qos\.logback/.*$</packageUrl>
        <cve>CVE-2021-42550</cve>
    </suppress>
    
    <!-- Druid 相关抑制 -->
    <suppress>
        <notes>
            Druid 连接池 - 已配置安全参数，风险可控
        </notes>
        <packageUrl regex="true">^pkg:maven/com\.alibaba/druid.*$</packageUrl>
        <cvssBelow>6.0</cvssBelow>
    </suppress>
    
    <!-- JWT 相关抑制 -->
    <suppress>
        <notes>
            JWT 库 - 项目中正确使用，无安全风险
        </notes>
        <packageUrl regex="true">^pkg:maven/io\.jsonwebtoken/.*$</packageUrl>
        <cvssBelow>5.0</cvssBelow>
    </suppress>
    
    <!-- 测试依赖抑制 -->
    <suppress>
        <notes>
            测试依赖 - 仅在测试环境使用，不影响生产安全
        </notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring-boot-starter-test.*$</packageUrl>
        <cvssBelow>7.0</cvssBelow>
    </suppress>
    
    <suppress>
        <notes>
            JUnit 测试框架 - 仅测试时使用
        </notes>
        <packageUrl regex="true">^pkg:maven/org\.junit\..*$</packageUrl>
        <cvssBelow>8.0</cvssBelow>
    </suppress>
    
    <!-- 开发工具抑制 -->
    <suppress>
        <notes>
            Spring Boot DevTools - 仅开发环境使用
        </notes>
        <packageUrl regex="true">^pkg:maven/org\.springframework\.boot/spring-boot-devtools.*$</packageUrl>
        <cvssBelow>9.0</cvssBelow>
    </suppress>
    
    <!-- 文档生成工具抑制 -->
    <suppress>
        <notes>
            SpringDoc OpenAPI - 文档生成工具，生产环境可禁用
        </notes>
        <packageUrl regex="true">^pkg:maven/org\.springdoc/.*$</packageUrl>
        <cvssBelow>6.0</cvssBelow>
    </suppress>
    
    <!-- 工具类库抑制 -->
    <suppress>
        <notes>
            Hutool 工具库 - 已知漏洞已在使用中避免
        </notes>
        <packageUrl regex="true">^pkg:maven/cn\.hutool/.*$</packageUrl>
        <cvssBelow>5.0</cvssBelow>
        <until>2024-12-31</until>
    </suppress>
    
    <!-- 特定CVE抑制示例 -->
    <suppress>
        <notes>
            CVE-2023-20861 - Spring Framework 漏洞，项目中未使用受影响功能
        </notes>
        <cve>CVE-2023-20861</cve>
        <until>2024-12-31</until>
    </suppress>
    
    <suppress>
        <notes>
            CVE-2023-20863 - Spring Framework 漏洞，项目中未使用受影响功能
        </notes>
        <cve>CVE-2023-20863</cve>
        <until>2024-12-31</until>
    </suppress>
    
    <!-- 基于文件路径的抑制示例 -->
    <suppress>
        <notes>
            第三方JAR文件 - 已评估风险可接受
        </notes>
        <filePath regex="true">.*[\\/]third-party-lib-.*\.jar</filePath>
        <cvssBelow>6.0</cvssBelow>
    </suppress>
    
    <!-- 基于SHA1的抑制示例 -->
    <suppress>
        <notes>
            特定版本的库文件 - 已知安全
        </notes>
        <sha1>da39a3ee5e6b4b0d3255bfef95601890afd80709</sha1>
        <cvssBelow>10.0</cvssBelow>
    </suppress>
    
    <!-- 临时抑制 - 有截止日期 -->
    <suppress>
        <notes>
            临时抑制 - 等待供应商修复，2024年底前重新评估
        </notes>
        <packageUrl regex="true">^pkg:maven/com\.example/.*$</packageUrl>
        <cvssBelow>7.0</cvssBelow>
        <until>2024-12-31</until>
    </suppress>
    
</suppressions>
