package com.wtms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wtms.entity.WorkflowInstance;
import com.wtms.entity.WorkflowTask;
import com.wtms.workflow.model.WorkflowExecutionResult;

import java.util.List;
import java.util.Map;

/**
 * 工作流实例服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface WorkflowInstanceService {

    /**
     * 启动工作流实例
     *
     * @param definitionId 工作流定义ID
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @param variables 初始变量
     * @param starterId 启动者ID
     * @return 工作流实例
     */
    WorkflowInstance startInstance(String definitionId, String businessId, String businessType, 
                                  Map<String, Object> variables, String starterId);

    /**
     * 根据ID获取工作流实例
     *
     * @param instanceId 实例ID
     * @return 工作流实例
     */
    WorkflowInstance getInstanceById(String instanceId);

    /**
     * 根据工作流定义ID获取实例
     *
     * @param definitionId 定义ID
     * @return 工作流实例列表
     */
    List<WorkflowInstance> getInstancesByDefinitionId(String definitionId);

    /**
     * 根据业务ID获取实例
     *
     * @param businessId 业务ID
     * @return 工作流实例列表
     */
    List<WorkflowInstance> getInstancesByBusinessId(String businessId);

    /**
     * 根据业务ID和业务类型获取实例
     *
     * @param businessId 业务ID
     * @param businessType 业务类型
     * @return 工作流实例列表
     */
    List<WorkflowInstance> getInstancesByBusinessIdAndType(String businessId, String businessType);

    /**
     * 根据状态获取实例
     *
     * @param status 状态
     * @return 工作流实例列表
     */
    List<WorkflowInstance> getInstancesByStatus(String status);

    /**
     * 根据启动者获取实例
     *
     * @param starterId 启动者ID
     * @param status 状态
     * @param page 页码
     * @param size 每页大小
     * @return 工作流实例分页结果
     */
    IPage<WorkflowInstance> getInstancesByStarter(String starterId, String status, Integer page, Integer size);

    /**
     * 获取活跃实例
     *
     * @return 活跃实例列表
     */
    List<WorkflowInstance> getActiveInstances();

    /**
     * 获取已完成实例
     *
     * @return 已完成实例列表
     */
    List<WorkflowInstance> getCompletedInstances();

    /**
     * 获取超期实例
     *
     * @return 超期实例列表
     */
    List<WorkflowInstance> getOverdueInstances();

    /**
     * 根据父实例ID获取子实例
     *
     * @param parentInstanceId 父实例ID
     * @return 子实例列表
     */
    List<WorkflowInstance> getChildInstances(String parentInstanceId);

    /**
     * 根据根实例ID获取所有相关实例
     *
     * @param rootInstanceId 根实例ID
     * @return 相关实例列表
     */
    List<WorkflowInstance> getRelatedInstances(String rootInstanceId);

    /**
     * 搜索工作流实例
     *
     * @param keyword 关键词
     * @param definitionId 定义ID
     * @param status 状态
     * @param businessType 业务类型
     * @param starterId 启动者ID
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    IPage<WorkflowInstance> searchInstances(String keyword, String definitionId, String status, 
                                           String businessType, String starterId, Integer page, Integer size);

    /**
     * 暂停工作流实例
     *
     * @param instanceId 实例ID
     * @param userId 用户ID
     * @return 执行结果
     */
    WorkflowExecutionResult suspendInstance(String instanceId, String userId);

    /**
     * 恢复工作流实例
     *
     * @param instanceId 实例ID
     * @param userId 用户ID
     * @return 执行结果
     */
    WorkflowExecutionResult resumeInstance(String instanceId, String userId);

    /**
     * 终止工作流实例
     *
     * @param instanceId 实例ID
     * @param reason 终止原因
     * @param userId 用户ID
     * @return 执行结果
     */
    WorkflowExecutionResult terminateInstance(String instanceId, String reason, String userId);

    /**
     * 取消工作流实例
     *
     * @param instanceId 实例ID
     * @param reason 取消原因
     * @param userId 用户ID
     * @return 执行结果
     */
    WorkflowExecutionResult cancelInstance(String instanceId, String reason, String userId);

    /**
     * 跳转到指定节点
     *
     * @param instanceId 实例ID
     * @param targetNodeId 目标节点ID
     * @param variables 变量
     * @param userId 用户ID
     * @return 执行结果
     */
    WorkflowExecutionResult jumpToNode(String instanceId, String targetNodeId, 
                                      Map<String, Object> variables, String userId);

    /**
     * 回退到上一个节点
     *
     * @param instanceId 实例ID
     * @param variables 变量
     * @param userId 用户ID
     * @return 执行结果
     */
    WorkflowExecutionResult rollbackToPreviousNode(String instanceId, Map<String, Object> variables, String userId);

    /**
     * 更新实例变量
     *
     * @param instanceId 实例ID
     * @param variables 变量
     * @return 是否成功
     */
    boolean updateInstanceVariables(String instanceId, Map<String, Object> variables);

    /**
     * 获取实例变量
     *
     * @param instanceId 实例ID
     * @return 变量Map
     */
    Map<String, Object> getInstanceVariables(String instanceId);

    /**
     * 获取实例的当前任务
     *
     * @param instanceId 实例ID
     * @return 当前任务列表
     */
    List<WorkflowTask> getInstanceCurrentTasks(String instanceId);

    /**
     * 获取实例的历史任务
     *
     * @param instanceId 实例ID
     * @return 历史任务列表
     */
    List<WorkflowTask> getInstanceHistoryTasks(String instanceId);

    /**
     * 获取实例的执行路径
     *
     * @param instanceId 实例ID
     * @return 执行路径
     */
    List<String> getInstanceExecutionPath(String instanceId);

    /**
     * 获取实例的下一个可能节点
     *
     * @param instanceId 实例ID
     * @return 下一个可能节点列表
     */
    List<String> getInstanceNextPossibleNodes(String instanceId);

    /**
     * 检查实例是否可以暂停
     *
     * @param instanceId 实例ID
     * @return 是否可以暂停
     */
    boolean canSuspendInstance(String instanceId);

    /**
     * 检查实例是否可以终止
     *
     * @param instanceId 实例ID
     * @return 是否可以终止
     */
    boolean canTerminateInstance(String instanceId);

    /**
     * 重启失败的工作流实例
     *
     * @param instanceId 实例ID
     * @param userId 用户ID
     * @return 执行结果
     */
    WorkflowExecutionResult restartFailedInstance(String instanceId, String userId);

    /**
     * 批量删除实例
     *
     * @param instanceIds 实例ID列表
     */
    void batchDeleteInstances(List<String> instanceIds);

    /**
     * 批量更新实例状态
     *
     * @param instanceIds 实例ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<String> instanceIds, String status);

    /**
     * 统计实例数量
     *
     * @return 总数量
     */
    int countAllInstances();

    /**
     * 根据状态统计实例数量
     *
     * @param status 状态
     * @return 数量
     */
    int countInstancesByStatus(String status);

    /**
     * 根据工作流定义统计实例数量
     *
     * @param definitionId 定义ID
     * @return 数量
     */
    int countInstancesByDefinition(String definitionId);

    /**
     * 根据启动者统计实例数量
     *
     * @param starterId 启动者ID
     * @return 数量
     */
    int countInstancesByStarter(String starterId);

    /**
     * 根据业务类型统计实例数量
     *
     * @param businessType 业务类型
     * @return 数量
     */
    int countInstancesByBusinessType(String businessType);

    /**
     * 获取最近启动的实例
     *
     * @param limit 数量限制
     * @return 最近启动的实例列表
     */
    List<WorkflowInstance> getRecentStartedInstances(Integer limit);

    /**
     * 获取最近完成的实例
     *
     * @param limit 数量限制
     * @return 最近完成的实例列表
     */
    List<WorkflowInstance> getRecentCompletedInstances(Integer limit);

    /**
     * 获取长时间运行的实例
     *
     * @param hours 小时数
     * @return 长时间运行的实例列表
     */
    List<WorkflowInstance> getLongRunningInstances(Integer hours);

    /**
     * 清理已完成的实例
     *
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanupCompletedInstances(Integer days);

    /**
     * 获取实例统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getInstanceStatistics();

    /**
     * 获取状态统计
     *
     * @return 状态统计
     */
    List<Map<String, Object>> getStatusStatistics();

    /**
     * 获取业务类型统计
     *
     * @return 业务类型统计
     */
    List<Map<String, Object>> getBusinessTypeStatistics();

    /**
     * 获取启动者统计
     *
     * @return 启动者统计
     */
    List<Map<String, Object>> getStarterStatistics();

    /**
     * 获取工作流定义统计
     *
     * @return 工作流定义统计
     */
    List<Map<String, Object>> getDefinitionStatistics();

    /**
     * 获取执行时长统计
     *
     * @param days 天数
     * @return 执行时长统计
     */
    List<Map<String, Object>> getDurationStatistics(Integer days);

    /**
     * 获取每日启动统计
     *
     * @param days 天数
     * @return 每日启动统计
     */
    List<Map<String, Object>> getDailyStartStatistics(Integer days);

    /**
     * 获取每日完成统计
     *
     * @param days 天数
     * @return 每日完成统计
     */
    List<Map<String, Object>> getDailyCompletionStatistics(Integer days);

    /**
     * 获取超期实例统计
     *
     * @return 超期实例统计
     */
    Map<String, Object> getOverdueStatistics();

    /**
     * 获取性能统计
     *
     * @param definitionId 定义ID
     * @param days 天数
     * @return 性能统计
     */
    Map<String, Object> getPerformanceStatistics(String definitionId, Integer days);
}
