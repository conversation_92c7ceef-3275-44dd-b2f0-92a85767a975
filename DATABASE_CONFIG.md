# WTMS 数据库配置说明

## 🔧 数据库连接信息

### 标准连接参数
- **Host**: `localhost`
- **Port**: `3308`
- **Username**: `root`
- **Password**: `ankaixin.docker.mysql`
- **Database**: `wtms_db`

### 连接字符串
```
*************************************************************************************************************************************************
```

## 📋 已更新的配置文件

### 后端配置
- ✅ `wtms-backend/src/main/resources/application.yml`
  - 数据库URL端口: 3308
  - 默认密码: ankaixin.docker.mysql

### Docker配置
- ✅ `docker-compose.yml` (生产环境)
  - MySQL端口映射: 3308:3306
  - Root密码: ankaixin.docker.mysql

- ✅ `docker-compose.dev.yml` (开发环境)
  - MySQL端口映射: 3308:3306
  - Root密码: ankaixin.docker.mysql
  - phpMyAdmin密码: ankaixin.docker.mysql

### 启动脚本
- ✅ `scripts/start-dev.sh`
  - 端口检查: 3308
  - 连接信息显示: 更新为新的端口和密码

### 文档
- ✅ `README.md` - 更新了数据库连接信息
- ✅ `PORT_CONFIG.md` - 更新了端口配置说明

## 🚀 数据库服务启动

### 使用Docker启动MySQL
```bash
# 启动开发环境数据库
docker-compose -f docker-compose.dev.yml up -d mysql-dev

# 或启动所有基础服务
./scripts/start-dev.sh
```

### 验证数据库连接
```bash
# 使用MySQL客户端连接
mysql -h localhost -P 3308 -u root -p
# 输入密码: ankaixin.docker.mysql

# 或使用Docker exec连接
docker exec -it wtms-mysql-dev mysql -u root -p
```

### 数据库管理工具
- **phpMyAdmin**: http://localhost:8081
  - 服务器: mysql-dev
  - 用户名: root
  - 密码: ankaixin.docker.mysql

## 📊 数据库结构

### 核心表结构
- `users` - 用户表
- `roles` - 角色表
- `departments` - 部门表
- `tasks` - 任务表
- `task_categories` - 任务分类表
- `task_assignments` - 任务分配表
- `task_comments` - 任务评论表
- `task_attachments` - 任务附件表

### 初始化数据
数据库初始化脚本: `MySQL数据库设计.sql`

包含：
- 完整的表结构定义
- 索引和约束
- 初始化数据（默认用户、角色等）

## 🔍 连接测试

### 应用连接测试
启动后端服务后，检查日志中的数据库连接状态：
```
INFO  - HikariPool-1 - Start completed.
INFO  - Started WtmsApplication in X.XXX seconds
```

### 手动连接测试
```sql
-- 连接后执行测试查询
USE wtms_db;
SHOW TABLES;
SELECT COUNT(*) FROM users;
```

## ⚠️ 重要注意事项

### 密码安全
- 生产环境请使用更强的密码
- 考虑使用环境变量管理敏感信息
- 定期更换数据库密码

### 端口配置
- 外部端口: 3308 (避免与系统MySQL冲突)
- 容器内部端口: 3306 (MySQL默认端口)
- 确保防火墙允许3308端口访问

### 数据持久化
- Docker卷映射确保数据持久化
- 定期备份数据库
- 监控磁盘空间使用

## 🛠️ 故障排查

### 常见问题
1. **连接被拒绝**
   - 检查MySQL服务是否启动
   - 验证端口3308是否开放
   - 确认密码是否正确

2. **权限错误**
   - 确认用户名和密码
   - 检查用户权限设置

3. **字符编码问题**
   - 确认连接字符串包含UTF-8设置
   - 检查数据库字符集配置

### 诊断命令
```bash
# 检查端口监听
netstat -an | grep :3308

# 检查Docker容器状态
docker ps | grep mysql

# 查看MySQL日志
docker logs wtms-mysql-dev
```

---

**数据库配置已按要求更新完成！**
