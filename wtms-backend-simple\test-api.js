const http = require('http');

// API测试工具
class APITester {
  constructor(baseURL = 'http://localhost:55557/api/v1') {
    this.baseURL = baseURL;
    this.token = null;
  }

  // 发送HTTP请求
  async request(method, path, data = null) {
    return new Promise((resolve, reject) => {
      const url = new URL(path, this.baseURL);
      const options = {
        hostname: url.hostname,
        port: url.port,
        path: url.pathname + url.search,
        method: method,
        headers: {
          'Content-Type': 'application/json',
          'Origin': 'http://localhost:33335'
        }
      };

      if (this.token) {
        options.headers['Authorization'] = `Bearer ${this.token}`;
      }

      const req = http.request(options, (res) => {
        let body = '';
        res.on('data', (chunk) => {
          body += chunk;
        });
        res.on('end', () => {
          try {
            const result = JSON.parse(body);
            resolve({ status: res.statusCode, data: result });
          } catch (error) {
            resolve({ status: res.statusCode, data: body });
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      if (data) {
        req.write(JSON.stringify(data));
      }

      req.end();
    });
  }

  // 测试健康检查
  async testHealth() {
    console.log('🔍 测试健康检查接口...');
    try {
      const result = await this.request('GET', '/health');
      if (result.status === 200 && result.data.success) {
        console.log('✅ 健康检查通过');
        console.log(`   服务状态: ${result.data.data.status}`);
        console.log(`   服务版本: ${result.data.data.version}`);
        return true;
      } else {
        console.log('❌ 健康检查失败');
        return false;
      }
    } catch (error) {
      console.log('❌ 健康检查错误:', error.message);
      return false;
    }
  }

  // 测试登录
  async testLogin() {
    console.log('\n🔍 测试登录接口...');
    try {
      const result = await this.request('POST', '/auth/login', {
        username: 'admin',
        password: 'admin123456'
      });

      if (result.status === 200 && result.data.success) {
        this.token = result.data.data.token;
        console.log('✅ 登录成功');
        console.log(`   用户: ${result.data.data.user.fullName}`);
        console.log(`   角色: ${result.data.data.user.roles.join(', ')}`);
        console.log(`   令牌: ${this.token.substring(0, 20)}...`);
        return true;
      } else {
        console.log('❌ 登录失败:', result.data.message);
        return false;
      }
    } catch (error) {
      console.log('❌ 登录错误:', error.message);
      return false;
    }
  }

  // 测试错误登录
  async testInvalidLogin() {
    console.log('\n🔍 测试错误登录...');
    try {
      const result = await this.request('POST', '/auth/login', {
        username: 'admin',
        password: 'wrongpassword'
      });

      if (result.status === 400 && !result.data.success) {
        console.log('✅ 错误登录正确拒绝');
        console.log(`   错误信息: ${result.data.message}`);
        return true;
      } else {
        console.log('❌ 错误登录应该被拒绝');
        return false;
      }
    } catch (error) {
      console.log('❌ 错误登录测试错误:', error.message);
      return false;
    }
  }

  // 测试获取用户信息
  async testUserProfile() {
    console.log('\n🔍 测试获取用户信息...');
    try {
      const result = await this.request('GET', '/user/profile');

      if (result.status === 200 && result.data.success) {
        console.log('✅ 获取用户信息成功');
        console.log(`   用户ID: ${result.data.data.id}`);
        console.log(`   用户名: ${result.data.data.username}`);
        console.log(`   权限数量: ${result.data.data.permissions.length}`);
        return true;
      } else {
        console.log('❌ 获取用户信息失败:', result.data.message);
        return false;
      }
    } catch (error) {
      console.log('❌ 获取用户信息错误:', error.message);
      return false;
    }
  }

  // 测试获取任务列表
  async testTaskList() {
    console.log('\n🔍 测试获取任务列表...');
    try {
      const result = await this.request('GET', '/tasks?page=1&size=10');

      if (result.status === 200 && result.data.success) {
        console.log('✅ 获取任务列表成功');
        console.log(`   任务数量: ${result.data.data.records.length}`);
        console.log(`   总数: ${result.data.data.total}`);
        console.log(`   第一个任务: ${result.data.data.records[0]?.title}`);
        return true;
      } else {
        console.log('❌ 获取任务列表失败:', result.data.message);
        return false;
      }
    } catch (error) {
      console.log('❌ 获取任务列表错误:', error.message);
      return false;
    }
  }

  // 测试获取任务详情
  async testTaskDetail() {
    console.log('\n🔍 测试获取任务详情...');
    try {
      const result = await this.request('GET', '/tasks/1');

      if (result.status === 200 && result.data.success) {
        console.log('✅ 获取任务详情成功');
        console.log(`   任务标题: ${result.data.data.title}`);
        console.log(`   任务状态: ${result.data.data.status}`);
        console.log(`   完成进度: ${result.data.data.progress}%`);
        return true;
      } else {
        console.log('❌ 获取任务详情失败:', result.data.message);
        return false;
      }
    } catch (error) {
      console.log('❌ 获取任务详情错误:', error.message);
      return false;
    }
  }

  // 测试创建任务
  async testCreateTask() {
    console.log('\n🔍 测试创建任务...');
    try {
      const result = await this.request('POST', '/tasks', {
        title: '测试任务',
        description: '这是一个API测试创建的任务',
        priority: 'HIGH'
      });

      if (result.status === 200 && result.data.success) {
        console.log('✅ 创建任务成功');
        console.log(`   任务ID: ${result.data.data.id}`);
        console.log(`   任务标题: ${result.data.data.title}`);
        return true;
      } else {
        console.log('❌ 创建任务失败:', result.data.message);
        return false;
      }
    } catch (error) {
      console.log('❌ 创建任务错误:', error.message);
      return false;
    }
  }

  // 测试获取任务评论
  async testTaskComments() {
    console.log('\n🔍 测试获取任务评论...');
    try {
      const result = await this.request('GET', '/tasks/1/comments');

      if (result.status === 200 && result.data.success) {
        console.log('✅ 获取任务评论成功');
        console.log(`   评论数量: ${result.data.data.length}`);
        if (result.data.data.length > 0) {
          console.log(`   第一条评论: ${result.data.data[0].content}`);
        }
        return true;
      } else {
        console.log('❌ 获取任务评论失败:', result.data.message);
        return false;
      }
    } catch (error) {
      console.log('❌ 获取任务评论错误:', error.message);
      return false;
    }
  }

  // 运行所有测试
  async runAllTests() {
    console.log('🚀 开始WTMS API完整测试\n');
    console.log('='.repeat(50));

    const tests = [
      { name: '健康检查', fn: () => this.testHealth() },
      { name: '用户登录', fn: () => this.testLogin() },
      { name: '错误登录', fn: () => this.testInvalidLogin() },
      { name: '用户信息', fn: () => this.testUserProfile() },
      { name: '任务列表', fn: () => this.testTaskList() },
      { name: '任务详情', fn: () => this.testTaskDetail() },
      { name: '创建任务', fn: () => this.testCreateTask() },
      { name: '任务评论', fn: () => this.testTaskComments() }
    ];

    let passed = 0;
    let failed = 0;

    for (const test of tests) {
      const result = await test.fn();
      if (result) {
        passed++;
      } else {
        failed++;
      }
    }

    console.log('\n' + '='.repeat(50));
    console.log('📊 测试结果汇总:');
    console.log(`✅ 通过: ${passed}`);
    console.log(`❌ 失败: ${failed}`);
    console.log(`📈 成功率: ${((passed / (passed + failed)) * 100).toFixed(1)}%`);

    if (failed === 0) {
      console.log('\n🎉 所有测试通过！WTMS API功能正常！');
    } else {
      console.log('\n⚠️ 部分测试失败，请检查相关功能。');
    }

    return failed === 0;
  }
}

// 运行测试
async function main() {
  const tester = new APITester();
  await tester.runAllTests();
}

// 如果直接运行此脚本
if (require.main === module) {
  main().catch(console.error);
}

module.exports = APITester;
