package com.wtms.security;

import com.wtms.entity.Role;
import com.wtms.entity.User;
import com.wtms.mapper.UserMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * Spring Security用户详情服务实现
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Autowired
    private UserMapper userMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        log.debug("Loading user by username: {}", username);
        
        // 根据用户名查询用户（包含角色信息）
        User user = userMapper.findByUsernameWithRole(username);
        
        if (user == null) {
            log.error("User not found with username: {}", username);
            throw new UsernameNotFoundException("用户不存在: " + username);
        }
        
        // 检查用户状态
        if (!user.isActive()) {
            log.error("User account is not active: {}", username);
            throw new UsernameNotFoundException("用户账户已被禁用: " + username);
        }
        
        return UserPrincipal.create(user);
    }

    /**
     * 根据用户ID加载用户详情
     */
    public UserDetails loadUserById(String userId) {
        User user = userMapper.findByIdWithRole(userId);
        
        if (user == null) {
            throw new UsernameNotFoundException("用户不存在: " + userId);
        }
        
        return UserPrincipal.create(user);
    }

    /**
     * 用户主体类
     */
    public static class UserPrincipal implements UserDetails {
        private String id;
        private String username;
        private String email;
        private String password;
        private Collection<? extends GrantedAuthority> authorities;
        private boolean enabled;
        private boolean accountNonExpired;
        private boolean accountNonLocked;
        private boolean credentialsNonExpired;

        public UserPrincipal(String id, String username, String email, String password,
                             Collection<? extends GrantedAuthority> authorities,
                             boolean enabled, boolean accountNonExpired,
                             boolean accountNonLocked, boolean credentialsNonExpired) {
            this.id = id;
            this.username = username;
            this.email = email;
            this.password = password;
            this.authorities = authorities;
            this.enabled = enabled;
            this.accountNonExpired = accountNonExpired;
            this.accountNonLocked = accountNonLocked;
            this.credentialsNonExpired = credentialsNonExpired;
        }

        public static UserPrincipal create(User user) {
            List<GrantedAuthority> authorities = new ArrayList<>();
            
            // 添加角色权限
            if (user.getRole() != null) {
                Role role = user.getRole();
                
                // 添加角色
                authorities.add(new SimpleGrantedAuthority("ROLE_" + role.getCode()));
                
                // 添加权限（如果有的话）
                if (role.getPermissionList() != null) {
                    for (String permission : role.getPermissionList()) {
                        authorities.add(new SimpleGrantedAuthority(permission));
                    }
                }
            }

            return new UserPrincipal(
                    user.getId(),
                    user.getUsername(),
                    user.getEmail(),
                    user.getPasswordHash(),
                    authorities,
                    user.isActive(),
                    true, // accountNonExpired
                    !user.isLocked(), // accountNonLocked
                    true  // credentialsNonExpired
            );
        }

        public String getId() {
            return id;
        }

        public String getEmail() {
            return email;
        }

        @Override
        public String getUsername() {
            return username;
        }

        @Override
        public String getPassword() {
            return password;
        }

        @Override
        public Collection<? extends GrantedAuthority> getAuthorities() {
            return authorities;
        }

        @Override
        public boolean isAccountNonExpired() {
            return accountNonExpired;
        }

        @Override
        public boolean isAccountNonLocked() {
            return accountNonLocked;
        }

        @Override
        public boolean isCredentialsNonExpired() {
            return credentialsNonExpired;
        }

        @Override
        public boolean isEnabled() {
            return enabled;
        }
    }
}
