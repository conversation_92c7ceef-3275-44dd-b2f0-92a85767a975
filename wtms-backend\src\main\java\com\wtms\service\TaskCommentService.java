package com.wtms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wtms.dto.request.CreateCommentRequest;
import com.wtms.dto.request.UpdateCommentRequest;
import com.wtms.dto.response.CommentResponse;
import com.wtms.entity.TaskComment;

import java.util.List;

/**
 * 任务评论服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface TaskCommentService {

    /**
     * 创建评论
     *
     * @param request 创建评论请求
     * @return 评论信息
     */
    TaskComment createComment(CreateCommentRequest request);

    /**
     * 更新评论
     *
     * @param commentId 评论ID
     * @param request 更新评论请求
     * @return 评论信息
     */
    TaskComment updateComment(String commentId, UpdateCommentRequest request);

    /**
     * 删除评论
     *
     * @param commentId 评论ID
     */
    void deleteComment(String commentId);

    /**
     * 根据ID获取评论
     *
     * @param commentId 评论ID
     * @return 评论信息
     */
    TaskComment getCommentById(String commentId);

    /**
     * 获取任务评论树
     *
     * @param taskId 任务ID
     * @return 评论树
     */
    List<CommentResponse> getTaskCommentTree(String taskId);

    /**
     * 分页获取任务评论
     *
     * @param taskId 任务ID
     * @param commentType 评论类型
     * @param status 状态
     * @param page 页码
     * @param size 每页大小
     * @return 评论分页结果
     */
    IPage<CommentResponse> getTaskComments(String taskId, String commentType, String status, Integer page, Integer size);

    /**
     * 获取用户评论
     *
     * @param commenterId 评论者ID
     * @param status 状态
     * @param page 页码
     * @param size 每页大小
     * @return 评论分页结果
     */
    IPage<CommentResponse> getUserComments(String commenterId, String status, Integer page, Integer size);

    /**
     * 获取子评论
     *
     * @param parentId 父评论ID
     * @return 子评论列表
     */
    List<CommentResponse> getChildComments(String parentId);

    /**
     * 获取根评论
     *
     * @param taskId 任务ID
     * @return 根评论列表
     */
    List<CommentResponse> getRootComments(String taskId);

    /**
     * 获取置顶评论
     *
     * @param taskId 任务ID
     * @return 置顶评论列表
     */
    List<CommentResponse> getPinnedComments(String taskId);

    /**
     * 获取最新评论
     *
     * @param taskId 任务ID
     * @param limit 数量限制
     * @return 最新评论列表
     */
    List<CommentResponse> getLatestComments(String taskId, Integer limit);

    /**
     * 获取热门评论
     *
     * @param taskId 任务ID
     * @param limit 数量限制
     * @return 热门评论列表
     */
    List<CommentResponse> getPopularComments(String taskId, Integer limit);

    /**
     * 搜索评论
     *
     * @param taskId 任务ID
     * @param keyword 关键词
     * @param commenterId 评论者ID
     * @param commentType 评论类型
     * @param status 状态
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    IPage<CommentResponse> searchComments(String taskId, String keyword, String commenterId, 
                                        String commentType, String status, Integer page, Integer size);

    /**
     * 点赞评论
     *
     * @param commentId 评论ID
     * @param userId 用户ID
     */
    void likeComment(String commentId, String userId);

    /**
     * 取消点赞评论
     *
     * @param commentId 评论ID
     * @param userId 用户ID
     */
    void unlikeComment(String commentId, String userId);

    /**
     * 置顶评论
     *
     * @param commentId 评论ID
     */
    void pinComment(String commentId);

    /**
     * 取消置顶评论
     *
     * @param commentId 评论ID
     */
    void unpinComment(String commentId);

    /**
     * 隐藏评论
     *
     * @param commentId 评论ID
     */
    void hideComment(String commentId);

    /**
     * 显示评论
     *
     * @param commentId 评论ID
     */
    void showComment(String commentId);

    /**
     * 批量删除评论
     *
     * @param commentIds 评论ID列表
     */
    void batchDeleteComments(List<String> commentIds);

    /**
     * 批量更新评论状态
     *
     * @param commentIds 评论ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<String> commentIds, String status);

    /**
     * 统计任务评论数量
     *
     * @param taskId 任务ID
     * @return 评论数量
     */
    int countTaskComments(String taskId);

    /**
     * 统计用户评论数量
     *
     * @param commenterId 评论者ID
     * @return 评论数量
     */
    int countUserComments(String commenterId);

    /**
     * 检查用户是否可以编辑评论
     *
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否可以编辑
     */
    boolean canEditComment(String commentId, String userId);

    /**
     * 检查用户是否可以删除评论
     *
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否可以删除
     */
    boolean canDeleteComment(String commentId, String userId);

    /**
     * 检查用户是否已点赞评论
     *
     * @param commentId 评论ID
     * @param userId 用户ID
     * @return 是否已点赞
     */
    boolean isCommentLiked(String commentId, String userId);

    /**
     * 获取评论统计信息
     *
     * @param taskId 任务ID
     * @return 统计信息
     */
    Object getCommentStatistics(String taskId);

    /**
     * 获取评论活跃度统计
     *
     * @param taskId 任务ID
     * @param days 天数
     * @return 活跃度统计
     */
    List<Object> getCommentActivityStats(String taskId, Integer days);

    /**
     * 创建系统评论
     *
     * @param taskId 任务ID
     * @param content 评论内容
     * @param commentType 评论类型
     * @return 系统评论
     */
    TaskComment createSystemComment(String taskId, String content, String commentType);
}
