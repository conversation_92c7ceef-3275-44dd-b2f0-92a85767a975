package com.wtms.controller;

import com.wtms.common.result.PageResult;
import com.wtms.common.result.Result;
import com.wtms.dto.request.BatchAssignRequest;
import com.wtms.dto.request.TaskAssignRequest;
import com.wtms.dto.response.TaskAssignResponse;
import com.wtms.service.TaskAssignmentService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 任务分配控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/task-assignments")
@Tag(name = "任务分配管理", description = "任务分配相关接口")
public class TaskAssignmentController {

    @Autowired
    private TaskAssignmentService taskAssignmentService;

    @PostMapping("/tasks/{taskId}/assign")
    @Operation(summary = "分配任务", description = "将任务分配给指定的用户或部门")
    @PreAuthorize("hasAuthority('task:assign') or hasRole('ADMIN') or hasRole('PM')")
    public Result<List<TaskAssignResponse>> assignTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Valid @RequestBody TaskAssignRequest request) {
        
        log.info("Assigning task: {} to targets: {}", taskId, request.getAssignTargets());
        
        List<TaskAssignResponse> responses = taskAssignmentService.assignTask(taskId, request);
        return Result.success("任务分配成功", responses);
    }

    @PostMapping("/batch-assign")
    @Operation(summary = "批量分配任务", description = "批量分配多个任务给指定目标")
    @PreAuthorize("hasAuthority('task:assign') or hasRole('ADMIN') or hasRole('PM')")
    public Result<List<TaskAssignResponse>> batchAssignTasks(@Valid @RequestBody BatchAssignRequest request) {
        log.info("Batch assigning {} tasks", request.getTaskIds().size());
        
        List<TaskAssignResponse> responses = taskAssignmentService.batchAssignTasks(request);
        return Result.success("批量分配成功", responses);
    }

    @PutMapping("/{assignmentId}/accept")
    @Operation(summary = "接受任务分配", description = "接受分配给自己的任务")
    public Result<TaskAssignResponse> acceptAssignment(
            @Parameter(description = "分配ID", required = true)
            @PathVariable @NotBlank(message = "分配ID不能为空") String assignmentId) {
        
        log.info("Accepting assignment: {}", assignmentId);
        
        TaskAssignResponse response = taskAssignmentService.acceptAssignment(assignmentId);
        return Result.success("任务接受成功", response);
    }

    @PutMapping("/{assignmentId}/reject")
    @Operation(summary = "拒绝任务分配", description = "拒绝分配给自己的任务")
    public Result<TaskAssignResponse> rejectAssignment(
            @Parameter(description = "分配ID", required = true)
            @PathVariable @NotBlank(message = "分配ID不能为空") String assignmentId,
            @Parameter(description = "拒绝原因")
            @RequestParam(required = false) String rejectReason) {
        
        log.info("Rejecting assignment: {} with reason: {}", assignmentId, rejectReason);
        
        TaskAssignResponse response = taskAssignmentService.rejectAssignment(assignmentId, rejectReason);
        return Result.success("任务拒绝成功", response);
    }

    @DeleteMapping("/{assignmentId}")
    @Operation(summary = "取消任务分配", description = "取消已分配的任务")
    @PreAuthorize("hasAuthority('task:assign') or hasRole('ADMIN') or hasRole('PM')")
    public Result<String> cancelAssignment(
            @Parameter(description = "分配ID", required = true)
            @PathVariable @NotBlank(message = "分配ID不能为空") String assignmentId) {
        
        log.info("Cancelling assignment: {}", assignmentId);
        
        taskAssignmentService.cancelAssignment(assignmentId);
        return Result.success("分配取消成功");
    }

    @PostMapping("/{assignmentId}/reassign")
    @Operation(summary = "重新分配任务", description = "重新分配已分配的任务")
    @PreAuthorize("hasAuthority('task:assign') or hasRole('ADMIN') or hasRole('PM')")
    public Result<List<TaskAssignResponse>> reassignTask(
            @Parameter(description = "分配ID", required = true)
            @PathVariable @NotBlank(message = "分配ID不能为空") String assignmentId,
            @Valid @RequestBody TaskAssignRequest request) {
        
        log.info("Reassigning task from assignment: {}", assignmentId);
        
        List<TaskAssignResponse> responses = taskAssignmentService.reassignTask(assignmentId, request);
        return Result.success("任务重新分配成功", responses);
    }

    @GetMapping("/tasks/{taskId}")
    @Operation(summary = "获取任务分配记录", description = "获取指定任务的所有分配记录")
    public Result<List<TaskAssignResponse>> getTaskAssignments(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        List<TaskAssignResponse> assignments = taskAssignmentService.getTaskAssignments(taskId);
        return Result.success(assignments);
    }

    @GetMapping("/users/{userId}")
    @Operation(summary = "获取用户分配记录", description = "获取指定用户的分配记录")
    public Result<PageResult<TaskAssignResponse>> getUserAssignments(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId,
            @Parameter(description = "分配状态") @RequestParam(required = false) String status,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") Integer size) {
        
        PageResult<TaskAssignResponse> result = taskAssignmentService.getUserAssignments(userId, status, page, size);
        return Result.success(result);
    }

    @GetMapping("/departments/{departmentId}")
    @Operation(summary = "获取部门分配记录", description = "获取指定部门的分配记录")
    @PreAuthorize("hasAuthority('task:view_department') or hasRole('ADMIN') or hasRole('PM')")
    public Result<PageResult<TaskAssignResponse>> getDepartmentAssignments(
            @Parameter(description = "部门ID", required = true)
            @PathVariable @NotBlank(message = "部门ID不能为空") String departmentId,
            @Parameter(description = "分配状态") @RequestParam(required = false) String status,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") Integer size) {
        
        PageResult<TaskAssignResponse> result = taskAssignmentService.getDepartmentAssignments(departmentId, status, page, size);
        return Result.success(result);
    }

    @GetMapping("/my-assignments")
    @Operation(summary = "获取我分配的任务", description = "获取当前用户分配给他人的任务记录")
    public Result<PageResult<TaskAssignResponse>> getMyAssignments(
            @Parameter(description = "分配状态") @RequestParam(required = false) String status,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") Integer size) {
        
        // TODO: 获取当前用户ID
        String currentUserId = "current-user-id";
        
        PageResult<TaskAssignResponse> result = taskAssignmentService.getMyAssignments(currentUserId, status, page, size);
        return Result.success(result);
    }

    @PostMapping("/tasks/{taskId}/auto-assign")
    @Operation(summary = "自动分配任务", description = "根据指定规则自动分配任务")
    @PreAuthorize("hasAuthority('task:assign') or hasRole('ADMIN') or hasRole('PM')")
    public Result<List<TaskAssignResponse>> autoAssignTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "分配规则", example = "workload_balance")
            @RequestParam(defaultValue = "workload_balance") String assignRule) {
        
        log.info("Auto assigning task: {} with rule: {}", taskId, assignRule);
        
        List<TaskAssignResponse> responses = taskAssignmentService.autoAssignTask(taskId, assignRule);
        return Result.success("自动分配成功", responses);
    }

    @GetMapping("/tasks/{taskId}/recommend-assignees")
    @Operation(summary = "推荐分配目标", description = "智能推荐适合的任务分配目标")
    @PreAuthorize("hasAuthority('task:assign') or hasRole('ADMIN') or hasRole('PM')")
    public Result<List<TaskAssignmentService.RecommendedAssignee>> recommendAssignees(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "推荐数量", example = "5")
            @RequestParam(defaultValue = "5") Integer limit) {
        
        List<TaskAssignmentService.RecommendedAssignee> recommendations = 
            taskAssignmentService.recommendAssignees(taskId, limit);
        return Result.success(recommendations);
    }

    @GetMapping("/workload/users/{userId}")
    @Operation(summary = "获取用户工作负载", description = "获取指定用户的当前工作负载信息")
    public Result<TaskAssignmentService.UserWorkloadInfo> getUserWorkload(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId) {
        
        TaskAssignmentService.UserWorkloadInfo workload = taskAssignmentService.getUserWorkload(userId);
        return Result.success(workload);
    }

    @GetMapping("/workload/departments/{departmentId}")
    @Operation(summary = "获取部门工作负载", description = "获取指定部门的当前工作负载信息")
    @PreAuthorize("hasAuthority('task:view_department') or hasRole('ADMIN') or hasRole('PM')")
    public Result<TaskAssignmentService.DepartmentWorkloadInfo> getDepartmentWorkload(
            @Parameter(description = "部门ID", required = true)
            @PathVariable @NotBlank(message = "部门ID不能为空") String departmentId) {
        
        TaskAssignmentService.DepartmentWorkloadInfo workload = taskAssignmentService.getDepartmentWorkload(departmentId);
        return Result.success(workload);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取分配统计", description = "获取任务分配的统计信息")
    @PreAuthorize("hasAuthority('task:statistics') or hasRole('ADMIN') or hasRole('PM')")
    public Result<Object> getAssignmentStatistics(
            @Parameter(description = "用户ID") @RequestParam(required = false) String userId,
            @Parameter(description = "部门ID") @RequestParam(required = false) String departmentId) {
        
        Object statistics = taskAssignmentService.getAssignmentStatistics(userId, departmentId);
        return Result.success(statistics);
    }

    @PostMapping("/{assignmentId}/notify")
    @Operation(summary = "发送分配通知", description = "手动发送任务分配通知")
    @PreAuthorize("hasAuthority('task:assign') or hasRole('ADMIN') or hasRole('PM')")
    public Result<String> sendAssignmentNotification(
            @Parameter(description = "分配ID", required = true)
            @PathVariable @NotBlank(message = "分配ID不能为空") String assignmentId) {
        
        log.info("Sending assignment notification: {}", assignmentId);
        
        taskAssignmentService.sendAssignmentNotification(assignmentId);
        return Result.success("通知发送成功");
    }
}
