# WTMS Spring Boot 后端API设计文档

## 1. 技术架构

### 1.1 技术栈
- **框架**: Spring Boot 2.7.18 (兼容JDK 8)
- **安全框架**: Spring Security 5.7+ + JWT
- **数据访问**: MyBatis-Plus 3.5.4+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 7.0+
- **API文档**: Swagger 3.0 (SpringDoc OpenAPI)
- **构建工具**: Maven 3.9+
- **Java版本**: JDK 8

### 1.2 项目结构
```
src/main/java/com/wtms/
├── WtmsApplication.java           # 启动类
├── config/                        # 配置类
│   ├── SecurityConfig.java        # 安全配置
│   ├── RedisConfig.java          # Redis配置
│   ├── SwaggerConfig.java        # Swagger配置
│   └── WebConfig.java            # Web配置
├── controller/                    # 控制器层
│   ├── AuthController.java       # 认证控制器
│   ├── UserController.java       # 用户管理
│   ├── TaskController.java       # 任务管理
│   ├── WorkflowController.java   # 工作流管理
│   └── AnalyticsController.java  # 数据分析
├── service/                       # 服务层
│   ├── impl/                     # 服务实现
│   ├── AuthService.java          # 认证服务
│   ├── UserService.java          # 用户服务
│   ├── TaskService.java          # 任务服务
│   └── WorkflowService.java      # 工作流服务
├── mapper/                        # 数据访问层
│   ├── UserMapper.java           # 用户Mapper
│   ├── TaskMapper.java           # 任务Mapper
│   └── WorkflowMapper.java       # 工作流Mapper
├── entity/                        # 实体类
│   ├── User.java                 # 用户实体
│   ├── Task.java                 # 任务实体
│   └── Workflow.java             # 工作流实体
├── dto/                          # 数据传输对象
│   ├── request/                  # 请求DTO
│   ├── response/                 # 响应DTO
│   └── common/                   # 通用DTO
├── common/                       # 公共类
│   ├── result/                   # 统一响应结果
│   ├── exception/                # 异常处理
│   ├── constant/                 # 常量定义
│   └── util/                     # 工具类
└── security/                     # 安全相关
    ├── JwtAuthenticationFilter.java
    ├── JwtTokenProvider.java
    └── UserDetailsServiceImpl.java
```

## 2. 统一响应格式

### 2.1 响应结果封装
```java
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Result<T> {
    private Boolean success;
    private Integer code;
    private String message;
    private T data;
    private Long timestamp;
    
    public static <T> Result<T> success(T data) {
        return new Result<>(true, 200, "操作成功", data, System.currentTimeMillis());
    }
    
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(true, 200, message, data, System.currentTimeMillis());
    }
    
    public static <T> Result<T> error(String message) {
        return new Result<>(false, 500, message, null, System.currentTimeMillis());
    }
    
    public static <T> Result<T> error(Integer code, String message) {
        return new Result<>(false, code, message, null, System.currentTimeMillis());
    }
}
```

### 2.2 分页响应封装
```java
@Data
@AllArgsConstructor
@NoArgsConstructor
public class PageResult<T> {
    private List<T> records;
    private Long total;
    private Long size;
    private Long current;
    private Long pages;
    
    public static <T> PageResult<T> of(IPage<T> page) {
        return new PageResult<>(
            page.getRecords(),
            page.getTotal(),
            page.getSize(),
            page.getCurrent(),
            page.getPages()
        );
    }
}
```

## 3. 认证和授权

### 3.1 JWT配置
```java
@Component
public class JwtTokenProvider {
    
    @Value("${jwt.secret}")
    private String jwtSecret;
    
    @Value("${jwt.expiration}")
    private int jwtExpirationInMs;
    
    public String generateToken(UserDetails userDetails) {
        Date expiryDate = new Date(System.currentTimeMillis() + jwtExpirationInMs);
        
        return Jwts.builder()
                .setSubject(userDetails.getUsername())
                .setIssuedAt(new Date())
                .setExpiration(expiryDate)
                .signWith(SignatureAlgorithm.HS512, jwtSecret)
                .compact();
    }
    
    public String getUsernameFromToken(String token) {
        Claims claims = Jwts.parser()
                .setSigningKey(jwtSecret)
                .parseClaimsJws(token)
                .getBody();
        return claims.getSubject();
    }
    
    public boolean validateToken(String token) {
        try {
            Jwts.parser().setSigningKey(jwtSecret).parseClaimsJws(token);
            return true;
        } catch (JwtException | IllegalArgumentException e) {
            return false;
        }
    }
}
```

### 3.2 认证控制器
```java
@RestController
@RequestMapping("/api/v1/auth")
@Api(tags = "认证管理")
@Slf4j
public class AuthController {
    
    @Autowired
    private AuthService authService;
    
    @PostMapping("/login")
    @ApiOperation("用户登录")
    public Result<LoginResponse> login(@RequestBody @Valid LoginRequest request) {
        LoginResponse response = authService.login(request);
        return Result.success("登录成功", response);
    }
    
    @PostMapping("/logout")
    @ApiOperation("用户登出")
    public Result<Void> logout(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        authService.logout(token);
        return Result.success("登出成功", null);
    }
    
    @PostMapping("/refresh")
    @ApiOperation("刷新Token")
    public Result<TokenResponse> refreshToken(@RequestBody RefreshTokenRequest request) {
        TokenResponse response = authService.refreshToken(request.getRefreshToken());
        return Result.success("Token刷新成功", response);
    }
    
    @GetMapping("/me")
    @ApiOperation("获取当前用户信息")
    @PreAuthorize("hasRole('USER')")
    public Result<UserInfoResponse> getCurrentUser() {
        UserInfoResponse userInfo = authService.getCurrentUserInfo();
        return Result.success(userInfo);
    }
    
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
```

## 4. 任务管理API

### 4.1 任务控制器
```java
@RestController
@RequestMapping("/api/v1/tasks")
@Api(tags = "任务管理")
@Slf4j
public class TaskController {
    
    @Autowired
    private TaskService taskService;
    
    @GetMapping
    @ApiOperation("获取任务列表")
    @PreAuthorize("hasPermission('task', 'read')")
    public Result<PageResult<TaskListResponse>> getTasks(
            @ModelAttribute TaskQueryRequest request,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        Page<Task> pageParam = new Page<>(page, size);
        IPage<Task> taskPage = taskService.getTasks(pageParam, request);
        
        List<TaskListResponse> responses = taskPage.getRecords().stream()
                .map(TaskListResponse::from)
                .collect(Collectors.toList());
        
        PageResult<TaskListResponse> pageResult = new PageResult<>();
        pageResult.setRecords(responses);
        pageResult.setTotal(taskPage.getTotal());
        pageResult.setCurrent(taskPage.getCurrent());
        pageResult.setSize(taskPage.getSize());
        pageResult.setPages(taskPage.getPages());
        
        return Result.success(pageResult);
    }
    
    @GetMapping("/{id}")
    @ApiOperation("获取任务详情")
    @PreAuthorize("hasPermission('task', 'read')")
    public Result<TaskDetailResponse> getTask(@PathVariable String id) {
        Task task = taskService.getById(id);
        if (task == null) {
            return Result.error(404, "任务不存在");
        }
        TaskDetailResponse response = TaskDetailResponse.from(task);
        return Result.success(response);
    }
    
    @PostMapping
    @ApiOperation("创建任务")
    @PreAuthorize("hasPermission('task', 'create')")
    public Result<TaskDetailResponse> createTask(@RequestBody @Valid CreateTaskRequest request) {
        Task task = taskService.createTask(request);
        TaskDetailResponse response = TaskDetailResponse.from(task);
        return Result.success("任务创建成功", response);
    }
    
    @PutMapping("/{id}")
    @ApiOperation("更新任务")
    @PreAuthorize("hasPermission('task', 'update')")
    public Result<TaskDetailResponse> updateTask(
            @PathVariable String id,
            @RequestBody @Valid UpdateTaskRequest request) {
        Task task = taskService.updateTask(id, request);
        TaskDetailResponse response = TaskDetailResponse.from(task);
        return Result.success("任务更新成功", response);
    }
    
    @DeleteMapping("/{id}")
    @ApiOperation("删除任务")
    @PreAuthorize("hasPermission('task', 'delete')")
    public Result<Void> deleteTask(@PathVariable String id) {
        taskService.deleteTask(id);
        return Result.success("任务删除成功", null);
    }
    
    @PutMapping("/{id}/status")
    @ApiOperation("更新任务状态")
    @PreAuthorize("hasPermission('task', 'update')")
    public Result<Void> updateTaskStatus(
            @PathVariable String id,
            @RequestBody @Valid UpdateTaskStatusRequest request) {
        taskService.updateTaskStatus(id, request);
        return Result.success("任务状态更新成功", null);
    }
    
    @PutMapping("/{id}/assign")
    @ApiOperation("分配任务")
    @PreAuthorize("hasPermission('task', 'assign')")
    public Result<Void> assignTask(
            @PathVariable String id,
            @RequestBody @Valid AssignTaskRequest request) {
        taskService.assignTask(id, request);
        return Result.success("任务分配成功", null);
    }
    
    @GetMapping("/{id}/comments")
    @ApiOperation("获取任务评论")
    @PreAuthorize("hasPermission('task', 'read')")
    public Result<PageResult<TaskCommentResponse>> getTaskComments(
            @PathVariable String id,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        Page<TaskComment> pageParam = new Page<>(page, size);
        IPage<TaskComment> commentPage = taskService.getTaskComments(id, pageParam);
        
        List<TaskCommentResponse> responses = commentPage.getRecords().stream()
                .map(TaskCommentResponse::from)
                .collect(Collectors.toList());
        
        PageResult<TaskCommentResponse> pageResult = PageResult.of(commentPage);
        pageResult.setRecords(responses);
        
        return Result.success(pageResult);
    }
    
    @PostMapping("/{id}/comments")
    @ApiOperation("添加任务评论")
    @PreAuthorize("hasPermission('task', 'comment')")
    public Result<TaskCommentResponse> addTaskComment(
            @PathVariable String id,
            @RequestBody @Valid AddTaskCommentRequest request) {
        TaskComment comment = taskService.addTaskComment(id, request);
        TaskCommentResponse response = TaskCommentResponse.from(comment);
        return Result.success("评论添加成功", response);
    }
}
```

### 4.2 任务服务实现
```java
@Service
@Transactional
@Slf4j
public class TaskServiceImpl implements TaskService {
    
    @Autowired
    private TaskMapper taskMapper;
    
    @Autowired
    private UserMapper userMapper;
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Override
    public IPage<Task> getTasks(Page<Task> page, TaskQueryRequest request) {
        QueryWrapper<Task> queryWrapper = new QueryWrapper<>();
        
        // 构建查询条件
        if (StringUtils.hasText(request.getStatus())) {
            queryWrapper.eq("status", request.getStatus());
        }
        if (request.getPriority() != null) {
            queryWrapper.eq("priority", request.getPriority());
        }
        if (StringUtils.hasText(request.getAssigneeId())) {
            queryWrapper.eq("assignee_id", request.getAssigneeId());
        }
        if (StringUtils.hasText(request.getSearch())) {
            queryWrapper.and(wrapper -> wrapper
                .like("title", request.getSearch())
                .or()
                .like("description", request.getSearch())
                .or()
                .like("task_code", request.getSearch())
            );
        }
        
        // 排序
        if (StringUtils.hasText(request.getSortBy())) {
            if ("desc".equalsIgnoreCase(request.getSortOrder())) {
                queryWrapper.orderByDesc(request.getSortBy());
            } else {
                queryWrapper.orderByAsc(request.getSortBy());
            }
        } else {
            queryWrapper.orderByDesc("created_at");
        }
        
        // 软删除过滤
        queryWrapper.isNull("deleted_at");
        
        return taskMapper.selectPage(page, queryWrapper);
    }
    
    @Override
    public Task createTask(CreateTaskRequest request) {
        // 生成任务编码
        String taskCode = generateTaskCode(request.getCategoryId());
        
        Task task = new Task();
        BeanUtils.copyProperties(request, task);
        task.setId(UUID.randomUUID().toString());
        task.setTaskCode(taskCode);
        task.setCreatorId(getCurrentUserId());
        task.setStatus(TaskStatus.DRAFT.getCode());
        task.setProgress(BigDecimal.ZERO);
        task.setCreatedAt(LocalDateTime.now());
        task.setUpdatedAt(LocalDateTime.now());
        
        taskMapper.insert(task);
        
        // 清除相关缓存
        clearTaskCache();
        
        // 发送通知
        if (StringUtils.hasText(task.getAssigneeId())) {
            sendTaskAssignNotification(task);
        }
        
        return task;
    }
    
    @Override
    public Task updateTask(String id, UpdateTaskRequest request) {
        Task existingTask = taskMapper.selectById(id);
        if (existingTask == null) {
            throw new BusinessException("任务不存在");
        }
        
        // 检查权限
        checkTaskPermission(existingTask, "update");
        
        Task task = new Task();
        BeanUtils.copyProperties(request, task);
        task.setId(id);
        task.setUpdatedAt(LocalDateTime.now());
        
        taskMapper.updateById(task);
        
        // 清除缓存
        clearTaskCache(id);
        
        return taskMapper.selectById(id);
    }
    
    @Override
    public void updateTaskStatus(String id, UpdateTaskStatusRequest request) {
        Task task = taskMapper.selectById(id);
        if (task == null) {
            throw new BusinessException("任务不存在");
        }
        
        // 验证状态流转
        validateStatusTransition(task.getStatus(), request.getStatus());
        
        Task updateTask = new Task();
        updateTask.setId(id);
        updateTask.setStatus(request.getStatus());
        updateTask.setUpdatedAt(LocalDateTime.now());
        
        // 如果是完成状态，设置完成时间
        if (TaskStatus.COMPLETED.getCode().equals(request.getStatus())) {
            updateTask.setActualEndDate(LocalDateTime.now());
            updateTask.setProgress(new BigDecimal("100"));
        }
        
        taskMapper.updateById(updateTask);
        
        // 记录状态变更日志
        recordStatusChange(id, task.getStatus(), request.getStatus(), request.getComment());
        
        // 清除缓存
        clearTaskCache(id);
        
        // 发送通知
        sendStatusChangeNotification(task, request.getStatus());
    }
    
    private String generateTaskCode(String categoryId) {
        // 获取分类前缀
        String prefix = getCategoryPrefix(categoryId);
        
        // 生成序号
        String key = "task_sequence:" + categoryId;
        Long sequence = redisTemplate.opsForValue().increment(key);
        
        return String.format("%s-%04d", prefix, sequence);
    }
    
    private void validateStatusTransition(String fromStatus, String toStatus) {
        // 实现状态流转验证逻辑
        Map<String, List<String>> allowedTransitions = getStatusTransitionRules();
        
        List<String> allowedToStatuses = allowedTransitions.get(fromStatus);
        if (allowedToStatuses == null || !allowedToStatuses.contains(toStatus)) {
            throw new BusinessException("不允许的状态流转: " + fromStatus + " -> " + toStatus);
        }
    }
    
    private void clearTaskCache() {
        redisTemplate.delete("task:list:*");
    }
    
    private void clearTaskCache(String taskId) {
        redisTemplate.delete("task:detail:" + taskId);
        redisTemplate.delete("task:list:*");
    }
}
```

## 5. 工作流管理API

### 5.1 工作流控制器
```java
@RestController
@RequestMapping("/api/v1/workflows")
@Api(tags = "工作流管理")
@Slf4j
public class WorkflowController {
    
    @Autowired
    private WorkflowService workflowService;
    
    @GetMapping
    @ApiOperation("获取工作流列表")
    @PreAuthorize("hasPermission('workflow', 'read')")
    public Result<PageResult<WorkflowListResponse>> getWorkflows(
            @RequestParam(required = false) String category,
            @RequestParam(required = false) String status,
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "20") Integer size) {
        
        Page<Workflow> pageParam = new Page<>(page, size);
        IPage<Workflow> workflowPage = workflowService.getWorkflows(pageParam, category, status);
        
        List<WorkflowListResponse> responses = workflowPage.getRecords().stream()
                .map(WorkflowListResponse::from)
                .collect(Collectors.toList());
        
        PageResult<WorkflowListResponse> pageResult = PageResult.of(workflowPage);
        pageResult.setRecords(responses);
        
        return Result.success(pageResult);
    }
    
    @PostMapping
    @ApiOperation("创建工作流")
    @PreAuthorize("hasPermission('workflow', 'create')")
    public Result<WorkflowDetailResponse> createWorkflow(@RequestBody @Valid CreateWorkflowRequest request) {
        Workflow workflow = workflowService.createWorkflow(request);
        WorkflowDetailResponse response = WorkflowDetailResponse.from(workflow);
        return Result.success("工作流创建成功", response);
    }
    
    @PostMapping("/{id}/deploy")
    @ApiOperation("发布工作流")
    @PreAuthorize("hasPermission('workflow', 'deploy')")
    public Result<Void> deployWorkflow(@PathVariable String id) {
        workflowService.deployWorkflow(id);
        return Result.success("工作流发布成功", null);
    }
    
    @PostMapping("/{workflowId}/instances")
    @ApiOperation("启动工作流实例")
    @PreAuthorize("hasPermission('workflow', 'execute')")
    public Result<WorkflowInstanceResponse> startWorkflowInstance(
            @PathVariable String workflowId,
            @RequestBody @Valid StartWorkflowInstanceRequest request) {
        WorkflowInstance instance = workflowService.startWorkflowInstance(workflowId, request);
        WorkflowInstanceResponse response = WorkflowInstanceResponse.from(instance);
        return Result.success("工作流实例启动成功", response);
    }
    
    @PostMapping("/instances/{instanceId}/approve")
    @ApiOperation("审批操作")
    @PreAuthorize("hasPermission('workflow', 'approve')")
    public Result<Void> approveWorkflowStep(
            @PathVariable String instanceId,
            @RequestBody @Valid ApprovalRequest request) {
        workflowService.approveWorkflowStep(instanceId, request);
        return Result.success("审批操作成功", null);
    }
}
```

## 6. 异常处理

### 6.1 全局异常处理器
```java
@RestControllerAdvice
@Slf4j
public class GlobalExceptionHandler {
    
    @ExceptionHandler(BusinessException.class)
    public Result<Void> handleBusinessException(BusinessException e) {
        log.warn("业务异常: {}", e.getMessage());
        return Result.error(e.getCode(), e.getMessage());
    }
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<Void> handleValidationException(MethodArgumentNotValidException e) {
        StringBuilder message = new StringBuilder("参数验证失败: ");
        e.getBindingResult().getFieldErrors().forEach(error -> 
            message.append(error.getField()).append(" ").append(error.getDefaultMessage()).append("; ")
        );
        return Result.error(400, message.toString());
    }
    
    @ExceptionHandler(AccessDeniedException.class)
    public Result<Void> handleAccessDeniedException(AccessDeniedException e) {
        return Result.error(403, "权限不足");
    }
    
    @ExceptionHandler(Exception.class)
    public Result<Void> handleException(Exception e) {
        log.error("系统异常", e);
        return Result.error("系统内部错误");
    }
}
```

## 7. 配置文件

### 7.1 application.yml
```yaml
server:
  port: 8080
  servlet:
    context-path: /

spring:
  application:
    name: wtms-backend
  
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ********************************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 3000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        max-wait: -1ms

mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: assign_uuid
      logic-delete-field: deleted_at
      logic-delete-value: now()
      logic-not-delete-value: 'null'

jwt:
  secret: ${JWT_SECRET:wtms-secret-key-2024}
  expiration: 86400000 # 24小时

logging:
  level:
    com.wtms: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
```

---

**注：** 本Spring Boot后端API设计基于最新的Spring Boot 2.7.18版本，兼容JDK 8，提供了完整的RESTful API设计和实现方案。具体实现时可根据实际业务需求进行调整优化。
