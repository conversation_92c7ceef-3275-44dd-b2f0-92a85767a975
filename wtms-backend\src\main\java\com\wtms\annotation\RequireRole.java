package com.wtms.annotation;

import java.lang.annotation.*;

/**
 * 角色验证注解
 * 用于方法级别的角色控制
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequireRole {

    /**
     * 角色编码
     */
    String[] value() default {};

    /**
     * 角色编码（别名）
     */
    String[] roles() default {};

    /**
     * 逻辑关系：AND（需要所有角色）或 OR（需要任意一个角色）
     */
    Logical logical() default Logical.AND;

    /**
     * 逻辑关系枚举
     */
    enum Logical {
        AND, OR
    }
}
