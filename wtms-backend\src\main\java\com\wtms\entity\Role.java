package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("roles")
@Schema(description = "角色实体")
public class Role implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "角色ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "角色名称")
    @TableField("name")
    private String name;

    @Schema(description = "角色编码")
    @TableField("code")
    private String code;

    @Schema(description = "角色描述")
    @TableField("description")
    private String description;

    @Schema(description = "权限列表")
    @TableField("permissions")
    private String permissions;

    @Schema(description = "是否系统角色")
    @TableField("is_system")
    private Boolean isSystem;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // 非数据库字段
    @Schema(description = "权限列表")
    @TableField(exist = false)
    private List<String> permissionList;

    @Schema(description = "权限对象列表")
    @TableField(exist = false)
    private List<Permission> permissionObjects;

    @Schema(description = "用户列表")
    @TableField(exist = false)
    private List<User> users;

    /**
     * 系统预定义角色
     */
    public static class SystemRole {
        public static final String ADMIN = "ADMIN";
        public static final String PM = "PM";
        public static final String DEVELOPER = "DEVELOPER";
        public static final String TESTER = "TESTER";
    }

    /**
     * 检查是否为系统角色
     */
    public boolean isSystemRole() {
        return Boolean.TRUE.equals(this.isSystem);
    }

    /**
     * 检查是否为管理员角色
     */
    public boolean isAdmin() {
        return SystemRole.ADMIN.equals(this.code);
    }
}
