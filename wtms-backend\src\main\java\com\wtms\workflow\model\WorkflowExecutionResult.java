package com.wtms.workflow.model;

import com.wtms.entity.WorkflowInstance;
import com.wtms.entity.WorkflowTask;
import com.wtms.entity.WorkflowNode;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 工作流执行结果
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowExecutionResult {

    /**
     * 执行是否成功
     */
    @Builder.Default
    private boolean success = true;

    /**
     * 结果代码
     */
    private String code;

    /**
     * 结果消息
     */
    private String message;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 异常堆栈
     */
    private String stackTrace;

    /**
     * 执行开始时间
     */
    @Builder.Default
    private LocalDateTime startTime = LocalDateTime.now();

    /**
     * 执行结束时间
     */
    private LocalDateTime endTime;

    /**
     * 执行时长（毫秒）
     */
    private Long duration;

    /**
     * 工作流实例
     */
    private WorkflowInstance instance;

    /**
     * 当前任务列表
     */
    private List<WorkflowTask> currentTasks;

    /**
     * 已完成任务列表
     */
    private List<WorkflowTask> completedTasks;

    /**
     * 当前节点列表
     */
    private List<WorkflowNode> currentNodes;

    /**
     * 下一个可能节点列表
     */
    private List<WorkflowNode> nextPossibleNodes;

    /**
     * 执行路径
     */
    private List<String> executionPath;

    /**
     * 结果数据
     */
    @Builder.Default
    private Map<String, Object> data = new HashMap<>();

    /**
     * 变量变更
     */
    @Builder.Default
    private Map<String, Object> variableChanges = new HashMap<>();

    /**
     * 执行统计
     */
    @Builder.Default
    private Map<String, Object> statistics = new HashMap<>();

    /**
     * 警告信息列表
     */
    private List<String> warnings;

    /**
     * 验证错误列表
     */
    private List<ValidationError> validationErrors;

    /**
     * 是否需要用户交互
     */
    @Builder.Default
    private boolean requiresUserInteraction = false;

    /**
     * 是否已完成
     */
    @Builder.Default
    private boolean completed = false;

    /**
     * 是否已暂停
     */
    @Builder.Default
    private boolean suspended = false;

    /**
     * 是否已终止
     */
    @Builder.Default
    private boolean terminated = false;

    /**
     * 验证错误内部类
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ValidationError {
        private String field;
        private String code;
        private String message;
        private Object value;
    }

    /**
     * 结果代码枚举
     */
    public enum ResultCode {
        SUCCESS("SUCCESS", "执行成功"),
        FAILED("FAILED", "执行失败"),
        ERROR("ERROR", "执行错误"),
        TIMEOUT("TIMEOUT", "执行超时"),
        CANCELLED("CANCELLED", "执行取消"),
        SUSPENDED("SUSPENDED", "执行暂停"),
        COMPLETED("COMPLETED", "流程完成"),
        TERMINATED("TERMINATED", "流程终止"),
        VALIDATION_ERROR("VALIDATION_ERROR", "验证错误"),
        PERMISSION_DENIED("PERMISSION_DENIED", "权限不足"),
        RESOURCE_NOT_FOUND("RESOURCE_NOT_FOUND", "资源不存在"),
        BUSINESS_ERROR("BUSINESS_ERROR", "业务错误"),
        SYSTEM_ERROR("SYSTEM_ERROR", "系统错误");

        private final String code;
        private final String description;

        ResultCode(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 创建成功结果
     */
    public static WorkflowExecutionResult success() {
        return WorkflowExecutionResult.builder()
                .success(true)
                .code(ResultCode.SUCCESS.getCode())
                .message(ResultCode.SUCCESS.getDescription())
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功结果（带消息）
     */
    public static WorkflowExecutionResult success(String message) {
        return WorkflowExecutionResult.builder()
                .success(true)
                .code(ResultCode.SUCCESS.getCode())
                .message(message)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建成功结果（带数据）
     */
    public static WorkflowExecutionResult success(String message, Map<String, Object> data) {
        return WorkflowExecutionResult.builder()
                .success(true)
                .code(ResultCode.SUCCESS.getCode())
                .message(message)
                .data(data)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果
     */
    public static WorkflowExecutionResult failure(String message) {
        return WorkflowExecutionResult.builder()
                .success(false)
                .code(ResultCode.FAILED.getCode())
                .message(message)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建失败结果（带错误信息）
     */
    public static WorkflowExecutionResult failure(String message, String errorMessage) {
        return WorkflowExecutionResult.builder()
                .success(false)
                .code(ResultCode.FAILED.getCode())
                .message(message)
                .errorMessage(errorMessage)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建错误结果
     */
    public static WorkflowExecutionResult error(String message, Throwable throwable) {
        return WorkflowExecutionResult.builder()
                .success(false)
                .code(ResultCode.ERROR.getCode())
                .message(message)
                .errorMessage(throwable.getMessage())
                .stackTrace(getStackTrace(throwable))
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 创建验证错误结果
     */
    public static WorkflowExecutionResult validationError(String message, List<ValidationError> errors) {
        return WorkflowExecutionResult.builder()
                .success(false)
                .code(ResultCode.VALIDATION_ERROR.getCode())
                .message(message)
                .validationErrors(errors)
                .endTime(LocalDateTime.now())
                .build();
    }

    /**
     * 设置数据
     */
    public WorkflowExecutionResult setData(String key, Object value) {
        if (data == null) {
            data = new HashMap<>();
        }
        data.put(key, value);
        return this;
    }

    /**
     * 获取数据
     */
    public Object getData(String key) {
        return data != null ? data.get(key) : null;
    }

    /**
     * 设置变量变更
     */
    public WorkflowExecutionResult setVariableChange(String key, Object value) {
        if (variableChanges == null) {
            variableChanges = new HashMap<>();
        }
        variableChanges.put(key, value);
        return this;
    }

    /**
     * 设置统计信息
     */
    public WorkflowExecutionResult setStatistic(String key, Object value) {
        if (statistics == null) {
            statistics = new HashMap<>();
        }
        statistics.put(key, value);
        return this;
    }

    /**
     * 计算执行时长
     */
    public void calculateDuration() {
        if (startTime != null && endTime != null) {
            duration = java.time.Duration.between(startTime, endTime).toMillis();
        }
    }

    /**
     * 完成执行
     */
    public WorkflowExecutionResult complete() {
        this.endTime = LocalDateTime.now();
        calculateDuration();
        return this;
    }

    /**
     * 检查是否有警告
     */
    public boolean hasWarnings() {
        return warnings != null && !warnings.isEmpty();
    }

    /**
     * 检查是否有验证错误
     */
    public boolean hasValidationErrors() {
        return validationErrors != null && !validationErrors.isEmpty();
    }

    /**
     * 获取异常堆栈信息
     */
    private static String getStackTrace(Throwable throwable) {
        if (throwable == null) {
            return null;
        }
        
        java.io.StringWriter sw = new java.io.StringWriter();
        java.io.PrintWriter pw = new java.io.PrintWriter(sw);
        throwable.printStackTrace(pw);
        return sw.toString();
    }
}
