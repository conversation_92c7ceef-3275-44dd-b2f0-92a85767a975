# WTMS Vue.js 前端界面设计方案

## 1. 技术栈架构

### 1.1 核心技术栈
- **框架**: Vue.js 3.4+ (Composition API)
- **构建工具**: Vite 5.0+
- **UI组件库**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由管理**: Vue Router 4.2+
- **图表库**: ECharts 5.4+ / D3.js 7.8+
- **图形编辑**: Fabric.js 5.3+ / Konva.js 9.2+
- **HTTP客户端**: Axios 1.6+
- **CSS预处理**: Sass/SCSS
- **TypeScript**: 5.2+

### 1.2 项目结构
```
src/
├── api/                    # API接口
├── assets/                 # 静态资源
├── components/             # 公共组件
│   ├── common/            # 通用组件
│   ├── charts/            # 图表组件
│   └── workflow/          # 工作流组件
├── composables/           # 组合式函数
├── layouts/               # 布局组件
├── pages/                 # 页面组件
│   ├── dashboard/         # 工作台
│   ├── tasks/            # 任务管理
│   ├── workflow/         # 工作流
│   ├── evaluation/       # 质量评价
│   ├── team/             # 团队管理
│   └── analytics/        # 数据分析
├── router/               # 路由配置
├── stores/               # Pinia状态管理
├── styles/               # 样式文件
├── types/                # TypeScript类型定义
└── utils/                # 工具函数
```

## 2. 整体设计理念

### 2.1 设计原则
- **用户体验优先**：简洁直观的界面设计，降低学习成本
- **响应式设计**：支持桌面端、平板、手机端的完美适配
- **一致性设计**：统一的视觉语言和交互模式
- **可访问性**：支持键盘导航、屏幕阅读器等无障碍功能
- **性能优化**：快速加载、流畅交互的用户体验

### 2.2 视觉风格
- **色彩方案**：
  - 主色调：#409EFF（Element Plus蓝色，专业可信赖）
  - 成功色：#67C23A（绿色，成功状态）
  - 警告色：#E6A23C（橙色，警告状态）
  - 危险色：#F56C6C（红色，错误状态）
  - 信息色：#909399（灰色，信息状态）

- **字体系统**：
  - 中文：PingFang SC, Microsoft YaHei, 微软雅黑
  - 英文：Helvetica Neue, Helvetica, Arial, sans-serif
  - 代码：Consolas, Monaco, Courier New, monospace

## 3. 核心组件设计

### 3.1 布局组件 (Layout)

#### 主布局组件 (MainLayout.vue)
```vue
<template>
  <el-container class="main-layout">
    <!-- 顶部导航栏 -->
    <el-header class="main-header">
      <TopNavbar />
    </el-header>
    
    <el-container>
      <!-- 左侧菜单栏 -->
      <el-aside class="main-sidebar" :width="sidebarWidth">
        <SidebarMenu />
      </el-aside>
      
      <!-- 主内容区 -->
      <el-main class="main-content">
        <router-view />
      </el-main>
    </el-container>
    
    <!-- 底部状态栏 -->
    <el-footer class="main-footer">
      <StatusBar />
    </el-footer>
  </el-container>
</template>
```

#### 顶部导航栏 (TopNavbar.vue)
**组件功能：**
- Logo和系统名称展示
- 全局搜索框（支持任务、人员、文档搜索）
- 快速操作按钮（新建任务、通知等）
- 用户头像和下拉菜单

```vue
<template>
  <div class="top-navbar">
    <div class="navbar-left">
      <img src="/logo.png" alt="WTMS" class="logo" />
      <span class="system-name">工作任务管理平台</span>
    </div>
    
    <div class="navbar-center">
      <el-input
        v-model="searchKeyword"
        placeholder="搜索任务、人员、文档..."
        prefix-icon="Search"
        class="search-input"
        @keyup.enter="handleSearch"
      />
    </div>
    
    <div class="navbar-right">
      <el-button type="primary" icon="Plus" @click="createTask">
        新建任务
      </el-button>
      
      <el-badge :value="unreadCount" class="notification-badge">
        <el-button icon="Bell" circle @click="showNotifications" />
      </el-badge>
      
      <el-dropdown @command="handleUserCommand">
        <el-avatar :src="userInfo.avatar" :size="32" />
        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item command="profile">个人资料</el-dropdown-item>
            <el-dropdown-item command="settings">系统设置</el-dropdown-item>
            <el-dropdown-item command="help">帮助文档</el-dropdown-item>
            <el-dropdown-item divided command="logout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>
  </div>
</template>
```

### 3.2 任务管理组件

#### 任务列表组件 (TaskList.vue)
```vue
<template>
  <div class="task-list">
    <!-- 筛选工具栏 -->
    <div class="filter-toolbar">
      <el-row :gutter="16">
        <el-col :span="6">
          <el-select v-model="filters.status" placeholder="任务状态" clearable>
            <el-option label="全部" value="" />
            <el-option label="待开始" value="pending" />
            <el-option label="进行中" value="in_progress" />
            <el-option label="已完成" value="completed" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filters.priority" placeholder="优先级" clearable>
            <el-option label="高" value="5" />
            <el-option label="中" value="3" />
            <el-option label="低" value="1" />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-select v-model="filters.assigneeId" placeholder="负责人" clearable>
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.fullName"
              :value="user.id"
            />
          </el-select>
        </el-col>
        <el-col :span="6">
          <el-button type="primary" @click="refreshList">刷新</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-col>
      </el-row>
    </div>
    
    <!-- 视图切换 -->
    <div class="view-switcher">
      <el-radio-group v-model="viewMode">
        <el-radio-button label="table">列表视图</el-radio-button>
        <el-radio-button label="kanban">看板视图</el-radio-button>
        <el-radio-button label="gantt">甘特图</el-radio-button>
      </el-radio-group>
    </div>
    
    <!-- 任务表格 -->
    <el-table
      v-if="viewMode === 'table'"
      :data="tasks"
      v-loading="loading"
      @selection-change="handleSelectionChange"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column prop="taskCode" label="任务编号" width="120" />
      <el-table-column prop="title" label="任务标题" min-width="200" />
      <el-table-column prop="status" label="状态" width="100">
        <template #default="{ row }">
          <TaskStatusTag :status="row.status" />
        </template>
      </el-table-column>
      <el-table-column prop="priority" label="优先级" width="100">
        <template #default="{ row }">
          <PriorityTag :priority="row.priority" />
        </template>
      </el-table-column>
      <el-table-column prop="assignee" label="负责人" width="120">
        <template #default="{ row }">
          <UserAvatar :user="row.assignee" />
        </template>
      </el-table-column>
      <el-table-column prop="dueDate" label="截止日期" width="120">
        <template #default="{ row }">
          <DateDisplay :date="row.dueDate" />
        </template>
      </el-table-column>
      <el-table-column label="操作" width="150" fixed="right">
        <template #default="{ row }">
          <el-button size="small" @click="editTask(row)">编辑</el-button>
          <el-button size="small" type="primary" @click="viewTask(row)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 看板视图 -->
    <TaskKanban v-else-if="viewMode === 'kanban'" :tasks="tasks" />
    
    <!-- 甘特图视图 -->
    <TaskGantt v-else-if="viewMode === 'gantt'" :tasks="tasks" />
    
    <!-- 分页 -->
    <el-pagination
      v-model:current-page="pagination.page"
      v-model:page-size="pagination.pageSize"
      :total="pagination.total"
      :page-sizes="[10, 20, 50, 100]"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>
```

#### 任务看板组件 (TaskKanban.vue)
```vue
<template>
  <div class="task-kanban">
    <div class="kanban-columns">
      <div
        v-for="column in columns"
        :key="column.status"
        class="kanban-column"
        @drop="handleDrop($event, column.status)"
        @dragover.prevent
      >
        <div class="column-header">
          <h3>{{ column.title }}</h3>
          <el-badge :value="getTaskCount(column.status)" />
        </div>
        
        <div class="column-content">
          <TaskCard
            v-for="task in getTasksByStatus(column.status)"
            :key="task.id"
            :task="task"
            draggable="true"
            @dragstart="handleDragStart($event, task)"
            @click="viewTask(task)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import type { Task } from '@/types/task'

interface Props {
  tasks: Task[]
}

const props = defineProps<Props>()

const columns = [
  { status: 'pending', title: '待开始', color: '#909399' },
  { status: 'in_progress', title: '进行中', color: '#409EFF' },
  { status: 'review', title: '待审核', color: '#E6A23C' },
  { status: 'completed', title: '已完成', color: '#67C23A' }
]

const getTasksByStatus = (status: string) => {
  return props.tasks.filter(task => task.status === status)
}

const getTaskCount = (status: string) => {
  return getTasksByStatus(status).length
}
</script>
```

### 3.3 工作流设计器组件

#### 工作流设计器 (WorkflowDesigner.vue)
```vue
<template>
  <div class="workflow-designer">
    <!-- 工具栏 -->
    <div class="designer-toolbar">
      <el-button-group>
        <el-button icon="Document" @click="saveWorkflow">保存</el-button>
        <el-button icon="Upload" @click="deployWorkflow">发布</el-button>
        <el-button icon="View" @click="previewWorkflow">预览</el-button>
      </el-button-group>
      
      <el-button-group>
        <el-button icon="Download" @click="exportWorkflow">导出</el-button>
        <el-button icon="Upload" @click="importWorkflow">导入</el-button>
      </el-button-group>
    </div>
    
    <div class="designer-content">
      <!-- 组件面板 -->
      <div class="component-panel">
        <h4>流程节点</h4>
        <div class="node-list">
          <div
            v-for="nodeType in nodeTypes"
            :key="nodeType.type"
            class="node-item"
            draggable="true"
            @dragstart="handleNodeDragStart($event, nodeType)"
          >
            <el-icon><component :is="nodeType.icon" /></el-icon>
            <span>{{ nodeType.name }}</span>
          </div>
        </div>
      </div>
      
      <!-- 画布区域 -->
      <div class="canvas-area">
        <div
          ref="canvasRef"
          class="workflow-canvas"
          @drop="handleCanvasDrop"
          @dragover.prevent
        >
          <!-- 工作流节点和连线将在这里渲染 -->
        </div>
      </div>
      
      <!-- 属性面板 -->
      <div class="property-panel">
        <h4>节点属性</h4>
        <div v-if="selectedNode" class="node-properties">
          <el-form :model="selectedNode" label-width="80px">
            <el-form-item label="节点名称">
              <el-input v-model="selectedNode.name" />
            </el-form-item>
            <el-form-item label="负责人">
              <el-select v-model="selectedNode.assignee" placeholder="选择负责人">
                <el-option
                  v-for="user in users"
                  :key="user.id"
                  :label="user.fullName"
                  :value="user.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item label="截止时间">
              <el-input v-model="selectedNode.dueDate" placeholder="如：3d, 1w" />
            </el-form-item>
          </el-form>
        </div>
      </div>
    </div>
  </div>
</template>
```

### 3.4 数据分析组件

#### 数据仪表板 (AnalyticsDashboard.vue)
```vue
<template>
  <div class="analytics-dashboard">
    <!-- 关键指标卡片 -->
    <el-row :gutter="16" class="metrics-cards">
      <el-col :span="6">
        <MetricCard
          title="任务完成率"
          :value="metrics.completionRate"
          suffix="%"
          trend="up"
          color="#67C23A"
        />
      </el-col>
      <el-col :span="6">
        <MetricCard
          title="平均处理时间"
          :value="metrics.avgProcessTime"
          suffix="天"
          trend="down"
          color="#409EFF"
        />
      </el-col>
      <el-col :span="6">
        <MetricCard
          title="质量得分"
          :value="metrics.qualityScore"
          suffix="分"
          trend="up"
          color="#E6A23C"
        />
      </el-col>
      <el-col :span="6">
        <MetricCard
          title="待办任务"
          :value="metrics.pendingTasks"
          suffix="个"
          trend="stable"
          color="#909399"
        />
      </el-col>
    </el-row>
    
    <!-- 图表区域 -->
    <el-row :gutter="16" class="charts-section">
      <el-col :span="12">
        <el-card title="任务完成趋势">
          <TrendChart :data="trendData" />
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card title="部门效率对比">
          <BarChart :data="departmentData" />
        </el-card>
      </el-col>
    </el-row>
    
    <el-row :gutter="16" class="charts-section">
      <el-col :span="8">
        <el-card title="任务状态分布">
          <PieChart :data="statusDistribution" />
        </el-card>
      </el-col>
      <el-col :span="16">
        <el-card title="个人绩效排行">
          <RankingList :data="performanceRanking" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>
```

## 4. 状态管理 (Pinia)

### 4.1 用户状态管理
```typescript
// stores/user.ts
import { defineStore } from 'pinia'
import type { User } from '@/types/user'

export const useUserStore = defineStore('user', {
  state: () => ({
    currentUser: null as User | null,
    token: localStorage.getItem('token') || '',
    permissions: [] as string[]
  }),
  
  getters: {
    isLoggedIn: (state) => !!state.token,
    hasPermission: (state) => (permission: string) => 
      state.permissions.includes(permission)
  },
  
  actions: {
    async login(credentials: LoginCredentials) {
      const response = await api.auth.login(credentials)
      this.token = response.data.token
      this.currentUser = response.data.user
      this.permissions = response.data.user.role.permissions
      localStorage.setItem('token', this.token)
    },
    
    logout() {
      this.token = ''
      this.currentUser = null
      this.permissions = []
      localStorage.removeItem('token')
    }
  }
})
```

### 4.2 任务状态管理
```typescript
// stores/task.ts
import { defineStore } from 'pinia'
import type { Task, TaskFilters } from '@/types/task'

export const useTaskStore = defineStore('task', {
  state: () => ({
    tasks: [] as Task[],
    currentTask: null as Task | null,
    filters: {
      status: '',
      priority: '',
      assigneeId: '',
      search: ''
    } as TaskFilters,
    pagination: {
      page: 1,
      pageSize: 20,
      total: 0
    },
    loading: false
  }),
  
  actions: {
    async fetchTasks() {
      this.loading = true
      try {
        const response = await api.tasks.list({
          ...this.filters,
          ...this.pagination
        })
        this.tasks = response.data.data
        this.pagination.total = response.data.pagination.total
      } finally {
        this.loading = false
      }
    },
    
    async createTask(taskData: CreateTaskData) {
      const response = await api.tasks.create(taskData)
      this.tasks.unshift(response.data)
      return response.data
    },
    
    async updateTask(id: string, taskData: UpdateTaskData) {
      const response = await api.tasks.update(id, taskData)
      const index = this.tasks.findIndex(task => task.id === id)
      if (index !== -1) {
        this.tasks[index] = response.data
      }
      return response.data
    }
  }
})
```

## 5. 路由配置

```typescript
// router/index.ts
import { createRouter, createWebHistory } from 'vue-router'
import { useUserStore } from '@/stores/user'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/pages/auth/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    meta: { requiresAuth: true },
    children: [
      {
        path: '',
        name: 'Dashboard',
        component: () => import('@/pages/dashboard/Dashboard.vue')
      },
      {
        path: '/tasks',
        name: 'Tasks',
        component: () => import('@/pages/tasks/TaskList.vue')
      },
      {
        path: '/tasks/:id',
        name: 'TaskDetail',
        component: () => import('@/pages/tasks/TaskDetail.vue')
      },
      {
        path: '/workflow',
        name: 'Workflow',
        component: () => import('@/pages/workflow/WorkflowList.vue')
      },
      {
        path: '/workflow/designer/:id?',
        name: 'WorkflowDesigner',
        component: () => import('@/pages/workflow/WorkflowDesigner.vue')
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 路由守卫
router.beforeEach((to, from, next) => {
  const userStore = useUserStore()
  
  if (to.meta.requiresAuth && !userStore.isLoggedIn) {
    next('/login')
  } else {
    next()
  }
})

export default router
```

## 6. 性能优化策略

### 6.1 组件懒加载
- 使用动态导入实现路由级别的代码分割
- 大型组件（如图表、编辑器）采用异步加载
- 使用Suspense组件处理加载状态

### 6.2 虚拟滚动
- 大列表使用虚拟滚动技术
- 表格组件支持虚拟滚动
- 无限滚动加载数据

### 6.3 缓存策略
- HTTP请求缓存
- 组件级别缓存
- 路由缓存优化

---

**注：** 本Vue.js前端设计方案基于最新的Vue 3 Composition API和Element Plus组件库，提供了完整的组件设计和状态管理方案。具体实现时可根据实际需求进行调整优化。
