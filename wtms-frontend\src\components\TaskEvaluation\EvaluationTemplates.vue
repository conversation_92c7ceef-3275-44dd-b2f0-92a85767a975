<template>
  <div class="evaluation-templates">
    <!-- 操作栏 -->
    <div class="action-bar">
      <div class="action-left">
        <el-button type="primary" @click="handleCreateTemplate">
          <el-icon><Plus /></el-icon>
          新建模板
        </el-button>
        <el-button @click="handleImportTemplate">
          <el-icon><Upload /></el-icon>
          导入模板
        </el-button>
      </div>
      <div class="action-right">
        <el-input
          v-model="searchKeyword"
          placeholder="搜索模板..."
          clearable
          style="width: 200px"
          @input="handleSearch"
        >
          <template #prefix>
            <el-icon><Search /></el-icon>
          </template>
        </el-input>
      </div>
    </div>

    <!-- 模板分类 -->
    <el-card class="category-card" shadow="never">
      <el-tabs v-model="activeCategory" @tab-change="handleCategoryChange">
        <el-tab-pane label="全部模板" name="all" />
        <el-tab-pane label="通用模板" name="general" />
        <el-tab-pane label="技术评价" name="technical" />
        <el-tab-pane label="管理评价" name="management" />
        <el-tab-pane label="客户服务" name="service" />
        <el-tab-pane label="自定义模板" name="custom" />
      </el-tabs>
    </el-card>

    <!-- 模板列表 -->
    <div v-loading="loading" class="templates-grid">
      <div
        v-for="template in filteredTemplates"
        :key="template.id"
        class="template-card"
        @click="handlePreviewTemplate(template)"
      >
        <div class="template-header">
          <div class="template-icon" :class="template.category">
            <el-icon><Document /></el-icon>
          </div>
          <div class="template-actions">
            <el-dropdown @command="(command) => handleTemplateAction(command, template)">
              <el-button size="small" text>
                <el-icon><MoreFilled /></el-icon>
              </el-button>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="use">使用模板</el-dropdown-item>
                  <el-dropdown-item command="edit">编辑模板</el-dropdown-item>
                  <el-dropdown-item command="copy">复制模板</el-dropdown-item>
                  <el-dropdown-item command="export">导出模板</el-dropdown-item>
                  <el-dropdown-item command="delete" divided>删除模板</el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>

        <div class="template-content">
          <h3 class="template-title">{{ template.name }}</h3>
          <p class="template-description">{{ template.description }}</p>
          
          <div class="template-meta">
            <div class="meta-item">
              <el-icon><Star /></el-icon>
              <span>{{ template.dimensions?.length || 0 }}个维度</span>
            </div>
            <div class="meta-item">
              <el-icon><User /></el-icon>
              <span>{{ template.usageCount || 0 }}次使用</span>
            </div>
            <div class="meta-item">
              <el-icon><Clock /></el-icon>
              <span>{{ formatDate(template.updatedAt) }}</span>
            </div>
          </div>

          <div class="template-tags">
            <el-tag
              v-for="tag in template.tags"
              :key="tag"
              size="small"
              style="margin-right: 4px;"
            >
              {{ tag }}
            </el-tag>
          </div>

          <div class="template-footer">
            <div class="template-category">
              <el-tag :type="getCategoryTagType(template.category)" size="small">
                {{ getCategoryText(template.category) }}
              </el-tag>
            </div>
            <div class="template-rating">
              <el-rate
                :model-value="template.rating || 0"
                disabled
                size="small"
                show-score
                text-color="#ff9900"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模板预览对话框 -->
    <el-dialog
      v-model="showPreviewDialog"
      :title="currentTemplate?.name"
      width="70%"
      :close-on-click-modal="false"
    >
      <div v-if="currentTemplate" class="template-preview">
        <div class="preview-header">
          <div class="preview-info">
            <h3>{{ currentTemplate.name }}</h3>
            <p>{{ currentTemplate.description }}</p>
          </div>
          <div class="preview-actions">
            <el-button type="primary" @click="handleUseTemplate(currentTemplate)">
              使用此模板
            </el-button>
          </div>
        </div>

        <div class="preview-content">
          <h4>评价维度</h4>
          <div class="dimensions-list">
            <div
              v-for="dimension in currentTemplate.dimensions"
              :key="dimension.id"
              class="dimension-item"
            >
              <div class="dimension-header">
                <div class="dimension-name">{{ dimension.name }}</div>
                <div class="dimension-weight">权重: {{ dimension.weight }}</div>
              </div>
              <div class="dimension-description">{{ dimension.description }}</div>
              <div class="dimension-criteria">
                <strong>评价标准:</strong> {{ dimension.criteria }}
              </div>
              <div class="dimension-score-range">
                评分范围: {{ dimension.minScore }} - {{ dimension.maxScore }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 模板编辑对话框 -->
    <el-dialog
      v-model="showEditDialog"
      :title="editMode === 'create' ? '新建模板' : '编辑模板'"
      width="80%"
      :close-on-click-modal="false"
    >
      <TemplateEditor
        v-if="showEditDialog"
        :template="currentTemplate"
        :mode="editMode"
        @save="handleTemplateSave"
        @cancel="showEditDialog = false"
      />
    </el-dialog>

    <!-- 使用模板对话框 -->
    <el-dialog
      v-model="showUseDialog"
      title="使用模板创建评价"
      width="80%"
      :close-on-click-modal="false"
    >
      <EvaluationForm
        v-if="showUseDialog"
        v-model="showUseDialog"
        :template="selectedTemplate"
        @success="handleEvaluationSuccess"
      />
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, Upload, Search, Document, MoreFilled, Star, User, Clock 
} from '@element-plus/icons-vue'
import EvaluationForm from './EvaluationForm.vue'
import TemplateEditor from './TemplateEditor.vue'

// 响应式数据
const loading = ref(false)
const searchKeyword = ref('')
const activeCategory = ref('all')
const showPreviewDialog = ref(false)
const showEditDialog = ref(false)
const showUseDialog = ref(false)
const editMode = ref<'create' | 'edit'>('create')
const currentTemplate = ref<any>()
const selectedTemplate = ref<any>()

// 模拟模板数据
const templates = ref([
  {
    id: '1',
    name: '通用员工评价模板',
    description: '适用于大多数岗位的通用评价模板，包含工作质量、效率、沟通等基础维度',
    category: 'general',
    tags: ['通用', '基础', '推荐'],
    rating: 4.8,
    usageCount: 156,
    dimensions: [
      {
        id: '1',
        name: '工作质量',
        description: '评价工作成果的质量水平',
        criteria: '工作准确性、完整性、符合标准程度',
        weight: 0.3,
        minScore: 0,
        maxScore: 100
      },
      {
        id: '2',
        name: '工作效率',
        description: '评价工作完成的速度和效率',
        criteria: '任务完成时间、工作节奏、时间管理',
        weight: 0.25,
        minScore: 0,
        maxScore: 100
      },
      {
        id: '3',
        name: '沟通协作',
        description: '评价团队协作和沟通能力',
        criteria: '团队合作、信息传递、协调配合',
        weight: 0.25,
        minScore: 0,
        maxScore: 100
      },
      {
        id: '4',
        name: '学习创新',
        description: '评价学习能力和创新思维',
        criteria: '学习主动性、创新意识、改进建议',
        weight: 0.2,
        minScore: 0,
        maxScore: 100
      }
    ],
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-20T15:30:00Z'
  },
  {
    id: '2',
    name: '技术人员评价模板',
    description: '专门针对技术岗位设计的评价模板，重点关注技术能力和代码质量',
    category: 'technical',
    tags: ['技术', '开发', '专业'],
    rating: 4.6,
    usageCount: 89,
    dimensions: [
      {
        id: '1',
        name: '技术能力',
        description: '评价技术水平和专业能力',
        criteria: '技术深度、广度、解决问题能力',
        weight: 0.4,
        minScore: 0,
        maxScore: 100
      },
      {
        id: '2',
        name: '代码质量',
        description: '评价代码编写质量',
        criteria: '代码规范、可读性、可维护性',
        weight: 0.3,
        minScore: 0,
        maxScore: 100
      },
      {
        id: '3',
        name: '项目贡献',
        description: '评价在项目中的贡献度',
        criteria: '功能实现、bug修复、优化改进',
        weight: 0.3,
        minScore: 0,
        maxScore: 100
      }
    ],
    createdAt: '2024-01-10T09:00:00Z',
    updatedAt: '2024-01-18T14:20:00Z'
  },
  {
    id: '3',
    name: '管理人员评价模板',
    description: '适用于管理岗位的评价模板，重点评价领导力和管理能力',
    category: 'management',
    tags: ['管理', '领导力', '团队'],
    rating: 4.7,
    usageCount: 67,
    dimensions: [
      {
        id: '1',
        name: '领导力',
        description: '评价领导和指导能力',
        criteria: '团队激励、决策能力、影响力',
        weight: 0.35,
        minScore: 0,
        maxScore: 100
      },
      {
        id: '2',
        name: '团队管理',
        description: '评价团队管理效果',
        criteria: '团队建设、人员发展、绩效管理',
        weight: 0.35,
        minScore: 0,
        maxScore: 100
      },
      {
        id: '3',
        name: '战略思维',
        description: '评价战略规划和执行能力',
        criteria: '战略制定、执行力、前瞻性',
        weight: 0.3,
        minScore: 0,
        maxScore: 100
      }
    ],
    createdAt: '2024-01-12T11:00:00Z',
    updatedAt: '2024-01-22T16:45:00Z'
  }
])

// 计算属性
const filteredTemplates = computed(() => {
  let result = templates.value

  // 按分类筛选
  if (activeCategory.value !== 'all') {
    result = result.filter(template => template.category === activeCategory.value)
  }

  // 按关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    result = result.filter(template =>
      template.name.toLowerCase().includes(keyword) ||
      template.description.toLowerCase().includes(keyword) ||
      template.tags.some(tag => tag.toLowerCase().includes(keyword))
    )
  }

  return result
})

// 方法
const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleCategoryChange = (category: string) => {
  activeCategory.value = category
}

const handleCreateTemplate = () => {
  editMode.value = 'create'
  currentTemplate.value = null
  showEditDialog.value = true
}

const handleImportTemplate = () => {
  ElMessage.info('导入模板功能开发中...')
}

const handlePreviewTemplate = (template: any) => {
  currentTemplate.value = template
  showPreviewDialog.value = true
}

const handleTemplateAction = async (command: string, template: any) => {
  switch (command) {
    case 'use':
      handleUseTemplate(template)
      break
    case 'edit':
      editMode.value = 'edit'
      currentTemplate.value = template
      showEditDialog.value = true
      break
    case 'copy':
      handleCopyTemplate(template)
      break
    case 'export':
      handleExportTemplate(template)
      break
    case 'delete':
      await handleDeleteTemplate(template)
      break
  }
}

const handleUseTemplate = (template: any) => {
  selectedTemplate.value = template
  showUseDialog.value = true
  showPreviewDialog.value = false
}

const handleCopyTemplate = (template: any) => {
  const newTemplate = {
    ...template,
    id: Date.now().toString(),
    name: `${template.name} (副本)`,
    usageCount: 0,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  templates.value.push(newTemplate)
  ElMessage.success('模板复制成功')
}

const handleExportTemplate = (template: any) => {
  const dataStr = JSON.stringify(template, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  const link = document.createElement('a')
  link.href = url
  link.download = `${template.name}.json`
  link.click()
  URL.revokeObjectURL(url)
  ElMessage.success('模板导出成功')
}

const handleDeleteTemplate = async (template: any) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除模板"${template.name}"吗？此操作不可撤销。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const index = templates.value.findIndex(t => t.id === template.id)
    if (index > -1) {
      templates.value.splice(index, 1)
      ElMessage.success('模板删除成功')
    }
  } catch (error) {
    // 用户取消删除
  }
}

const handleTemplateSave = (template: any) => {
  if (editMode.value === 'create') {
    template.id = Date.now().toString()
    template.usageCount = 0
    template.rating = 0
    template.createdAt = new Date().toISOString()
    template.updatedAt = new Date().toISOString()
    templates.value.push(template)
    ElMessage.success('模板创建成功')
  } else {
    const index = templates.value.findIndex(t => t.id === template.id)
    if (index > -1) {
      template.updatedAt = new Date().toISOString()
      templates.value[index] = template
      ElMessage.success('模板更新成功')
    }
  }
  showEditDialog.value = false
}

const handleEvaluationSuccess = () => {
  ElMessage.success('评价创建成功')
  // 增加模板使用次数
  if (selectedTemplate.value) {
    selectedTemplate.value.usageCount++
  }
}

// 工具方法
const getCategoryText = (category: string) => {
  const categoryMap: Record<string, string> = {
    general: '通用',
    technical: '技术',
    management: '管理',
    service: '服务',
    custom: '自定义'
  }
  return categoryMap[category] || category
}

const getCategoryTagType = (category: string) => {
  const typeMap: Record<string, string> = {
    general: '',
    technical: 'success',
    management: 'warning',
    service: 'info',
    custom: 'danger'
  }
  return typeMap[category] || ''
}

const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 生命周期
onMounted(() => {
  // 初始化数据
})
</script>

<style scoped>
.evaluation-templates {
  padding: 20px;
}

.action-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.action-left {
  display: flex;
  gap: 12px;
}

.category-card {
  margin-bottom: 20px;
}

.templates-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.template-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s;
  background: white;
}

.template-card:hover {
  border-color: #409eff;
  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
  transform: translateY(-2px);
}

.template-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.template-icon {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: white;
}

.template-icon.general {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.template-icon.technical {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.template-icon.management {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.template-icon.service {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.template-icon.custom {
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
}

.template-content {
  flex: 1;
}

.template-title {
  margin: 0 0 8px 0;
  font-size: 18px;
  font-weight: 600;
  color: #303133;
}

.template-description {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #606266;
  line-height: 1.5;
}

.template-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 12px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #909399;
}

.template-tags {
  margin-bottom: 16px;
  min-height: 24px;
}

.template-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.template-preview {
  max-height: 600px;
  overflow-y: auto;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.preview-info h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.preview-info p {
  margin: 0;
  color: #606266;
}

.preview-content h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.dimensions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dimension-item {
  padding: 16px;
  border: 1px solid #ebeef5;
  border-radius: 8px;
  background: #fafafa;
}

.dimension-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.dimension-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.dimension-weight {
  font-size: 12px;
  color: #909399;
}

.dimension-description {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.dimension-criteria {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
}

.dimension-score-range {
  font-size: 12px;
  color: #909399;
}

:deep(.el-tabs__content) {
  padding: 0;
}
</style>
