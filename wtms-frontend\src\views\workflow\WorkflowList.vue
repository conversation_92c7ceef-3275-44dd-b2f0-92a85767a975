<template>
  <div class="workflow-page">
    <div class="page-header">
      <h1>工作流管理</h1>
      <el-button type="primary">
        <el-icon><Plus /></el-icon>
        新建工作流
      </el-button>
    </div>

    <el-card>
      <el-empty description="工作流管理功能开发中..." />
    </el-card>
  </div>
</template>

<script setup lang="ts">
// 占位组件
</script>

<style lang="scss" scoped>
.workflow-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
}
</style>
