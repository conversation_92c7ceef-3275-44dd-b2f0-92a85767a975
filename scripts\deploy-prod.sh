#!/bin/bash

# WTMS生产环境部署脚本

set -e

echo "🚀 部署WTMS生产环境..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查必要文件
if [ ! -f "docker-compose.yml" ]; then
    echo "❌ docker-compose.yml文件不存在"
    exit 1
fi

# 构建前端项目
echo "🏗️  构建前端项目..."
cd wtms-frontend
if [ ! -f "package.json" ]; then
    echo "❌ 前端项目package.json不存在"
    exit 1
fi

npm ci
npm run build
cd ..

# 构建后端项目
echo "🏗️  构建后端项目..."
cd wtms-backend
if [ ! -f "pom.xml" ]; then
    echo "❌ 后端项目pom.xml不存在"
    exit 1
fi

mvn clean package -DskipTests
cd ..

# 停止现有服务
echo "🛑 停止现有服务..."
docker-compose down --remove-orphans

# 构建并启动服务
echo "🐳 构建并启动服务..."
docker-compose up -d --build

# 等待服务启动
echo "⏳ 等待服务启动..."
sleep 30

# 健康检查
echo "🔍 执行健康检查..."
check_service() {
    local service=$1
    local url=$2
    local max_attempts=30
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -f -s $url > /dev/null; then
            echo "✅ $service 服务正常"
            return 0
        fi
        echo "⏳ 等待 $service 服务启动... ($attempt/$max_attempts)"
        sleep 2
        attempt=$((attempt + 1))
    done
    
    echo "❌ $service 服务启动失败"
    return 1
}

# 检查各个服务
check_service "前端" "http://localhost:80/health" || exit 1
check_service "后端" "http://localhost:8080/actuator/health" || exit 1

echo ""
echo "🎉 生产环境部署完成！"
echo ""
echo "📊 服务访问地址："
echo "   应用首页:    http://localhost"
echo "   API文档:     http://localhost/swagger-ui.html"
echo "   健康检查:    http://localhost/actuator/health"
echo ""
echo "📝 查看服务状态: docker-compose ps"
echo "📝 查看服务日志: docker-compose logs -f [service-name]"
echo "🛑 停止服务:     docker-compose down"
