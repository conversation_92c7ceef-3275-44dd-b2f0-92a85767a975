package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务附件实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("task_attachments")
@Schema(description = "任务附件实体")
public class TaskAttachment implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "附件ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "任务ID")
    @TableField("task_id")
    private String taskId;

    @Schema(description = "评论ID")
    @TableField("comment_id")
    private String commentId;

    @Schema(description = "上传者ID")
    @TableField("uploader_id")
    private String uploaderId;

    @Schema(description = "文件名")
    @TableField("file_name")
    private String fileName;

    @Schema(description = "原始文件名")
    @TableField("original_name")
    private String originalName;

    @Schema(description = "文件路径")
    @TableField("file_path")
    private String filePath;

    @Schema(description = "文件URL")
    @TableField("file_url")
    private String fileUrl;

    @Schema(description = "文件大小")
    @TableField("file_size")
    private Long fileSize;

    @Schema(description = "文件类型")
    @TableField("file_type")
    private String fileType;

    @Schema(description = "MIME类型")
    @TableField("mime_type")
    private String mimeType;

    @Schema(description = "文件扩展名")
    @TableField("file_extension")
    private String fileExtension;

    @Schema(description = "文件MD5")
    @TableField("file_md5")
    private String fileMd5;

    @Schema(description = "缩略图路径")
    @TableField("thumbnail_path")
    private String thumbnailPath;

    @Schema(description = "缩略图URL")
    @TableField("thumbnail_url")
    private String thumbnailUrl;

    @Schema(description = "下载次数")
    @TableField("download_count")
    private Integer downloadCount;

    @Schema(description = "是否公开")
    @TableField("is_public")
    private Boolean isPublic;

    @Schema(description = "状态")
    @TableField("status")
    private String status;

    @Schema(description = "存储类型")
    @TableField("storage_type")
    private String storageType;

    @Schema(description = "存储配置")
    @TableField("storage_config")
    private String storageConfig;

    @Schema(description = "过期时间")
    @TableField("expires_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "任务信息")
    @TableField(exist = false)
    private Task task;

    @Schema(description = "评论信息")
    @TableField(exist = false)
    private TaskComment comment;

    @Schema(description = "上传者信息")
    @TableField(exist = false)
    private User uploader;

    /**
     * 文件类型枚举
     */
    public enum FileType {
        IMAGE("image", "图片"),
        DOCUMENT("document", "文档"),
        VIDEO("video", "视频"),
        AUDIO("audio", "音频"),
        ARCHIVE("archive", "压缩包"),
        OTHER("other", "其他");

        private final String code;
        private final String description;

        FileType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static FileType fromMimeType(String mimeType) {
            if (mimeType == null) return OTHER;
            
            if (mimeType.startsWith("image/")) return IMAGE;
            if (mimeType.startsWith("video/")) return VIDEO;
            if (mimeType.startsWith("audio/")) return AUDIO;
            if (mimeType.contains("pdf") || mimeType.contains("word") || 
                mimeType.contains("excel") || mimeType.contains("powerpoint") ||
                mimeType.contains("text")) return DOCUMENT;
            if (mimeType.contains("zip") || mimeType.contains("rar") || 
                mimeType.contains("tar") || mimeType.contains("gz")) return ARCHIVE;
            
            return OTHER;
        }
    }

    /**
     * 附件状态枚举
     */
    public enum Status {
        ACTIVE("active", "正常"),
        PROCESSING("processing", "处理中"),
        FAILED("failed", "失败"),
        EXPIRED("expired", "已过期"),
        DELETED("deleted", "已删除");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 存储类型枚举
     */
    public enum StorageType {
        LOCAL("local", "本地存储"),
        OSS("oss", "阿里云OSS"),
        COS("cos", "腾讯云COS"),
        S3("s3", "AWS S3"),
        MINIO("minio", "MinIO");

        private final String code;
        private final String description;

        StorageType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查是否为公开附件
     */
    public boolean isPublic() {
        return Boolean.TRUE.equals(this.isPublic);
    }

    /**
     * 检查是否为图片文件
     */
    public boolean isImage() {
        return FileType.IMAGE.getCode().equals(this.fileType);
    }

    /**
     * 检查是否为文档文件
     */
    public boolean isDocument() {
        return FileType.DOCUMENT.getCode().equals(this.fileType);
    }

    /**
     * 检查是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(LocalDateTime.now());
    }

    /**
     * 检查是否可下载
     */
    public boolean isDownloadable() {
        return Status.ACTIVE.getCode().equals(this.status) && !isExpired();
    }

    /**
     * 获取文件大小的可读格式
     */
    public String getReadableFileSize() {
        if (fileSize == null || fileSize <= 0) {
            return "0 B";
        }
        
        String[] units = {"B", "KB", "MB", "GB", "TB"};
        int unitIndex = 0;
        double size = fileSize.doubleValue();
        
        while (size >= 1024 && unitIndex < units.length - 1) {
            size /= 1024;
            unitIndex++;
        }
        
        return String.format("%.1f %s", size, units[unitIndex]);
    }
}
