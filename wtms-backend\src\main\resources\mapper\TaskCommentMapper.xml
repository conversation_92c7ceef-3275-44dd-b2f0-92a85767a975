<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.wtms.mapper.TaskCommentMapper">

    <!-- 评论结果映射 -->
    <resultMap id="TaskCommentResultMap" type="com.wtms.entity.TaskComment">
        <id column="id" property="id"/>
        <result column="task_id" property="taskId"/>
        <result column="parent_id" property="parentId"/>
        <result column="commenter_id" property="commenterId"/>
        <result column="content" property="content"/>
        <result column="comment_type" property="commentType"/>
        <result column="level" property="level"/>
        <result column="path" property="path"/>
        <result column="is_pinned" property="isPinned"/>
        <result column="is_private" property="isPrivate"/>
        <result column="like_count" property="likeCount"/>
        <result column="reply_count" property="replyCount"/>
        <result column="status" property="status"/>
        <result column="ip_address" property="ipAddress"/>
        <result column="user_agent" property="userAgent"/>
        <result column="created_at" property="createdAt"/>
        <result column="updated_at" property="updatedAt"/>
        <result column="deleted_at" property="deletedAt"/>
    </resultMap>

    <!-- 评论树结构映射 -->
    <resultMap id="CommentTreeResultMap" type="com.wtms.entity.TaskComment" extends="TaskCommentResultMap">
        <association property="commenter" javaType="com.wtms.entity.User">
            <id column="commenter_id" property="id"/>
            <result column="commenter_username" property="username"/>
            <result column="commenter_full_name" property="fullName"/>
            <result column="commenter_avatar" property="avatarUrl"/>
            <result column="commenter_department_id" property="departmentId"/>
            <association property="department" javaType="com.wtms.entity.Department">
                <id column="commenter_department_id" property="id"/>
                <result column="commenter_department_name" property="name"/>
                <result column="commenter_department_code" property="code"/>
            </association>
        </association>
        <collection property="attachments" ofType="com.wtms.entity.TaskAttachment">
            <id column="attachment_id" property="id"/>
            <result column="attachment_file_name" property="fileName"/>
            <result column="attachment_original_name" property="originalName"/>
            <result column="attachment_file_size" property="fileSize"/>
            <result column="attachment_file_type" property="fileType"/>
            <result column="attachment_file_url" property="fileUrl"/>
            <result column="attachment_thumbnail_url" property="thumbnailUrl"/>
        </collection>
        <collection property="children" ofType="com.wtms.entity.TaskComment" 
                   column="id" select="selectByParentId"/>
    </resultMap>

    <!-- 根据任务ID查询评论（树形结构） -->
    <select id="selectCommentsByTaskId" resultMap="CommentTreeResultMap">
        SELECT
            c.*,
            u.username as commenter_username,
            u.full_name as commenter_full_name,
            u.avatar_url as commenter_avatar,
            u.department_id as commenter_department_id,
            d.name as commenter_department_name,
            d.code as commenter_department_code,
            a.id as attachment_id,
            a.file_name as attachment_file_name,
            a.original_name as attachment_original_name,
            a.file_size as attachment_file_size,
            a.file_type as attachment_file_type,
            a.file_url as attachment_file_url,
            a.thumbnail_url as attachment_thumbnail_url
        FROM task_comments c
        LEFT JOIN users u ON c.commenter_id = u.id
        LEFT JOIN departments d ON u.department_id = d.id
        LEFT JOIN task_attachments a ON c.id = a.comment_id AND a.deleted_at IS NULL
        WHERE c.task_id = #{taskId}
          AND c.parent_id IS NULL
          AND c.deleted_at IS NULL
        ORDER BY c.is_pinned DESC, c.created_at DESC
    </select>

    <!-- 根据任务ID查询评论（分页） -->
    <select id="selectCommentsByTaskIdWithPage" resultMap="CommentTreeResultMap">
        SELECT
            c.*,
            u.username as commenter_username,
            u.full_name as commenter_full_name,
            u.avatar_url as commenter_avatar,
            u.department_id as commenter_department_id,
            d.name as commenter_department_name,
            d.code as commenter_department_code
        FROM task_comments c
        LEFT JOIN users u ON c.commenter_id = u.id
        LEFT JOIN departments d ON u.department_id = d.id
        WHERE c.task_id = #{taskId}
          AND c.deleted_at IS NULL
        <if test="commentType != null and commentType != ''">
            AND c.comment_type = #{commentType}
        </if>
        <if test="status != null and status != ''">
            AND c.status = #{status}
        </if>
        ORDER BY c.is_pinned DESC, c.created_at DESC
    </select>

    <!-- 根据评论者ID查询评论 -->
    <select id="selectCommentsByCommenterId" resultMap="TaskCommentResultMap">
        SELECT c.*, t.title as task_title
        FROM task_comments c
        LEFT JOIN tasks t ON c.task_id = t.id
        WHERE c.commenter_id = #{commenterId} 
          AND c.deleted_at IS NULL
        <if test="status != null and status != ''">
            AND c.status = #{status}
        </if>
        ORDER BY c.created_at DESC
    </select>

    <!-- 搜索评论 -->
    <select id="searchComments" resultMap="CommentTreeResultMap">
        SELECT
            c.*,
            u.username as commenter_username,
            u.full_name as commenter_full_name,
            u.avatar_url as commenter_avatar,
            u.department_id as commenter_department_id,
            d.name as commenter_department_name,
            d.code as commenter_department_code
        FROM task_comments c
        LEFT JOIN users u ON c.commenter_id = u.id
        LEFT JOIN departments d ON u.department_id = d.id
        WHERE c.deleted_at IS NULL
        <if test="taskId != null and taskId != ''">
            AND c.task_id = #{taskId}
        </if>
        <if test="keyword != null and keyword != ''">
            AND c.content LIKE CONCAT('%', #{keyword}, '%')
        </if>
        <if test="commenterId != null and commenterId != ''">
            AND c.commenter_id = #{commenterId}
        </if>
        <if test="commentType != null and commentType != ''">
            AND c.comment_type = #{commentType}
        </if>
        <if test="status != null and status != ''">
            AND c.status = #{status}
        </if>
        ORDER BY c.is_pinned DESC, c.created_at DESC
    </select>

    <!-- 批量删除评论 -->
    <update id="batchDeleteComments">
        UPDATE task_comments 
        SET deleted_at = NOW(), updated_at = NOW()
        WHERE id IN
        <foreach collection="commentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 批量更新评论状态 -->
    <update id="batchUpdateStatus">
        UPDATE task_comments 
        SET status = #{status}, updated_at = NOW()
        WHERE id IN
        <foreach collection="commentIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

    <!-- 查询评论统计信息 -->
    <select id="selectCommentStatistics" resultType="java.util.HashMap">
        SELECT 
            COUNT(*) as total_comments,
            COUNT(CASE WHEN comment_type = 'normal' THEN 1 END) as normal_comments,
            COUNT(CASE WHEN comment_type = 'system' THEN 1 END) as system_comments,
            COUNT(CASE WHEN is_pinned = TRUE THEN 1 END) as pinned_comments,
            COUNT(CASE WHEN is_private = TRUE THEN 1 END) as private_comments,
            COUNT(DISTINCT commenter_id) as unique_commenters,
            COALESCE(SUM(like_count), 0) as total_likes,
            COALESCE(AVG(like_count), 0) as avg_likes
        FROM task_comments 
        WHERE task_id = #{taskId} AND deleted_at IS NULL
    </select>

    <!-- 查询评论活跃度统计 -->
    <select id="selectCommentActivityStats" resultType="java.util.HashMap">
        SELECT 
            DATE(created_at) as comment_date,
            COUNT(*) as comment_count,
            COUNT(DISTINCT commenter_id) as unique_commenters
        FROM task_comments 
        WHERE task_id = #{taskId} 
          AND created_at >= DATE_SUB(NOW(), INTERVAL #{days} DAY)
          AND deleted_at IS NULL
        GROUP BY DATE(created_at)
        ORDER BY comment_date DESC
    </select>

</mapper>
