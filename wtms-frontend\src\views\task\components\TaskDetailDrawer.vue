<template>
  <el-drawer
    v-model="visible"
    :title="task?.title || '任务详情'"
    size="60%"
    @close="handleClose"
  >
    <div v-if="loading" class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
    
    <div v-else-if="task" class="task-detail">
      <!-- 任务基本信息 -->
      <el-card class="info-card" shadow="never">
        <template #header>
          <div class="card-header">
            <span>基本信息</span>
            <el-button
              v-if="task.isEditable"
              type="primary"
              size="small"
              @click="handleEdit"
            >
              编辑
            </el-button>
          </div>
        </template>
        
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务编号">
            {{ task.taskCode }}
          </el-descriptions-item>
          
          <el-descriptions-item label="任务状态">
            <el-tag :type="getStatusType(task.status)">
              {{ task.statusText }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="优先级">
            <el-tag :type="getPriorityType(task.priority)" size="small">
              {{ task.priorityText }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="难度等级">
            <el-rate
              v-model="task.difficultyLevel"
              disabled
              show-score
              text-color="#ff9900"
            />
          </el-descriptions-item>
          
          <el-descriptions-item label="任务分类">
            <el-tag
              v-if="task.category"
              :color="task.category.color"
              size="small"
            >
              <el-icon>
                <component :is="task.category.icon" />
              </el-icon>
              {{ task.category.name }}
            </el-tag>
          </el-descriptions-item>
          
          <el-descriptions-item label="进度">
            <el-progress
              :percentage="task.progress || 0"
              :stroke-width="8"
            />
          </el-descriptions-item>
          
          <el-descriptions-item label="创建者">
            <div v-if="task.creator" class="user-info">
              <el-avatar :size="24" :src="task.creator.avatar">
                {{ task.creator.fullName?.charAt(0) }}
              </el-avatar>
              <span>{{ task.creator.fullName }}</span>
            </div>
          </el-descriptions-item>
          
          <el-descriptions-item label="负责人">
            <div v-if="task.assignee" class="user-info">
              <el-avatar :size="24" :src="task.assignee.avatar">
                {{ task.assignee.fullName?.charAt(0) }}
              </el-avatar>
              <span>{{ task.assignee.fullName }}</span>
            </div>
            <span v-else class="text-muted">未分配</span>
          </el-descriptions-item>
          
          <el-descriptions-item label="审核人">
            <div v-if="task.reviewer" class="user-info">
              <el-avatar :size="24" :src="task.reviewer.avatar">
                {{ task.reviewer.fullName?.charAt(0) }}
              </el-avatar>
              <span>{{ task.reviewer.fullName }}</span>
            </div>
            <span v-else class="text-muted">未设置</span>
          </el-descriptions-item>
          
          <el-descriptions-item label="预估工时">
            {{ task.estimatedHours || 0 }} 小时
          </el-descriptions-item>
          
          <el-descriptions-item label="实际工时">
            {{ task.actualHours || 0 }} 小时
          </el-descriptions-item>
          
          <el-descriptions-item label="计划开始">
            {{ task.plannedStartDate ? formatDateTime(task.plannedStartDate) : '未设置' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="计划完成">
            {{ task.plannedEndDate ? formatDateTime(task.plannedEndDate) : '未设置' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="实际开始">
            {{ task.actualStartDate ? formatDateTime(task.actualStartDate) : '未开始' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="实际完成">
            {{ task.actualEndDate ? formatDateTime(task.actualEndDate) : '未完成' }}
          </el-descriptions-item>
          
          <el-descriptions-item label="创建时间" :span="2">
            {{ formatDateTime(task.createdAt) }}
          </el-descriptions-item>
          
          <el-descriptions-item label="更新时间" :span="2">
            {{ formatDateTime(task.updatedAt) }}
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 任务描述 -->
      <el-card v-if="task.description" class="info-card" shadow="never">
        <template #header>
          <span>任务描述</span>
        </template>
        <div class="task-description" v-html="task.description"></div>
      </el-card>

      <!-- 标签 -->
      <el-card v-if="task.tags && task.tags.length > 0" class="info-card" shadow="never">
        <template #header>
          <span>标签</span>
        </template>
        <div class="tags-container">
          <el-tag
            v-for="tag in task.tags"
            :key="tag"
            class="tag-item"
          >
            {{ tag }}
          </el-tag>
        </div>
      </el-card>

      <!-- 父任务 -->
      <el-card v-if="task.parent" class="info-card" shadow="never">
        <template #header>
          <span>父任务</span>
        </template>
        <div class="parent-task">
          <el-link type="primary" @click="handleViewParent">
            {{ task.parent.taskCode }} - {{ task.parent.title }}
          </el-link>
          <el-tag :type="getStatusType(task.parent.status)" size="small">
            {{ task.parent.statusText }}
          </el-tag>
        </div>
      </el-card>

      <!-- 子任务 -->
      <el-card v-if="task.children && task.children.length > 0" class="info-card" shadow="never">
        <template #header>
          <span>子任务 ({{ task.children.length }})</span>
        </template>
        <div class="subtasks-container">
          <div
            v-for="child in task.children"
            :key="child.id"
            class="subtask-item"
          >
            <div class="subtask-info">
              <el-link type="primary" @click="handleViewChild(child.id)">
                {{ child.taskCode }} - {{ child.title }}
              </el-link>
              <el-tag :type="getStatusType(child.status)" size="small">
                {{ child.statusText }}
              </el-tag>
            </div>
            <div class="subtask-meta">
              <span v-if="child.assigneeName" class="assignee">
                负责人: {{ child.assigneeName }}
              </span>
              <el-progress
                :percentage="child.progress || 0"
                :stroke-width="4"
                :show-text="false"
                class="progress"
              />
              <span class="progress-text">{{ child.progress || 0 }}%</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button-group>
          <el-button
            v-for="action in availableActions"
            :key="action.key"
            :type="action.type"
            @click="handleAction(action.key)"
          >
            {{ action.label }}
          </el-button>
        </el-button-group>
      </div>
    </div>

    <!-- 任务编辑对话框 -->
    <TaskEditDialog
      v-model:visible="editVisible"
      :task-id="taskId"
      mode="edit"
      @success="handleEditSuccess"
    />
  </el-drawer>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import { getTaskDetail, startTask, completeTask, pauseTask, cancelTask, type TaskDetail } from '@/api/task'
import { getAvailableNextStatuses } from '@/api/taskStatus'
import TaskEditDialog from './TaskEditDialog.vue'

// Props
interface Props {
  visible: boolean
  taskId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  refresh: []
}>()

// 响应式数据
const loading = ref(false)
const task = ref<TaskDetail | null>(null)
const availableStatuses = ref<string[]>([])
const editVisible = ref(false)

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const availableActions = computed(() => {
  if (!task.value) return []
  
  const actions = []
  
  if (availableStatuses.value.includes('in_progress')) {
    actions.push({ key: 'start', label: '开始', type: 'primary' })
  }
  
  if (availableStatuses.value.includes('completed')) {
    actions.push({ key: 'complete', label: '完成', type: 'success' })
  }
  
  if (availableStatuses.value.includes('paused')) {
    actions.push({ key: 'pause', label: '暂停', type: 'warning' })
  }
  
  if (availableStatuses.value.includes('cancelled')) {
    actions.push({ key: 'cancel', label: '取消', type: 'danger' })
  }
  
  return actions
})

// 方法
const loadTaskDetail = async () => {
  if (!props.taskId) return
  
  try {
    loading.value = true
    const [taskRes, statusRes] = await Promise.all([
      getTaskDetail(props.taskId),
      getAvailableNextStatuses(props.taskId)
    ])
    
    task.value = taskRes.data
    availableStatuses.value = statusRes.data
  } catch (error) {
    ElMessage.error('加载任务详情失败')
  } finally {
    loading.value = false
  }
}

const handleClose = () => {
  visible.value = false
}

const handleEdit = () => {
  editVisible.value = true
}

const handleEditSuccess = () => {
  editVisible.value = false
  loadTaskDetail()
  emit('refresh')
}

const handleAction = async (action: string) => {
  if (!task.value) return
  
  try {
    switch (action) {
      case 'start':
        await startTask(task.value.id)
        ElMessage.success('任务已开始')
        break
      case 'complete':
        await completeTask(task.value.id)
        ElMessage.success('任务已完成')
        break
      case 'pause':
        await pauseTask(task.value.id)
        ElMessage.success('任务已暂停')
        break
      case 'cancel':
        await cancelTask(task.value.id)
        ElMessage.success('任务已取消')
        break
    }
    
    loadTaskDetail()
    emit('refresh')
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleViewParent = () => {
  if (task.value?.parent) {
    emit('update:visible', false)
    // TODO: 导航到父任务详情
  }
}

const handleViewChild = (childId: string) => {
  emit('update:visible', false)
  // TODO: 导航到子任务详情
}

// 工具方法
const getStatusType = (status: string) => {
  const statusTypes: Record<string, string> = {
    draft: 'info',
    pending: 'warning',
    in_progress: 'primary',
    review: 'warning',
    testing: 'warning',
    completed: 'success',
    paused: 'info',
    cancelled: 'danger',
    archived: 'info'
  }
  return statusTypes[status] || 'info'
}

const getPriorityType = (priority: number) => {
  const priorityTypes: Record<number, string> = {
    1: 'info',
    2: 'info',
    3: 'warning',
    4: 'danger',
    5: 'danger'
  }
  return priorityTypes[priority] || 'info'
}

const formatDateTime = (dateStr: string) => {
  return new Date(dateStr).toLocaleString()
}

// 监听器
watch(() => props.taskId, (newId) => {
  if (newId && props.visible) {
    loadTaskDetail()
  }
})

watch(() => props.visible, (visible) => {
  if (visible && props.taskId) {
    loadTaskDetail()
  }
})
</script>

<style scoped>
.loading-container {
  padding: 20px;
}

.task-detail {
  padding: 0 20px 20px;
}

.info-card {
  margin-bottom: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.text-muted {
  color: #999;
}

.task-description {
  line-height: 1.6;
  color: #333;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.parent-task {
  display: flex;
  align-items: center;
  gap: 12px;
}

.subtasks-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.subtask-item {
  padding: 12px;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.subtask-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.subtask-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  color: #666;
}

.assignee {
  min-width: 80px;
}

.progress {
  flex: 1;
  max-width: 100px;
}

.progress-text {
  min-width: 30px;
}

.action-buttons {
  position: sticky;
  bottom: 0;
  background: white;
  padding: 20px 0;
  border-top: 1px solid #e4e7ed;
  margin: 0 -20px;
  text-align: center;
}
</style>
