#!/bin/bash

# ================================
# WTMS 安全扫描脚本
# ================================

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查命令是否存在
check_command() {
    if ! command -v $1 &> /dev/null; then
        log_error "$1 命令未找到，请先安装"
        return 1
    fi
}

# 创建报告目录
create_report_dir() {
    local report_dir="security-reports/$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$report_dir"
    echo "$report_dir"
}

# 后端安全扫描
scan_backend() {
    log_info "开始后端安全扫描..."
    
    cd wtms-backend
    
    # 检查Maven
    if ! check_command mvn; then
        log_error "Maven未安装，跳过后端扫描"
        cd ..
        return 1
    fi
    
    # OWASP Dependency Check
    log_info "运行OWASP Dependency Check..."
    if mvn org.owasp:dependency-check-maven:check -DskipTests; then
        log_success "OWASP Dependency Check 完成"
        
        # 复制报告
        if [ -d "target/dependency-check" ]; then
            cp -r target/dependency-check/* "../$1/backend-owasp/"
            mkdir -p "../$1/backend-owasp"
        fi
    else
        log_warning "OWASP Dependency Check 发现安全问题，请查看报告"
    fi
    
    # Snyk扫描 (如果配置了token)
    if [ ! -z "$SNYK_TOKEN" ]; then
        log_info "运行Snyk扫描..."
        if mvn io.snyk:snyk-maven-plugin:test -DskipTests; then
            log_success "Snyk扫描完成"
        else
            log_warning "Snyk扫描发现安全问题"
        fi
    else
        log_warning "未配置SNYK_TOKEN，跳过Snyk扫描"
    fi
    
    # SpotBugs安全检查
    log_info "运行SpotBugs安全检查..."
    if mvn com.github.spotbugs:spotbugs-maven-plugin:check -DskipTests; then
        log_success "SpotBugs检查完成"
        
        # 复制报告
        if [ -f "target/spotbugsXml.xml" ]; then
            mkdir -p "../$1/backend-spotbugs"
            cp target/spotbugsXml.xml "../$1/backend-spotbugs/"
        fi
    else
        log_warning "SpotBugs发现潜在问题"
    fi
    
    cd ..
}

# 前端安全扫描
scan_frontend() {
    log_info "开始前端安全扫描..."
    
    cd wtms-frontend
    
    # 检查npm
    if ! check_command npm; then
        log_error "npm未安装，跳过前端扫描"
        cd ..
        return 1
    fi
    
    # npm audit
    log_info "运行npm audit..."
    if npm audit --audit-level=moderate --json > "../$1/frontend-npm-audit.json" 2>/dev/null; then
        log_success "npm audit完成，未发现中高危漏洞"
    else
        log_warning "npm audit发现安全问题，请查看报告"
        npm audit --audit-level=moderate > "../$1/frontend-npm-audit.txt" 2>/dev/null || true
    fi
    
    # Snyk扫描 (如果安装了snyk)
    if command -v snyk &> /dev/null && [ ! -z "$SNYK_TOKEN" ]; then
        log_info "运行Snyk前端扫描..."
        
        # 认证
        snyk auth $SNYK_TOKEN
        
        # 测试
        if snyk test --json > "../$1/frontend-snyk.json" 2>/dev/null; then
            log_success "Snyk前端扫描完成，未发现问题"
        else
            log_warning "Snyk前端扫描发现安全问题"
            snyk test > "../$1/frontend-snyk.txt" 2>/dev/null || true
        fi
        
        # 监控 (可选)
        if [ "$SNYK_MONITOR" = "true" ]; then
            snyk monitor
            log_info "项目已添加到Snyk监控"
        fi
    else
        log_warning "Snyk未安装或未配置token，跳过Snyk扫描"
    fi
    
    # 检查package.json中的已知漏洞包
    log_info "检查已知漏洞包..."
    check_vulnerable_packages "../$1/frontend-vulnerable-packages.txt"
    
    cd ..
}

# 检查已知漏洞包
check_vulnerable_packages() {
    local output_file="$1"
    local vulnerable_packages=(
        "lodash@<4.17.21"
        "axios@<0.21.2"
        "minimist@<1.2.6"
        "yargs-parser@<13.1.2"
        "node-fetch@<2.6.7"
        "tar@<4.4.18"
    )
    
    echo "检查已知漏洞包..." > "$output_file"
    echo "===================" >> "$output_file"
    
    for package in "${vulnerable_packages[@]}"; do
        if npm list "$package" &>/dev/null; then
            echo "⚠️  发现漏洞包: $package" >> "$output_file"
        fi
    done
    
    echo "检查完成" >> "$output_file"
}

# 生成安全报告摘要
generate_summary() {
    local report_dir="$1"
    local summary_file="$report_dir/security-summary.md"
    
    log_info "生成安全报告摘要..."
    
    cat > "$summary_file" << EOF
# WTMS 安全扫描报告摘要

**扫描时间**: $(date '+%Y-%m-%d %H:%M:%S')
**扫描范围**: 前端 + 后端
**报告目录**: $report_dir

## 扫描工具

### 后端扫描
- ✅ OWASP Dependency Check
- ✅ SpotBugs
- ⚠️  Snyk (需要配置token)

### 前端扫描  
- ✅ npm audit
- ⚠️  Snyk (需要配置token)
- ✅ 已知漏洞包检查

## 报告文件

### 后端报告
- \`backend-owasp/\` - OWASP依赖检查报告
- \`backend-spotbugs/\` - SpotBugs静态分析报告

### 前端报告
- \`frontend-npm-audit.json\` - npm audit JSON报告
- \`frontend-npm-audit.txt\` - npm audit文本报告
- \`frontend-snyk.json\` - Snyk JSON报告
- \`frontend-snyk.txt\` - Snyk文本报告
- \`frontend-vulnerable-packages.txt\` - 已知漏洞包检查

## 建议操作

1. 查看各个报告文件，重点关注高危和中危漏洞
2. 对于依赖漏洞，优先升级到安全版本
3. 对于误报，可以在抑制配置文件中添加规则
4. 定期运行安全扫描，建议每周执行一次
5. 在CI/CD流水线中集成安全扫描

## 联系方式

如有疑问，请联系安全团队或项目维护者。

---
*此报告由WTMS安全扫描脚本自动生成*
EOF

    log_success "安全报告摘要已生成: $summary_file"
}

# 主函数
main() {
    log_info "WTMS 安全扫描开始..."
    log_info "当前目录: $(pwd)"
    
    # 检查项目结构
    if [ ! -d "wtms-backend" ] || [ ! -d "wtms-frontend" ]; then
        log_error "请在WTMS项目根目录下运行此脚本"
        exit 1
    fi
    
    # 创建报告目录
    local report_dir=$(create_report_dir)
    log_info "报告将保存到: $report_dir"
    
    # 后端扫描
    if scan_backend "$report_dir"; then
        log_success "后端安全扫描完成"
    else
        log_warning "后端安全扫描遇到问题"
    fi
    
    # 前端扫描
    if scan_frontend "$report_dir"; then
        log_success "前端安全扫描完成"
    else
        log_warning "前端安全扫描遇到问题"
    fi
    
    # 生成摘要报告
    generate_summary "$report_dir"
    
    log_success "安全扫描完成！"
    log_info "报告位置: $report_dir"
    log_info "请查看 security-summary.md 了解扫描结果"
}

# 脚本入口
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
