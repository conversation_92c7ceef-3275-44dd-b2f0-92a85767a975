package com.wtms;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.ResultSet;
import java.sql.Statement;

/**
 * 数据库连接测试工具
 */
public class DatabaseConnectionTest {
    
    private static final String URL = "**********************************************************************************************************************************************";
    private static final String USERNAME = "root";
    private static final String PASSWORD = "ankaixin.docker.mysql";
    
    public static void main(String[] args) {
        System.out.println("=== WTMS数据库连接测试 ===");
        
        // 测试数据库连接
        testDatabaseConnection();
        
        // 检查数据库是否存在
        checkDatabaseExists();
        
        // 检查数据表
        checkTables();
    }
    
    private static void testDatabaseConnection() {
        System.out.println("\n1. 测试数据库连接...");
        try {
            Class.forName("com.mysql.cj.jdbc.Driver");
            Connection connection = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            System.out.println("✅ 数据库连接成功！");
            System.out.println("   连接URL: " + URL);
            System.out.println("   用户名: " + USERNAME);
            connection.close();
        } catch (Exception e) {
            System.out.println("❌ 数据库连接失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkDatabaseExists() {
        System.out.println("\n2. 检查数据库是否存在...");
        try {
            String baseUrl = "******************************************************************************************************************************************";
            Connection connection = DriverManager.getConnection(baseUrl, USERNAME, PASSWORD);
            Statement statement = connection.createStatement();
            
            ResultSet resultSet = statement.executeQuery("SHOW DATABASES LIKE 'wtms'");
            if (resultSet.next()) {
                System.out.println("✅ wtms数据库存在");
            } else {
                System.out.println("❌ wtms数据库不存在");
                System.out.println("   正在创建wtms数据库...");
                statement.executeUpdate("CREATE DATABASE IF NOT EXISTS wtms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
                System.out.println("✅ wtms数据库创建成功");
            }
            
            connection.close();
        } catch (Exception e) {
            System.out.println("❌ 检查数据库失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void checkTables() {
        System.out.println("\n3. 检查数据表...");
        try {
            Connection connection = DriverManager.getConnection(URL, USERNAME, PASSWORD);
            Statement statement = connection.createStatement();
            
            // 检查主要数据表
            String[] tables = {"users", "roles", "permissions", "user_roles", "role_permissions", "tasks", "task_comments"};
            
            for (String table : tables) {
                ResultSet resultSet = statement.executeQuery("SHOW TABLES LIKE '" + table + "'");
                if (resultSet.next()) {
                    System.out.println("✅ 表 " + table + " 存在");
                    
                    // 检查表结构
                    ResultSet countResult = statement.executeQuery("SELECT COUNT(*) as count FROM " + table);
                    if (countResult.next()) {
                        int count = countResult.getInt("count");
                        System.out.println("   记录数: " + count);
                    }
                } else {
                    System.out.println("❌ 表 " + table + " 不存在");
                }
            }
            
            connection.close();
        } catch (Exception e) {
            System.out.println("❌ 检查数据表失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
