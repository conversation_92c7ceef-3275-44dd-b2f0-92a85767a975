package com.wtms.interceptor;

import com.wtms.annotation.RequirePermission;
import com.wtms.annotation.RequireRole;
import com.wtms.common.exception.BusinessException;
import com.wtms.common.result.ResultCode;
import com.wtms.entity.User;
import com.wtms.mapper.UserMapper;
import com.wtms.service.PermissionService;
import com.wtms.service.UserRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.lang.reflect.Method;

/**
 * 权限拦截器
 * 用于处理自定义权限注解
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class PermissionInterceptor implements HandlerInterceptor {

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private com.wtms.service.DevelopmentPermissionService developmentPermissionService;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) throws Exception {
        if (!(handler instanceof HandlerMethod)) {
            return true;
        }

        HandlerMethod handlerMethod = (HandlerMethod) handler;
        Method method = handlerMethod.getMethod();

        // 检查方法级别的权限注解
        RequirePermission methodPermission = method.getAnnotation(RequirePermission.class);
        RequireRole methodRole = method.getAnnotation(RequireRole.class);

        // 检查类级别的权限注解
        RequirePermission classPermission = method.getDeclaringClass().getAnnotation(RequirePermission.class);
        RequireRole classRole = method.getDeclaringClass().getAnnotation(RequireRole.class);

        // 获取当前用户
        String userId = getCurrentUserId();
        if (userId == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            log.debug("Development mode: skipping permission check for user {}", userId);
            return true;
        }

        // 超级管理员拥有所有权限
        if (userRoleService.isSuperAdmin(userId)) {
            log.debug("Super admin: skipping permission check for user {}", userId);
            return true;
        }

        // 检查权限
        if (methodPermission != null) {
            checkPermissions(userId, methodPermission);
        } else if (classPermission != null) {
            checkPermissions(userId, classPermission);
        }

        // 检查角色
        if (methodRole != null) {
            checkRoles(userId, methodRole);
        } else if (classRole != null) {
            checkRoles(userId, classRole);
        }

        return true;
    }

    /**
     * 检查权限
     */
    private void checkPermissions(String userId, RequirePermission requirePermission) {
        String[] permissions = requirePermission.permissions().length > 0 ? 
                              requirePermission.permissions() : requirePermission.value();

        if (permissions.length == 0) {
            return;
        }

        boolean hasPermission;
        if (requirePermission.logical() == RequirePermission.Logical.AND) {
            hasPermission = permissionService.hasAllPermissions(userId, permissions);
        } else {
            hasPermission = permissionService.hasAnyPermission(userId, permissions);
        }

        if (!hasPermission) {
            log.warn("Permission denied for user {}: required permissions {}", userId, permissions);
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }
    }

    /**
     * 检查角色
     */
    private void checkRoles(String userId, RequireRole requireRole) {
        String[] roles = requireRole.roles().length > 0 ? 
                        requireRole.roles() : requireRole.value();

        if (roles.length == 0) {
            return;
        }

        boolean hasRole;
        if (requireRole.logical() == RequireRole.Logical.AND) {
            hasRole = true;
            for (String role : roles) {
                if (!userRoleService.hasRoleByCode(userId, role)) {
                    hasRole = false;
                    break;
                }
            }
        } else {
            hasRole = false;
            for (String role : roles) {
                if (userRoleService.hasRoleByCode(userId, role)) {
                    hasRole = true;
                    break;
                }
            }
        }

        if (!hasRole) {
            log.warn("Role denied for user {}: required roles {}", userId, roles);
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            return null;
        }

        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails) {
            String username = ((UserDetails) principal).getUsername();
            User user = userMapper.findByUsernameWithRole(username);
            return user != null ? user.getId() : null;
        }
        return null;
    }


}
