package com.wtms.security;

import com.alibaba.fastjson2.JSON;
import com.wtms.common.result.Result;
import com.wtms.common.result.ResultCode;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.web.AuthenticationEntryPoint;
import org.springframework.stereotype.Component;

import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * JWT认证入口点
 * 当用户尝试访问受保护的资源但未提供有效的JWT时，会调用此类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class JwtAuthenticationEntryPoint implements AuthenticationEntryPoint {

    @Override
    public void commence(HttpServletRequest request, HttpServletResponse response,
                         AuthenticationException authException) throws IOException, ServletException {
        
        log.error("Responding with unauthorized error. Message - {}", authException.getMessage());
        
        response.setContentType("application/json;charset=UTF-8");
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        
        Result<Void> result = Result.error(ResultCode.UNAUTHORIZED.getCode(), "访问此资源需要完整的身份验证");
        
        response.getWriter().write(JSON.toJSONString(result));
    }
}
