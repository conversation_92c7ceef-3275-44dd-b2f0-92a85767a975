package com.wtms.common.result;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 响应状态码枚举
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Getter
@AllArgsConstructor
public enum ResultCode {

    // 成功
    SUCCESS(200, "操作成功"),

    // 客户端错误
    BAD_REQUEST(400, "请求参数错误"),
    UNAUTHORIZED(401, "未授权"),
    FORBIDDEN(403, "权限不足"),
    NOT_FOUND(404, "资源不存在"),
    METHOD_NOT_ALLOWED(405, "请求方法不允许"),
    CONFLICT(409, "资源冲突"),
    UNPROCESSABLE_ENTITY(422, "请求格式正确但语义错误"),
    TOO_MANY_REQUESTS(429, "请求过于频繁"),

    // 服务器错误
    INTERNAL_SERVER_ERROR(500, "服务器内部错误"),
    BAD_GATEWAY(502, "网关错误"),
    SERVICE_UNAVAILABLE(503, "服务不可用"),
    GATEWAY_TIMEOUT(504, "网关超时"),

    // 业务错误码 (1000-1999)
    BUSINESS_ERROR(1000, "业务处理失败"),
    VALIDATION_ERROR(1001, "参数验证失败"),
    INVALID_PARAMETER(1002, "参数无效"),
    DATA_NOT_FOUND(1003, "数据不存在"),
    DATA_ALREADY_EXISTS(1004, "数据已存在"),
    OPERATION_NOT_ALLOWED(1005, "操作不被允许"),

    // 用户相关错误 (2000-2099)
    USER_NOT_FOUND(2000, "用户不存在"),
    USER_ALREADY_EXISTS(2001, "用户已存在"),
    USER_DISABLED(2002, "用户已被禁用"),
    USER_LOCKED(2003, "用户已被锁定"),
    INVALID_CREDENTIALS(2004, "用户名或密码错误"),
    PASSWORD_EXPIRED(2005, "密码已过期"),
    ACCOUNT_EXPIRED(2006, "账户已过期"),

    // 认证相关错误 (2100-2199)
    TOKEN_INVALID(2100, "Token无效"),
    TOKEN_EXPIRED(2101, "Token已过期"),
    TOKEN_MISSING(2102, "Token缺失"),
    REFRESH_TOKEN_INVALID(2103, "刷新Token无效"),
    REFRESH_TOKEN_EXPIRED(2104, "刷新Token已过期"),

    // 权限相关错误 (2200-2299)
    PERMISSION_DENIED(2200, "权限不足"),
    ROLE_NOT_FOUND(2201, "角色不存在"),
    PERMISSION_NOT_FOUND(2202, "权限不存在"),

    // 任务相关错误 (3000-3099)
    TASK_NOT_FOUND(3000, "任务不存在"),
    TASK_ALREADY_EXISTS(3001, "任务已存在"),
    TASK_STATUS_INVALID(3002, "任务状态无效"),
    TASK_CANNOT_BE_DELETED(3003, "任务无法删除"),
    TASK_DEPENDENCY_CYCLE(3004, "任务依赖存在循环"),

    // 工作流相关错误 (3100-3199)
    WORKFLOW_NOT_FOUND(3100, "工作流不存在"),
    WORKFLOW_INVALID(3101, "工作流定义无效"),
    WORKFLOW_INSTANCE_NOT_FOUND(3102, "工作流实例不存在"),
    WORKFLOW_STEP_INVALID(3103, "工作流步骤无效"),

    // 文件相关错误 (4000-4099)
    FILE_NOT_FOUND(4000, "文件不存在"),
    FILE_UPLOAD_FAILED(4001, "文件上传失败"),
    FILE_TYPE_NOT_SUPPORTED(4002, "文件类型不支持"),
    FILE_SIZE_EXCEEDED(4003, "文件大小超出限制"),

    // 系统相关错误 (5000-5099)
    SYSTEM_BUSY(5000, "系统繁忙，请稍后重试"),
    SYSTEM_MAINTENANCE(5001, "系统维护中"),
    DATABASE_ERROR(5002, "数据库操作失败"),
    CACHE_ERROR(5003, "缓存操作失败"),
    EXTERNAL_SERVICE_ERROR(5004, "外部服务调用失败");

    private final Integer code;
    private final String message;

    /**
     * 根据状态码获取枚举
     */
    public static ResultCode getByCode(Integer code) {
        for (ResultCode resultCode : values()) {
            if (resultCode.getCode().equals(code)) {
                return resultCode;
            }
        }
        return INTERNAL_SERVER_ERROR;
    }
}
