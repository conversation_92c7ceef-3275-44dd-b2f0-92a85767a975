package com.wtms.service;

import com.wtms.entity.TaskCategory;

import java.util.List;

/**
 * 任务分类服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface TaskCategoryService {

    /**
     * 获取所有启用的分类
     *
     * @return 分类列表
     */
    List<TaskCategory> getEnabledCategories();

    /**
     * 根据ID获取分类
     *
     * @param categoryId 分类ID
     * @return 分类信息
     */
    TaskCategory getCategoryById(String categoryId);

    /**
     * 根据编码获取分类
     *
     * @param code 分类编码
     * @return 分类信息
     */
    TaskCategory getCategoryByCode(String code);

    /**
     * 创建分类
     *
     * @param category 分类信息
     * @return 创建的分类
     */
    TaskCategory createCategory(TaskCategory category);

    /**
     * 更新分类
     *
     * @param categoryId 分类ID
     * @param category 分类信息
     * @return 更新的分类
     */
    TaskCategory updateCategory(String categoryId, TaskCategory category);

    /**
     * 删除分类
     *
     * @param categoryId 分类ID
     */
    void deleteCategory(String categoryId);

    /**
     * 启用分类
     *
     * @param categoryId 分类ID
     */
    void enableCategory(String categoryId);

    /**
     * 禁用分类
     *
     * @param categoryId 分类ID
     */
    void disableCategory(String categoryId);

    /**
     * 检查分类编码是否存在
     *
     * @param code 分类编码
     * @return 是否存在
     */
    boolean existsByCode(String code);

    /**
     * 检查分类名称是否存在
     *
     * @param name 分类名称
     * @return 是否存在
     */
    boolean existsByName(String name);
}
