<template>
  <div class="quality-ranking">
    <!-- 筛选条件 -->
    <el-card class="filter-card" shadow="never">
      <el-form :model="filterForm" inline>
        <el-form-item label="排名类型">
          <el-select v-model="filterForm.targetType" @change="loadRankingData" style="width: 120px">
            <el-option label="用户排名" value="user" />
            <el-option label="任务排名" value="task" />
            <el-option label="部门排名" value="department" />
            <el-option label="团队排名" value="team" />
          </el-select>
        </el-form-item>
        <el-form-item label="统计周期">
          <el-select v-model="filterForm.periodType" @change="loadRankingData" style="width: 120px">
            <el-option label="本周" value="weekly" />
            <el-option label="本月" value="monthly" />
            <el-option label="本季度" value="quarterly" />
            <el-option label="本年" value="yearly" />
          </el-select>
        </el-form-item>
        <el-form-item label="时间范围">
          <el-date-picker
            v-model="filterForm.dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            @change="loadRankingData"
            style="width: 240px"
          />
        </el-form-item>
        <el-form-item label="显示数量">
          <el-input-number
            v-model="filterForm.limit"
            :min="5"
            :max="100"
            :step="5"
            @change="loadRankingData"
            style="width: 120px"
          />
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 排行榜 -->
    <el-row :gutter="20">
      <!-- 前三名展示 -->
      <el-col :span="24">
        <el-card class="podium-card" shadow="never">
          <template #header>
            <div class="podium-header">
              <el-icon><Trophy /></el-icon>
              <span>{{ getRankingTitle() }}前三名</span>
            </div>
          </template>
          <div class="podium">
            <!-- 第二名 -->
            <div v-if="topThree[1]" class="podium-item second">
              <div class="podium-rank">
                <el-icon class="silver"><Medal /></el-icon>
                <span class="rank-number">2</span>
              </div>
              <div class="podium-avatar">
                <el-avatar :size="60" :src="topThree[1].avatar">
                  {{ topThree[1].name?.charAt(0) }}
                </el-avatar>
              </div>
              <div class="podium-info">
                <div class="podium-name">{{ topThree[1].name }}</div>
                <div class="podium-score">{{ topThree[1].score?.toFixed(1) }}</div>
                <div class="podium-count">{{ topThree[1].count }}次评价</div>
              </div>
            </div>

            <!-- 第一名 -->
            <div v-if="topThree[0]" class="podium-item first">
              <div class="podium-rank">
                <el-icon class="gold"><Trophy /></el-icon>
                <span class="rank-number">1</span>
              </div>
              <div class="podium-avatar">
                <el-avatar :size="80" :src="topThree[0].avatar">
                  {{ topThree[0].name?.charAt(0) }}
                </el-avatar>
              </div>
              <div class="podium-info">
                <div class="podium-name">{{ topThree[0].name }}</div>
                <div class="podium-score">{{ topThree[0].score?.toFixed(1) }}</div>
                <div class="podium-count">{{ topThree[0].count }}次评价</div>
              </div>
              <div class="crown">👑</div>
            </div>

            <!-- 第三名 -->
            <div v-if="topThree[2]" class="podium-item third">
              <div class="podium-rank">
                <el-icon class="bronze"><Medal /></el-icon>
                <span class="rank-number">3</span>
              </div>
              <div class="podium-avatar">
                <el-avatar :size="60" :src="topThree[2].avatar">
                  {{ topThree[2].name?.charAt(0) }}
                </el-avatar>
              </div>
              <div class="podium-info">
                <div class="podium-name">{{ topThree[2].name }}</div>
                <div class="podium-score">{{ topThree[2].score?.toFixed(1) }}</div>
                <div class="podium-count">{{ topThree[2].count }}次评价</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 完整排行榜 -->
    <el-row :gutter="20" class="ranking-section">
      <el-col :span="16">
        <el-card class="ranking-list-card" shadow="never">
          <template #header>
            <div class="ranking-header">
              <span>完整排行榜</span>
              <div class="ranking-actions">
                <el-button size="small" @click="handleExport">
                  <el-icon><Download /></el-icon>
                  导出
                </el-button>
                <el-button size="small" @click="handleRefresh">
                  <el-icon><Refresh /></el-icon>
                  刷新
                </el-button>
              </div>
            </div>
          </template>
          
          <div v-loading="loading" class="ranking-list">
            <div
              v-for="(item, index) in rankingData"
              :key="item.id"
              class="ranking-item"
              :class="{ 'top-three': index < 3 }"
            >
              <div class="ranking-position">
                <div class="position-number" :class="getRankClass(index)">
                  {{ index + 1 }}
                </div>
                <div v-if="item.change" class="position-change" :class="item.change > 0 ? 'up' : 'down'">
                  <el-icon v-if="item.change > 0"><ArrowUp /></el-icon>
                  <el-icon v-else><ArrowDown /></el-icon>
                  <span>{{ Math.abs(item.change) }}</span>
                </div>
              </div>
              
              <div class="ranking-avatar">
                <el-avatar :size="40" :src="item.avatar">
                  {{ item.name?.charAt(0) }}
                </el-avatar>
              </div>
              
              <div class="ranking-info">
                <div class="ranking-name">{{ item.name }}</div>
                <div class="ranking-desc">{{ item.description }}</div>
              </div>
              
              <div class="ranking-metrics">
                <div class="metric-item">
                  <div class="metric-label">评分</div>
                  <div class="metric-value score">{{ item.score?.toFixed(1) }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">评价数</div>
                  <div class="metric-value">{{ item.count }}</div>
                </div>
                <div class="metric-item">
                  <div class="metric-label">优秀率</div>
                  <div class="metric-value">{{ ((item.excellentCount / item.count) * 100).toFixed(1) }}%</div>
                </div>
              </div>
              
              <div class="ranking-progress">
                <el-progress
                  :percentage="item.score"
                  :color="getScoreColor(item.score)"
                  :stroke-width="6"
                  :show-text="false"
                />
              </div>
              
              <div class="ranking-actions">
                <el-button size="small" @click="handleViewDetail(item)">
                  查看详情
                </el-button>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <!-- 统计信息 -->
      <el-col :span="8">
        <el-card class="stats-card" shadow="never">
          <template #header>
            <span>统计信息</span>
          </template>
          
          <div class="stats-content">
            <div class="stat-item">
              <div class="stat-label">参与排名数量</div>
              <div class="stat-value">{{ rankingData.length }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">平均评分</div>
              <div class="stat-value">{{ averageScore.toFixed(1) }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">最高评分</div>
              <div class="stat-value">{{ maxScore.toFixed(1) }}</div>
            </div>
            <div class="stat-item">
              <div class="stat-label">最低评分</div>
              <div class="stat-value">{{ minScore.toFixed(1) }}</div>
            </div>
          </div>
          
          <div class="score-distribution">
            <h4>评分分布</h4>
            <div ref="distributionChartRef" class="distribution-chart"></div>
          </div>
        </el-card>
        
        <!-- 趋势分析 -->
        <el-card class="trend-card" shadow="never">
          <template #header>
            <span>趋势分析</span>
          </template>
          
          <div class="trend-content">
            <div class="trend-item">
              <div class="trend-icon improving">
                <el-icon><TrendCharts /></el-icon>
              </div>
              <div class="trend-info">
                <div class="trend-label">进步最快</div>
                <div class="trend-name">{{ fastestImproving?.name || '-' }}</div>
              </div>
            </div>
            <div class="trend-item">
              <div class="trend-icon stable">
                <el-icon><Minus /></el-icon>
              </div>
              <div class="trend-info">
                <div class="trend-label">表现稳定</div>
                <div class="trend-name">{{ mostStable?.name || '-' }}</div>
              </div>
            </div>
            <div class="trend-item">
              <div class="trend-icon declining">
                <el-icon><Bottom /></el-icon>
              </div>
              <div class="trend-info">
                <div class="trend-label">需要关注</div>
                <div class="trend-name">{{ needsAttention?.name || '-' }}</div>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, nextTick } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Trophy, Medal, Download, Refresh, ArrowUp, ArrowDown, 
  TrendCharts, Minus, Bottom 
} from '@element-plus/icons-vue'
import * as echarts from 'echarts'
import { getQualityRanking } from '@/api/evaluation'

// 响应式数据
const loading = ref(false)
const distributionChartRef = ref()
let distributionChart: echarts.ECharts

const filterForm = reactive({
  targetType: 'user',
  periodType: 'monthly',
  dateRange: [],
  limit: 20
})

const rankingData = ref<any[]>([])
const fastestImproving = ref<any>()
const mostStable = ref<any>()
const needsAttention = ref<any>()

// 计算属性
const topThree = computed(() => {
  return rankingData.value.slice(0, 3)
})

const averageScore = computed(() => {
  if (rankingData.value.length === 0) return 0
  const total = rankingData.value.reduce((sum, item) => sum + (item.score || 0), 0)
  return total / rankingData.value.length
})

const maxScore = computed(() => {
  if (rankingData.value.length === 0) return 0
  return Math.max(...rankingData.value.map(item => item.score || 0))
})

const minScore = computed(() => {
  if (rankingData.value.length === 0) return 0
  return Math.min(...rankingData.value.map(item => item.score || 0))
})

// 方法
const loadRankingData = async () => {
  loading.value = true
  try {
    const [startDate, endDate] = filterForm.dateRange || []
    const response = await getQualityRanking(
      filterForm.targetType,
      filterForm.periodType,
      startDate || '',
      endDate || '',
      filterForm.limit
    )
    
    rankingData.value = response.data.map((item: any, index: number) => ({
      id: item.targetId,
      name: item.targetName || `${filterForm.targetType}_${item.targetId}`,
      description: getTargetDescription(item),
      score: item.avgOverallScore || 0,
      count: item.totalEvaluations || 0,
      excellentCount: item.excellentCount || 0,
      change: Math.floor(Math.random() * 10) - 5, // 模拟排名变化
      avatar: item.avatar
    }))
    
    // 分析趋势
    analyzeTrends()
    
    // 更新分布图
    updateDistributionChart()
  } catch (error) {
    console.error('Failed to load ranking data:', error)
    ElMessage.error('加载排行榜数据失败')
  } finally {
    loading.value = false
  }
}

const analyzeTrends = () => {
  if (rankingData.value.length === 0) return
  
  // 找出进步最快的
  fastestImproving.value = rankingData.value.reduce((prev, current) => 
    (current.change > (prev.change || 0)) ? current : prev
  )
  
  // 找出最稳定的（变化最小的）
  mostStable.value = rankingData.value.reduce((prev, current) => 
    (Math.abs(current.change) < Math.abs(prev.change || 0)) ? current : prev
  )
  
  // 找出需要关注的（评分最低的）
  needsAttention.value = rankingData.value.reduce((prev, current) => 
    (current.score < (prev.score || 100)) ? current : prev
  )
}

const updateDistributionChart = () => {
  if (!distributionChart) return
  
  const scoreRanges = [
    { name: '90-100', min: 90, max: 100, color: '#67C23A' },
    { name: '80-89', min: 80, max: 90, color: '#409EFF' },
    { name: '70-79', min: 70, max: 80, color: '#E6A23C' },
    { name: '60-69', min: 60, max: 70, color: '#F56C6C' },
    { name: '<60', min: 0, max: 60, color: '#909399' }
  ]
  
  const distribution = scoreRanges.map(range => ({
    name: range.name,
    value: rankingData.value.filter(item => 
      item.score >= range.min && item.score < range.max
    ).length,
    itemStyle: { color: range.color }
  }))
  
  const option = {
    tooltip: {
      trigger: 'item',
      formatter: '{a} <br/>{b}: {c} ({d}%)'
    },
    series: [{
      name: '评分分布',
      type: 'pie',
      radius: '70%',
      data: distribution,
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }]
  }
  
  distributionChart.setOption(option)
}

const initDistributionChart = () => {
  nextTick(() => {
    distributionChart = echarts.init(distributionChartRef.value)
    window.addEventListener('resize', () => {
      distributionChart?.resize()
    })
  })
}

const getRankingTitle = () => {
  const typeMap: Record<string, string> = {
    user: '用户质量',
    task: '任务质量',
    department: '部门质量',
    team: '团队质量'
  }
  return typeMap[filterForm.targetType] || '质量'
}

const getTargetDescription = (item: any) => {
  const typeMap: Record<string, string> = {
    user: '用户',
    task: '任务',
    department: '部门',
    team: '团队'
  }
  return typeMap[filterForm.targetType] || ''
}

const getRankClass = (index: number) => {
  if (index === 0) return 'first'
  if (index === 1) return 'second'
  if (index === 2) return 'third'
  return ''
}

const getScoreColor = (score: number) => {
  if (score >= 90) return '#67C23A'
  if (score >= 80) return '#409EFF'
  if (score >= 70) return '#E6A23C'
  if (score >= 60) return '#F56C6C'
  return '#909399'
}

const handleExport = () => {
  ElMessage.info('导出功能开发中...')
}

const handleRefresh = () => {
  loadRankingData()
}

const handleViewDetail = (item: any) => {
  ElMessage.info(`查看 ${item.name} 的详细信息`)
}

// 生命周期
onMounted(() => {
  initDistributionChart()
  loadRankingData()
})
</script>

<style scoped>
.quality-ranking {
  padding: 20px;
}

.filter-card {
  margin-bottom: 20px;
}

.podium-card {
  margin-bottom: 20px;
}

.podium-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  font-size: 16px;
}

.podium {
  display: flex;
  justify-content: center;
  align-items: end;
  gap: 40px;
  padding: 40px 0;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 12px;
  position: relative;
}

.podium-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
  position: relative;
}

.podium-item.first {
  order: 2;
  transform: translateY(-20px);
}

.podium-item.second {
  order: 1;
}

.podium-item.third {
  order: 3;
}

.podium-rank {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.podium-rank .el-icon {
  font-size: 24px;
}

.podium-rank .gold {
  color: #ffd700;
}

.podium-rank .silver {
  color: #c0c0c0;
}

.podium-rank .bronze {
  color: #cd7f32;
}

.rank-number {
  font-size: 14px;
  font-weight: 600;
  color: #606266;
}

.podium-info {
  text-align: center;
}

.podium-name {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.podium-score {
  font-size: 20px;
  font-weight: 700;
  color: #409eff;
  margin-bottom: 2px;
}

.podium-count {
  font-size: 12px;
  color: #909399;
}

.crown {
  position: absolute;
  top: -30px;
  font-size: 24px;
}

.ranking-section {
  margin-top: 20px;
}

.ranking-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.ranking-actions {
  display: flex;
  gap: 8px;
}

.ranking-list {
  max-height: 600px;
  overflow-y: auto;
}

.ranking-item {
  display: flex;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #ebeef5;
  gap: 16px;
}

.ranking-item.top-three {
  background: linear-gradient(90deg, rgba(64, 158, 255, 0.05) 0%, rgba(255, 255, 255, 0) 100%);
}

.ranking-position {
  width: 60px;
  text-align: center;
}

.position-number {
  font-size: 18px;
  font-weight: 600;
  color: #606266;
  margin-bottom: 4px;
}

.position-number.first {
  color: #ffd700;
}

.position-number.second {
  color: #c0c0c0;
}

.position-number.third {
  color: #cd7f32;
}

.position-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
  font-size: 12px;
}

.position-change.up {
  color: #67c23a;
}

.position-change.down {
  color: #f56c6c;
}

.ranking-info {
  flex: 1;
}

.ranking-name {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.ranking-desc {
  font-size: 12px;
  color: #909399;
}

.ranking-metrics {
  display: flex;
  gap: 24px;
}

.metric-item {
  text-align: center;
}

.metric-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.metric-value {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.metric-value.score {
  font-size: 16px;
  color: #409eff;
}

.ranking-progress {
  width: 120px;
}

.stats-content {
  margin-bottom: 24px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
}

.stat-label {
  font-size: 14px;
  color: #606266;
}

.stat-value {
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.score-distribution h4 {
  margin: 0 0 16px 0;
  font-size: 14px;
  color: #303133;
}

.distribution-chart {
  height: 200px;
}

.trend-card {
  margin-top: 20px;
}

.trend-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.trend-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background: #f5f7fa;
}

.trend-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 16px;
}

.trend-icon.improving {
  background: #67c23a;
}

.trend-icon.stable {
  background: #409eff;
}

.trend-icon.declining {
  background: #f56c6c;
}

.trend-info {
  flex: 1;
}

.trend-label {
  font-size: 12px;
  color: #909399;
  margin-bottom: 2px;
}

.trend-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}
</style>
