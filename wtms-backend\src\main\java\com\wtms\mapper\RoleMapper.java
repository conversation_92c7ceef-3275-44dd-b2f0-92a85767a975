package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.entity.Role;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 角色Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface RoleMapper extends BaseMapper<Role> {

    /**
     * 根据角色编码查询角色
     */
    @Select("SELECT * FROM roles WHERE code = #{code}")
    Role selectByCode(@Param("code") String code);

    /**
     * 根据角色名称查询角色
     */
    @Select("SELECT * FROM roles WHERE name = #{name}")
    Role selectByName(@Param("name") String name);

    /**
     * 查询所有启用的角色
     */
    @Select("SELECT * FROM roles WHERE is_active = TRUE ORDER BY created_at DESC")
    List<Role> selectActiveRoles();

    /**
     * 查询系统角色
     */
    @Select("SELECT * FROM roles WHERE is_system = TRUE ORDER BY created_at DESC")
    List<Role> selectSystemRoles();

    /**
     * 查询非系统角色
     */
    @Select("SELECT * FROM roles WHERE is_system = FALSE ORDER BY created_at DESC")
    List<Role> selectNonSystemRoles();

    /**
     * 分页查询角色
     */
    IPage<Role> selectRolesWithPage(Page<Role> page, @Param("name") String name, @Param("code") String code);

    /**
     * 根据用户ID查询用户角色
     */
    List<Role> selectRolesByUserId(@Param("userId") String userId);

    /**
     * 检查角色编码是否存在
     */
    @Select("SELECT COUNT(*) FROM roles WHERE code = #{code}")
    int countByCode(@Param("code") String code);

    /**
     * 检查角色名称是否存在
     */
    @Select("SELECT COUNT(*) FROM roles WHERE name = #{name}")
    int countByName(@Param("name") String name);

    /**
     * 批量插入角色
     */
    int batchInsert(@Param("roles") List<Role> roles);

    /**
     * 批量更新角色状态
     */
    int batchUpdateStatus(@Param("ids") List<String> ids, @Param("isActive") Boolean isActive);
}
