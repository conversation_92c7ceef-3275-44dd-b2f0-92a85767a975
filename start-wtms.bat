@echo off
echo ========================================
echo WTMS 工作任务管理系统启动脚本
echo ========================================
echo.

echo 检查Docker是否可用...
docker --version >nul 2>&1
if %errorlevel% equ 0 (
    echo Docker已安装，使用Docker启动服务...
    echo.
    
    echo 启动所有服务...
    docker-compose up -d
    
    echo.
    echo 等待服务启动...
    timeout /t 30 /nobreak >nul
    
    echo.
    echo 检查服务状态...
    docker-compose ps
    
    echo.
    echo ========================================
    echo 服务启动完成！
    echo ========================================
    echo 前端地址: http://localhost:33335
    echo 后端API: http://localhost:55557/api/v1
    echo 默认账户: admin / admin123456
    echo ========================================
    
) else (
    echo Docker未安装或未启动
    echo.
    echo 请选择启动方式：
    echo 1. 安装Docker Desktop后重新运行此脚本
    echo 2. 手动启动服务（参考STARTUP_GUIDE.md）
    echo.
    
    echo 检查MySQL服务...
    sc query mysql >nul 2>&1
    if %errorlevel% equ 0 (
        echo MySQL服务已安装
        net start mysql
    ) else (
        echo MySQL服务未安装，请先安装MySQL 8.0
    )
    
    echo.
    echo 检查端口占用情况...
    echo 检查端口3308 (MySQL):
    netstat -an | findstr "3308"
    
    echo 检查端口6379 (Redis):
    netstat -an | findstr "6379"
    
    echo 检查端口55557 (后端):
    netstat -an | findstr "55557"
    
    echo 检查端口33335 (前端):
    netstat -an | findstr "33335"
    
    echo.
    echo ========================================
    echo 手动启动步骤：
    echo ========================================
    echo 1. 启动MySQL服务 (端口3308)
    echo 2. 启动Redis服务 (端口6379)
    echo 3. 初始化数据库 (运行database/schema.sql)
    echo 4. 启动后端服务 (cd wtms-backend && mvn spring-boot:run)
    echo 5. 启动前端服务 (cd wtms-frontend && npm run dev)
    echo.
    echo 详细步骤请参考 STARTUP_GUIDE.md
    echo ========================================
)

echo.
pause
