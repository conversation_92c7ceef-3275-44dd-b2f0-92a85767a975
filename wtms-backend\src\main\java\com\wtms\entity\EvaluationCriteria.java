package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 评价标准实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("evaluation_criteria")
@Schema(description = "评价标准实体")
public class EvaluationCriteria implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "标准ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "评价维度ID")
    @TableField("evaluation_dimension_id")
    private String evaluationDimensionId;

    @Schema(description = "标准名称")
    @TableField("criteria_name")
    private String criteriaName;

    @Schema(description = "标准编码")
    @TableField("criteria_code")
    private String criteriaCode;

    @Schema(description = "标准描述")
    @TableField("description")
    private String description;

    @Schema(description = "评分标准")
    @TableField("scoring_standard")
    private String scoringStandard;

    @Schema(description = "最高分值")
    @TableField("max_score")
    private BigDecimal maxScore;

    @Schema(description = "最低分值")
    @TableField("min_score")
    private BigDecimal minScore;

    @Schema(description = "权重")
    @TableField("weight")
    private BigDecimal weight;

    @Schema(description = "标准类型")
    @TableField("criteria_type")
    private String criteriaType;

    @Schema(description = "评价方式")
    @TableField("evaluation_method")
    private String evaluationMethod;

    @Schema(description = "是否必填")
    @TableField("is_required")
    private Boolean isRequired;

    @Schema(description = "是否启用")
    @TableField("is_enabled")
    private Boolean isEnabled;

    @Schema(description = "排序号")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "创建者ID")
    @TableField("creator_id")
    private String creatorId;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "评价维度信息")
    @TableField(exist = false)
    private EvaluationDimension evaluationDimension;

    @Schema(description = "创建者信息")
    @TableField(exist = false)
    private User creator;

    /**
     * 标准类型枚举
     */
    public enum CriteriaType {
        QUANTITATIVE("quantitative", "定量标准"),
        QUALITATIVE("qualitative", "定性标准"),
        BINARY("binary", "二元标准"),
        SCALE("scale", "量表标准"),
        CUSTOM("custom", "自定义标准");

        private final String code;
        private final String description;

        CriteriaType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static CriteriaType fromCode(String code) {
            for (CriteriaType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return CUSTOM;
        }
    }

    /**
     * 评价方式枚举
     */
    public enum EvaluationMethod {
        SCORE("score", "评分"),
        RATING("rating", "等级"),
        CHECKBOX("checkbox", "勾选"),
        TEXT("text", "文本"),
        SLIDER("slider", "滑块"),
        DROPDOWN("dropdown", "下拉选择"),
        RADIO("radio", "单选"),
        MULTIPLE("multiple", "多选");

        private final String code;
        private final String description;

        EvaluationMethod(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static EvaluationMethod fromCode(String code) {
            for (EvaluationMethod method : values()) {
                if (method.code.equals(code)) {
                    return method;
                }
            }
            return SCORE;
        }
    }

    /**
     * 检查是否必填
     */
    public boolean isRequired() {
        return Boolean.TRUE.equals(this.isRequired);
    }

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.isEnabled);
    }

    /**
     * 获取标准类型描述
     */
    public String getCriteriaTypeText() {
        return CriteriaType.fromCode(this.criteriaType).getDescription();
    }

    /**
     * 获取评价方式描述
     */
    public String getEvaluationMethodText() {
        return EvaluationMethod.fromCode(this.evaluationMethod).getDescription();
    }

    /**
     * 获取分数范围描述
     */
    public String getScoreRange() {
        StringBuilder range = new StringBuilder();
        
        if (minScore != null) {
            range.append(minScore);
        } else {
            range.append("0");
        }
        
        range.append(" - ");
        
        if (maxScore != null) {
            range.append(maxScore);
        } else {
            range.append("100");
        }
        
        return range.toString();
    }

    /**
     * 验证分数是否在有效范围内
     */
    public boolean isScoreValid(BigDecimal score) {
        if (score == null) {
            return !isRequired();
        }
        
        boolean minValid = minScore == null || score.compareTo(minScore) >= 0;
        boolean maxValid = maxScore == null || score.compareTo(maxScore) <= 0;
        
        return minValid && maxValid;
    }

    /**
     * 计算分数百分比
     */
    public BigDecimal calculateScorePercentage(BigDecimal score) {
        if (score == null || maxScore == null || maxScore.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        
        BigDecimal minValue = minScore != null ? minScore : BigDecimal.ZERO;
        BigDecimal range = maxScore.subtract(minValue);
        
        if (range.compareTo(BigDecimal.ZERO) == 0) {
            return new BigDecimal("100");
        }
        
        return score.subtract(minValue).divide(range, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
    }

    /**
     * 获取评分等级
     */
    public String getScoreGrade(BigDecimal score) {
        BigDecimal percentage = calculateScorePercentage(score);
        
        if (percentage.compareTo(new BigDecimal("90")) >= 0) {
            return "优秀";
        } else if (percentage.compareTo(new BigDecimal("80")) >= 0) {
            return "良好";
        } else if (percentage.compareTo(new BigDecimal("70")) >= 0) {
            return "中等";
        } else if (percentage.compareTo(new BigDecimal("60")) >= 0) {
            return "及格";
        } else {
            return "不及格";
        }
    }

    /**
     * 检查是否为定量标准
     */
    public boolean isQuantitative() {
        return CriteriaType.QUANTITATIVE.getCode().equals(this.criteriaType);
    }

    /**
     * 检查是否为定性标准
     */
    public boolean isQualitative() {
        return CriteriaType.QUALITATIVE.getCode().equals(this.criteriaType);
    }

    /**
     * 检查是否为二元标准
     */
    public boolean isBinary() {
        return CriteriaType.BINARY.getCode().equals(this.criteriaType);
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (maxScore == null) {
            maxScore = new BigDecimal("100");
        }
        if (minScore == null) {
            minScore = BigDecimal.ZERO;
        }
        if (weight == null) {
            weight = new BigDecimal("1.0");
        }
        if (criteriaType == null) {
            criteriaType = CriteriaType.QUANTITATIVE.getCode();
        }
        if (evaluationMethod == null) {
            evaluationMethod = EvaluationMethod.SCORE.getCode();
        }
        if (isRequired == null) {
            isRequired = false;
        }
        if (isEnabled == null) {
            isEnabled = true;
        }
        if (sortOrder == null) {
            sortOrder = 0;
        }
    }
}
