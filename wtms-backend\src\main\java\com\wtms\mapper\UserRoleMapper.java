package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wtms.entity.UserRole;
import org.apache.ibatis.annotations.Delete;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 用户角色关联Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface UserRoleMapper extends BaseMapper<UserRole> {

    /**
     * 根据用户ID查询角色关联
     */
    @Select("SELECT * FROM user_roles WHERE user_id = #{userId}")
    List<UserRole> selectByUserId(@Param("userId") String userId);

    /**
     * 根据角色ID查询用户关联
     */
    @Select("SELECT * FROM user_roles WHERE role_id = #{roleId}")
    List<UserRole> selectByRoleId(@Param("roleId") String roleId);

    /**
     * 根据用户ID和角色ID查询关联
     */
    @Select("SELECT * FROM user_roles WHERE user_id = #{userId} AND role_id = #{roleId}")
    UserRole selectByUserIdAndRoleId(@Param("userId") String userId, @Param("roleId") String roleId);

    /**
     * 根据用户ID查询有效的角色关联
     */
    @Select("SELECT * FROM user_roles WHERE user_id = #{userId} AND is_enabled = TRUE " +
            "AND (expires_at IS NULL OR expires_at > NOW())")
    List<UserRole> selectValidByUserId(@Param("userId") String userId);

    /**
     * 根据角色ID查询有效的用户关联
     */
    @Select("SELECT * FROM user_roles WHERE role_id = #{roleId} AND is_enabled = TRUE " +
            "AND (expires_at IS NULL OR expires_at > NOW())")
    List<UserRole> selectValidByRoleId(@Param("roleId") String roleId);

    /**
     * 删除用户的所有角色关联
     */
    @Delete("DELETE FROM user_roles WHERE user_id = #{userId}")
    int deleteByUserId(@Param("userId") String userId);

    /**
     * 删除角色的所有用户关联
     */
    @Delete("DELETE FROM user_roles WHERE role_id = #{roleId}")
    int deleteByRoleId(@Param("roleId") String roleId);

    /**
     * 删除指定用户和角色的关联
     */
    @Delete("DELETE FROM user_roles WHERE user_id = #{userId} AND role_id = #{roleId}")
    int deleteByUserIdAndRoleId(@Param("userId") String userId, @Param("roleId") String roleId);

    /**
     * 批量插入用户角色关联
     */
    int batchInsert(@Param("userRoles") List<UserRole> userRoles);

    /**
     * 批量删除用户角色关联
     */
    int batchDeleteByUserIdAndRoleIds(@Param("userId") String userId, @Param("roleIds") List<String> roleIds);

    /**
     * 检查用户角色关联是否存在
     */
    @Select("SELECT COUNT(*) FROM user_roles WHERE user_id = #{userId} AND role_id = #{roleId}")
    int countByUserIdAndRoleId(@Param("userId") String userId, @Param("roleId") String roleId);

    /**
     * 统计用户的角色数量
     */
    @Select("SELECT COUNT(*) FROM user_roles WHERE user_id = #{userId} AND is_enabled = TRUE")
    int countRolesByUserId(@Param("userId") String userId);

    /**
     * 统计角色的用户数量
     */
    @Select("SELECT COUNT(*) FROM user_roles WHERE role_id = #{roleId} AND is_enabled = TRUE")
    int countUsersByRoleId(@Param("roleId") String roleId);

    /**
     * 查询过期的角色关联
     */
    @Select("SELECT * FROM user_roles WHERE expires_at IS NOT NULL AND expires_at <= NOW()")
    List<UserRole> selectExpiredUserRoles();

    /**
     * 更新过期的角色关联状态
     */
    int updateExpiredUserRoles();

    /**
     * 根据用户ID查询角色关联（包含角色信息）
     */
    List<UserRole> selectUserRolesWithRoleInfo(@Param("userId") String userId);

    /**
     * 根据角色ID查询用户关联（包含用户信息）
     */
    List<UserRole> selectRoleUsersWithUserInfo(@Param("roleId") String roleId);
}
