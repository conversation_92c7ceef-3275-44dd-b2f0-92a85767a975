@echo off
chcp 65001 >nul
echo ========================================
echo WTMS 系统检查与启动脚本
echo ========================================
echo 配置信息：
echo - 数据库：localhost:3308 (root/ankaixin.docker.mysql)
echo - 前端端口：33335
echo - 后端端口：55557
echo ========================================
echo.

echo [1/6] 检查Docker环境...
docker --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Docker已安装
    docker info >nul 2>&1
    if %errorlevel% equ 0 (
        echo ✅ Docker服务运行正常
        set DOCKER_AVAILABLE=1
    ) else (
        echo ❌ Docker服务未启动，请启动Docker Desktop
        set DOCKER_AVAILABLE=0
    )
) else (
    echo ❌ Docker未安装
    set DOCKER_AVAILABLE=0
)
echo.

echo [2/6] 检查端口占用情况...
echo 检查端口 3308 (MySQL):
netstat -an | findstr ":3308" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  端口3308已被占用
) else (
    echo ✅ 端口3308可用
)

echo 检查端口 6379 (Redis):
netstat -an | findstr ":6379" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  端口6379已被占用
) else (
    echo ✅ 端口6379可用
)

echo 检查端口 55557 (后端):
netstat -an | findstr ":55557" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  端口55557已被占用
) else (
    echo ✅ 端口55557可用
)

echo 检查端口 33335 (前端):
netstat -an | findstr ":33335" >nul 2>&1
if %errorlevel% equ 0 (
    echo ⚠️  端口33335已被占用
) else (
    echo ✅ 端口33335可用
)
echo.

echo [3/6] 检查项目文件...
if exist "docker-compose.yml" (
    echo ✅ docker-compose.yml 存在
) else (
    echo ❌ docker-compose.yml 不存在
)

if exist "database\schema.sql" (
    echo ✅ database\schema.sql 存在
) else (
    echo ❌ database\schema.sql 不存在
)

if exist "database\test-data.sql" (
    echo ✅ database\test-data.sql 存在
) else (
    echo ❌ database\test-data.sql 不存在
)

if exist "wtms-backend\src\main\resources\application.yml" (
    echo ✅ 后端配置文件存在
) else (
    echo ❌ 后端配置文件不存在
)

if exist "wtms-frontend\.env" (
    echo ✅ 前端配置文件存在
) else (
    echo ❌ 前端配置文件不存在
)
echo.

echo [4/6] 检查开发环境...
java -version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Java环境可用
    java -version 2>&1 | findstr "17\|18\|19\|20\|21" >nul
    if %errorlevel% equ 0 (
        echo ✅ Java版本符合要求 (17+)
    ) else (
        echo ⚠️  建议使用Java 17或更高版本
    )
) else (
    echo ❌ Java环境未安装
)

node --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js环境可用
    for /f "tokens=1 delims=." %%a in ('node --version') do set NODE_MAJOR=%%a
    set NODE_MAJOR=%NODE_MAJOR:v=%
    if %NODE_MAJOR% geq 18 (
        echo ✅ Node.js版本符合要求 (18+)
    ) else (
        echo ⚠️  建议使用Node.js 18或更高版本
    )
) else (
    echo ❌ Node.js环境未安装
)

mvn --version >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Maven环境可用
) else (
    echo ❌ Maven环境未安装
)
echo.

echo [5/6] 启动服务...
if %DOCKER_AVAILABLE%==1 (
    echo 使用Docker启动所有服务...
    docker-compose up -d
    
    echo 等待服务启动...
    timeout /t 30 /nobreak >nul
    
    echo 检查容器状态...
    docker-compose ps
    
) else (
    echo Docker不可用，请手动启动服务：
    echo.
    echo 1. 启动MySQL服务 (端口3308)
    echo 2. 启动Redis服务 (端口6379)  
    echo 3. 初始化数据库：
    echo    mysql -h localhost -P 3308 -u root -pankaixin.docker.mysql ^< database\schema.sql
    echo    mysql -h localhost -P 3308 -u root -pankaixin.docker.mysql wtms ^< database\test-data.sql
    echo 4. 启动后端服务：
    echo    cd wtms-backend ^&^& mvn spring-boot:run
    echo 5. 启动前端服务：
    echo    cd wtms-frontend ^&^& npm install ^&^& npm run dev
)
echo.

echo [6/6] 验证服务...
echo 等待服务完全启动...
timeout /t 10 /nobreak >nul

echo 检查前端服务...
curl -s http://localhost:33335 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 前端服务响应正常
) else (
    echo ❌ 前端服务无响应
)

echo 检查后端服务...
curl -s http://localhost:55557/api/v1/actuator/health >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 后端服务响应正常
) else (
    echo ❌ 后端服务无响应
)

echo.
echo ========================================
echo 🎉 系统检查完成！
echo ========================================
echo 访问地址：
echo - 前端应用: http://localhost:33335
echo - 后端API: http://localhost:55557/api/v1
echo - API文档: http://localhost:55557/swagger-ui.html
echo.
echo 默认账户: admin / admin123456
echo ========================================
echo.

if %DOCKER_AVAILABLE%==1 (
    echo 如需查看日志，请运行: docker-compose logs -f
    echo 如需停止服务，请运行: docker-compose down
) else (
    echo 请按照上述步骤手动启动各个服务
)

echo.
pause
