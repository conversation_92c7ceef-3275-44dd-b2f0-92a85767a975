<template>
  <div class="task-detail">
    <el-page-header @back="goBack" :content="task?.title || '任务详情'">
      <template #extra>
        <el-button-group>
          <el-button type="primary" @click="editTask" v-if="canEdit">
            编辑
          </el-button>
          <el-button @click="shareTask">
            分享
          </el-button>
        </el-button-group>
      </template>
    </el-page-header>

    <div class="task-content" v-loading="loading">
      <el-row :gutter="20" v-if="task">
        <!-- 左侧主要内容 -->
        <el-col :span="16">
          <el-card class="task-info-card">
            <template #header>
              <div class="card-header">
                <span>任务信息</span>
                <el-tag :type="getStatusType(task.status)">
                  {{ getStatusText(task.status) }}
                </el-tag>
              </div>
            </template>

            <el-descriptions :column="2" border>
              <el-descriptions-item label="任务标题">
                {{ task.title }}
              </el-descriptions-item>
              <el-descriptions-item label="优先级">
                <el-tag :type="getPriorityType(task.priority)">
                  {{ getPriorityText(task.priority) }}
                </el-tag>
              </el-descriptions-item>
              <el-descriptions-item label="创建人">
                {{ task.creator?.fullName }}
              </el-descriptions-item>
              <el-descriptions-item label="负责人">
                {{ task.assignee?.fullName || '未分配' }}
              </el-descriptions-item>
              <el-descriptions-item label="开始时间">
                {{ formatDate(task.startDate) }}
              </el-descriptions-item>
              <el-descriptions-item label="截止时间">
                {{ formatDate(task.dueDate) }}
              </el-descriptions-item>
              <el-descriptions-item label="预估工时">
                {{ task.estimatedHours || 0 }} 小时
              </el-descriptions-item>
              <el-descriptions-item label="实际工时">
                {{ task.actualHours || 0 }} 小时
              </el-descriptions-item>
            </el-descriptions>

            <div class="task-description" v-if="task.description">
              <h4>任务描述</h4>
              <div class="description-content" v-html="task.description"></div>
            </div>

            <div class="task-progress" v-if="task.progress !== undefined">
              <h4>完成进度</h4>
              <el-progress :percentage="task.progress" :status="task.progress === 100 ? 'success' : ''" />
            </div>
          </el-card>

          <!-- 任务评论 -->
          <el-card class="comments-card">
            <template #header>
              <span>任务评论</span>
            </template>
            <div class="comments-section">
              <!-- 评论列表 -->
              <div class="comment-list" v-if="comments.length > 0">
                <div v-for="comment in comments" :key="comment.id" class="comment-item">
                  <el-avatar :src="comment.commenter?.avatarUrl" :size="40">
                    {{ comment.commenter?.fullName?.charAt(0) }}
                  </el-avatar>
                  <div class="comment-content">
                    <div class="comment-header">
                      <span class="commenter-name">{{ comment.commenter?.fullName }}</span>
                      <span class="comment-time">{{ formatDate(comment.createdAt) }}</span>
                    </div>
                    <div class="comment-text">{{ comment.content }}</div>
                  </div>
                </div>
              </div>
              <el-empty v-else description="暂无评论" />

              <!-- 添加评论 -->
              <div class="add-comment">
                <el-input
                  v-model="newComment"
                  type="textarea"
                  :rows="3"
                  placeholder="添加评论..."
                  maxlength="500"
                  show-word-limit
                />
                <div class="comment-actions">
                  <el-button type="primary" @click="addComment" :disabled="!newComment.trim()">
                    发表评论
                  </el-button>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>

        <!-- 右侧边栏 -->
        <el-col :span="8">
          <el-card class="sidebar-card">
            <template #header>
              <span>操作</span>
            </template>
            <div class="action-buttons">
              <el-button type="success" @click="completeTask" v-if="task.status !== 'DONE'">
                完成任务
              </el-button>
              <el-button type="warning" @click="pauseTask" v-if="task.status === 'IN_PROGRESS'">
                暂停任务
              </el-button>
              <el-button type="primary" @click="startTask" v-if="task.status === 'TODO'">
                开始任务
              </el-button>
              <el-button type="info" @click="archiveTask" v-if="task.status === 'DONE'">
                归档任务
              </el-button>
            </div>
          </el-card>

          <el-card class="sidebar-card">
            <template #header>
              <span>相关信息</span>
            </template>
            <div class="related-info">
              <div class="info-item">
                <span class="label">创建时间：</span>
                <span>{{ formatDate(task.createdAt) }}</span>
              </div>
              <div class="info-item">
                <span class="label">更新时间：</span>
                <span>{{ formatDate(task.updatedAt) }}</span>
              </div>
              <div class="info-item" v-if="task.tags && task.tags.length > 0">
                <span class="label">标签：</span>
                <div class="tags">
                  <el-tag v-for="tag in task.tags" :key="tag" size="small">{{ tag }}</el-tag>
                </div>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- 编辑任务对话框 -->
    <TaskEditDialog
      v-model="editDialogVisible"
      :task="task"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Edit, Share } from '@element-plus/icons-vue'
import TaskEditDialog from './components/TaskEditDialog.vue'
import { taskApi } from '@/api/task'
import { formatDate } from '@/utils/date'
import type { Task, Comment } from '@/types/task'

const route = useRoute()
const router = useRouter()

const loading = ref(false)
const task = ref<Task | null>(null)
const comments = ref<Comment[]>([])
const newComment = ref('')
const editDialogVisible = ref(false)

const taskId = computed(() => route.params.id as string)

const canEdit = computed(() => {
  // 这里可以添加权限判断逻辑
  return true
})

const getStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    'TODO': 'info',
    'IN_PROGRESS': 'warning',
    'REVIEW': 'primary',
    'DONE': 'success',
    'CANCELLED': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    'TODO': '待开始',
    'IN_PROGRESS': '进行中',
    'REVIEW': '待审核',
    'DONE': '已完成',
    'CANCELLED': '已取消'
  }
  return statusMap[status] || status
}

const getPriorityType = (priority: string) => {
  const priorityMap: Record<string, string> = {
    'LOW': 'success',
    'MEDIUM': 'warning',
    'HIGH': 'danger',
    'URGENT': 'danger'
  }
  return priorityMap[priority] || 'info'
}

const getPriorityText = (priority: string) => {
  const priorityMap: Record<string, string> = {
    'LOW': '低',
    'MEDIUM': '中',
    'HIGH': '高',
    'URGENT': '紧急'
  }
  return priorityMap[priority] || priority
}

const fetchTaskDetail = async () => {
  try {
    loading.value = true
    const response = await taskApi.getTaskById(taskId.value)
    task.value = response.data
  } catch (error) {
    ElMessage.error('获取任务详情失败')
    console.error('获取任务详情失败:', error)
  } finally {
    loading.value = false
  }
}

const fetchComments = async () => {
  try {
    const response = await taskApi.getTaskComments(taskId.value)
    comments.value = response.data
  } catch (error) {
    console.error('获取评论失败:', error)
  }
}

const addComment = async () => {
  if (!newComment.value.trim()) return

  try {
    await taskApi.addTaskComment(taskId.value, {
      content: newComment.value.trim()
    })
    newComment.value = ''
    await fetchComments()
    ElMessage.success('评论添加成功')
  } catch (error) {
    ElMessage.error('添加评论失败')
    console.error('添加评论失败:', error)
  }
}

const editTask = () => {
  editDialogVisible.value = true
}

const shareTask = () => {
  const url = window.location.href
  navigator.clipboard.writeText(url).then(() => {
    ElMessage.success('任务链接已复制到剪贴板')
  }).catch(() => {
    ElMessage.error('复制失败')
  })
}

const completeTask = async () => {
  try {
    await ElMessageBox.confirm('确认完成此任务？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await taskApi.updateTaskStatus(taskId.value, 'DONE')
    await fetchTaskDetail()
    ElMessage.success('任务已完成')
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
      console.error('完成任务失败:', error)
    }
  }
}

const startTask = async () => {
  try {
    await taskApi.updateTaskStatus(taskId.value, 'IN_PROGRESS')
    await fetchTaskDetail()
    ElMessage.success('任务已开始')
  } catch (error) {
    ElMessage.error('操作失败')
    console.error('开始任务失败:', error)
  }
}

const pauseTask = async () => {
  try {
    await taskApi.updateTaskStatus(taskId.value, 'TODO')
    await fetchTaskDetail()
    ElMessage.success('任务已暂停')
  } catch (error) {
    ElMessage.error('操作失败')
    console.error('暂停任务失败:', error)
  }
}

const archiveTask = async () => {
  try {
    await ElMessageBox.confirm('确认归档此任务？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await taskApi.archiveTask(taskId.value)
    ElMessage.success('任务已归档')
    goBack()
  } catch (error) {
    if (error !== 'cancel') {
      ElMessage.error('操作失败')
      console.error('归档任务失败:', error)
    }
  }
}

const handleEditSuccess = () => {
  fetchTaskDetail()
}

const goBack = () => {
  router.back()
}

onMounted(() => {
  fetchTaskDetail()
  fetchComments()
})
</script>

<style scoped lang="scss">
.task-detail {
  padding: 20px;

  .task-content {
    margin-top: 20px;
  }

  .task-info-card {
    margin-bottom: 20px;

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .task-description {
      margin-top: 20px;

      h4 {
        margin-bottom: 10px;
        color: #303133;
      }

      .description-content {
        padding: 15px;
        background-color: #f5f7fa;
        border-radius: 4px;
        line-height: 1.6;
      }
    }

    .task-progress {
      margin-top: 20px;

      h4 {
        margin-bottom: 10px;
        color: #303133;
      }
    }
  }

  .comments-card {
    .comments-section {
      .comment-list {
        margin-bottom: 20px;

        .comment-item {
          display: flex;
          margin-bottom: 15px;
          padding-bottom: 15px;
          border-bottom: 1px solid #ebeef5;

          &:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
          }

          .comment-content {
            flex: 1;
            margin-left: 12px;

            .comment-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 8px;

              .commenter-name {
                font-weight: 500;
                color: #303133;
              }

              .comment-time {
                font-size: 12px;
                color: #909399;
              }
            }

            .comment-text {
              color: #606266;
              line-height: 1.5;
            }
          }
        }
      }

      .add-comment {
        .comment-actions {
          margin-top: 10px;
          text-align: right;
        }
      }
    }
  }

  .sidebar-card {
    margin-bottom: 20px;

    .action-buttons {
      display: flex;
      flex-direction: column;
      gap: 10px;
    }

    .related-info {
      .info-item {
        margin-bottom: 12px;
        display: flex;
        align-items: flex-start;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          font-weight: 500;
          color: #606266;
          min-width: 80px;
        }

        .tags {
          display: flex;
          flex-wrap: wrap;
          gap: 5px;
        }
      }
    }
  }
}
</style>
