/// <reference types="cypress" />

describe('任务管理E2E测试', () => {
  beforeEach(() => {
    // 登录系统
    cy.visit('/login')
    cy.get('[data-cy=username]').type('admin')
    cy.get('[data-cy=password]').type('admin123456')
    cy.get('[data-cy=login-btn]').click()
    
    // 等待登录成功并跳转到工作台
    cy.url().should('include', '/dashboard')
    cy.get('[data-cy=user-menu]').should('contain', 'admin')
  })

  describe('任务列表功能', () => {
    beforeEach(() => {
      cy.visit('/tasks')
    })

    it('应该能够显示任务列表', () => {
      cy.get('[data-cy=task-list]').should('be.visible')
      cy.get('[data-cy=task-item]').should('have.length.at.least', 1)
    })

    it('应该能够搜索任务', () => {
      cy.get('[data-cy=search-input]').type('测试任务')
      cy.get('[data-cy=search-btn]').click()
      
      cy.get('[data-cy=task-item]').each(($el) => {
        cy.wrap($el).should('contain', '测试任务')
      })
    })

    it('应该能够按状态筛选任务', () => {
      cy.get('[data-cy=status-filter]').click()
      cy.get('[data-cy=status-option-in-progress]').click()
      
      cy.get('[data-cy=task-item]').each(($el) => {
        cy.wrap($el).find('[data-cy=task-status]').should('contain', '进行中')
      })
    })

    it('应该能够按优先级筛选任务', () => {
      cy.get('[data-cy=priority-filter]').click()
      cy.get('[data-cy=priority-option-high]').click()
      
      cy.get('[data-cy=task-item]').each(($el) => {
        cy.wrap($el).find('[data-cy=task-priority]').should('contain', '高')
      })
    })

    it('应该能够分页浏览任务', () => {
      // 检查分页组件存在
      cy.get('[data-cy=pagination]').should('be.visible')
      
      // 点击下一页
      cy.get('[data-cy=next-page]').click()
      cy.url().should('include', 'page=2')
      
      // 点击上一页
      cy.get('[data-cy=prev-page]').click()
      cy.url().should('include', 'page=1')
    })
  })

  describe('任务创建功能', () => {
    beforeEach(() => {
      cy.visit('/tasks')
      cy.get('[data-cy=create-task-btn]').click()
    })

    it('应该能够创建新任务', () => {
      const taskTitle = `E2E测试任务-${Date.now()}`
      
      cy.get('[data-cy=task-title]').type(taskTitle)
      cy.get('[data-cy=task-description]').type('这是一个E2E测试创建的任务')
      
      // 选择优先级
      cy.get('[data-cy=priority-select]').click()
      cy.get('[data-cy=priority-option-high]').click()
      
      // 选择任务类型
      cy.get('[data-cy=type-select]').click()
      cy.get('[data-cy=type-option-testing]').click()
      
      // 设置截止日期
      cy.get('[data-cy=due-date]').click()
      cy.get('.el-date-picker__header-label').first().click()
      cy.get('.el-year-table td').contains('2024').click()
      cy.get('.el-month-table td').contains('二月').click()
      cy.get('.el-date-table td').contains('15').click()
      
      // 添加标签
      cy.get('[data-cy=tags-input]').type('E2E测试{enter}')
      cy.get('[data-cy=tags-input]').type('自动化{enter}')
      
      // 提交表单
      cy.get('[data-cy=submit-btn]').click()
      
      // 验证任务创建成功
      cy.get('[data-cy=success-message]').should('contain', '任务创建成功')
      cy.url().should('include', '/tasks')
      
      // 验证任务出现在列表中
      cy.get('[data-cy=task-item]').first().should('contain', taskTitle)
    })

    it('应该验证必填字段', () => {
      cy.get('[data-cy=submit-btn]').click()
      
      cy.get('[data-cy=title-error]').should('contain', '任务标题不能为空')
    })

    it('应该能够取消创建任务', () => {
      cy.get('[data-cy=cancel-btn]').click()
      cy.url().should('include', '/tasks')
    })
  })

  describe('任务详情功能', () => {
    it('应该能够查看任务详情', () => {
      cy.visit('/tasks')
      cy.get('[data-cy=task-item]').first().click()
      
      // 验证任务详情页面
      cy.get('[data-cy=task-detail]').should('be.visible')
      cy.get('[data-cy=task-title]').should('not.be.empty')
      cy.get('[data-cy=task-description]').should('be.visible')
      cy.get('[data-cy=task-creator]').should('be.visible')
      cy.get('[data-cy=task-created-time]').should('be.visible')
    })

    it('应该能够编辑任务', () => {
      cy.visit('/tasks')
      cy.get('[data-cy=task-item]').first().click()
      
      cy.get('[data-cy=edit-task-btn]').click()
      
      const newTitle = `更新的任务标题-${Date.now()}`
      cy.get('[data-cy=task-title]').clear().type(newTitle)
      cy.get('[data-cy=submit-btn]').click()
      
      cy.get('[data-cy=success-message]').should('contain', '任务更新成功')
      cy.get('[data-cy=task-title]').should('contain', newTitle)
    })

    it('应该能够更新任务状态', () => {
      cy.visit('/tasks')
      cy.get('[data-cy=task-item]').first().click()
      
      cy.get('[data-cy=status-update-btn]').click()
      cy.get('[data-cy=status-option-in-progress]').click()
      
      cy.get('[data-cy=status-comment]').type('开始执行任务')
      cy.get('[data-cy=confirm-btn]').click()
      
      cy.get('[data-cy=success-message]').should('contain', '状态更新成功')
      cy.get('[data-cy=task-status]').should('contain', '进行中')
    })

    it('应该能够分配任务', () => {
      cy.visit('/tasks')
      cy.get('[data-cy=task-item]').first().click()
      
      cy.get('[data-cy=assign-task-btn]').click()
      
      // 选择负责人
      cy.get('[data-cy=assignee-select]').click()
      cy.get('[data-cy=user-option]').first().click()
      
      cy.get('[data-cy=assign-comment]').type('分配任务给测试用户')
      cy.get('[data-cy=confirm-btn]').click()
      
      cy.get('[data-cy=success-message]').should('contain', '任务分配成功')
      cy.get('[data-cy=task-assignee]').should('not.be.empty')
    })
  })

  describe('任务评论功能', () => {
    beforeEach(() => {
      cy.visit('/tasks')
      cy.get('[data-cy=task-item]').first().click()
    })

    it('应该能够添加评论', () => {
      const commentContent = `E2E测试评论-${Date.now()}`
      
      cy.get('[data-cy=comment-input]').type(commentContent)
      cy.get('[data-cy=submit-comment-btn]').click()
      
      cy.get('[data-cy=comment-item]').first().should('contain', commentContent)
      cy.get('[data-cy=comment-author]').should('contain', 'admin')
    })

    it('应该能够回复评论', () => {
      // 点击第一个评论的回复按钮
      cy.get('[data-cy=comment-item]').first().find('[data-cy=reply-btn]').click()
      
      const replyContent = `E2E测试回复-${Date.now()}`
      cy.get('[data-cy=reply-input]').type(replyContent)
      cy.get('[data-cy=submit-reply-btn]').click()
      
      cy.get('[data-cy=reply-item]').should('contain', replyContent)
    })

    it('应该能够@提及用户', () => {
      cy.get('[data-cy=comment-input]').type('@admin 请查看这个任务')
      cy.get('[data-cy=submit-comment-btn]').click()
      
      cy.get('[data-cy=comment-item]').first().should('contain', '@admin')
    })
  })

  describe('任务附件功能', () => {
    beforeEach(() => {
      cy.visit('/tasks')
      cy.get('[data-cy=task-item]').first().click()
    })

    it('应该能够上传附件', () => {
      // 创建测试文件
      const fileName = 'test-file.txt'
      const fileContent = 'This is a test file for E2E testing'
      
      cy.get('[data-cy=file-upload]').selectFile({
        contents: Cypress.Buffer.from(fileContent),
        fileName: fileName,
        mimeType: 'text/plain'
      })
      
      cy.get('[data-cy=upload-btn]').click()
      
      cy.get('[data-cy=success-message]').should('contain', '文件上传成功')
      cy.get('[data-cy=attachment-item]').should('contain', fileName)
    })

    it('应该能够下载附件', () => {
      cy.get('[data-cy=attachment-item]').first().find('[data-cy=download-btn]').click()
      
      // 验证下载请求
      cy.get('[data-cy=download-btn]').should('have.attr', 'href').and('include', '/api/v1/files/download/')
    })

    it('应该能够删除附件', () => {
      cy.get('[data-cy=attachment-item]').first().find('[data-cy=delete-btn]').click()
      
      cy.get('[data-cy=confirm-dialog]').should('be.visible')
      cy.get('[data-cy=confirm-delete-btn]').click()
      
      cy.get('[data-cy=success-message]').should('contain', '附件删除成功')
    })
  })

  describe('批量操作功能', () => {
    beforeEach(() => {
      cy.visit('/tasks')
    })

    it('应该能够批量选择任务', () => {
      cy.get('[data-cy=select-all-checkbox]').click()
      cy.get('[data-cy=task-checkbox]').should('be.checked')
      
      cy.get('[data-cy=batch-actions]').should('be.visible')
    })

    it('应该能够批量更新状态', () => {
      // 选择多个任务
      cy.get('[data-cy=task-checkbox]').first().click()
      cy.get('[data-cy=task-checkbox]').eq(1).click()
      
      cy.get('[data-cy=batch-status-btn]').click()
      cy.get('[data-cy=batch-status-option-in-progress]').click()
      
      cy.get('[data-cy=confirm-btn]').click()
      
      cy.get('[data-cy=success-message]').should('contain', '批量操作成功')
    })

    it('应该能够批量删除任务', () => {
      // 选择多个任务
      cy.get('[data-cy=task-checkbox]').first().click()
      cy.get('[data-cy=task-checkbox]').eq(1).click()
      
      cy.get('[data-cy=batch-delete-btn]').click()
      
      cy.get('[data-cy=confirm-dialog]').should('be.visible')
      cy.get('[data-cy=confirm-delete-btn]').click()
      
      cy.get('[data-cy=success-message]').should('contain', '批量删除成功')
    })
  })

  describe('响应式设计测试', () => {
    it('应该在移动端正常显示', () => {
      cy.viewport('iphone-6')
      cy.visit('/tasks')
      
      cy.get('[data-cy=mobile-menu-btn]').should('be.visible')
      cy.get('[data-cy=task-list]').should('be.visible')
      
      // 测试移动端任务卡片布局
      cy.get('[data-cy=task-item]').first().should('have.class', 'mobile-layout')
    })

    it('应该在平板端正常显示', () => {
      cy.viewport('ipad-2')
      cy.visit('/tasks')
      
      cy.get('[data-cy=task-list]').should('be.visible')
      cy.get('[data-cy=sidebar]').should('be.visible')
    })
  })

  describe('错误处理测试', () => {
    it('应该处理网络错误', () => {
      // 模拟网络错误
      cy.intercept('GET', '/api/v1/tasks', { forceNetworkError: true })
      
      cy.visit('/tasks')
      
      cy.get('[data-cy=error-message]').should('contain', '网络连接失败')
      cy.get('[data-cy=retry-btn]').should('be.visible')
    })

    it('应该处理服务器错误', () => {
      // 模拟服务器错误
      cy.intercept('GET', '/api/v1/tasks', { statusCode: 500 })
      
      cy.visit('/tasks')
      
      cy.get('[data-cy=error-message]').should('contain', '服务器错误')
    })

    it('应该处理权限不足错误', () => {
      // 模拟权限不足
      cy.intercept('POST', '/api/v1/tasks', { statusCode: 403 })
      
      cy.visit('/tasks')
      cy.get('[data-cy=create-task-btn]').click()
      
      cy.get('[data-cy=task-title]').type('测试任务')
      cy.get('[data-cy=submit-btn]').click()
      
      cy.get('[data-cy=error-message]').should('contain', '权限不足')
    })
  })

  afterEach(() => {
    // 清理测试数据
    cy.window().then((win) => {
      win.localStorage.clear()
      win.sessionStorage.clear()
    })
  })
})
