# WTMS 工作任务管理平台

## 项目简介

WTMS（Work Task Management System）是一个企业级的工作任务管理平台，提供完整的任务管理、工作流控制、质量评价、团队协作等功能。

## 技术栈

### 前端技术栈
- **框架**: Vue.js 3.4+ (Composition API)
- **构建工具**: Vite 5.0+
- **UI组件库**: Element Plus 2.4+
- **状态管理**: Pinia 2.1+
- **路由管理**: Vue Router 4.2+
- **图表库**: ECharts 5.4+
- **HTTP客户端**: Axios 1.6+
- **CSS预处理**: Sass/SCSS
- **TypeScript**: 5.2+

### 后端技术栈
- **框架**: Spring Boot 2.7.18 (兼容JDK 8)
- **安全框架**: Spring Security 5.7+ + JWT
- **数据访问**: MyBatis-Plus 3.5.4+
- **数据库**: MySQL 8.0+
- **缓存**: Redis 7.0+
- **API文档**: Swagger 3.0 (SpringDoc OpenAPI)
- **构建工具**: Maven 3.9+

### 基础设施
- **容器化**: Docker + Docker Compose
- **反向代理**: Nginx
- **监控**: Spring Boot Actuator
- **日志**: Logback + ELK Stack

## 核心功能

- ✅ **任务基础信息管理**：标准化命名、富文本编辑、分类标签
- ✅ **任务流程控制系统**：状态管理、工作流自动化、依赖关系
- ✅ **执行标准与规范**：标准定义、文档管理、质量检查
- ✅ **质量评价系统**：多维度评价、评价流程、结果分析
- ✅ **能力需求管理**：技能匹配、人员分配、培训分析
- ✅ **难度与价值评估**：难度评估、价值分析、工作量估算

## 快速开始

### 环境要求

- Node.js 18+
- JDK 8+
- Maven 3.6+
- Docker & Docker Compose
- MySQL 8.0+
- Redis 7.0+

### 开发环境搭建

#### 1. 克隆项目
```bash
git clone <repository-url>
cd wtms
```

#### 2. 启动基础服务（MySQL + Redis）
```bash
# 启动开发环境的数据库和缓存服务
docker-compose -f docker-compose.dev.yml up -d mysql-dev redis-dev

# 可选：启动数据库管理工具
docker-compose -f docker-compose.dev.yml up -d phpmyadmin redis-commander
```

#### 3. 后端开发环境
```bash
cd wtms-backend

# 编译项目
mvn clean compile

# 启动应用
mvn spring-boot:run

# 或者使用IDE直接运行 WtmsApplication.java
```

#### 4. 前端开发环境
```bash
cd wtms-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev
```

### 生产环境部署

#### 使用Docker Compose一键部署
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

#### 手动部署

1. **数据库初始化**
```bash
mysql -u root -p < MySQL数据库设计.sql
```

2. **后端部署**
```bash
cd wtms-backend
mvn clean package -DskipTests
java -jar target/wtms-backend-1.0.0.jar
```

3. **前端部署**
```bash
cd wtms-frontend
npm run build
# 将 dist 目录部署到 Web 服务器
```

## 访问地址

- **前端应用**: http://localhost:33335
- **后端API**: http://localhost:55557/api/v1
- **API文档**: http://localhost:55557/swagger-ui.html
- **数据库管理**: http://localhost:8081 (phpMyAdmin)
- **Redis管理**: http://localhost:8082 (Redis Commander)

## 默认账户

**应用登录：**
- **用户名**: admin
- **密码**: 123456

**数据库连接：**
- **Host**: localhost
- **Port**: 3308
- **Username**: root
- **Password**: ankaixin.docker.mysql
- **Database**: wtms

## 项目结构

```
wtms/
├── wtms-frontend/          # Vue.js前端项目
│   ├── src/
│   │   ├── api/           # API接口
│   │   ├── components/    # 公共组件
│   │   ├── layouts/       # 布局组件
│   │   ├── pages/         # 页面组件
│   │   ├── router/        # 路由配置
│   │   ├── stores/        # Pinia状态管理
│   │   ├── styles/        # 样式文件
│   │   ├── types/         # TypeScript类型
│   │   └── utils/         # 工具函数
│   ├── package.json
│   └── vite.config.ts
├── wtms-backend/           # Spring Boot后端项目
│   ├── src/main/java/com/wtms/
│   │   ├── config/        # 配置类
│   │   ├── controller/    # 控制器
│   │   ├── service/       # 服务层
│   │   ├── mapper/        # 数据访问层
│   │   ├── entity/        # 实体类
│   │   ├── dto/           # 数据传输对象
│   │   ├── common/        # 公共类
│   │   └── security/      # 安全相关
│   ├── src/main/resources/
│   │   ├── application.yml
│   │   └── mapper/        # MyBatis XML文件
│   └── pom.xml
├── docker-compose.yml      # 生产环境Docker配置
├── docker-compose.dev.yml  # 开发环境Docker配置
├── MySQL数据库设计.sql     # 数据库初始化脚本
└── README.md
```

## 开发指南

### 代码规范

- **前端**: 使用ESLint + Prettier进行代码格式化
- **后端**: 遵循阿里巴巴Java开发手册
- **数据库**: 使用下划线命名法
- **API**: 遵循RESTful设计规范

### Git提交规范

```
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 代码重构
test: 测试相关
chore: 构建过程或辅助工具的变动
```

### 测试

```bash
# 后端测试
cd wtms-backend
mvn test

# 前端测试
cd wtms-frontend
npm run test
```

## 常见问题

### 1. 数据库连接失败
- 检查MySQL服务是否启动
- 确认数据库连接配置是否正确
- 检查防火墙设置

### 2. 前端无法访问后端API
- 确认后端服务已启动
- 检查跨域配置
- 验证API地址配置

### 3. Docker容器启动失败
- 检查Docker和Docker Compose版本
- 确认端口是否被占用
- 查看容器日志排查问题

## 贡献指南

1. Fork 项目
2. 创建特性分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 联系我们

- 项目主页: https://github.com/wtms/wtms
- 问题反馈: https://github.com/wtms/wtms/issues
- 邮箱: <EMAIL>

## 📖 项目文档

完整的项目文档已准备就绪：

- [📋 项目总结报告](docs/PROJECT_SUMMARY.md) - 项目概述、技术架构、功能特性、开发成果
- [🚀 部署文档](docs/deployment/DEPLOYMENT_GUIDE.md) - 详细的部署指南和配置说明
- [👥 用户手册](docs/user-manual/USER_MANUAL.md) - 面向最终用户的使用指南
- [🔧 管理员手册](docs/user-manual/ADMIN_MANUAL.md) - 系统管理员操作手册
- [🔌 API文档](docs/api/API_DOCUMENTATION.md) - 完整的API接口文档
- [📊 OpenAPI规范](docs/api/openapi.yaml) - 标准的OpenAPI 3.0规范文件
- [🧪 测试文档](docs/testing/TEST_PLAN.md) - 测试计划、用例和数据

## 🎉 项目完成状态

**🎊 项目已全面完成！**

所有计划的功能模块均已开发完成，包括：
- ✅ 系统设计方案制定
- ✅ 前后端项目开发
- ✅ 数据库设计和实现
- ✅ 核心功能开发（任务管理、工作流、权限、评价）
- ✅ 部署环境配置
- ✅ 完整文档编写
- ✅ 测试用例准备
- ✅ 项目总结报告

### 交付成果
1. **完整的源代码** - 前后端完整实现
2. **数据库脚本** - 建表脚本和测试数据
3. **部署配置** - Docker Compose一键部署
4. **技术文档** - 设计文档、API文档、部署文档
5. **用户手册** - 用户使用指南和管理员手册
6. **测试资料** - 测试计划、用例和Postman集合
7. **项目总结** - 完整的项目总结报告

---

**🎯 WTMS 工作任务管理平台 - 让工作更高效，让管理更智能！**

**⭐ 如果这个项目对你有帮助，请给我们一个 Star！**

Made with ❤️ by WTMS Team
