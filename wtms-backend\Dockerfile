# 使用OpenJDK 8作为基础镜像
FROM openjdk:8-jre-alpine

# 设置工作目录
WORKDIR /app

# 安装必要的工具
RUN apk add --no-cache curl tzdata

# 设置时区
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建应用用户
RUN addgroup -g 1000 wtms && \
    adduser -D -s /bin/sh -u 1000 -G wtms wtms

# 创建必要的目录
RUN mkdir -p /app/logs /app/uploads && \
    chown -R wtms:wtms /app

# 复制Maven构建的jar文件
COPY target/wtms-backend-*.jar app.jar

# 修改文件权限
RUN chown wtms:wtms app.jar

# 切换到应用用户
USER wtms

# 暴露端口
EXPOSE 8080

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s --retries=3 \
    CMD curl -f http://localhost:8080/actuator/health || exit 1

# 启动应用
ENTRYPOINT ["java", \
    "-Djava.security.egd=file:/dev/./urandom", \
    "-Dspring.profiles.active=docker", \
    "-Xms256m", \
    "-Xmx512m", \
    "-jar", \
    "app.jar"]
