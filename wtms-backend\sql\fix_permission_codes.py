#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复权限编码规范性脚本
将不规范的权限编码修复为标准格式
"""

import pymysql
import sys
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3308,
    'user': 'root',
    'password': 'ankaixin.docker.mysql',
    'database': 'wtms',
    'charset': 'utf8mb4'
}

def fix_permission_codes():
    """修复权限编码"""
    print("🔧 修复权限编码规范性...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 权限编码映射表
        code_mappings = {
            'permission': 'permission:manage',
            'project': 'project:manage', 
            'report': 'report:manage',
            'role': 'role:manage',
            'system': 'system:manage',
            'task': 'task:manage',
            'user': 'user:manage'
        }
        
        fixed_count = 0
        
        for old_code, new_code in code_mappings.items():
            # 检查是否存在需要修复的权限
            cursor.execute("SELECT id, name FROM permissions WHERE code = %s", (old_code,))
            result = cursor.fetchone()
            
            if result:
                permission_id, permission_name = result
                
                # 检查新编码是否已存在
                cursor.execute("SELECT id FROM permissions WHERE code = %s", (new_code,))
                if cursor.fetchone():
                    print(f"  ⚠️ 权限编码 {new_code} 已存在，跳过 {old_code}")
                    continue
                
                # 更新权限编码
                cursor.execute("UPDATE permissions SET code = %s WHERE id = %s", (new_code, permission_id))
                print(f"  ✅ 修复权限编码: {old_code} -> {new_code} ({permission_name})")
                fixed_count += 1
        
        connection.commit()
        connection.close()
        
        print(f"🎉 成功修复 {fixed_count} 个权限编码")
        return True
        
    except Exception as e:
        print(f"❌ 修复权限编码失败: {str(e)}")
        return False

def add_missing_permissions():
    """添加缺失的权限"""
    print("\n🔧 添加缺失的权限...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 需要添加的权限
        missing_permissions = [
            ('system:test', '系统测试', '系统测试权限', 'system', 'action'),
            ('task:comment:manage', '评论管理', '管理任务评论', 'task', 'action'),
            ('task:attachment:manage', '附件管理', '管理任务附件', 'task', 'action'),
            ('task:category:manage', '分类管理', '管理任务分类', 'task', 'action'),
            ('user:profile:update', '个人资料', '更新个人资料', 'user', 'action'),
            ('system:config:update', '系统配置', '更新系统配置', 'system', 'action')
        ]
        
        added_count = 0
        
        for code, name, description, group_name, permission_type in missing_permissions:
            # 检查权限是否已存在
            cursor.execute("SELECT id FROM permissions WHERE code = %s", (code,))
            if cursor.fetchone():
                continue
            
            # 生成权限ID
            permission_id = f"perm-{code.replace(':', '-')}"
            
            # 插入权限
            cursor.execute("""
                INSERT INTO permissions (id, code, name, description, group_name, type, is_enabled, is_system)
                VALUES (%s, %s, %s, %s, %s, %s, 1, 1)
            """, (permission_id, code, name, description, group_name, permission_type))
            
            print(f"  ✅ 添加权限: {code} ({name})")
            added_count += 1
        
        connection.commit()
        connection.close()
        
        print(f"🎉 成功添加 {added_count} 个权限")
        return True
        
    except Exception as e:
        print(f"❌ 添加权限失败: {str(e)}")
        return False

def update_role_permissions():
    """更新角色权限分配"""
    print("\n🔧 更新角色权限分配...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 为超级管理员添加所有新权限
        cursor.execute("SELECT id FROM roles WHERE code = 'SUPER_ADMIN'")
        super_admin_role = cursor.fetchone()
        
        if super_admin_role:
            role_id = super_admin_role[0]
            
            # 获取所有权限
            cursor.execute("SELECT id FROM permissions WHERE is_enabled = 1")
            all_permissions = cursor.fetchall()
            
            # 获取已分配的权限
            cursor.execute("SELECT permission_id FROM role_permissions WHERE role_id = %s", (role_id,))
            assigned_permissions = {row[0] for row in cursor.fetchall()}
            
            # 添加缺失的权限
            added_count = 0
            for permission_id_tuple in all_permissions:
                permission_id = permission_id_tuple[0]
                if permission_id not in assigned_permissions:
                    # 生成唯一的角色权限ID
                    import uuid
                    rp_id = f"rp-super-{str(uuid.uuid4())[:8]}"

                    try:
                        cursor.execute("""
                            INSERT INTO role_permissions (id, role_id, permission_id)
                            VALUES (%s, %s, %s)
                        """, (rp_id, role_id, permission_id))
                        added_count += 1
                    except Exception as e:
                        if 'Duplicate entry' not in str(e):
                            print(f"    ⚠️ 添加权限关联失败: {permission_id} - {str(e)}")
                        continue
            
            if added_count > 0:
                print(f"  ✅ 为超级管理员添加了 {added_count} 个新权限")
            else:
                print("  ✅ 超级管理员权限已完整")
        
        connection.commit()
        connection.close()
        
        return True
        
    except Exception as e:
        print(f"❌ 更新角色权限失败: {str(e)}")
        return False

def verify_fixes():
    """验证修复结果"""
    print("\n🔍 验证修复结果...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 检查权限编码规范性
        cursor.execute("SELECT code FROM permissions WHERE code NOT LIKE '%:%' AND is_enabled = 1")
        invalid_codes = cursor.fetchall()
        
        if invalid_codes:
            print(f"  ⚠️ 仍有 {len(invalid_codes)} 个不规范的权限编码")
            for code_tuple in invalid_codes[:5]:
                print(f"    - {code_tuple[0]}")
        else:
            print("  ✅ 所有权限编码都符合规范")
        
        # 检查超级管理员权限数量
        cursor.execute("""
            SELECT COUNT(DISTINCT p.id)
            FROM roles r
            JOIN role_permissions rp ON r.id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id AND p.is_enabled = 1
            WHERE r.code = 'SUPER_ADMIN'
        """)
        super_admin_perms = cursor.fetchone()[0]
        print(f"  📊 超级管理员权限数量: {super_admin_perms}")
        
        # 检查开发用户权限
        cursor.execute("""
            SELECT u.username, COUNT(DISTINCT p.id) as perm_count
            FROM users u
            JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = 1
            JOIN roles r ON ur.role_id = r.id
            JOIN role_permissions rp ON r.id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id AND p.is_enabled = 1
            WHERE u.username IN ('dev001', 'dev002')
            GROUP BY u.id, u.username
        """)
        
        dev_users = cursor.fetchall()
        print("  🛠️ 开发用户权限验证:")
        for username, perm_count in dev_users:
            status = "✅" if perm_count >= 50 else "⚠️"
            print(f"    {status} {username}: {perm_count} 个权限")
        
        connection.close()
        return len(invalid_codes) == 0
        
    except Exception as e:
        print(f"❌ 验证修复结果失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 WTMS 权限编码规范性修复")
    print("="*60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    success = True
    
    # 1. 修复权限编码
    if not fix_permission_codes():
        success = False
    
    # 2. 添加缺失的权限
    if not add_missing_permissions():
        success = False
    
    # 3. 更新角色权限分配
    if not update_role_permissions():
        success = False
    
    # 4. 验证修复结果
    if not verify_fixes():
        success = False
    
    print("\n" + "="*60)
    if success:
        print("🎉 权限编码修复完成！")
        print("📋 修复内容:")
        print("   ✅ 权限编码规范化")
        print("   ✅ 添加缺失权限")
        print("   ✅ 更新角色权限分配")
        print("   ✅ 验证修复结果")
    else:
        print("❌ 权限编码修复失败！请检查上述错误信息。")
        sys.exit(1)
    print("="*60)
    print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    try:
        import pymysql
    except ImportError:
        print("❌ 缺少pymysql模块，请安装: pip install pymysql")
        sys.exit(1)
    
    main()
