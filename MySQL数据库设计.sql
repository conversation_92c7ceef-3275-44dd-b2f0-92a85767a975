-- WTMS 工作任务管理平台数据库设计
-- 数据库: MySQL 8.0+
-- 字符集: UTF8MB4
-- 排序规则: utf8mb4_unicode_ci

-- 创建数据库
CREATE DATABASE IF NOT EXISTS wtms_db 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

USE wtms_db;

-- ================================
-- 1. 用户管理模块
-- ================================

-- 部门表
CREATE TABLE departments (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL COMMENT '部门名称',
    code VARCHAR(20) UNIQUE NOT NULL COMMENT '部门编码',
    description TEXT COMMENT '部门描述',
    parent_id CHAR(36) COMMENT '父部门ID',
    manager_id CHAR(36) COMMENT '部门经理ID',
    level INT DEFAULT 1 COMMENT '部门层级',
    sort_order INT DEFAULT 0 COMMENT '排序',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-活跃, inactive-停用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_parent_id (parent_id),
    INDEX idx_manager_id (manager_id),
    INDEX idx_status (status),
    FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 角色表
CREATE TABLE roles (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(50) NOT NULL COMMENT '角色名称',
    code VARCHAR(20) UNIQUE NOT NULL COMMENT '角色编码',
    description TEXT COMMENT '角色描述',
    permissions JSON COMMENT '权限列表',
    is_system BOOLEAN DEFAULT FALSE COMMENT '是否系统角色',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code),
    INDEX idx_is_system (is_system)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 用户表
CREATE TABLE users (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    email VARCHAR(100) UNIQUE NOT NULL COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    salt VARCHAR(50) NOT NULL COMMENT '密码盐值',
    full_name VARCHAR(100) NOT NULL COMMENT '姓名',
    avatar_url VARCHAR(500) COMMENT '头像URL',
    department_id CHAR(36) COMMENT '部门ID',
    role_id CHAR(36) COMMENT '角色ID',
    employee_id VARCHAR(50) UNIQUE COMMENT '工号',
    hire_date DATE COMMENT '入职日期',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态：active-活跃, inactive-停用, locked-锁定',
    last_login_at TIMESTAMP NULL COMMENT '最后登录时间',
    login_count INT DEFAULT 0 COMMENT '登录次数',
    settings JSON COMMENT '用户设置',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_username (username),
    INDEX idx_email (email),
    INDEX idx_department_id (department_id),
    INDEX idx_role_id (role_id),
    INDEX idx_employee_id (employee_id),
    INDEX idx_status (status),
    FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE SET NULL,
    FOREIGN KEY (role_id) REFERENCES roles(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 添加部门管理者外键约束
ALTER TABLE departments ADD CONSTRAINT fk_departments_manager 
    FOREIGN KEY (manager_id) REFERENCES users(id) ON DELETE SET NULL;

-- ================================
-- 2. 技能管理模块
-- ================================

-- 技能分类表
CREATE TABLE skill_categories (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    description TEXT COMMENT '分类描述',
    parent_id CHAR(36) COMMENT '父分类ID',
    icon VARCHAR(50) COMMENT '图标',
    color VARCHAR(7) COMMENT '颜色代码',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_parent_id (parent_id),
    FOREIGN KEY (parent_id) REFERENCES skill_categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='技能分类表';

-- 技能表
CREATE TABLE skills (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL COMMENT '技能名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '技能编码',
    description TEXT COMMENT '技能描述',
    category_id CHAR(36) COMMENT '分类ID',
    level_definitions JSON COMMENT '各等级定义',
    assessment_criteria JSON COMMENT '评估标准',
    related_skills JSON COMMENT '相关技能ID数组',
    is_core BOOLEAN DEFAULT FALSE COMMENT '是否核心技能',
    status VARCHAR(20) DEFAULT 'active' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_code (code),
    INDEX idx_category_id (category_id),
    INDEX idx_is_core (is_core),
    INDEX idx_status (status),
    FOREIGN KEY (category_id) REFERENCES skill_categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='技能表';

-- 用户技能表
CREATE TABLE user_skills (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    user_id CHAR(36) NOT NULL COMMENT '用户ID',
    skill_id CHAR(36) NOT NULL COMMENT '技能ID',
    level INT CHECK (level >= 1 AND level <= 5) COMMENT '技能等级 1-5',
    certified BOOLEAN DEFAULT FALSE COMMENT '是否认证',
    certified_at TIMESTAMP NULL COMMENT '认证时间',
    certified_by CHAR(36) COMMENT '认证人',
    notes TEXT COMMENT '备注',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    UNIQUE KEY uk_user_skill (user_id, skill_id),
    INDEX idx_user_id (user_id),
    INDEX idx_skill_id (skill_id),
    INDEX idx_level (level),
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE,
    FOREIGN KEY (certified_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户技能表';

-- ================================
-- 3. 任务管理模块
-- ================================

-- 任务分类表
CREATE TABLE task_categories (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    name VARCHAR(100) NOT NULL COMMENT '分类名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '分类编码',
    description TEXT COMMENT '分类描述',
    parent_id CHAR(36) COMMENT '父分类ID',
    color VARCHAR(7) COMMENT '颜色代码',
    icon VARCHAR(50) COMMENT '图标',
    naming_template VARCHAR(200) COMMENT '命名模板',
    default_workflow_id CHAR(36) COMMENT '默认工作流',
    sort_order INT DEFAULT 0 COMMENT '排序',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    INDEX idx_code (code),
    INDEX idx_parent_id (parent_id),
    FOREIGN KEY (parent_id) REFERENCES task_categories(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务分类表';

-- 任务表
CREATE TABLE tasks (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    title VARCHAR(200) NOT NULL COMMENT '任务标题',
    description TEXT COMMENT '任务描述',
    task_code VARCHAR(100) UNIQUE NOT NULL COMMENT '任务编号',
    category_id CHAR(36) COMMENT '分类ID',
    parent_id CHAR(36) COMMENT '父任务ID（子任务支持）',
    
    -- 基本信息
    priority INT DEFAULT 3 CHECK (priority >= 1 AND priority <= 5) COMMENT '优先级 1-5',
    difficulty_level INT DEFAULT 1 CHECK (difficulty_level >= 1 AND difficulty_level <= 5) COMMENT '难度等级 1-5',
    business_value INT DEFAULT 3 CHECK (business_value >= 1 AND business_value <= 5) COMMENT '业务价值 1-5',
    
    -- 时间管理
    estimated_hours DECIMAL(8,2) COMMENT '预估工时',
    actual_hours DECIMAL(8,2) COMMENT '实际工时',
    planned_start_date TIMESTAMP NULL COMMENT '计划开始时间',
    planned_end_date TIMESTAMP NULL COMMENT '计划结束时间',
    actual_start_date TIMESTAMP NULL COMMENT '实际开始时间',
    actual_end_date TIMESTAMP NULL COMMENT '实际结束时间',
    due_date TIMESTAMP NULL COMMENT '截止时间',
    
    -- 状态管理
    status VARCHAR(20) DEFAULT 'draft' COMMENT '状态：draft-草稿, pending-待开始, in_progress-进行中, review-待审核, testing-测试中, completed-已完成, paused-已暂停, cancelled-已取消, archived-已归档',
    progress DECIMAL(5,2) DEFAULT 0 CHECK (progress >= 0 AND progress <= 100) COMMENT '进度百分比',
    
    -- 人员分配
    creator_id CHAR(36) NOT NULL COMMENT '创建人ID',
    assignee_id CHAR(36) COMMENT '负责人ID',
    reviewer_id CHAR(36) COMMENT '审核人ID',
    
    -- 扩展信息
    tags JSON COMMENT '标签数组',
    custom_fields JSON COMMENT '自定义字段',
    attachments JSON COMMENT '附件信息',
    
    -- 质量评估
    quality_score DECIMAL(3,1) COMMENT '质量得分',
    completion_quality JSON COMMENT '完成质量详情',
    
    -- 审计信息
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    deleted_at TIMESTAMP NULL COMMENT '软删除时间',
    
    INDEX idx_task_code (task_code),
    INDEX idx_category_id (category_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_creator_id (creator_id),
    INDEX idx_assignee_id (assignee_id),
    INDEX idx_reviewer_id (reviewer_id),
    INDEX idx_status (status),
    INDEX idx_priority (priority),
    INDEX idx_due_date (due_date),
    INDEX idx_created_at (created_at),
    INDEX idx_deleted_at (deleted_at),
    FULLTEXT idx_title_description (title, description),
    
    FOREIGN KEY (category_id) REFERENCES task_categories(id) ON DELETE SET NULL,
    FOREIGN KEY (parent_id) REFERENCES tasks(id) ON DELETE SET NULL,
    FOREIGN KEY (creator_id) REFERENCES users(id) ON DELETE RESTRICT,
    FOREIGN KEY (assignee_id) REFERENCES users(id) ON DELETE SET NULL,
    FOREIGN KEY (reviewer_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 任务描述版本表
CREATE TABLE task_descriptions (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    task_id CHAR(36) NOT NULL COMMENT '任务ID',
    content TEXT NOT NULL COMMENT 'Markdown格式内容',
    structured_fields JSON COMMENT '结构化字段',
    version INT NOT NULL COMMENT '版本号',
    created_by CHAR(36) COMMENT '创建人',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_task_version (task_id, version),
    INDEX idx_task_id (task_id),
    INDEX idx_created_by (created_by),
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务描述版本表';

-- 任务依赖关系表
CREATE TABLE task_dependencies (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    predecessor_id CHAR(36) NOT NULL COMMENT '前置任务ID',
    successor_id CHAR(36) NOT NULL COMMENT '后续任务ID',
    dependency_type VARCHAR(2) DEFAULT 'FS' COMMENT '依赖类型：FS-完成到开始, SS-开始到开始, FF-完成到完成, SF-开始到完成',
    lag_days INT DEFAULT 0 COMMENT '延迟天数',
    description TEXT COMMENT '依赖描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_predecessor_successor (predecessor_id, successor_id),
    INDEX idx_predecessor_id (predecessor_id),
    INDEX idx_successor_id (successor_id),
    FOREIGN KEY (predecessor_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (successor_id) REFERENCES tasks(id) ON DELETE CASCADE,
    CHECK (predecessor_id != successor_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务依赖关系表';

-- 任务技能需求表
CREATE TABLE task_skill_requirements (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    task_id CHAR(36) NOT NULL COMMENT '任务ID',
    skill_id CHAR(36) NOT NULL COMMENT '技能ID',
    required_level INT CHECK (required_level >= 1 AND required_level <= 5) COMMENT '要求等级',
    is_mandatory BOOLEAN DEFAULT TRUE COMMENT '是否必需',
    weight DECIMAL(3,2) DEFAULT 1.0 COMMENT '权重',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    UNIQUE KEY uk_task_skill (task_id, skill_id),
    INDEX idx_task_id (task_id),
    INDEX idx_skill_id (skill_id),
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (skill_id) REFERENCES skills(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务技能需求表';

-- 任务评论表
CREATE TABLE task_comments (
    id CHAR(36) PRIMARY KEY DEFAULT (UUID()),
    task_id CHAR(36) NOT NULL COMMENT '任务ID',
    parent_id CHAR(36) COMMENT '父评论ID（支持回复）',
    author_id CHAR(36) COMMENT '作者ID',
    content TEXT NOT NULL COMMENT '评论内容',
    comment_type VARCHAR(20) DEFAULT 'comment' COMMENT '评论类型：comment-评论, system-系统, status_change-状态变更',
    attachments JSON COMMENT '附件信息',
    is_internal BOOLEAN DEFAULT FALSE COMMENT '是否内部评论',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    INDEX idx_task_id (task_id),
    INDEX idx_parent_id (parent_id),
    INDEX idx_author_id (author_id),
    INDEX idx_comment_type (comment_type),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (task_id) REFERENCES tasks(id) ON DELETE CASCADE,
    FOREIGN KEY (parent_id) REFERENCES task_comments(id) ON DELETE CASCADE,
    FOREIGN KEY (author_id) REFERENCES users(id) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务评论表';

-- ================================
-- 4. 初始化数据
-- ================================

-- 插入默认角色
INSERT INTO roles (id, name, code, description, permissions, is_system, created_at, updated_at) VALUES
(UUID(), '系统管理员', 'ADMIN', '系统管理员，拥有所有权限', '["*"]', TRUE, NOW(), NOW()),
(UUID(), '项目经理', 'PM', '项目经理，负责项目管理', '["task:*", "workflow:*", "evaluation:read"]', TRUE, NOW(), NOW()),
(UUID(), '开发人员', 'DEVELOPER', '开发人员，负责任务开发', '["task:read", "task:update", "task:comment"]', TRUE, NOW(), NOW()),
(UUID(), '测试人员', 'TESTER', '测试人员，负责质量测试', '["task:read", "task:comment", "evaluation:*"]', TRUE, NOW(), NOW());

-- 插入默认部门
INSERT INTO departments (id, name, code, description, level, sort_order, status, created_at, updated_at) VALUES
(UUID(), '技术部', 'TECH', '技术开发部门', 1, 1, 'active', NOW(), NOW()),
(UUID(), '产品部', 'PRODUCT', '产品管理部门', 1, 2, 'active', NOW(), NOW()),
(UUID(), '运营部', 'OPERATION', '运营管理部门', 1, 3, 'active', NOW(), NOW());

-- 插入默认管理员用户
SET @admin_role_id = (SELECT id FROM roles WHERE code = 'ADMIN' LIMIT 1);
SET @tech_dept_id = (SELECT id FROM departments WHERE code = 'TECH' LIMIT 1);

INSERT INTO users (id, username, email, password_hash, salt, full_name, department_id, role_id, employee_id, status, login_count, created_at, updated_at) VALUES
(UUID(), 'admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBaLIU8pMzxKw6', 'admin_salt', '系统管理员', @tech_dept_id, @admin_role_id, 'EMP001', 'active', 0, NOW(), NOW());

-- 插入任务分类
INSERT INTO task_categories (id, name, code, description, color, icon, naming_template, sort_order, created_at) VALUES
(UUID(), '开发任务', 'DEV', '软件开发相关任务', '#409EFF', 'code', 'DEV-{YYYY}{MM}{DD}-{###}', 1, NOW()),
(UUID(), '测试任务', 'TEST', '软件测试相关任务', '#67C23A', 'test', 'TEST-{YYYY}{MM}{DD}-{###}', 2, NOW()),
(UUID(), '设计任务', 'DESIGN', '设计相关任务', '#E6A23C', 'design', 'DESIGN-{YYYY}{MM}{DD}-{###}', 3, NOW()),
(UUID(), '运维任务', 'OPS', '运维相关任务', '#F56C6C', 'ops', 'OPS-{YYYY}{MM}{DD}-{###}', 4, NOW());

-- 插入技能分类
INSERT INTO skill_categories (id, name, description, icon, color, sort_order, created_at) VALUES
(UUID(), '编程语言', '各种编程语言技能', 'code', '#409EFF', 1, NOW()),
(UUID(), '框架技术', '各种开发框架技能', 'framework', '#67C23A', 2, NOW()),
(UUID(), '数据库', '数据库相关技能', 'database', '#E6A23C', 3, NOW()),
(UUID(), '工具软件', '开发工具软件技能', 'tool', '#F56C6C', 4, NOW());

-- 插入基础技能
SET @prog_cat_id = (SELECT id FROM skill_categories WHERE name = '编程语言' LIMIT 1);
SET @frame_cat_id = (SELECT id FROM skill_categories WHERE name = '框架技术' LIMIT 1);
SET @db_cat_id = (SELECT id FROM skill_categories WHERE name = '数据库' LIMIT 1);

INSERT INTO skills (id, name, code, description, category_id, level_definitions, is_core, status, created_at, updated_at) VALUES
(UUID(), 'Java', 'JAVA', 'Java编程语言', @prog_cat_id, '{"1":"入门","2":"初级","3":"中级","4":"高级","5":"专家"}', TRUE, 'active', NOW(), NOW()),
(UUID(), 'JavaScript', 'JS', 'JavaScript编程语言', @prog_cat_id, '{"1":"入门","2":"初级","3":"中级","4":"高级","5":"专家"}', TRUE, 'active', NOW(), NOW()),
(UUID(), 'Spring Boot', 'SPRING_BOOT', 'Spring Boot框架', @frame_cat_id, '{"1":"入门","2":"初级","3":"中级","4":"高级","5":"专家"}', TRUE, 'active', NOW(), NOW()),
(UUID(), 'Vue.js', 'VUE', 'Vue.js前端框架', @frame_cat_id, '{"1":"入门","2":"初级","3":"中级","4":"高级","5":"专家"}', TRUE, 'active', NOW(), NOW()),
(UUID(), 'MySQL', 'MYSQL', 'MySQL数据库', @db_cat_id, '{"1":"入门","2":"初级","3":"中级","4":"高级","5":"专家"}', TRUE, 'active', NOW(), NOW());
