package com.wtms.dto.response;

import com.wtms.entity.User;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户信息响应DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户信息响应")
public class UserInfoResponse {

    @Schema(description = "用户ID")
    private String id;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "邮箱")
    private String email;

    @Schema(description = "手机号")
    private String phone;

    @Schema(description = "姓名")
    private String fullName;

    @Schema(description = "头像URL")
    private String avatar;

    @Schema(description = "工号")
    private String employeeId;

    @Schema(description = "入职日期")
    private LocalDate hireDate;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "最后登录时间")
    private LocalDateTime lastLoginAt;

    @Schema(description = "登录次数")
    private Integer loginCount;

    @Schema(description = "部门信息")
    private DepartmentInfo department;

    @Schema(description = "角色信息")
    private RoleInfo role;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    /**
     * 部门信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "部门信息")
    public static class DepartmentInfo {
        @Schema(description = "部门ID")
        private String id;

        @Schema(description = "部门名称")
        private String name;

        @Schema(description = "部门编码")
        private String code;
    }

    /**
     * 角色信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "角色信息")
    public static class RoleInfo {
        @Schema(description = "角色ID")
        private String id;

        @Schema(description = "角色名称")
        private String name;

        @Schema(description = "角色编码")
        private String code;

        @Schema(description = "权限列表")
        private List<String> permissions;
    }

    /**
     * 从User实体转换
     */
    public static UserInfoResponse from(User user) {
        UserInfoResponseBuilder builder = UserInfoResponse.builder()
                .id(user.getId())
                .username(user.getUsername())
                .email(user.getEmail())
                .phone(user.getPhone())
                .fullName(user.getFullName())
                .avatar(user.getAvatarUrl())
                .employeeId(user.getEmployeeId())
                .hireDate(user.getHireDate())
                .status(user.getStatus())
                .lastLoginAt(user.getLastLoginAt())
                .loginCount(user.getLoginCount())
                .createdAt(user.getCreatedAt())
                .updatedAt(user.getUpdatedAt());

        // 设置部门信息
        if (user.getDepartment() != null) {
            builder.department(DepartmentInfo.builder()
                    .id(user.getDepartment().getId())
                    .name(user.getDepartment().getName())
                    .code(user.getDepartment().getCode())
                    .build());
        }

        // 设置角色信息
        if (user.getRole() != null) {
            builder.role(RoleInfo.builder()
                    .id(user.getRole().getId())
                    .name(user.getRole().getName())
                    .code(user.getRole().getCode())
                    .permissions(user.getRole().getPermissionList())
                    .build());
        }

        return builder.build();
    }
}
