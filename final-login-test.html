<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WTMS 最终登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 2px solid #ddd;
            border-radius: 8px;
        }
        .test-section.success {
            border-color: #28a745;
            background-color: #d4edda;
        }
        .test-section.error {
            border-color: #dc3545;
            background-color: #f8d7da;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        button.danger {
            background: #dc3545;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
        .status {
            font-weight: bold;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WTMS 最终登录测试</h1>
        
        <div class="test-section">
            <h3>📋 测试步骤</h3>
            <p>这个测试将验证完整的登录流程：</p>
            <ol>
                <li>清除所有本地存储</li>
                <li>测试后端API连接</li>
                <li>执行登录请求</li>
                <li>验证token和用户信息</li>
                <li>自动跳转到主界面</li>
            </ol>
            
            <button onclick="runFullTest()" style="font-size: 18px; padding: 15px 30px;">🚀 开始完整测试</button>
            <button onclick="clearAll()" class="danger">🧹 清除存储</button>
            <button onclick="openMainApp()" class="success">🌐 打开主应用</button>
        </div>
        
        <div id="status" class="status info">准备开始测试...</div>
        <div id="log" class="log"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:55557/api/v1';
        const FRONTEND_URL = 'http://localhost:33336';
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearAll() {
            localStorage.clear();
            sessionStorage.clear();
            document.getElementById('log').innerHTML = '';
            updateStatus('所有存储已清除', 'info');
            log('🧹 清除了所有本地存储');
        }
        
        function openMainApp() {
            window.open(FRONTEND_URL, '_blank');
        }
        
        async function runFullTest() {
            updateStatus('正在执行完整测试...', 'info');
            document.getElementById('log').innerHTML = '';
            
            try {
                // 步骤1: 清除存储
                log('步骤1: 清除所有本地存储...');
                localStorage.clear();
                sessionStorage.clear();
                
                // 步骤2: 测试API连接
                log('步骤2: 测试后端API连接...');
                const healthResponse = await fetch(`${API_BASE}/health`);
                if (!healthResponse.ok) {
                    throw new Error(`API健康检查失败: ${healthResponse.status}`);
                }
                const healthData = await healthResponse.json();
                log(`✅ API连接正常: ${JSON.stringify(healthData)}`);
                
                // 步骤3: 执行登录
                log('步骤3: 执行登录请求...');
                const loginResponse = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123456'
                    })
                });
                
                log(`登录响应状态: ${loginResponse.status}`);
                
                if (!loginResponse.ok) {
                    throw new Error(`登录请求失败: ${loginResponse.status}`);
                }
                
                const loginData = await loginResponse.json();
                log(`登录响应: ${JSON.stringify(loginData, null, 2)}`);
                
                if (!loginData.success || !loginData.data) {
                    throw new Error(`登录失败: ${loginData.message || '未知错误'}`);
                }
                
                // 步骤4: 验证和保存数据
                log('步骤4: 验证和保存登录数据...');
                const { token, user, refreshToken } = loginData.data;
                
                if (!token) {
                    throw new Error('登录响应中缺少token');
                }
                
                if (!user) {
                    throw new Error('登录响应中缺少用户信息');
                }
                
                // 保存到localStorage
                localStorage.setItem('wtms_token', token);
                if (refreshToken) {
                    localStorage.setItem('refreshToken', refreshToken);
                }
                
                log(`✅ Token已保存: ${token.substring(0, 50)}...`);
                log(`✅ 用户信息: ${user.fullName} (${user.username})`);
                log(`✅ 用户角色: ${user.role?.name} (${user.role?.code})`);
                log(`✅ 权限数量: ${user.role?.permissions?.length || 0}`);
                
                // 步骤5: 验证token有效性
                log('步骤5: 验证token有效性...');
                const profileResponse = await fetch(`${API_BASE}/user/profile`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (profileResponse.ok) {
                    const profileData = await profileResponse.json();
                    log(`✅ Token验证成功: ${JSON.stringify(profileData.data?.user?.fullName)}`);
                } else {
                    log(`⚠️ Token验证失败: ${profileResponse.status}`);
                }
                
                // 成功
                updateStatus('🎉 登录测试完全成功！现在可以访问主应用了。', 'success');
                log('🎉 所有测试步骤完成！');
                log('💡 现在点击"打开主应用"按钮，应该能够直接进入主界面。');
                
                // 自动打开主应用
                setTimeout(() => {
                    log('🌐 自动打开主应用...');
                    window.open(FRONTEND_URL, '_blank');
                }, 2000);
                
            } catch (error) {
                updateStatus(`❌ 测试失败: ${error.message}`, 'error');
                log(`❌ 错误: ${error.message}`);
                console.error('Test failed:', error);
            }
        }
        
        // 页面加载时显示当前存储状态
        window.addEventListener('load', function() {
            const token = localStorage.getItem('wtms_token');
            const refreshToken = localStorage.getItem('refreshToken');
            
            if (token) {
                log(`当前存储的token: ${token.substring(0, 50)}...`);
                updateStatus('检测到已保存的登录信息', 'success');
            } else {
                log('当前没有保存的登录信息');
                updateStatus('没有检测到登录信息', 'info');
            }
            
            if (refreshToken) {
                log(`当前存储的refreshToken: ${refreshToken.substring(0, 30)}...`);
            }
        });
    </script>
</body>
</html>
