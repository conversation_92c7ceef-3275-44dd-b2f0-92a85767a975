package com.wtms.service.impl;

import com.wtms.common.exception.BusinessException;
import com.wtms.common.result.ResultCode;
import com.wtms.entity.Task;
import com.wtms.entity.User;
import com.wtms.mapper.TaskMapper;
import com.wtms.mapper.UserMapper;
import com.wtms.service.TaskStatusService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 任务状态流转服务实现
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class TaskStatusServiceImpl implements TaskStatusService {

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private UserMapper userMapper;

    // 状态流转规则定义
    private static final Map<String, List<String>> STATUS_TRANSITION_RULES = new HashMap<>();
    
    static {
        // 草稿状态可以流转到：待开始、已取消
        STATUS_TRANSITION_RULES.put(Task.Status.DRAFT.getCode(), 
            Arrays.asList(Task.Status.PENDING.getCode(), Task.Status.CANCELLED.getCode()));
        
        // 待开始状态可以流转到：进行中、已取消
        STATUS_TRANSITION_RULES.put(Task.Status.PENDING.getCode(), 
            Arrays.asList(Task.Status.IN_PROGRESS.getCode(), Task.Status.CANCELLED.getCode()));
        
        // 进行中状态可以流转到：待审核、测试中、已完成、已暂停、已取消
        STATUS_TRANSITION_RULES.put(Task.Status.IN_PROGRESS.getCode(), 
            Arrays.asList(Task.Status.REVIEW.getCode(), Task.Status.TESTING.getCode(), 
                         Task.Status.COMPLETED.getCode(), Task.Status.PAUSED.getCode(), 
                         Task.Status.CANCELLED.getCode()));
        
        // 待审核状态可以流转到：进行中、测试中、已完成、已取消
        STATUS_TRANSITION_RULES.put(Task.Status.REVIEW.getCode(), 
            Arrays.asList(Task.Status.IN_PROGRESS.getCode(), Task.Status.TESTING.getCode(), 
                         Task.Status.COMPLETED.getCode(), Task.Status.CANCELLED.getCode()));
        
        // 测试中状态可以流转到：进行中、已完成、已取消
        STATUS_TRANSITION_RULES.put(Task.Status.TESTING.getCode(), 
            Arrays.asList(Task.Status.IN_PROGRESS.getCode(), Task.Status.COMPLETED.getCode(), 
                         Task.Status.CANCELLED.getCode()));
        
        // 已暂停状态可以流转到：进行中、已取消
        STATUS_TRANSITION_RULES.put(Task.Status.PAUSED.getCode(), 
            Arrays.asList(Task.Status.IN_PROGRESS.getCode(), Task.Status.CANCELLED.getCode()));
        
        // 已完成状态可以流转到：已归档
        STATUS_TRANSITION_RULES.put(Task.Status.COMPLETED.getCode(), 
            Arrays.asList(Task.Status.ARCHIVED.getCode()));
        
        // 已取消和已归档状态不能流转到其他状态
        STATUS_TRANSITION_RULES.put(Task.Status.CANCELLED.getCode(), Collections.emptyList());
        STATUS_TRANSITION_RULES.put(Task.Status.ARCHIVED.getCode(), Collections.emptyList());
    }

    @Override
    public List<String> getAvailableNextStatuses(String taskId, String userId) {
        Task task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "任务不存在");
        }

        String currentStatus = task.getStatus();
        List<String> availableStatuses = STATUS_TRANSITION_RULES.getOrDefault(currentStatus, Collections.emptyList());

        // 根据用户权限过滤可用状态
        return availableStatuses.stream()
                .filter(status -> hasStatusTransitionPermission(taskId, userId, status))
                .collect(Collectors.toList());
    }

    @Override
    public boolean isValidStatusTransition(String currentStatus, String targetStatus) {
        List<String> allowedStatuses = STATUS_TRANSITION_RULES.getOrDefault(currentStatus, Collections.emptyList());
        return allowedStatuses.contains(targetStatus);
    }

    @Override
    public boolean hasStatusTransitionPermission(String taskId, String userId, String targetStatus) {
        Task task = taskMapper.selectById(taskId);
        if (task == null) {
            return false;
        }

        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        // 系统管理员拥有所有权限
        if (user.getRole() != null && "ADMIN".equals(user.getRole().getCode())) {
            return true;
        }

        // 任务创建者拥有大部分权限
        if (task.getCreatorId().equals(userId)) {
            return true;
        }

        // 根据目标状态和用户角色判断权限
        switch (targetStatus) {
            case "pending":
                // 开始任务：创建者、负责人、项目经理
                return task.getCreatorId().equals(userId) || 
                       (task.getAssigneeId() != null && task.getAssigneeId().equals(userId)) ||
                       (user.getRole() != null && "PM".equals(user.getRole().getCode()));
                       
            case "in_progress":
                // 进行中：负责人、项目经理
                return (task.getAssigneeId() != null && task.getAssigneeId().equals(userId)) ||
                       (user.getRole() != null && "PM".equals(user.getRole().getCode()));
                       
            case "review":
                // 提交审核：负责人
                return task.getAssigneeId() != null && task.getAssigneeId().equals(userId);
                
            case "testing":
                // 提交测试：负责人、审核人
                return (task.getAssigneeId() != null && task.getAssigneeId().equals(userId)) ||
                       (task.getReviewerId() != null && task.getReviewerId().equals(userId));
                       
            case "completed":
                // 完成任务：负责人、审核人、项目经理
                return (task.getAssigneeId() != null && task.getAssigneeId().equals(userId)) ||
                       (task.getReviewerId() != null && task.getReviewerId().equals(userId)) ||
                       (user.getRole() != null && "PM".equals(user.getRole().getCode()));
                       
            case "paused":
                // 暂停任务：负责人、项目经理
                return (task.getAssigneeId() != null && task.getAssigneeId().equals(userId)) ||
                       (user.getRole() != null && "PM".equals(user.getRole().getCode()));
                       
            case "cancelled":
                // 取消任务：创建者、项目经理
                return task.getCreatorId().equals(userId) ||
                       (user.getRole() != null && "PM".equals(user.getRole().getCode()));
                       
            case "archived":
                // 归档任务：创建者、项目经理
                return task.getCreatorId().equals(userId) ||
                       (user.getRole() != null && "PM".equals(user.getRole().getCode()));
                       
            default:
                return false;
        }
    }

    @Override
    public Task executeStatusTransition(String taskId, String targetStatus, String userId, String comment) {
        log.info("Executing status transition: task={}, targetStatus={}, user={}", taskId, targetStatus, userId);

        Task task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "任务不存在");
        }

        String currentStatus = task.getStatus();

        // 检查状态流转是否有效
        if (!isValidStatusTransition(currentStatus, targetStatus)) {
            throw new BusinessException(ResultCode.OPERATION_NOT_ALLOWED.getCode(), 
                String.format("不允许从状态 %s 流转到 %s", 
                    Task.Status.fromCode(currentStatus).getDescription(),
                    Task.Status.fromCode(targetStatus).getDescription()));
        }

        // 检查用户权限
        if (!hasStatusTransitionPermission(taskId, userId, targetStatus)) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED.getCode(), "没有权限执行此状态流转");
        }

        // 执行状态更新
        taskMapper.updateTaskStatus(taskId, targetStatus);

        // 根据状态更新相关时间字段
        LocalDateTime now = LocalDateTime.now();
        switch (targetStatus) {
            case "in_progress":
                if (task.getActualStartDate() == null) {
                    taskMapper.updateTaskActualStartDate(taskId, now);
                }
                break;
            case "completed":
                taskMapper.updateTaskActualEndDate(taskId, now);
                taskMapper.updateTaskProgress(taskId, new java.math.BigDecimal("100"));
                break;
        }

        // 记录状态流转历史
        recordStatusHistory(taskId, currentStatus, targetStatus, userId, comment);

        log.info("Status transition completed: task={}, {} -> {}", taskId, currentStatus, targetStatus);

        return taskMapper.selectById(taskId);
    }

    @Override
    public List<TaskStatusHistory> getStatusHistory(String taskId) {
        // TODO: 实现状态历史查询
        // 这里需要创建状态历史表和相应的Mapper
        return Collections.emptyList();
    }

    @Override
    public Map<String, String> getAllTaskStatuses() {
        Map<String, String> statuses = new HashMap<>();
        for (Task.Status status : Task.Status.values()) {
            statuses.put(status.getCode(), status.getDescription());
        }
        return statuses;
    }

    @Override
    public Map<String, List<String>> getStatusTransitionRules() {
        return new HashMap<>(STATUS_TRANSITION_RULES);
    }

    /**
     * 记录状态流转历史
     */
    private void recordStatusHistory(String taskId, String fromStatus, String toStatus, String operatorId, String comment) {
        // TODO: 实现状态历史记录
        // 这里需要创建状态历史表和相应的插入逻辑
        log.info("Recording status history: task={}, {} -> {}, operator={}", taskId, fromStatus, toStatus, operatorId);
    }
}
