@echo off
setlocal enabledelayedexpansion

REM ================================
REM WTMS 安全扫描脚本 (Windows版本)
REM ================================

echo ================================
echo WTMS 安全扫描工具
echo ================================
echo.

REM 设置变量
set "REPORT_DIR=security-reports\%date:~0,4%%date:~5,2%%date:~8,2%_%time:~0,2%%time:~3,2%%time:~6,2%"
set "REPORT_DIR=%REPORT_DIR: =0%"

REM 创建报告目录
if not exist "%REPORT_DIR%" mkdir "%REPORT_DIR%"
echo 报告将保存到: %REPORT_DIR%
echo.

REM 检查项目结构
if not exist "wtms-backend" (
    echo [ERROR] 未找到wtms-backend目录
    goto :error
)

if not exist "wtms-frontend" (
    echo [ERROR] 未找到wtms-frontend目录
    goto :error
)

REM 后端安全扫描
echo [INFO] 开始后端安全扫描...
echo ================================

cd wtms-backend

REM 检查Maven
where mvn >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] Maven未安装，跳过后端扫描
    cd ..
    goto :frontend_scan
)

REM OWASP Dependency Check
echo [INFO] 运行OWASP Dependency Check...
call mvn org.owasp:dependency-check-maven:check -DskipTests
if %errorlevel% equ 0 (
    echo [SUCCESS] OWASP Dependency Check 完成
    if exist "target\dependency-check" (
        if not exist "..\%REPORT_DIR%\backend-owasp" mkdir "..\%REPORT_DIR%\backend-owasp"
        xcopy "target\dependency-check\*" "..\%REPORT_DIR%\backend-owasp\" /E /Y >nul
    )
) else (
    echo [WARNING] OWASP Dependency Check 发现安全问题
)

REM Snyk扫描 (如果配置了token)
if defined SNYK_TOKEN (
    echo [INFO] 运行Snyk扫描...
    call mvn io.snyk:snyk-maven-plugin:test -DskipTests
    if %errorlevel% equ 0 (
        echo [SUCCESS] Snyk扫描完成
    ) else (
        echo [WARNING] Snyk扫描发现安全问题
    )
) else (
    echo [WARNING] 未配置SNYK_TOKEN，跳过Snyk扫描
)

REM SpotBugs安全检查
echo [INFO] 运行SpotBugs安全检查...
call mvn com.github.spotbugs:spotbugs-maven-plugin:check -DskipTests
if %errorlevel% equ 0 (
    echo [SUCCESS] SpotBugs检查完成
    if exist "target\spotbugsXml.xml" (
        if not exist "..\%REPORT_DIR%\backend-spotbugs" mkdir "..\%REPORT_DIR%\backend-spotbugs"
        copy "target\spotbugsXml.xml" "..\%REPORT_DIR%\backend-spotbugs\" >nul
    )
) else (
    echo [WARNING] SpotBugs发现潜在问题
)

cd ..

:frontend_scan
REM 前端安全扫描
echo.
echo [INFO] 开始前端安全扫描...
echo ================================

cd wtms-frontend

REM 检查npm
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo [ERROR] npm未安装，跳过前端扫描
    cd ..
    goto :generate_summary
)

REM npm audit
echo [INFO] 运行npm audit...
npm audit --audit-level=moderate --json > "..\%REPORT_DIR%\frontend-npm-audit.json" 2>nul
if %errorlevel% equ 0 (
    echo [SUCCESS] npm audit完成，未发现中高危漏洞
) else (
    echo [WARNING] npm audit发现安全问题
    npm audit --audit-level=moderate > "..\%REPORT_DIR%\frontend-npm-audit.txt" 2>nul
)

REM Snyk扫描 (如果安装了snyk)
where snyk >nul 2>&1
if %errorlevel% equ 0 (
    if defined SNYK_TOKEN (
        echo [INFO] 运行Snyk前端扫描...
        
        REM 认证
        call snyk auth %SNYK_TOKEN%
        
        REM 测试
        call snyk test --json > "..\%REPORT_DIR%\frontend-snyk.json" 2>nul
        if %errorlevel% equ 0 (
            echo [SUCCESS] Snyk前端扫描完成，未发现问题
        ) else (
            echo [WARNING] Snyk前端扫描发现安全问题
            call snyk test > "..\%REPORT_DIR%\frontend-snyk.txt" 2>nul
        )
        
        REM 监控 (可选)
        if "%SNYK_MONITOR%"=="true" (
            call snyk monitor
            echo [INFO] 项目已添加到Snyk监控
        )
    ) else (
        echo [WARNING] 未配置SNYK_TOKEN，跳过Snyk扫描
    )
) else (
    echo [WARNING] Snyk未安装，跳过Snyk扫描
)

REM 检查已知漏洞包
echo [INFO] 检查已知漏洞包...
call :check_vulnerable_packages "..\%REPORT_DIR%\frontend-vulnerable-packages.txt"

cd ..

:generate_summary
REM 生成安全报告摘要
echo.
echo [INFO] 生成安全报告摘要...

set "SUMMARY_FILE=%REPORT_DIR%\security-summary.md"

(
echo # WTMS 安全扫描报告摘要
echo.
echo **扫描时间**: %date% %time%
echo **扫描范围**: 前端 + 后端
echo **报告目录**: %REPORT_DIR%
echo.
echo ## 扫描工具
echo.
echo ### 后端扫描
echo - ✅ OWASP Dependency Check
echo - ✅ SpotBugs
echo - ⚠️  Snyk ^(需要配置token^)
echo.
echo ### 前端扫描
echo - ✅ npm audit
echo - ⚠️  Snyk ^(需要配置token^)
echo - ✅ 已知漏洞包检查
echo.
echo ## 报告文件
echo.
echo ### 后端报告
echo - `backend-owasp/` - OWASP依赖检查报告
echo - `backend-spotbugs/` - SpotBugs静态分析报告
echo.
echo ### 前端报告
echo - `frontend-npm-audit.json` - npm audit JSON报告
echo - `frontend-npm-audit.txt` - npm audit文本报告
echo - `frontend-snyk.json` - Snyk JSON报告
echo - `frontend-snyk.txt` - Snyk文本报告
echo - `frontend-vulnerable-packages.txt` - 已知漏洞包检查
echo.
echo ## 建议操作
echo.
echo 1. 查看各个报告文件，重点关注高危和中危漏洞
echo 2. 对于依赖漏洞，优先升级到安全版本
echo 3. 对于误报，可以在抑制配置文件中添加规则
echo 4. 定期运行安全扫描，建议每周执行一次
echo 5. 在CI/CD流水线中集成安全扫描
echo.
echo ## 联系方式
echo.
echo 如有疑问，请联系安全团队或项目维护者。
echo.
echo ---
echo *此报告由WTMS安全扫描脚本自动生成*
) > "%SUMMARY_FILE%"

echo [SUCCESS] 安全报告摘要已生成: %SUMMARY_FILE%
echo.
echo ================================
echo 安全扫描完成！
echo ================================
echo 报告位置: %REPORT_DIR%
echo 请查看 security-summary.md 了解扫描结果
echo.

goto :end

:check_vulnerable_packages
set "OUTPUT_FILE=%~1"
(
echo 检查已知漏洞包...
echo ===================
echo.
) > "%OUTPUT_FILE%"

REM 检查一些已知的漏洞包
set "PACKAGES=lodash@^<4.17.21 axios@^<0.21.2 minimist@^<1.2.6 yargs-parser@^<13.1.2 node-fetch@^<2.6.7 tar@^<4.4.18"

for %%p in (%PACKAGES%) do (
    npm list "%%p" >nul 2>&1
    if !errorlevel! equ 0 (
        echo ⚠️  发现漏洞包: %%p >> "%OUTPUT_FILE%"
    )
)

echo. >> "%OUTPUT_FILE%"
echo 检查完成 >> "%OUTPUT_FILE%"
goto :eof

:error
echo.
echo [ERROR] 请在WTMS项目根目录下运行此脚本
echo 当前目录: %CD%
pause
exit /b 1

:end
pause
exit /b 0
