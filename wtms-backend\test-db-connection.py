#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WTMS数据库连接测试脚本
使用Python和pymysql测试数据库连接
"""

import sys

try:
    import pymysql
    print("✅ pymysql模块已安装")
except ImportError:
    print("❌ pymysql模块未安装，请运行: pip install pymysql")
    sys.exit(1)

def test_database_connection():
    """测试数据库连接"""
    print("\n=== WTMS数据库连接测试 ===")
    
    # 数据库配置
    config = {
        'host': 'localhost',
        'port': 3308,
        'user': 'root',
        'password': 'ankaixin.docker.mysql',
        'charset': 'utf8mb4'
    }
    
    try:
        # 1. 测试基本连接
        print("\n1. 测试MySQL服务器连接...")
        connection = pymysql.connect(**config)
        print("✅ MySQL服务器连接成功")
        
        cursor = connection.cursor()
        
        # 2. 检查wtms数据库是否存在
        print("\n2. 检查wtms数据库...")
        cursor.execute("SHOW DATABASES LIKE 'wtms'")
        result = cursor.fetchone()
        
        if result:
            print("✅ wtms数据库存在")
        else:
            print("❌ wtms数据库不存在，正在创建...")
            cursor.execute("CREATE DATABASE IF NOT EXISTS wtms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ wtms数据库创建成功")
        
        # 3. 连接到wtms数据库
        connection.close()
        config['database'] = 'wtms'
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        print("✅ 成功连接到wtms数据库")
        
        # 4. 检查数据表
        print("\n3. 检查数据表...")
        tables = ['users', 'roles', 'permissions', 'user_roles', 'role_permissions', 'tasks', 'task_comments']
        
        cursor.execute("SHOW TABLES")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        for table in tables:
            if table in existing_tables:
                cursor.execute(f"SELECT COUNT(*) FROM {table}")
                count = cursor.fetchone()[0]
                print(f"✅ 表 {table} 存在，记录数: {count}")
            else:
                print(f"❌ 表 {table} 不存在")
        
        # 5. 测试数据库版本和字符集
        print("\n4. 数据库信息...")
        cursor.execute("SELECT VERSION()")
        version = cursor.fetchone()[0]
        print(f"MySQL版本: {version}")
        
        cursor.execute("SELECT @@character_set_database, @@collation_database")
        charset_info = cursor.fetchone()
        print(f"数据库字符集: {charset_info[0]}")
        print(f"数据库排序规则: {charset_info[1]}")
        
        connection.close()
        print("\n✅ 数据库连接测试完成")
        
    except Exception as e:
        print(f"❌ 数据库连接测试失败: {str(e)}")
        return False
    
    return True

if __name__ == "__main__":
    test_database_connection()
