-- =====================================================
-- WTMS 工作任务管理系统 - 数据库完整初始化脚本
-- 版本: 1.0.0
-- 创建时间: 2025-07-24
-- 说明: 执行完整的数据库初始化，包括建表和数据初始化
-- =====================================================

-- 检查数据库是否存在，如果不存在则创建
CREATE DATABASE IF NOT EXISTS `wtms` 
CHARACTER SET utf8mb4 
COLLATE utf8mb4_unicode_ci;

-- 使用wtms数据库
USE `wtms`;

-- 显示当前数据库信息
SELECT 
    'WTMS数据库初始化开始' as message,
    DATABASE() as current_database,
    @@character_set_database as charset,
    @@collation_database as collation,
    NOW() as start_time;

-- =====================================================
-- 执行建表脚本
-- =====================================================
SOURCE 01_create_tables.sql;

-- 检查表创建结果
SELECT 
    'WTMS数据表创建完成' as message,
    COUNT(*) as table_count
FROM information_schema.tables 
WHERE table_schema = 'wtms';

-- 显示创建的表列表
SELECT 
    table_name as '表名',
    table_comment as '表注释',
    table_rows as '预估行数',
    ROUND(((data_length + index_length) / 1024 / 1024), 2) as '大小(MB)'
FROM information_schema.tables 
WHERE table_schema = 'wtms' 
ORDER BY table_name;

-- =====================================================
-- 执行数据初始化脚本
-- =====================================================
SOURCE 02_init_data.sql;

-- 检查数据初始化结果
SELECT 
    'WTMS基础数据初始化完成' as message,
    NOW() as completion_time;

-- 显示各表数据统计
SELECT 
    '部门' as table_name, COUNT(*) as record_count FROM departments
UNION ALL
SELECT 
    '角色' as table_name, COUNT(*) as record_count FROM roles
UNION ALL
SELECT 
    '权限' as table_name, COUNT(*) as record_count FROM permissions
UNION ALL
SELECT 
    '用户' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 
    '用户角色关联' as table_name, COUNT(*) as record_count FROM user_roles
UNION ALL
SELECT 
    '角色权限关联' as table_name, COUNT(*) as record_count FROM role_permissions
UNION ALL
SELECT 
    '任务分类' as table_name, COUNT(*) as record_count FROM task_categories
UNION ALL
SELECT 
    '任务' as table_name, COUNT(*) as record_count FROM tasks
UNION ALL
SELECT 
    '任务评论' as table_name, COUNT(*) as record_count FROM task_comments;

-- =====================================================
-- 验证数据完整性
-- =====================================================

-- 验证超级管理员用户
SELECT 
    '验证超级管理员用户' as check_item,
    CASE 
        WHEN EXISTS (
            SELECT 1 FROM users u 
            JOIN user_roles ur ON u.id = ur.user_id 
            JOIN roles r ON ur.role_id = r.id 
            WHERE u.username = 'admin' AND r.code = 'SUPER_ADMIN'
        ) THEN '✓ 通过' 
        ELSE '✗ 失败' 
    END as result;

-- 验证权限数据
SELECT 
    '验证权限数据完整性' as check_item,
    CASE 
        WHEN (SELECT COUNT(*) FROM permissions WHERE is_system = 1) >= 30 
        THEN '✓ 通过' 
        ELSE '✗ 失败' 
    END as result;

-- 验证角色权限关联
SELECT 
    '验证角色权限关联' as check_item,
    CASE 
        WHEN (SELECT COUNT(*) FROM role_permissions) > 0 
        THEN '✓ 通过' 
        ELSE '✗ 失败' 
    END as result;

-- 验证外键约束
SELECT 
    '验证外键约束' as check_item,
    CASE 
        WHEN (
            SELECT COUNT(*) 
            FROM information_schema.table_constraints 
            WHERE constraint_schema = 'wtms' 
            AND constraint_type = 'FOREIGN KEY'
        ) > 10 
        THEN '✓ 通过' 
        ELSE '✗ 失败' 
    END as result;

-- =====================================================
-- 显示默认用户信息
-- =====================================================
SELECT 
    '=== 默认用户账户信息 ===' as info;

SELECT 
    u.username as '用户名',
    u.full_name as '姓名',
    u.email as '邮箱',
    r.name as '角色',
    d.name as '部门',
    u.status as '状态',
    'admin123' as '默认密码'
FROM users u
LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = 1
LEFT JOIN roles r ON ur.role_id = r.id
LEFT JOIN departments d ON u.department_id = d.id
ORDER BY u.username;

-- =====================================================
-- 显示系统配置信息
-- =====================================================
SELECT 
    '=== 系统配置信息 ===' as info;

SELECT 
    'MySQL版本' as config_item,
    VERSION() as config_value
UNION ALL
SELECT 
    '数据库字符集' as config_item,
    @@character_set_database as config_value
UNION ALL
SELECT 
    '数据库排序规则' as config_item,
    @@collation_database as config_value
UNION ALL
SELECT 
    '时区设置' as config_item,
    @@time_zone as config_value
UNION ALL
SELECT 
    '外键检查' as config_item,
    CASE @@foreign_key_checks WHEN 1 THEN '启用' ELSE '禁用' END as config_value;

-- =====================================================
-- 完成信息
-- =====================================================
SELECT 
    '🎉 WTMS数据库初始化完成！' as message,
    '请使用以下信息登录系统：' as login_info,
    'URL: http://localhost:55557/api/v1' as api_url,
    '用户名: admin' as username,
    '密码: admin123' as password,
    'Swagger文档: http://localhost:55557/api/v1/swagger-ui.html' as swagger_url,
    'Druid监控: http://localhost:55557/api/v1/druid/' as druid_url;
