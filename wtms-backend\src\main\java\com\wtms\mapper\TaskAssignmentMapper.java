package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.entity.TaskAssignment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务分配Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface TaskAssignmentMapper extends BaseMapper<TaskAssignment> {

    /**
     * 根据任务ID查询分配记录（包含关联信息）
     */
    List<TaskAssignment> selectAssignmentsByTaskId(@Param("taskId") String taskId);

    /**
     * 根据用户ID查询分配给该用户的任务
     */
    IPage<TaskAssignment> selectAssignmentsByUserId(Page<TaskAssignment> page, 
                                                   @Param("userId") String userId,
                                                   @Param("status") String status);

    /**
     * 根据部门ID查询分配给该部门的任务
     */
    IPage<TaskAssignment> selectAssignmentsByDepartmentId(Page<TaskAssignment> page,
                                                         @Param("departmentId") String departmentId,
                                                         @Param("status") String status);

    /**
     * 根据分配者ID查询分配记录
     */
    IPage<TaskAssignment> selectAssignmentsByAssignerId(Page<TaskAssignment> page,
                                                       @Param("assignerId") String assignerId,
                                                       @Param("status") String status);

    /**
     * 更新分配状态
     */
    @Update("UPDATE task_assignments SET status = #{status}, updated_at = NOW() WHERE id = #{assignmentId}")
    int updateAssignmentStatus(@Param("assignmentId") String assignmentId, @Param("status") String status);

    /**
     * 接受分配
     */
    @Update("UPDATE task_assignments SET status = 'accepted', accepted_at = #{acceptedAt}, updated_at = NOW() " +
            "WHERE id = #{assignmentId}")
    int acceptAssignment(@Param("assignmentId") String assignmentId, @Param("acceptedAt") LocalDateTime acceptedAt);

    /**
     * 拒绝分配
     */
    @Update("UPDATE task_assignments SET status = 'rejected', rejected_at = #{rejectedAt}, " +
            "reject_reason = #{rejectReason}, updated_at = NOW() WHERE id = #{assignmentId}")
    int rejectAssignment(@Param("assignmentId") String assignmentId, 
                        @Param("rejectedAt") LocalDateTime rejectedAt,
                        @Param("rejectReason") String rejectReason);

    /**
     * 取消分配
     */
    @Update("UPDATE task_assignments SET status = 'cancelled', updated_at = NOW() WHERE id = #{assignmentId}")
    int cancelAssignment(@Param("assignmentId") String assignmentId);

    /**
     * 标记通知已发送
     */
    @Update("UPDATE task_assignments SET is_notified = TRUE, updated_at = NOW() WHERE id = #{assignmentId}")
    int markAsNotified(@Param("assignmentId") String assignmentId);

    /**
     * 查询待通知的分配记录
     */
    @Select("SELECT * FROM task_assignments WHERE is_notified = FALSE AND status = 'pending' AND deleted_at IS NULL")
    List<TaskAssignment> selectPendingNotifications();

    /**
     * 查询过期的分配记录
     */
    @Select("SELECT * FROM task_assignments WHERE status = 'pending' AND deadline < NOW() AND deleted_at IS NULL")
    List<TaskAssignment> selectExpiredAssignments();

    /**
     * 批量更新过期分配状态
     */
    @Update("UPDATE task_assignments SET status = 'expired', updated_at = NOW() " +
            "WHERE status = 'pending' AND deadline < NOW()")
    int updateExpiredAssignments();

    /**
     * 统计用户的任务分配情况
     */
    UserAssignmentStatistics getUserAssignmentStatistics(@Param("userId") String userId);

    /**
     * 统计部门的任务分配情况
     */
    DepartmentAssignmentStatistics getDepartmentAssignmentStatistics(@Param("departmentId") String departmentId);

    /**
     * 查询用户当前的任务负载
     */
    @Select("SELECT COUNT(*) FROM task_assignments ta " +
            "JOIN tasks t ON ta.task_id = t.id " +
            "WHERE ta.target_type = 'user' AND ta.target_id = #{userId} " +
            "AND ta.status = 'accepted' AND t.status IN ('pending', 'in_progress') " +
            "AND ta.deleted_at IS NULL AND t.deleted_at IS NULL")
    int getUserCurrentWorkload(@Param("userId") String userId);

    /**
     * 查询部门当前的任务负载
     */
    @Select("SELECT COUNT(*) FROM task_assignments ta " +
            "JOIN tasks t ON ta.task_id = t.id " +
            "WHERE ta.target_type = 'department' AND ta.target_id = #{departmentId} " +
            "AND ta.status = 'accepted' AND t.status IN ('pending', 'in_progress') " +
            "AND ta.deleted_at IS NULL AND t.deleted_at IS NULL")
    int getDepartmentCurrentWorkload(@Param("departmentId") String departmentId);

    /**
     * 用户分配统计内部类
     */
    class UserAssignmentStatistics {
        private String userId;
        private String userName;
        private Long totalAssigned;
        private Long acceptedCount;
        private Long rejectedCount;
        private Long pendingCount;
        private Long completedCount;
        private Double acceptanceRate;
        private Double completionRate;

        // getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public Long getTotalAssigned() { return totalAssigned; }
        public void setTotalAssigned(Long totalAssigned) { this.totalAssigned = totalAssigned; }
        public Long getAcceptedCount() { return acceptedCount; }
        public void setAcceptedCount(Long acceptedCount) { this.acceptedCount = acceptedCount; }
        public Long getRejectedCount() { return rejectedCount; }
        public void setRejectedCount(Long rejectedCount) { this.rejectedCount = rejectedCount; }
        public Long getPendingCount() { return pendingCount; }
        public void setPendingCount(Long pendingCount) { this.pendingCount = pendingCount; }
        public Long getCompletedCount() { return completedCount; }
        public void setCompletedCount(Long completedCount) { this.completedCount = completedCount; }
        public Double getAcceptanceRate() { return acceptanceRate; }
        public void setAcceptanceRate(Double acceptanceRate) { this.acceptanceRate = acceptanceRate; }
        public Double getCompletionRate() { return completionRate; }
        public void setCompletionRate(Double completionRate) { this.completionRate = completionRate; }
    }

    /**
     * 部门分配统计内部类
     */
    class DepartmentAssignmentStatistics {
        private String departmentId;
        private String departmentName;
        private Long totalAssigned;
        private Long acceptedCount;
        private Long rejectedCount;
        private Long pendingCount;
        private Long completedCount;
        private Integer memberCount;
        private Double avgWorkload;

        // getters and setters
        public String getDepartmentId() { return departmentId; }
        public void setDepartmentId(String departmentId) { this.departmentId = departmentId; }
        public String getDepartmentName() { return departmentName; }
        public void setDepartmentName(String departmentName) { this.departmentName = departmentName; }
        public Long getTotalAssigned() { return totalAssigned; }
        public void setTotalAssigned(Long totalAssigned) { this.totalAssigned = totalAssigned; }
        public Long getAcceptedCount() { return acceptedCount; }
        public void setAcceptedCount(Long acceptedCount) { this.acceptedCount = acceptedCount; }
        public Long getRejectedCount() { return rejectedCount; }
        public void setRejectedCount(Long rejectedCount) { this.rejectedCount = rejectedCount; }
        public Long getPendingCount() { return pendingCount; }
        public void setPendingCount(Long pendingCount) { this.pendingCount = pendingCount; }
        public Long getCompletedCount() { return completedCount; }
        public void setCompletedCount(Long completedCount) { this.completedCount = completedCount; }
        public Integer getMemberCount() { return memberCount; }
        public void setMemberCount(Integer memberCount) { this.memberCount = memberCount; }
        public Double getAvgWorkload() { return avgWorkload; }
        public void setAvgWorkload(Double avgWorkload) { this.avgWorkload = avgWorkload; }
    }
}
