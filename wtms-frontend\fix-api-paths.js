const fs = require('fs');
const path = require('path');

// 需要修复的API文件
const apiFiles = [
  'src/api/task.ts',
  'src/api/comment.ts',
  'src/api/evaluation.ts',
  'src/api/permission.ts',
  'src/api/taskAssignment.ts',
  'src/api/taskCategory.ts',
  'src/api/taskStatus.ts',
  'src/api/workflow.ts'
];

// 修复API路径的函数
function fixApiPaths(filePath) {
  try {
    const fullPath = path.join(__dirname, filePath);
    if (!fs.existsSync(fullPath)) {
      console.log(`文件不存在: ${filePath}`);
      return;
    }

    let content = fs.readFileSync(fullPath, 'utf8');
    let modified = false;

    // 替换 /api/v1/ 为 /
    const regex1 = /['"`]\/api\/v1\//g;
    if (regex1.test(content)) {
      content = content.replace(regex1, (match) => {
        const quote = match[0];
        return `${quote}/`;
      });
      modified = true;
      console.log(`修复了 ${filePath} 中的 /api/v1/ 路径`);
    }

    // 替换 '/v1/ 为 '/
    const regex2 = /['"`]\/v1\//g;
    if (regex2.test(content)) {
      content = content.replace(regex2, (match) => {
        const quote = match[0];
        return `${quote}/`;
      });
      modified = true;
      console.log(`修复了 ${filePath} 中的 /v1/ 路径`);
    }

    if (modified) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ 已修复: ${filePath}`);
    } else {
      console.log(`⏭️ 无需修复: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ 修复失败 ${filePath}:`, error.message);
  }
}

// 执行修复
console.log('🚀 开始修复API路径...\n');

apiFiles.forEach(file => {
  fixApiPaths(file);
});

console.log('\n✅ API路径修复完成！');
console.log('\n现在前端API调用应该正确指向后端服务了。');
console.log('请重新测试登录功能。');
