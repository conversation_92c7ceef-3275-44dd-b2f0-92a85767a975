package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 评价维度实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("evaluation_dimensions")
@Schema(description = "评价维度实体")
public class EvaluationDimension implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "维度ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "任务评价ID")
    @TableField("task_evaluation_id")
    private String taskEvaluationId;

    @Schema(description = "维度名称")
    @TableField("dimension_name")
    private String dimensionName;

    @Schema(description = "维度编码")
    @TableField("dimension_code")
    private String dimensionCode;

    @Schema(description = "维度描述")
    @TableField("description")
    private String description;

    @Schema(description = "评分")
    @TableField("score")
    private BigDecimal score;

    @Schema(description = "最大分值")
    @TableField("max_score")
    private BigDecimal maxScore;

    @Schema(description = "最小分值")
    @TableField("min_score")
    private BigDecimal minScore;

    @Schema(description = "权重")
    @TableField("weight")
    private BigDecimal weight;

    @Schema(description = "评价内容")
    @TableField("evaluation_content")
    private String evaluationContent;

    @Schema(description = "评价标准")
    @TableField("evaluation_criteria")
    private String evaluationCriteria;

    @Schema(description = "是否必填")
    @TableField("is_required")
    private Boolean isRequired;

    @Schema(description = "是否启用")
    @TableField("is_enabled")
    private Boolean isEnabled;

    @Schema(description = "排序号")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "任务评价信息")
    @TableField(exist = false)
    private TaskEvaluation taskEvaluation;

    @Schema(description = "评价指标列表")
    @TableField(exist = false)
    private List<EvaluationCriteria> criteriaList;

    /**
     * 维度类型枚举
     */
    public enum DimensionType {
        QUALITY("quality", "质量"),
        EFFICIENCY("efficiency", "效率"),
        COMMUNICATION("communication", "沟通"),
        INNOVATION("innovation", "创新"),
        TEAMWORK("teamwork", "团队协作"),
        LEADERSHIP("leadership", "领导力"),
        PROBLEM_SOLVING("problem_solving", "问题解决"),
        LEARNING("learning", "学习能力"),
        ATTITUDE("attitude", "工作态度"),
        RESPONSIBILITY("responsibility", "责任心"),
        CUSTOM("custom", "自定义");

        private final String code;
        private final String description;

        DimensionType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static DimensionType fromCode(String code) {
            for (DimensionType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return CUSTOM;
        }
    }

    /**
     * 检查是否必填
     */
    public boolean isRequired() {
        return Boolean.TRUE.equals(this.isRequired);
    }

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.isEnabled);
    }

    /**
     * 计算加权分数
     */
    public BigDecimal calculateWeightedScore() {
        if (score == null || weight == null) {
            return score;
        }
        return score.multiply(weight);
    }

    /**
     * 计算得分率
     */
    public BigDecimal calculateScoreRate() {
        if (score == null || maxScore == null || maxScore.compareTo(BigDecimal.ZERO) == 0) {
            return BigDecimal.ZERO;
        }
        return score.divide(maxScore, 4, BigDecimal.ROUND_HALF_UP).multiply(new BigDecimal("100"));
    }

    /**
     * 获取评分等级
     */
    public String getScoreGrade() {
        BigDecimal rate = calculateScoreRate();
        
        if (rate.compareTo(new BigDecimal("90")) >= 0) {
            return "优秀";
        } else if (rate.compareTo(new BigDecimal("80")) >= 0) {
            return "良好";
        } else if (rate.compareTo(new BigDecimal("70")) >= 0) {
            return "中等";
        } else if (rate.compareTo(new BigDecimal("60")) >= 0) {
            return "及格";
        } else {
            return "不及格";
        }
    }

    /**
     * 检查分数是否在有效范围内
     */
    public boolean isScoreValid() {
        if (score == null) {
            return !isRequired();
        }
        
        boolean minValid = minScore == null || score.compareTo(minScore) >= 0;
        boolean maxValid = maxScore == null || score.compareTo(maxScore) <= 0;
        
        return minValid && maxValid;
    }

    /**
     * 获取分数范围描述
     */
    public String getScoreRange() {
        StringBuilder range = new StringBuilder();
        
        if (minScore != null) {
            range.append(minScore);
        } else {
            range.append("0");
        }
        
        range.append(" - ");
        
        if (maxScore != null) {
            range.append(maxScore);
        } else {
            range.append("100");
        }
        
        return range.toString();
    }

    /**
     * 检查是否已评分
     */
    public boolean isScored() {
        return score != null;
    }

    /**
     * 重置评分
     */
    public void resetScore() {
        this.score = null;
        this.evaluationContent = null;
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (maxScore == null) {
            maxScore = new BigDecimal("100");
        }
        if (minScore == null) {
            minScore = BigDecimal.ZERO;
        }
        if (weight == null) {
            weight = new BigDecimal("1.0");
        }
        if (isRequired == null) {
            isRequired = false;
        }
        if (isEnabled == null) {
            isEnabled = true;
        }
        if (sortOrder == null) {
            sortOrder = 0;
        }
    }
}
