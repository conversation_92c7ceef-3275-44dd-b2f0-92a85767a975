import Cookies from 'js-cookie'

const TOKEN_KEY = 'wtms_token'
const REFRESH_TOKEN_KEY = 'wtms_refresh_token'

/**
 * 获取token
 */
export function getToken(): string | undefined {
  // 优先从localStorage获取，然后从Cookie获取
  return localStorage.getItem(TOKEN_KEY) || Cookies.get(TOKEN_KEY)
}

/**
 * 设置token
 */
export function setToken(token: string): void {
  // 同时保存到Cookie和localStorage
  Cookies.set(TOKEN_KEY, token, { expires: 7 }) // 7天过期
  localStorage.setItem(TOKEN_KEY, token)
}

/**
 * 移除token
 */
export function removeToken(): void {
  Cookies.remove(TOKEN_KEY)
  Cookies.remove(REFRESH_TOKEN_KEY)
  localStorage.removeItem(TOKEN_KEY)
  localStorage.removeItem('refreshToken')
}

/**
 * 获取刷新token
 */
export function getRefreshToken(): string | null {
  return localStorage.getItem('refreshToken')
}

/**
 * 设置刷新token
 */
export function setRefreshToken(refreshToken: string): void {
  localStorage.setItem('refreshToken', refreshToken)
}

/**
 * 检查token是否过期
 */
export function isTokenExpired(token: string): boolean {
  if (!token) return true
  
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    const currentTime = Date.now() / 1000
    return payload.exp < currentTime
  } catch (error) {
    return true
  }
}

/**
 * 从token中解析用户信息
 */
export function parseTokenPayload(token: string): any {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]))
    return payload
  } catch (error) {
    return null
  }
}
