<template>
  <div class="workflow-designer">
    <!-- 工具栏 -->
    <div class="designer-toolbar">
      <div class="toolbar-left">
        <el-button type="primary" :icon="Plus" @click="createNewWorkflow">
          新建工作流
        </el-button>
        <el-button :icon="FolderOpened" @click="openWorkflow">
          打开工作流
        </el-button>
        <el-button :icon="Download" @click="saveWorkflow" :disabled="!currentWorkflow">
          保存工作流
        </el-button>
        <el-divider direction="vertical" />
        <el-button :icon="View" @click="previewWorkflow" :disabled="!currentWorkflow">
          预览
        </el-button>
        <el-button :icon="VideoPlay" @click="validateWorkflow" :disabled="!currentWorkflow">
          验证
        </el-button>
        <el-button type="success" :icon="Promotion" @click="publishWorkflow" :disabled="!currentWorkflow">
          发布
        </el-button>
      </div>
      <div class="toolbar-right">
        <el-button :icon="ZoomIn" @click="zoomIn">放大</el-button>
        <el-button :icon="ZoomOut" @click="zoomOut">缩小</el-button>
        <el-button :icon="Refresh" @click="resetZoom">重置</el-button>
        <span class="zoom-level">{{ Math.round(zoomLevel * 100) }}%</span>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="designer-content">
      <!-- 节点面板 -->
      <div class="node-panel">
        <div class="panel-header">
          <h4>节点库</h4>
        </div>
        <div class="panel-content">
          <div class="node-category" v-for="category in nodeCategories" :key="category.name">
            <div class="category-header" @click="toggleCategory(category.name)">
              <el-icon><component :is="category.expanded ? 'ArrowDown' : 'ArrowRight'" /></el-icon>
              <span>{{ category.label }}</span>
            </div>
            <div v-show="category.expanded" class="category-nodes">
              <div 
                v-for="node in category.nodes" 
                :key="node.type"
                class="node-item"
                :draggable="true"
                @dragstart="handleNodeDragStart($event, node)"
              >
                <el-icon><component :is="node.icon" /></el-icon>
                <span>{{ node.label }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 画布区域 -->
      <div class="canvas-container">
        <div class="canvas-header">
          <div class="workflow-info" v-if="currentWorkflow">
            <h3>{{ currentWorkflow.name }}</h3>
            <span class="workflow-status">{{ getStatusText(currentWorkflow.status) }}</span>
          </div>
          <div v-else class="empty-state">
            <span>请创建或打开一个工作流</span>
          </div>
        </div>
        <div 
          ref="canvasRef"
          class="canvas"
          :style="{ transform: `scale(${zoomLevel})` }"
          @drop="handleCanvasDrop"
          @dragover="handleCanvasDragOver"
          @click="handleCanvasClick"
        >
          <!-- 网格背景 -->
          <div class="canvas-grid"></div>
          
          <!-- 工作流节点 -->
          <WorkflowNode
            v-for="node in workflowNodes"
            :key="node.id"
            :node="node"
            :selected="selectedNodeId === node.id"
            @select="selectNode"
            @move="moveNode"
            @delete="deleteNode"
            @connect="startConnection"
          />

          <!-- 连接线 -->
          <WorkflowEdge
            v-for="edge in workflowEdges"
            :key="edge.id"
            :edge="edge"
            :selected="selectedEdgeId === edge.id"
            @select="selectEdge"
            @delete="deleteEdge"
          />

          <!-- 临时连接线 -->
          <svg v-if="connecting" class="temp-connection">
            <line
              :x1="connectionStart.x"
              :y1="connectionStart.y"
              :x2="mousePosition.x"
              :y2="mousePosition.y"
              stroke="#409EFF"
              stroke-width="2"
              stroke-dasharray="5,5"
            />
          </svg>
        </div>
      </div>

      <!-- 属性面板 -->
      <div class="property-panel">
        <div class="panel-header">
          <h4>属性配置</h4>
        </div>
        <div class="panel-content">
          <!-- 工作流属性 -->
          <WorkflowProperties
            v-if="!selectedNodeId && !selectedEdgeId && currentWorkflow"
            :workflow="currentWorkflow"
            @update="updateWorkflowProperties"
          />
          
          <!-- 节点属性 -->
          <NodeProperties
            v-else-if="selectedNodeId"
            :node="selectedNode"
            @update="updateNodeProperties"
          />
          
          <!-- 连接线属性 -->
          <EdgeProperties
            v-else-if="selectedEdgeId"
            :edge="selectedEdge"
            @update="updateEdgeProperties"
          />
          
          <!-- 空状态 -->
          <div v-else class="empty-properties">
            <el-empty description="请选择要配置的元素" />
          </div>
        </div>
      </div>
    </div>

    <!-- 工作流列表对话框 -->
    <WorkflowListDialog
      v-model:visible="showWorkflowList"
      @select="loadWorkflow"
    />

    <!-- 预览对话框 -->
    <WorkflowPreviewDialog
      v-model:visible="showPreview"
      :workflow="currentWorkflow"
    />

    <!-- 验证结果对话框 -->
    <WorkflowValidationDialog
      v-model:visible="showValidation"
      :validation-result="validationResult"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, onUnmounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { 
  Plus, FolderOpened, Download, View, VideoPlay, Promotion,
  ZoomIn, ZoomOut, Refresh, ArrowDown, ArrowRight
} from '@element-plus/icons-vue'
import { 
  createWorkflowDefinition, 
  updateWorkflowDefinition,
  validateWorkflowDefinition,
  publishWorkflowDefinition,
  type WorkflowDefinition,
  type WorkflowNode as WorkflowNodeType,
  type WorkflowEdge as WorkflowEdgeType
} from '@/api/workflow'
import WorkflowNode from './WorkflowNode.vue'
import WorkflowEdge from './WorkflowEdge.vue'
import WorkflowProperties from './WorkflowProperties.vue'
import NodeProperties from './NodeProperties.vue'
import EdgeProperties from './EdgeProperties.vue'
import WorkflowListDialog from './WorkflowListDialog.vue'
import WorkflowPreviewDialog from './WorkflowPreviewDialog.vue'
import WorkflowValidationDialog from './WorkflowValidationDialog.vue'
import { useUserStore } from '@/stores/user'

// 响应式数据
const userStore = useUserStore()
const canvasRef = ref<HTMLElement>()
const currentWorkflow = ref<WorkflowDefinition>()
const workflowNodes = ref<WorkflowNodeType[]>([])
const workflowEdges = ref<WorkflowEdgeType[]>([])
const selectedNodeId = ref<string>()
const selectedEdgeId = ref<string>()
const zoomLevel = ref(1)
const connecting = ref(false)
const connectionStart = ref({ x: 0, y: 0, nodeId: '' })
const mousePosition = ref({ x: 0, y: 0 })
const showWorkflowList = ref(false)
const showPreview = ref(false)
const showValidation = ref(false)
const validationResult = ref<any>()

// 节点分类配置
const nodeCategories = ref([
  {
    name: 'events',
    label: '事件',
    expanded: true,
    nodes: [
      { type: 'start', label: '开始', icon: 'VideoPlay' },
      { type: 'end', label: '结束', icon: 'VideoPause' },
      { type: 'timer', label: '定时器', icon: 'Timer' },
      { type: 'message', label: '消息', icon: 'Message' }
    ]
  },
  {
    name: 'tasks',
    label: '任务',
    expanded: true,
    nodes: [
      { type: 'user_task', label: '用户任务', icon: 'User' },
      { type: 'service_task', label: '服务任务', icon: 'Setting' },
      { type: 'script_task', label: '脚本任务', icon: 'Document' },
      { type: 'mail_task', label: '邮件任务', icon: 'Message' }
    ]
  },
  {
    name: 'gateways',
    label: '网关',
    expanded: true,
    nodes: [
      { type: 'exclusive_gateway', label: '排他网关', icon: 'Switch' },
      { type: 'parallel_gateway', label: '并行网关', icon: 'Grid' },
      { type: 'inclusive_gateway', label: '包容网关', icon: 'Menu' }
    ]
  }
])

// 计算属性
const selectedNode = computed(() => {
  return workflowNodes.value.find(node => node.id === selectedNodeId.value)
})

const selectedEdge = computed(() => {
  return workflowEdges.value.find(edge => edge.id === selectedEdgeId.value)
})

// 方法
const createNewWorkflow = async () => {
  try {
    const { value: workflowName } = await ElMessageBox.prompt('请输入工作流名称', '新建工作流', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      inputPattern: /^.{1,50}$/,
      inputErrorMessage: '工作流名称长度应为1-50个字符'
    })

    const newWorkflow: WorkflowDefinition = {
      id: '',
      name: workflowName,
      code: `workflow_${Date.now()}`,
      category: 'custom',
      version: '1.0',
      status: 'draft',
      definitionJson: JSON.stringify({ nodes: [], edges: [] }),
      isEnabled: true,
      isDefault: false,
      creatorId: userStore.userInfo?.id || '',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    }

    const response = await createWorkflowDefinition(newWorkflow)
    currentWorkflow.value = response.data
    workflowNodes.value = []
    workflowEdges.value = []
    selectedNodeId.value = undefined
    selectedEdgeId.value = undefined

    ElMessage.success('工作流创建成功')
  } catch (error) {
    console.error('Failed to create workflow:', error)
  }
}

const openWorkflow = () => {
  showWorkflowList.value = true
}

const loadWorkflow = (workflow: WorkflowDefinition) => {
  currentWorkflow.value = workflow
  
  // 解析工作流定义
  try {
    const definition = JSON.parse(workflow.definitionJson)
    workflowNodes.value = definition.nodes || []
    workflowEdges.value = definition.edges || []
  } catch (error) {
    console.error('Failed to parse workflow definition:', error)
    workflowNodes.value = []
    workflowEdges.value = []
  }
  
  selectedNodeId.value = undefined
  selectedEdgeId.value = undefined
  showWorkflowList.value = false
  
  ElMessage.success('工作流加载成功')
}

const saveWorkflow = async () => {
  if (!currentWorkflow.value) return

  try {
    const definitionJson = JSON.stringify({
      nodes: workflowNodes.value,
      edges: workflowEdges.value
    })

    await updateWorkflowDefinition(currentWorkflow.value.id, {
      definitionJson
    })

    ElMessage.success('工作流保存成功')
  } catch (error) {
    console.error('Failed to save workflow:', error)
    ElMessage.error('工作流保存失败')
  }
}

const previewWorkflow = () => {
  showPreview.value = true
}

const validateWorkflow = async () => {
  if (!currentWorkflow.value) return

  try {
    const response = await validateWorkflowDefinition(currentWorkflow.value.id)
    validationResult.value = response.data
    showValidation.value = true
  } catch (error) {
    console.error('Failed to validate workflow:', error)
    ElMessage.error('工作流验证失败')
  }
}

const publishWorkflow = async () => {
  if (!currentWorkflow.value) return

  try {
    await ElMessageBox.confirm('确定要发布此工作流吗？发布后将可以被其他用户使用。', '确认发布', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })

    await publishWorkflowDefinition(currentWorkflow.value.id, userStore.userInfo?.id || '')
    currentWorkflow.value.status = 'published'
    
    ElMessage.success('工作流发布成功')
  } catch (error) {
    console.error('Failed to publish workflow:', error)
  }
}

const zoomIn = () => {
  zoomLevel.value = Math.min(zoomLevel.value + 0.1, 2)
}

const zoomOut = () => {
  zoomLevel.value = Math.max(zoomLevel.value - 0.1, 0.5)
}

const resetZoom = () => {
  zoomLevel.value = 1
}

const toggleCategory = (categoryName: string) => {
  const category = nodeCategories.value.find(cat => cat.name === categoryName)
  if (category) {
    category.expanded = !category.expanded
  }
}

const handleNodeDragStart = (event: DragEvent, node: any) => {
  if (event.dataTransfer) {
    event.dataTransfer.setData('application/json', JSON.stringify(node))
  }
}

const handleCanvasDrop = (event: DragEvent) => {
  event.preventDefault()
  
  if (!event.dataTransfer) return
  
  try {
    const nodeData = JSON.parse(event.dataTransfer.getData('application/json'))
    const rect = canvasRef.value?.getBoundingClientRect()
    
    if (rect) {
      const x = (event.clientX - rect.left) / zoomLevel.value
      const y = (event.clientY - rect.top) / zoomLevel.value
      
      addNode(nodeData, x, y)
    }
  } catch (error) {
    console.error('Failed to drop node:', error)
  }
}

const handleCanvasDragOver = (event: DragEvent) => {
  event.preventDefault()
}

const handleCanvasClick = (event: MouseEvent) => {
  if (event.target === canvasRef.value) {
    selectedNodeId.value = undefined
    selectedEdgeId.value = undefined
  }
}

const addNode = (nodeData: any, x: number, y: number) => {
  const newNode: WorkflowNodeType = {
    id: `node_${Date.now()}`,
    workflowDefinitionId: currentWorkflow.value?.id || '',
    nodeCode: `${nodeData.type}_${Date.now()}`,
    nodeName: nodeData.label,
    nodeType: nodeData.type,
    positionX: Math.round(x),
    positionY: Math.round(y),
    width: 100,
    height: 60,
    isStart: nodeData.type === 'start',
    isEnd: nodeData.type === 'end',
    isEnabled: true,
    sortOrder: workflowNodes.value.length,
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString()
  }
  
  workflowNodes.value.push(newNode)
  selectedNodeId.value = newNode.id
}

const selectNode = (nodeId: string) => {
  selectedNodeId.value = nodeId
  selectedEdgeId.value = undefined
}

const selectEdge = (edgeId: string) => {
  selectedEdgeId.value = edgeId
  selectedNodeId.value = undefined
}

const moveNode = (nodeId: string, x: number, y: number) => {
  const node = workflowNodes.value.find(n => n.id === nodeId)
  if (node) {
    node.positionX = x
    node.positionY = y
  }
}

const deleteNode = (nodeId: string) => {
  const index = workflowNodes.value.findIndex(n => n.id === nodeId)
  if (index > -1) {
    workflowNodes.value.splice(index, 1)
    
    // 删除相关连接线
    workflowEdges.value = workflowEdges.value.filter(
      edge => edge.sourceNodeId !== nodeId && edge.targetNodeId !== nodeId
    )
    
    if (selectedNodeId.value === nodeId) {
      selectedNodeId.value = undefined
    }
  }
}

const deleteEdge = (edgeId: string) => {
  const index = workflowEdges.value.findIndex(e => e.id === edgeId)
  if (index > -1) {
    workflowEdges.value.splice(index, 1)
    
    if (selectedEdgeId.value === edgeId) {
      selectedEdgeId.value = undefined
    }
  }
}

const startConnection = (nodeId: string, x: number, y: number) => {
  connecting.value = true
  connectionStart.value = { x, y, nodeId }
}

const updateWorkflowProperties = (properties: any) => {
  if (currentWorkflow.value) {
    Object.assign(currentWorkflow.value, properties)
  }
}

const updateNodeProperties = (properties: any) => {
  const node = selectedNode.value
  if (node) {
    Object.assign(node, properties)
  }
}

const updateEdgeProperties = (properties: any) => {
  const edge = selectedEdge.value
  if (edge) {
    Object.assign(edge, properties)
  }
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    published: '已发布',
    deprecated: '已废弃',
    archived: '已归档'
  }
  return statusMap[status] || status
}

// 鼠标移动事件处理
const handleMouseMove = (event: MouseEvent) => {
  if (connecting.value && canvasRef.value) {
    const rect = canvasRef.value.getBoundingClientRect()
    mousePosition.value = {
      x: (event.clientX - rect.left) / zoomLevel.value,
      y: (event.clientY - rect.top) / zoomLevel.value
    }
  }
}

// 生命周期
onMounted(() => {
  document.addEventListener('mousemove', handleMouseMove)
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleMouseMove)
})
</script>

<style scoped>
.workflow-designer {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
}

.designer-toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 8px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 8px;
}

.zoom-level {
  margin-left: 8px;
  font-size: 14px;
  color: #666;
}

.designer-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.node-panel {
  width: 240px;
  background-color: white;
  border-right: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #e0e0e0;
  background-color: #fafafa;
}

.panel-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 8px;
}

.node-category {
  margin-bottom: 8px;
}

.category-header {
  display: flex;
  align-items: center;
  padding: 8px;
  cursor: pointer;
  border-radius: 4px;
  transition: background-color 0.2s;
}

.category-header:hover {
  background-color: #f0f0f0;
}

.category-header span {
  margin-left: 8px;
  font-size: 13px;
  font-weight: 500;
  color: #333;
}

.category-nodes {
  padding-left: 16px;
}

.node-item {
  display: flex;
  align-items: center;
  padding: 8px;
  margin: 2px 0;
  cursor: grab;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: white;
  transition: all 0.2s;
}

.node-item:hover {
  border-color: #409EFF;
  box-shadow: 0 2px 4px rgba(64, 158, 255, 0.2);
}

.node-item:active {
  cursor: grabbing;
}

.node-item span {
  margin-left: 8px;
  font-size: 12px;
  color: #333;
}

.canvas-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  background-color: #fafafa;
}

.canvas-header {
  padding: 16px;
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
}

.workflow-info h3 {
  margin: 0 0 4px 0;
  font-size: 16px;
  color: #333;
}

.workflow-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  background-color: #e6f7ff;
  color: #1890ff;
}

.empty-state {
  color: #999;
  font-size: 14px;
}

.canvas {
  flex: 1;
  position: relative;
  overflow: hidden;
  transform-origin: top left;
  transition: transform 0.2s;
}

.canvas-grid {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: 
    linear-gradient(to right, #e0e0e0 1px, transparent 1px),
    linear-gradient(to bottom, #e0e0e0 1px, transparent 1px);
  background-size: 20px 20px;
  opacity: 0.5;
}

.temp-connection {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: 1000;
}

.property-panel {
  width: 300px;
  background-color: white;
  border-left: 1px solid #e0e0e0;
  display: flex;
  flex-direction: column;
}

.empty-properties {
  padding: 40px 20px;
  text-align: center;
}
</style>
