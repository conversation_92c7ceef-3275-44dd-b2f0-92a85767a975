import request from '@/utils/request'

/**
 * 任务分类
 */
export interface TaskCategory {
  id: string
  name: string
  code: string
  description: string
  color: string
  icon: string
  namingTemplate: string
  sortOrder: number
  isEnabled: boolean
  createdAt: string
  updatedAt: string
}

/**
 * 创建任务分类请求
 */
export interface CreateTaskCategoryRequest {
  name: string
  code: string
  description?: string
  color?: string
  icon?: string
  namingTemplate?: string
  sortOrder?: number
  isEnabled?: boolean
}

/**
 * 更新任务分类请求
 */
export interface UpdateTaskCategoryRequest {
  name?: string
  code?: string
  description?: string
  color?: string
  icon?: string
  namingTemplate?: string
  sortOrder?: number
  isEnabled?: boolean
}

// API 接口函数

/**
 * 获取任务分类列表
 */
export function getTaskCategories() {
  return request.get<TaskCategory[]>('/api/v1/task-categories')
}

/**
 * 获取任务分类详情
 */
export function getTaskCategoryById(categoryId: string) {
  return request.get<TaskCategory>(`/api/v1/task-categories/${categoryId}`)
}

/**
 * 创建任务分类
 */
export function createTaskCategory(data: CreateTaskCategoryRequest) {
  return request.post<TaskCategory>('/api/v1/task-categories', data)
}

/**
 * 更新任务分类
 */
export function updateTaskCategory(categoryId: string, data: UpdateTaskCategoryRequest) {
  return request.put<TaskCategory>(`/api/v1/task-categories/${categoryId}`, data)
}

/**
 * 删除任务分类
 */
export function deleteTaskCategory(categoryId: string) {
  return request.delete(`/api/v1/task-categories/${categoryId}`)
}

/**
 * 启用任务分类
 */
export function enableTaskCategory(categoryId: string) {
  return request.put(`/api/v1/task-categories/${categoryId}/enable`)
}

/**
 * 禁用任务分类
 */
export function disableTaskCategory(categoryId: string) {
  return request.put(`/api/v1/task-categories/${categoryId}/disable`)
}
