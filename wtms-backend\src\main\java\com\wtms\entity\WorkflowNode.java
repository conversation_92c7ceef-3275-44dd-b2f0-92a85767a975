package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 工作流节点实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("workflow_nodes")
@Schema(description = "工作流节点实体")
public class WorkflowNode implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "节点ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "工作流定义ID")
    @TableField("workflow_definition_id")
    private String workflowDefinitionId;

    @Schema(description = "节点编码")
    @TableField("node_code")
    private String nodeCode;

    @Schema(description = "节点名称")
    @TableField("node_name")
    private String nodeName;

    @Schema(description = "节点类型")
    @TableField("node_type")
    private String nodeType;

    @Schema(description = "节点描述")
    @TableField("description")
    private String description;

    @Schema(description = "节点配置JSON")
    @TableField("config_json")
    private String configJson;

    @Schema(description = "节点位置X坐标")
    @TableField("position_x")
    private Integer positionX;

    @Schema(description = "节点位置Y坐标")
    @TableField("position_y")
    private Integer positionY;

    @Schema(description = "节点宽度")
    @TableField("width")
    private Integer width;

    @Schema(description = "节点高度")
    @TableField("height")
    private Integer height;

    @Schema(description = "是否为开始节点")
    @TableField("is_start")
    private Boolean isStart;

    @Schema(description = "是否为结束节点")
    @TableField("is_end")
    private Boolean isEnd;

    @Schema(description = "是否启用")
    @TableField("is_enabled")
    private Boolean isEnabled;

    @Schema(description = "排序号")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "工作流定义信息")
    @TableField(exist = false)
    private WorkflowDefinition workflowDefinition;

    @Schema(description = "输入连接线列表")
    @TableField(exist = false)
    private List<WorkflowEdge> incomingEdges;

    @Schema(description = "输出连接线列表")
    @TableField(exist = false)
    private List<WorkflowEdge> outgoingEdges;

    /**
     * 节点类型枚举
     */
    public enum NodeType {
        START("start", "开始节点"),
        END("end", "结束节点"),
        USER_TASK("user_task", "用户任务"),
        SERVICE_TASK("service_task", "服务任务"),
        SCRIPT_TASK("script_task", "脚本任务"),
        MAIL_TASK("mail_task", "邮件任务"),
        TIMER_TASK("timer_task", "定时任务"),
        EXCLUSIVE_GATEWAY("exclusive_gateway", "排他网关"),
        PARALLEL_GATEWAY("parallel_gateway", "并行网关"),
        INCLUSIVE_GATEWAY("inclusive_gateway", "包容网关"),
        EVENT_GATEWAY("event_gateway", "事件网关"),
        SUBPROCESS("subprocess", "子流程"),
        CALL_ACTIVITY("call_activity", "调用活动"),
        BOUNDARY_EVENT("boundary_event", "边界事件"),
        INTERMEDIATE_EVENT("intermediate_event", "中间事件"),
        MESSAGE_EVENT("message_event", "消息事件"),
        TIMER_EVENT("timer_event", "定时事件"),
        ERROR_EVENT("error_event", "错误事件"),
        SIGNAL_EVENT("signal_event", "信号事件"),
        CONDITION_EVENT("condition_event", "条件事件");

        private final String code;
        private final String description;

        NodeType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static NodeType fromCode(String code) {
            for (NodeType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return USER_TASK;
        }

        /**
         * 检查是否为任务节点
         */
        public boolean isTask() {
            return this == USER_TASK || this == SERVICE_TASK || this == SCRIPT_TASK || 
                   this == MAIL_TASK || this == TIMER_TASK;
        }

        /**
         * 检查是否为网关节点
         */
        public boolean isGateway() {
            return this == EXCLUSIVE_GATEWAY || this == PARALLEL_GATEWAY || 
                   this == INCLUSIVE_GATEWAY || this == EVENT_GATEWAY;
        }

        /**
         * 检查是否为事件节点
         */
        public boolean isEvent() {
            return this == BOUNDARY_EVENT || this == INTERMEDIATE_EVENT || 
                   this == MESSAGE_EVENT || this == TIMER_EVENT || 
                   this == ERROR_EVENT || this == SIGNAL_EVENT || this == CONDITION_EVENT;
        }
    }

    /**
     * 检查是否为开始节点
     */
    public boolean isStart() {
        return Boolean.TRUE.equals(this.isStart);
    }

    /**
     * 检查是否为结束节点
     */
    public boolean isEnd() {
        return Boolean.TRUE.equals(this.isEnd);
    }

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.isEnabled);
    }

    /**
     * 获取节点类型描述
     */
    public String getNodeTypeText() {
        return NodeType.fromCode(this.nodeType).getDescription();
    }

    /**
     * 检查是否为任务节点
     */
    public boolean isTaskNode() {
        return NodeType.fromCode(this.nodeType).isTask();
    }

    /**
     * 检查是否为网关节点
     */
    public boolean isGatewayNode() {
        return NodeType.fromCode(this.nodeType).isGateway();
    }

    /**
     * 检查是否为事件节点
     */
    public boolean isEventNode() {
        return NodeType.fromCode(this.nodeType).isEvent();
    }

    /**
     * 获取节点位置信息
     */
    public String getPositionInfo() {
        return String.format("(%d, %d)", positionX != null ? positionX : 0, positionY != null ? positionY : 0);
    }

    /**
     * 获取节点尺寸信息
     */
    public String getSizeInfo() {
        return String.format("%dx%d", width != null ? width : 100, height != null ? height : 60);
    }
}
