package com.wtms.security;

import com.wtms.entity.User;
import com.wtms.mapper.UserMapper;
import com.wtms.service.DevelopmentPermissionService;
import com.wtms.service.PermissionService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;

import javax.servlet.FilterChain;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * JWT认证过滤器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
public class JwtAuthenticationFilter extends OncePerRequestFilter {

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Autowired
    private UserDetailsServiceImpl userDetailsService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private DevelopmentPermissionService developmentPermissionService;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        
        try {
            String jwt = getJwtFromRequest(request);
            
            if (StringUtils.hasText(jwt) && tokenProvider.validateToken(jwt)) {
                String username = tokenProvider.getUsernameFromToken(jwt);

                // 从数据库获取用户信息
                User user = userMapper.findByUsernameWithRole(username);
                if (user != null && user.isActive()) {
                    // 获取用户的实时权限
                    List<SimpleGrantedAuthority> authorityList = getUserAuthorities(user);

                    // 创建用户详情
                    UserDetails userDetails = org.springframework.security.core.userdetails.User.builder()
                            .username(user.getUsername())
                            .password("") // 密码不需要，因为已经通过JWT验证
                            .authorities(authorityList)
                            .build();

                    UsernamePasswordAuthenticationToken authentication =
                            new UsernamePasswordAuthenticationToken(userDetails, null, authorityList);
                    authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));

                    SecurityContextHolder.getContext().setAuthentication(authentication);

                    log.debug("Set Authentication in SecurityContext for user: {} with {} authorities",
                             username, authorityList.size());
                } else {
                    log.warn("User not found or inactive: {}", username);
                }
            }
        } catch (Exception ex) {
            log.error("Could not set user authentication in security context", ex);
        }
        
        filterChain.doFilter(request, response);
    }

    /**
     * 从请求中获取JWT Token
     */
    private String getJwtFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }

    /**
     * 获取用户权限列表
     */
    private List<SimpleGrantedAuthority> getUserAuthorities(User user) {
        List<SimpleGrantedAuthority> authorities = new java.util.ArrayList<>();

        try {
            // 检查开发环境权限覆盖
            if (developmentPermissionService.shouldTreatAsSuperAdmin(user.getId())) {
                // 开发环境下，给予所有权限
                authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
                authorities.add(new SimpleGrantedAuthority("ROLE_SUPER_ADMIN"));
                log.debug("Development mode: granted super admin authorities to user {}", user.getUsername());
                return authorities;
            }

            // 获取用户的实际权限
            java.util.Set<String> permissionCodes = permissionService.getUserPermissionCodes(user.getId());

            // 添加权限
            for (String permissionCode : permissionCodes) {
                authorities.add(new SimpleGrantedAuthority(permissionCode));
            }

            // 添加基本角色权限
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));

            // 检查是否为超级管理员
            if (permissionCodes.size() >= 45) { // 如果权限数量足够多，认为是管理员
                authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN"));
            }

        } catch (Exception e) {
            log.error("Error getting user authorities for user: {}", user.getUsername(), e);
        }

        return authorities;
    }
}
