package com.wtms.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 批量任务分配请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "批量任务分配请求")
public class BatchAssignRequest {

    @Schema(description = "任务ID列表", example = "[\"task-id-1\", \"task-id-2\"]")
    @NotEmpty(message = "任务列表不能为空")
    private List<String> taskIds;

    @Schema(description = "分配规则", example = "round_robin", allowableValues = {"round_robin", "workload_balance", "skill_match", "manual"})
    private String assignRule = "manual";

    @Schema(description = "分配目标列表")
    @NotEmpty(message = "分配目标不能为空")
    private List<AssignTarget> assignTargets;

    @Schema(description = "分配备注")
    private String comment;

    @Schema(description = "是否发送通知", example = "true")
    private Boolean sendNotification = true;

    /**
     * 分配目标
     */
    @Data
    @Schema(description = "分配目标")
    public static class AssignTarget {
        @Schema(description = "目标类型", allowableValues = {"user", "department", "role"})
        private String targetType;

        @Schema(description = "目标ID")
        private String targetId;

        @Schema(description = "目标名称")
        private String targetName;

        @Schema(description = "权重", example = "1.0")
        private Double weight = 1.0;

        @Schema(description = "最大任务数", example = "10")
        private Integer maxTasks;
    }
}
