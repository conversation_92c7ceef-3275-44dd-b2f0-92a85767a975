package com.wtms.dto.response;

import com.wtms.entity.Permission;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 权限树响应DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "权限树响应")
public class PermissionTreeResponse {

    @Schema(description = "权限ID")
    private String id;

    @Schema(description = "权限编码")
    private String code;

    @Schema(description = "权限名称")
    private String name;

    @Schema(description = "权限描述")
    private String description;

    @Schema(description = "权限类型")
    private String type;

    @Schema(description = "权限类型描述")
    private String typeText;

    @Schema(description = "权限分组")
    private String groupName;

    @Schema(description = "父权限ID")
    private String parentId;

    @Schema(description = "权限路径")
    private String path;

    @Schema(description = "权限层级")
    private Integer level;

    @Schema(description = "排序")
    private Integer sortOrder;

    @Schema(description = "资源标识")
    private String resource;

    @Schema(description = "操作标识")
    private String action;

    @Schema(description = "权限表达式")
    private String expression;

    @Schema(description = "完整权限表达式")
    private String fullExpression;

    @Schema(description = "是否启用")
    private Boolean isEnabled;

    @Schema(description = "是否系统权限")
    private Boolean isSystem;

    @Schema(description = "是否叶子节点")
    private Boolean isLeaf;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "子权限列表")
    private List<PermissionTreeResponse> children;

    /**
     * 从Permission实体转换
     */
    public static PermissionTreeResponse from(Permission permission) {
        PermissionTreeResponseBuilder builder = PermissionTreeResponse.builder()
                .id(permission.getId())
                .code(permission.getCode())
                .name(permission.getName())
                .description(permission.getDescription())
                .type(permission.getType())
                .typeText(Permission.Type.fromCode(permission.getType()).getDescription())
                .groupName(permission.getGroupName())
                .parentId(permission.getParentId())
                .path(permission.getPath())
                .level(permission.getLevel())
                .sortOrder(permission.getSortOrder())
                .resource(permission.getResource())
                .action(permission.getAction())
                .expression(permission.getExpression())
                .fullExpression(permission.getFullExpression())
                .isEnabled(permission.getIsEnabled())
                .isSystem(permission.getIsSystem())
                .isLeaf(permission.isLeaf())
                .createdAt(permission.getCreatedAt())
                .updatedAt(permission.getUpdatedAt());

        // 转换子权限
        if (permission.getChildren() != null && !permission.getChildren().isEmpty()) {
            List<PermissionTreeResponse> childrenResponse = permission.getChildren().stream()
                    .map(PermissionTreeResponse::from)
                    .collect(Collectors.toList());
            builder.children(childrenResponse);
        }

        return builder.build();
    }

    /**
     * 从Permission列表转换为树形结构
     */
    public static List<PermissionTreeResponse> buildTree(List<Permission> permissions) {
        return permissions.stream()
                .filter(Permission::isRoot)
                .map(PermissionTreeResponse::from)
                .collect(Collectors.toList());
    }
}
