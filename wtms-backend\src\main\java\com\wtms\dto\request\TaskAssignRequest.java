package com.wtms.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 任务分配请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "任务分配请求")
public class TaskAssignRequest {

    @Schema(description = "分配类型", example = "user", allowableValues = {"user", "department", "role"})
    @NotBlank(message = "分配类型不能为空")
    private String assignType;

    @Schema(description = "分配目标ID列表", example = "[\"user-id-1\", \"user-id-2\"]")
    @NotEmpty(message = "分配目标不能为空")
    private List<String> assignTargets;

    @Schema(description = "分配备注", example = "紧急任务，请优先处理")
    private String comment;

    @Schema(description = "是否发送通知", example = "true")
    private Boolean sendNotification = true;

    @Schema(description = "截止时间", example = "2024-01-31 18:00:00")
    private String deadline;

    @Schema(description = "优先级调整", example = "4")
    private Integer priorityAdjustment;
}
