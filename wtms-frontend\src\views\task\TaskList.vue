<template>
  <div class="task-list-container">
    <!-- 搜索和筛选区域 -->
    <el-card class="search-card" shadow="never">
      <el-form :model="searchForm" inline>
        <el-form-item label="关键词">
          <el-input
            v-model="searchForm.search"
            placeholder="搜索任务标题或描述"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        
        <el-form-item label="状态">
          <el-select
            v-model="searchForm.status"
            placeholder="选择状态"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="(label, value) in taskStatuses"
              :key="value"
              :label="label"
              :value="value"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="优先级">
          <el-select
            v-model="searchForm.priority"
            placeholder="选择优先级"
            clearable
            style="width: 120px"
          >
            <el-option label="最低" :value="1" />
            <el-option label="低" :value="2" />
            <el-option label="中" :value="3" />
            <el-option label="高" :value="4" />
            <el-option label="最高" :value="5" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="分类">
          <el-select
            v-model="searchForm.categoryId"
            placeholder="选择分类"
            clearable
            style="width: 120px"
          >
            <el-option
              v-for="category in taskCategories"
              :key="category.id"
              :label="category.name"
              :value="category.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item label="负责人">
          <el-select
            v-model="searchForm.assigneeId"
            placeholder="选择负责人"
            clearable
            filterable
            style="width: 150px"
          >
            <el-option
              v-for="user in users"
              :key="user.id"
              :label="user.fullName"
              :value="user.id"
            />
          </el-select>
        </el-form-item>
        
        <el-form-item>
          <el-button type="primary" @click="handleSearch">
            <el-icon><Search /></el-icon>
            搜索
          </el-button>
          <el-button @click="handleReset">
            <el-icon><Refresh /></el-icon>
            重置
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 工具栏 -->
    <el-card class="toolbar-card" shadow="never">
      <div class="toolbar">
        <div class="toolbar-left">
          <el-button type="primary" @click="handleCreate">
            <el-icon><Plus /></el-icon>
            新建任务
          </el-button>
          
          <el-dropdown
            v-if="selectedTasks.length > 0"
            @command="handleBatchAction"
          >
            <el-button type="default">
              批量操作
              <el-icon class="el-icon--right"><ArrowDown /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="assign">批量分配</el-dropdown-item>
                <el-dropdown-item command="status">批量更新状态</el-dropdown-item>
                <el-dropdown-item command="delete" divided>批量删除</el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
        
        <div class="toolbar-right">
          <el-radio-group v-model="viewMode" size="small">
            <el-radio-button label="list">列表</el-radio-button>
            <el-radio-button label="card">卡片</el-radio-button>
            <el-radio-button label="kanban">看板</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="table-card" shadow="never">
      <el-table
        v-if="viewMode === 'list'"
        v-loading="loading"
        :data="taskList"
        @selection-change="handleSelectionChange"
        @row-click="handleRowClick"
        stripe
        style="width: 100%"
      >
        <el-table-column type="selection" width="55" />
        
        <el-table-column prop="taskCode" label="任务编号" width="140">
          <template #default="{ row }">
            <el-link type="primary" @click="handleView(row)">
              {{ row.taskCode }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="title" label="任务标题" min-width="200">
          <template #default="{ row }">
            <div class="task-title">
              <span>{{ row.title }}</span>
              <el-tag
                v-for="tag in row.tags"
                :key="tag"
                size="small"
                class="task-tag"
              >
                {{ tag }}
              </el-tag>
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)">
              {{ row.statusText }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="priority" label="优先级" width="80">
          <template #default="{ row }">
            <el-tag :type="getPriorityType(row.priority)" size="small">
              {{ row.priorityText }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="category" label="分类" width="100">
          <template #default="{ row }">
            <el-tag
              v-if="row.category"
              :color="row.category.color"
              size="small"
            >
              {{ row.category.name }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="assignee" label="负责人" width="120">
          <template #default="{ row }">
            <div v-if="row.assignee" class="user-info">
              <el-avatar :size="24" :src="row.assignee.avatar">
                {{ row.assignee.fullName?.charAt(0) }}
              </el-avatar>
              <span class="user-name">{{ row.assignee.fullName }}</span>
            </div>
            <span v-else class="text-muted">未分配</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="progress" label="进度" width="120">
          <template #default="{ row }">
            <el-progress
              :percentage="row.progress || 0"
              :stroke-width="6"
              :show-text="false"
            />
            <span class="progress-text">{{ row.progress || 0 }}%</span>
          </template>
        </el-table-column>
        
        <el-table-column prop="plannedEndDate" label="计划完成" width="120">
          <template #default="{ row }">
            <span v-if="row.plannedEndDate">
              {{ formatDate(row.plannedEndDate) }}
            </span>
            <span v-else class="text-muted">未设置</span>
          </template>
        </el-table-column>
        
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button-group size="small">
              <el-button @click="handleView(row)">查看</el-button>
              <el-button @click="handleEdit(row)">编辑</el-button>
              
              <el-dropdown @command="(command) => handleAction(command, row)">
                <el-button>
                  更多
                  <el-icon class="el-icon--right"><ArrowDown /></el-icon>
                </el-button>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item command="start" v-if="canStart(row)">
                      开始
                    </el-dropdown-item>
                    <el-dropdown-item command="complete" v-if="canComplete(row)">
                      完成
                    </el-dropdown-item>
                    <el-dropdown-item command="pause" v-if="canPause(row)">
                      暂停
                    </el-dropdown-item>
                    <el-dropdown-item command="copy">复制</el-dropdown-item>
                    <el-dropdown-item command="delete" divided>删除</el-dropdown-item>
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-button-group>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 任务详情抽屉 -->
    <TaskDetailDrawer
      v-model:visible="detailVisible"
      :task-id="currentTaskId"
      @refresh="loadTaskList"
    />

    <!-- 任务编辑对话框 -->
    <TaskEditDialog
      v-model:visible="editVisible"
      :task-id="currentTaskId"
      :mode="editMode"
      @success="handleEditSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Search, Refresh, Plus, ArrowDown } from '@element-plus/icons-vue'
import { getTaskList, deleteTask, type TaskListItem, type TaskQueryParams } from '@/api/task'
import { getTaskCategories, type TaskCategory } from '@/api/taskCategory'
import { getAllTaskStatuses } from '@/api/taskStatus'
import TaskDetailDrawer from './components/TaskDetailDrawer.vue'
import TaskEditDialog from './components/TaskEditDialog.vue'

// 响应式数据
const loading = ref(false)
const taskList = ref<TaskListItem[]>([])
const taskCategories = ref<TaskCategory[]>([])
const taskStatuses = ref<Record<string, string>>({})
const users = ref<any[]>([]) // TODO: 定义用户类型
const selectedTasks = ref<TaskListItem[]>([])
const viewMode = ref('list')

// 搜索表单
const searchForm = reactive<TaskQueryParams>({
  search: '',
  status: '',
  priority: undefined,
  categoryId: '',
  assigneeId: '',
  sortBy: 'created_at',
  sortOrder: 'desc'
})

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 对话框状态
const detailVisible = ref(false)
const editVisible = ref(false)
const currentTaskId = ref('')
const editMode = ref<'create' | 'edit'>('create')

// 计算属性
const searchParams = computed(() => ({
  ...searchForm,
  page: pagination.current,
  size: pagination.size
}))

// 方法
const loadTaskList = async () => {
  try {
    loading.value = true
    const { data } = await getTaskList(searchParams.value)
    taskList.value = data.records
    pagination.total = data.total
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const loadTaskCategories = async () => {
  try {
    const { data } = await getTaskCategories()
    taskCategories.value = data
  } catch (error) {
    console.error('加载任务分类失败:', error)
  }
}

const loadTaskStatuses = async () => {
  try {
    const { data } = await getAllTaskStatuses()
    taskStatuses.value = data
  } catch (error) {
    console.error('加载任务状态失败:', error)
  }
}

const handleSearch = () => {
  pagination.current = 1
  loadTaskList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    search: '',
    status: '',
    priority: undefined,
    categoryId: '',
    assigneeId: '',
    sortBy: 'created_at',
    sortOrder: 'desc'
  })
  handleSearch()
}

const handleCreate = () => {
  editMode.value = 'create'
  currentTaskId.value = ''
  editVisible.value = true
}

const handleView = (row: TaskListItem) => {
  currentTaskId.value = row.id
  detailVisible.value = true
}

const handleEdit = (row: TaskListItem) => {
  editMode.value = 'edit'
  currentTaskId.value = row.id
  editVisible.value = true
}

const handleRowClick = (row: TaskListItem) => {
  handleView(row)
}

const handleSelectionChange = (selection: TaskListItem[]) => {
  selectedTasks.value = selection
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadTaskList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadTaskList()
}

const handleEditSuccess = () => {
  editVisible.value = false
  loadTaskList()
}

// 状态相关方法
const getStatusType = (status: string) => {
  const statusTypes: Record<string, string> = {
    draft: 'info',
    pending: 'warning',
    in_progress: 'primary',
    review: 'warning',
    testing: 'warning',
    completed: 'success',
    paused: 'info',
    cancelled: 'danger',
    archived: 'info'
  }
  return statusTypes[status] || 'info'
}

const getPriorityType = (priority: number) => {
  const priorityTypes: Record<number, string> = {
    1: 'info',
    2: 'info',
    3: 'warning',
    4: 'danger',
    5: 'danger'
  }
  return priorityTypes[priority] || 'info'
}

const canStart = (task: TaskListItem) => {
  return ['draft', 'pending', 'paused'].includes(task.status)
}

const canComplete = (task: TaskListItem) => {
  return ['in_progress', 'review', 'testing'].includes(task.status)
}

const canPause = (task: TaskListItem) => {
  return task.status === 'in_progress'
}

// 工具方法
const formatDate = (dateStr: string) => {
  return new Date(dateStr).toLocaleDateString()
}

// 生命周期
onMounted(() => {
  loadTaskList()
  loadTaskCategories()
  loadTaskStatuses()
})
</script>

<style scoped>
.task-list-container {
  padding: 20px;
}

.search-card,
.toolbar-card,
.table-card {
  margin-bottom: 20px;
}

.toolbar {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.task-tag {
  margin-left: 4px;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.user-name {
  font-size: 12px;
}

.progress-text {
  margin-left: 8px;
  font-size: 12px;
  color: #666;
}

.text-muted {
  color: #999;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
