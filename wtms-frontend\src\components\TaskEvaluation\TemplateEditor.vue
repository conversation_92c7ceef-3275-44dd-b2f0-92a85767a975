<template>
  <div class="template-editor">
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px">
      <!-- 基本信息 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <span>基本信息</span>
        </template>
        
        <el-form-item label="模板名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入模板名称" />
        </el-form-item>
        
        <el-form-item label="模板描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :rows="3"
            placeholder="请输入模板描述"
          />
        </el-form-item>
        
        <el-form-item label="模板分类" prop="category">
          <el-select v-model="form.category" placeholder="请选择分类">
            <el-option label="通用模板" value="general" />
            <el-option label="技术评价" value="technical" />
            <el-option label="管理评价" value="management" />
            <el-option label="客户服务" value="service" />
            <el-option label="自定义模板" value="custom" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="标签">
          <el-tag
            v-for="tag in form.tags"
            :key="tag"
            closable
            @close="removeTag(tag)"
            style="margin-right: 8px;"
          >
            {{ tag }}
          </el-tag>
          <el-input
            v-if="tagInputVisible"
            ref="tagInputRef"
            v-model="tagInputValue"
            size="small"
            style="width: 120px;"
            @keyup.enter="addTag"
            @blur="addTag"
          />
          <el-button v-else size="small" @click="showTagInput">
            + 添加标签
          </el-button>
        </el-form-item>
      </el-card>

      <!-- 评价维度 -->
      <el-card class="form-section" shadow="never">
        <template #header>
          <div class="section-header">
            <span>评价维度</span>
            <el-button size="small" type="primary" @click="addDimension">
              <el-icon><Plus /></el-icon>
              添加维度
            </el-button>
          </div>
        </template>
        
        <div class="dimensions-list">
          <div
            v-for="(dimension, index) in form.dimensions"
            :key="dimension.id"
            class="dimension-item"
          >
            <div class="dimension-header">
              <span class="dimension-index">{{ index + 1 }}</span>
              <el-button
                size="small"
                type="danger"
                text
                @click="removeDimension(index)"
              >
                <el-icon><Delete /></el-icon>
              </el-button>
            </div>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :prop="`dimensions.${index}.name`" :rules="dimensionRules.name">
                  <template #label>
                    <span>维度名称 <span style="color: red;">*</span></span>
                  </template>
                  <el-input v-model="dimension.name" placeholder="请输入维度名称" />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :prop="`dimensions.${index}.weight`" :rules="dimensionRules.weight">
                  <template #label>
                    <span>权重 <span style="color: red;">*</span></span>
                  </template>
                  <el-input-number
                    v-model="dimension.weight"
                    :min="0.1"
                    :max="1"
                    :step="0.1"
                    :precision="1"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
            
            <el-form-item :prop="`dimensions.${index}.description`">
              <template #label>维度描述</template>
              <el-input
                v-model="dimension.description"
                type="textarea"
                :rows="2"
                placeholder="请输入维度描述"
              />
            </el-form-item>
            
            <el-form-item :prop="`dimensions.${index}.criteria`">
              <template #label>评价标准</template>
              <el-input
                v-model="dimension.criteria"
                type="textarea"
                :rows="2"
                placeholder="请输入评价标准"
              />
            </el-form-item>
            
            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :prop="`dimensions.${index}.minScore`">
                  <template #label>最小分值</template>
                  <el-input-number
                    v-model="dimension.minScore"
                    :min="0"
                    :max="100"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :prop="`dimensions.${index}.maxScore`">
                  <template #label>最大分值</template>
                  <el-input-number
                    v-model="dimension.maxScore"
                    :min="0"
                    :max="100"
                    style="width: 100%"
                  />
                </el-form-item>
              </el-col>
            </el-row>
          </div>
        </div>
        
        <el-empty v-if="form.dimensions.length === 0" description="暂无评价维度，请添加" />
      </el-card>
    </el-form>

    <!-- 操作按钮 -->
    <div class="form-actions">
      <el-button @click="handleCancel">取消</el-button>
      <el-button @click="handlePreview">预览</el-button>
      <el-button type="primary" @click="handleSave">保存模板</el-button>
    </div>

    <!-- 预览对话框 -->
    <el-dialog v-model="showPreview" title="模板预览" width="60%">
      <div class="template-preview">
        <div class="preview-header">
          <h3>{{ form.name }}</h3>
          <p>{{ form.description }}</p>
          <div class="preview-meta">
            <el-tag>{{ getCategoryText(form.category) }}</el-tag>
            <el-tag v-for="tag in form.tags" :key="tag" style="margin-left: 8px;">
              {{ tag }}
            </el-tag>
          </div>
        </div>
        
        <div class="preview-dimensions">
          <h4>评价维度 ({{ form.dimensions.length }}个)</h4>
          <div class="dimensions-preview">
            <div
              v-for="(dimension, index) in form.dimensions"
              :key="dimension.id"
              class="dimension-preview"
            >
              <div class="dimension-preview-header">
                <span class="dimension-preview-name">{{ index + 1 }}. {{ dimension.name }}</span>
                <span class="dimension-preview-weight">权重: {{ dimension.weight }}</span>
              </div>
              <div class="dimension-preview-desc">{{ dimension.description }}</div>
              <div class="dimension-preview-criteria">
                <strong>评价标准:</strong> {{ dimension.criteria }}
              </div>
              <div class="dimension-preview-range">
                评分范围: {{ dimension.minScore }} - {{ dimension.maxScore }}
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, nextTick, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Plus, Delete } from '@element-plus/icons-vue'

// Props
interface Props {
  template?: any
  mode: 'create' | 'edit'
}

const props = withDefaults(defineProps<Props>(), {
  mode: 'create'
})

// Emits
const emit = defineEmits<{
  'save': [template: any]
  'cancel': []
}>()

// 响应式数据
const formRef = ref()
const tagInputRef = ref()
const showPreview = ref(false)
const tagInputVisible = ref(false)
const tagInputValue = ref('')

const form = reactive({
  id: '',
  name: '',
  description: '',
  category: 'general',
  tags: [] as string[],
  dimensions: [] as any[]
})

const rules = {
  name: [{ required: true, message: '请输入模板名称', trigger: 'blur' }],
  description: [{ required: true, message: '请输入模板描述', trigger: 'blur' }],
  category: [{ required: true, message: '请选择模板分类', trigger: 'change' }]
}

const dimensionRules = {
  name: [{ required: true, message: '请输入维度名称', trigger: 'blur' }],
  weight: [{ required: true, message: '请输入权重', trigger: 'blur' }]
}

// 方法
const initForm = () => {
  if (props.template && props.mode === 'edit') {
    Object.assign(form, {
      id: props.template.id,
      name: props.template.name,
      description: props.template.description,
      category: props.template.category,
      tags: [...(props.template.tags || [])],
      dimensions: props.template.dimensions?.map((dim: any) => ({
        id: dim.id || Date.now().toString(),
        name: dim.name,
        description: dim.description,
        criteria: dim.criteria,
        weight: dim.weight,
        minScore: dim.minScore || 0,
        maxScore: dim.maxScore || 100
      })) || []
    })
  } else {
    // 创建模式，添加默认维度
    addDimension()
  }
}

const addDimension = () => {
  form.dimensions.push({
    id: Date.now().toString(),
    name: '',
    description: '',
    criteria: '',
    weight: 0.2,
    minScore: 0,
    maxScore: 100
  })
}

const removeDimension = (index: number) => {
  form.dimensions.splice(index, 1)
}

const showTagInput = () => {
  tagInputVisible.value = true
  nextTick(() => {
    tagInputRef.value?.focus()
  })
}

const addTag = () => {
  const tag = tagInputValue.value.trim()
  if (tag && !form.tags.includes(tag)) {
    form.tags.push(tag)
  }
  tagInputVisible.value = false
  tagInputValue.value = ''
}

const removeTag = (tag: string) => {
  const index = form.tags.indexOf(tag)
  if (index > -1) {
    form.tags.splice(index, 1)
  }
}

const handleCancel = () => {
  emit('cancel')
}

const handlePreview = () => {
  showPreview.value = true
}

const handleSave = async () => {
  try {
    await formRef.value?.validate()
    
    // 验证权重总和
    const totalWeight = form.dimensions.reduce((sum, dim) => sum + (dim.weight || 0), 0)
    if (Math.abs(totalWeight - 1) > 0.1) {
      ElMessage.warning('所有维度权重之和应该等于1.0')
      return
    }
    
    // 验证维度名称唯一性
    const dimensionNames = form.dimensions.map(dim => dim.name)
    const uniqueNames = [...new Set(dimensionNames)]
    if (dimensionNames.length !== uniqueNames.length) {
      ElMessage.warning('维度名称不能重复')
      return
    }
    
    emit('save', { ...form })
  } catch (error) {
    console.error('Form validation failed:', error)
  }
}

const getCategoryText = (category: string) => {
  const categoryMap: Record<string, string> = {
    general: '通用模板',
    technical: '技术评价',
    management: '管理评价',
    service: '客户服务',
    custom: '自定义模板'
  }
  return categoryMap[category] || category
}

// 生命周期
onMounted(() => {
  initForm()
})
</script>

<style scoped>
.template-editor {
  max-height: 70vh;
  overflow-y: auto;
}

.form-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dimensions-list {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.dimension-item {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  background: #fafafa;
}

.dimension-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.dimension-index {
  font-size: 16px;
  font-weight: 600;
  color: #409eff;
}

.form-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.template-preview {
  max-height: 500px;
  overflow-y: auto;
}

.preview-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #ebeef5;
}

.preview-header h3 {
  margin: 0 0 8px 0;
  font-size: 20px;
  color: #303133;
}

.preview-header p {
  margin: 0 0 12px 0;
  color: #606266;
}

.preview-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.preview-dimensions h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  color: #303133;
}

.dimensions-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dimension-preview {
  padding: 12px;
  border: 1px solid #ebeef5;
  border-radius: 6px;
  background: #f9f9f9;
}

.dimension-preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.dimension-preview-name {
  font-size: 14px;
  font-weight: 500;
  color: #303133;
}

.dimension-preview-weight {
  font-size: 12px;
  color: #909399;
}

.dimension-preview-desc {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
}

.dimension-preview-criteria {
  font-size: 13px;
  color: #606266;
  margin-bottom: 6px;
}

.dimension-preview-range {
  font-size: 12px;
  color: #909399;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
