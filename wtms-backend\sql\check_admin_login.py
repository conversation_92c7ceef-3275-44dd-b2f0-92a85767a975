#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WTMS Admin用户登录问题诊断脚本
"""

import pymysql
import bcrypt
import json
from datetime import datetime

# 数据库配置
config = {
    'host': 'localhost',
    'port': 3308,
    'user': 'root',
    'password': 'ankaixin.docker.mysql',
    'database': 'wtms',
    'charset': 'utf8mb4'
}

def check_admin_user():
    """检查admin用户基本信息"""
    connection = pymysql.connect(**config)
    cursor = connection.cursor()
    
    print('🔍 1. 检查admin用户基本信息...')
    cursor.execute('''
        SELECT id, username, password, email, status, enabled, 
               created_at, updated_at, last_login_at
        FROM users 
        WHERE username = 'admin'
    ''')
    
    user_info = cursor.fetchone()
    if user_info:
        print('✅ admin用户存在:')
        print(f'  ID: {user_info[0]}')
        print(f'  用户名: {user_info[1]}')
        print(f'  密码哈希: {user_info[2][:50]}...')
        print(f'  邮箱: {user_info[3]}')
        print(f'  状态: {user_info[4]}')
        print(f'  启用状态: {user_info[5]}')
        print(f'  创建时间: {user_info[6]}')
        print(f'  更新时间: {user_info[7]}')
        print(f'  最后登录: {user_info[8]}')
        
        # 验证密码哈希格式
        password_hash = user_info[2]
        if password_hash.startswith('$2a$') or password_hash.startswith('$2b$'):
            print('✅ 密码使用BCrypt加密')
        else:
            print('❌ 密码加密格式异常')
            
        return user_info[0], user_info[2]  # 返回用户ID和密码哈希
    else:
        print('❌ 未找到admin用户!')
        return None, None
    
    connection.close()

def check_admin_roles(user_id):
    """检查admin用户角色分配"""
    if not user_id:
        return
        
    connection = pymysql.connect(**config)
    cursor = connection.cursor()
    
    print('\n🔍 2. 检查admin用户角色分配...')
    cursor.execute('''
        SELECT ur.id, ur.user_id, ur.role_id, ur.status, ur.assigned_at, ur.expires_at,
               r.name as role_name, r.code as role_code, r.status as role_status
        FROM user_roles ur
        JOIN roles r ON ur.role_id = r.id
        WHERE ur.user_id = %s
    ''', (user_id,))
    
    roles = cursor.fetchall()
    if roles:
        print(f'✅ admin用户已分配 {len(roles)} 个角色:')
        for role in roles:
            print(f'  角色: {role[6]} ({role[7]})')
            print(f'    分配状态: {role[3]}')
            print(f'    角色状态: {role[8]}')
            print(f'    分配时间: {role[4]}')
            print(f'    过期时间: {role[5]}')
            print('  ---')
    else:
        print('❌ admin用户未分配任何角色!')
    
    connection.close()

def check_super_admin_role():
    """检查SUPER_ADMIN角色是否存在"""
    connection = pymysql.connect(**config)
    cursor = connection.cursor()
    
    print('\n🔍 3. 检查SUPER_ADMIN角色...')
    cursor.execute('''
        SELECT id, name, code, description, status, created_at
        FROM roles 
        WHERE code = 'SUPER_ADMIN'
    ''')
    
    role_info = cursor.fetchone()
    if role_info:
        print('✅ SUPER_ADMIN角色存在:')
        print(f'  ID: {role_info[0]}')
        print(f'  名称: {role_info[1]}')
        print(f'  代码: {role_info[2]}')
        print(f'  描述: {role_info[3]}')
        print(f'  状态: {role_info[4]}')
        print(f'  创建时间: {role_info[5]}')
    else:
        print('❌ SUPER_ADMIN角色不存在!')
    
    connection.close()

def test_password_verification():
    """测试密码验证"""
    print('\n🔍 4. 测试密码验证...')
    
    # 测试常见密码
    test_passwords = ['123456', 'admin', 'password', 'admin123']
    
    connection = pymysql.connect(**config)
    cursor = connection.cursor()
    
    cursor.execute('SELECT password FROM users WHERE username = "admin"')
    result = cursor.fetchone()
    
    if result:
        stored_hash = result[0]
        print(f'存储的密码哈希: {stored_hash[:50]}...')
        
        for pwd in test_passwords:
            try:
                if bcrypt.checkpw(pwd.encode('utf-8'), stored_hash.encode('utf-8')):
                    print(f'✅ 密码 "{pwd}" 验证成功!')
                    return pwd
                else:
                    print(f'❌ 密码 "{pwd}" 验证失败')
            except Exception as e:
                print(f'❌ 密码 "{pwd}" 验证出错: {e}')
    
    connection.close()
    return None

def check_database_connection():
    """检查数据库连接"""
    print('\n🔍 5. 检查数据库连接...')
    try:
        connection = pymysql.connect(**config)
        cursor = connection.cursor()
        cursor.execute('SELECT VERSION()')
        version = cursor.fetchone()
        print(f'✅ 数据库连接正常，MySQL版本: {version[0]}')
        connection.close()
        return True
    except Exception as e:
        print(f'❌ 数据库连接失败: {e}')
        return False

if __name__ == '__main__':
    print('🚀 WTMS Admin用户登录问题诊断开始...\n')
    
    # 检查数据库连接
    if not check_database_connection():
        exit(1)
    
    # 检查admin用户
    user_id, password_hash = check_admin_user()
    
    # 检查角色分配
    check_admin_roles(user_id)
    
    # 检查SUPER_ADMIN角色
    check_super_admin_role()
    
    # 测试密码验证
    correct_password = test_password_verification()
    
    print('\n📋 诊断总结:')
    if user_id:
        print('✅ admin用户存在')
    else:
        print('❌ admin用户不存在')
    
    if correct_password:
        print(f'✅ 找到正确密码: {correct_password}')
    else:
        print('❌ 未找到正确密码')
    
    print('\n🎯 建议的解决方案将在诊断完成后提供...')
