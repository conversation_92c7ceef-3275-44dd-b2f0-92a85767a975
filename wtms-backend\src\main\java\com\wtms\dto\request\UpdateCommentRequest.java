package com.wtms.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Size;
import java.util.List;

/**
 * 更新评论请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "更新评论请求")
public class UpdateCommentRequest {

    @Schema(description = "评论内容", example = "这是更新后的评论内容")
    @Size(max = 2000, message = "评论内容不能超过2000个字符")
    private String content;

    @Schema(description = "是否置顶", example = "false")
    private Boolean isPinned;

    @Schema(description = "是否私有", example = "false")
    private Boolean isPrivate;

    @Schema(description = "附件ID列表", example = "[\"attachment-uuid-1\", \"attachment-uuid-2\"]")
    private List<String> attachmentIds;

    @Schema(description = "提及的用户ID列表", example = "[\"user-uuid-1\", \"user-uuid-2\"]")
    private List<String> mentionedUserIds;
}
