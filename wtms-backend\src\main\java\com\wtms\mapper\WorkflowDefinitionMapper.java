package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.entity.WorkflowDefinition;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 工作流定义Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface WorkflowDefinitionMapper extends BaseMapper<WorkflowDefinition> {

    /**
     * 根据编码查询工作流定义
     */
    @Select("SELECT * FROM workflow_definitions WHERE code = #{code} AND deleted_at IS NULL ORDER BY version DESC")
    List<WorkflowDefinition> selectByCode(@Param("code") String code);

    /**
     * 根据编码和版本查询工作流定义
     */
    @Select("SELECT * FROM workflow_definitions WHERE code = #{code} AND version = #{version} AND deleted_at IS NULL")
    WorkflowDefinition selectByCodeAndVersion(@Param("code") String code, @Param("version") String version);

    /**
     * 根据编码查询最新版本的工作流定义
     */
    @Select("SELECT * FROM workflow_definitions WHERE code = #{code} AND deleted_at IS NULL ORDER BY version DESC LIMIT 1")
    WorkflowDefinition selectLatestByCode(@Param("code") String code);

    /**
     * 根据分类查询工作流定义
     */
    @Select("SELECT * FROM workflow_definitions WHERE category = #{category} AND deleted_at IS NULL ORDER BY sort_order ASC, created_at DESC")
    List<WorkflowDefinition> selectByCategory(@Param("category") String category);

    /**
     * 根据状态查询工作流定义
     */
    @Select("SELECT * FROM workflow_definitions WHERE status = #{status} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<WorkflowDefinition> selectByStatus(@Param("status") String status);

    /**
     * 查询已发布的工作流定义
     */
    @Select("SELECT * FROM workflow_definitions WHERE status = 'published' AND is_enabled = TRUE AND deleted_at IS NULL ORDER BY sort_order ASC, created_at DESC")
    List<WorkflowDefinition> selectPublished();

    /**
     * 查询启用的工作流定义
     */
    @Select("SELECT * FROM workflow_definitions WHERE is_enabled = TRUE AND deleted_at IS NULL ORDER BY sort_order ASC, created_at DESC")
    List<WorkflowDefinition> selectEnabled();

    /**
     * 查询默认工作流定义
     */
    @Select("SELECT * FROM workflow_definitions WHERE is_default = TRUE AND deleted_at IS NULL ORDER BY sort_order ASC")
    List<WorkflowDefinition> selectDefault();

    /**
     * 根据创建者查询工作流定义
     */
    IPage<WorkflowDefinition> selectByCreatorId(Page<WorkflowDefinition> page, @Param("creatorId") String creatorId, 
                                               @Param("status") String status);

    /**
     * 搜索工作流定义
     */
    IPage<WorkflowDefinition> searchWorkflowDefinitions(Page<WorkflowDefinition> page, @Param("keyword") String keyword,
                                                       @Param("category") String category, @Param("status") String status,
                                                       @Param("creatorId") String creatorId);

    /**
     * 统计工作流定义数量
     */
    @Select("SELECT COUNT(*) FROM workflow_definitions WHERE deleted_at IS NULL")
    int countAll();

    /**
     * 根据状态统计工作流定义数量
     */
    @Select("SELECT COUNT(*) FROM workflow_definitions WHERE status = #{status} AND deleted_at IS NULL")
    int countByStatus(@Param("status") String status);

    /**
     * 根据分类统计工作流定义数量
     */
    @Select("SELECT COUNT(*) FROM workflow_definitions WHERE category = #{category} AND deleted_at IS NULL")
    int countByCategory(@Param("category") String category);

    /**
     * 根据创建者统计工作流定义数量
     */
    @Select("SELECT COUNT(*) FROM workflow_definitions WHERE creator_id = #{creatorId} AND deleted_at IS NULL")
    int countByCreatorId(@Param("creatorId") String creatorId);

    /**
     * 发布工作流定义
     */
    @Update("UPDATE workflow_definitions SET status = 'published', publisher_id = #{publisherId}, published_at = NOW(), updated_at = NOW() WHERE id = #{definitionId}")
    int publishDefinition(@Param("definitionId") String definitionId, @Param("publisherId") String publisherId);

    /**
     * 废弃工作流定义
     */
    @Update("UPDATE workflow_definitions SET status = 'deprecated', updated_at = NOW() WHERE id = #{definitionId}")
    int deprecateDefinition(@Param("definitionId") String definitionId);

    /**
     * 归档工作流定义
     */
    @Update("UPDATE workflow_definitions SET status = 'archived', updated_at = NOW() WHERE id = #{definitionId}")
    int archiveDefinition(@Param("definitionId") String definitionId);

    /**
     * 启用工作流定义
     */
    @Update("UPDATE workflow_definitions SET is_enabled = TRUE, updated_at = NOW() WHERE id = #{definitionId}")
    int enableDefinition(@Param("definitionId") String definitionId);

    /**
     * 禁用工作流定义
     */
    @Update("UPDATE workflow_definitions SET is_enabled = FALSE, updated_at = NOW() WHERE id = #{definitionId}")
    int disableDefinition(@Param("definitionId") String definitionId);

    /**
     * 设置为默认工作流定义
     */
    @Update("UPDATE workflow_definitions SET is_default = TRUE, updated_at = NOW() WHERE id = #{definitionId}")
    int setAsDefault(@Param("definitionId") String definitionId);

    /**
     * 取消默认工作流定义
     */
    @Update("UPDATE workflow_definitions SET is_default = FALSE, updated_at = NOW() WHERE id = #{definitionId}")
    int unsetDefault(@Param("definitionId") String definitionId);

    /**
     * 批量删除工作流定义
     */
    int batchDeleteDefinitions(@Param("definitionIds") List<String> definitionIds);

    /**
     * 批量更新工作流定义状态
     */
    int batchUpdateStatus(@Param("definitionIds") List<String> definitionIds, @Param("status") String status);

    /**
     * 查询工作流定义统计信息
     */
    Object selectDefinitionStatistics();

    /**
     * 查询分类统计
     */
    List<Object> selectCategoryStatistics();

    /**
     * 查询创建者统计
     */
    List<Object> selectCreatorStatistics();

    /**
     * 查询版本统计
     */
    List<Object> selectVersionStatistics(@Param("code") String code);

    /**
     * 检查编码是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM workflow_definitions WHERE code = #{code} AND deleted_at IS NULL")
    boolean existsByCode(@Param("code") String code);

    /**
     * 检查编码和版本是否存在
     */
    @Select("SELECT COUNT(*) > 0 FROM workflow_definitions WHERE code = #{code} AND version = #{version} AND deleted_at IS NULL")
    boolean existsByCodeAndVersion(@Param("code") String code, @Param("version") String version);

    /**
     * 获取下一个版本号
     */
    @Select("SELECT COALESCE(MAX(CAST(version AS UNSIGNED)), 0) + 1 FROM workflow_definitions WHERE code = #{code} AND deleted_at IS NULL")
    Integer getNextVersion(@Param("code") String code);

    /**
     * 查询热门工作流定义
     */
    List<WorkflowDefinition> selectPopularDefinitions(@Param("limit") Integer limit);

    /**
     * 查询最近创建的工作流定义
     */
    @Select("SELECT * FROM workflow_definitions WHERE deleted_at IS NULL ORDER BY created_at DESC LIMIT #{limit}")
    List<WorkflowDefinition> selectRecentDefinitions(@Param("limit") Integer limit);

    /**
     * 查询最近更新的工作流定义
     */
    @Select("SELECT * FROM workflow_definitions WHERE deleted_at IS NULL ORDER BY updated_at DESC LIMIT #{limit}")
    List<WorkflowDefinition> selectRecentlyUpdatedDefinitions(@Param("limit") Integer limit);
}
