package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.wtms.entity.TaskCategory;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 任务分类Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface TaskCategoryMapper extends BaseMapper<TaskCategory> {

    /**
     * 查询所有启用的分类
     */
    @Select("SELECT * FROM task_categories WHERE is_enabled = TRUE ORDER BY sort_order ASC, created_at ASC")
    List<TaskCategory> selectEnabledCategories();

    /**
     * 根据编码查询分类
     */
    @Select("SELECT * FROM task_categories WHERE code = #{code}")
    TaskCategory selectByCode(@Param("code") String code);

    /**
     * 检查分类编码是否存在
     */
    @Select("SELECT COUNT(*) FROM task_categories WHERE code = #{code}")
    int countByCode(@Param("code") String code);

    /**
     * 检查分类名称是否存在
     */
    @Select("SELECT COUNT(*) FROM task_categories WHERE name = #{name}")
    int countByName(@Param("name") String name);

    /**
     * 获取下一个排序号
     */
    @Select("SELECT COALESCE(MAX(sort_order), 0) + 1 FROM task_categories")
    Integer getNextSortOrder();
}
