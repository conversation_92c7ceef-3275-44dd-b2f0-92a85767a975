<template>
  <div class="my-tasks-container">
    <!-- 统计卡片 -->
    <el-row :gutter="20" class="stats-row">
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon pending">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.pending }}</div>
              <div class="stats-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon in-progress">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.inProgress }}</div>
              <div class="stats-label">进行中</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon completed">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.completed }}</div>
              <div class="stats-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="stats-card">
          <div class="stats-content">
            <div class="stats-icon overdue">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="stats-info">
              <div class="stats-number">{{ stats.overdue }}</div>
              <div class="stats-label">已逾期</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 任务筛选 -->
    <el-card class="filter-card" shadow="never">
      <el-tabs v-model="activeTab" @tab-change="handleTabChange">
        <el-tab-pane label="我负责的" name="assigned" />
        <el-tab-pane label="我创建的" name="created" />
        <el-tab-pane label="我参与的" name="participated" />
      </el-tabs>
      
      <div class="filter-controls">
        <el-select
          v-model="statusFilter"
          placeholder="筛选状态"
          clearable
          style="width: 120px; margin-right: 12px"
          @change="handleFilterChange"
        >
          <el-option
            v-for="(label, value) in taskStatuses"
            :key="value"
            :label="label"
            :value="value"
          />
        </el-select>
        
        <el-select
          v-model="priorityFilter"
          placeholder="筛选优先级"
          clearable
          style="width: 120px"
          @change="handleFilterChange"
        >
          <el-option label="最低" :value="1" />
          <el-option label="低" :value="2" />
          <el-option label="中" :value="3" />
          <el-option label="高" :value="4" />
          <el-option label="最高" :value="5" />
        </el-select>
      </div>
    </el-card>

    <!-- 任务列表 -->
    <el-card class="task-list-card" shadow="never">
      <div v-loading="loading" class="task-list">
        <div
          v-for="task in taskList"
          :key="task.id"
          class="task-item"
          @click="handleTaskClick(task)"
        >
          <div class="task-header">
            <div class="task-title">
              <el-tag :type="getStatusType(task.status)" size="small">
                {{ task.statusText }}
              </el-tag>
              <span class="title-text">{{ task.title }}</span>
              <el-tag
                v-if="task.priority >= 4"
                type="danger"
                size="small"
                class="priority-tag"
              >
                {{ task.priorityText }}
              </el-tag>
            </div>
            
            <div class="task-actions">
              <el-button-group size="small">
                <el-button @click.stop="handleQuickAction(task, 'start')" v-if="canStart(task)">
                  开始
                </el-button>
                <el-button @click.stop="handleQuickAction(task, 'complete')" v-if="canComplete(task)">
                  完成
                </el-button>
                <el-button @click.stop="handleTaskClick(task)">
                  查看
                </el-button>
              </el-button-group>
            </div>
          </div>
          
          <div class="task-content">
            <div class="task-info">
              <div class="info-item">
                <el-icon><Calendar /></el-icon>
                <span>{{ formatDate(task.plannedEndDate) || '未设置截止时间' }}</span>
              </div>
              
              <div class="info-item" v-if="task.category">
                <el-tag :color="task.category.color" size="small">
                  {{ task.category.name }}
                </el-tag>
              </div>
              
              <div class="info-item" v-if="activeTab === 'created' && task.assignee">
                <el-avatar :size="20" :src="task.assignee.avatar">
                  {{ task.assignee.fullName?.charAt(0) }}
                </el-avatar>
                <span>{{ task.assignee.fullName }}</span>
              </div>
            </div>
            
            <div class="task-progress">
              <el-progress
                :percentage="task.progress || 0"
                :stroke-width="6"
                :show-text="false"
              />
              <span class="progress-text">{{ task.progress || 0 }}%</span>
            </div>
          </div>
          
          <div class="task-tags" v-if="task.tags && task.tags.length > 0">
            <el-tag
              v-for="tag in task.tags.slice(0, 3)"
              :key="tag"
              size="small"
              class="tag-item"
            >
              {{ tag }}
            </el-tag>
            <span v-if="task.tags.length > 3" class="more-tags">
              +{{ task.tags.length - 3 }}
            </span>
          </div>
        </div>
        
        <!-- 空状态 -->
        <el-empty
          v-if="!loading && taskList.length === 0"
          description="暂无任务"
          :image-size="120"
        />
      </div>

      <!-- 分页 -->
      <div class="pagination-container" v-if="taskList.length > 0">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 任务详情抽屉 -->
    <TaskDetailDrawer
      v-model:visible="detailVisible"
      :task-id="currentTaskId"
      @refresh="loadTaskList"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Clock, Loading, Check, Warning, Calendar } from '@element-plus/icons-vue'
import { getMyTasks, getMyCreatedTasks, startTask, completeTask, type TaskListItem } from '@/api/task'
import { getAllTaskStatuses } from '@/api/taskStatus'
import TaskDetailDrawer from './components/TaskDetailDrawer.vue'

// 响应式数据
const loading = ref(false)
const taskList = ref<TaskListItem[]>([])
const taskStatuses = ref<Record<string, string>>({})
const activeTab = ref('assigned')
const statusFilter = ref('')
const priorityFilter = ref<number>()

// 统计数据
const stats = reactive({
  pending: 0,
  inProgress: 0,
  completed: 0,
  overdue: 0
})

// 分页
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
})

// 对话框状态
const detailVisible = ref(false)
const currentTaskId = ref('')

// 计算属性
const searchParams = computed(() => ({
  status: statusFilter.value,
  priority: priorityFilter.value,
  page: pagination.current,
  size: pagination.size
}))

// 方法
const loadTaskList = async () => {
  try {
    loading.value = true
    
    let apiCall
    if (activeTab.value === 'assigned') {
      apiCall = getMyTasks(searchParams.value)
    } else if (activeTab.value === 'created') {
      apiCall = getMyCreatedTasks(searchParams.value)
    } else {
      // TODO: 实现我参与的任务接口
      apiCall = getMyTasks(searchParams.value)
    }
    
    const { data } = await apiCall
    taskList.value = data.records
    pagination.total = data.total
    
    // 更新统计数据
    updateStats(data.records)
  } catch (error) {
    ElMessage.error('加载任务列表失败')
  } finally {
    loading.value = false
  }
}

const loadTaskStatuses = async () => {
  try {
    const { data } = await getAllTaskStatuses()
    taskStatuses.value = data
  } catch (error) {
    console.error('加载任务状态失败:', error)
  }
}

const updateStats = (tasks: TaskListItem[]) => {
  stats.pending = tasks.filter(t => ['draft', 'pending'].includes(t.status)).length
  stats.inProgress = tasks.filter(t => ['in_progress', 'review', 'testing'].includes(t.status)).length
  stats.completed = tasks.filter(t => t.status === 'completed').length
  
  // 计算逾期任务
  const now = new Date()
  stats.overdue = tasks.filter(t => {
    if (!t.plannedEndDate || t.status === 'completed') return false
    return new Date(t.plannedEndDate) < now
  }).length
}

const handleTabChange = (tab: string) => {
  activeTab.value = tab
  pagination.current = 1
  loadTaskList()
}

const handleFilterChange = () => {
  pagination.current = 1
  loadTaskList()
}

const handleTaskClick = (task: TaskListItem) => {
  currentTaskId.value = task.id
  detailVisible.value = true
}

const handleQuickAction = async (task: TaskListItem, action: string) => {
  try {
    switch (action) {
      case 'start':
        await startTask(task.id)
        ElMessage.success('任务已开始')
        break
      case 'complete':
        await completeTask(task.id)
        ElMessage.success('任务已完成')
        break
    }
    
    loadTaskList()
  } catch (error) {
    ElMessage.error('操作失败')
  }
}

const handleSizeChange = (size: number) => {
  pagination.size = size
  loadTaskList()
}

const handleCurrentChange = (current: number) => {
  pagination.current = current
  loadTaskList()
}

// 状态相关方法
const getStatusType = (status: string) => {
  const statusTypes: Record<string, string> = {
    draft: 'info',
    pending: 'warning',
    in_progress: 'primary',
    review: 'warning',
    testing: 'warning',
    completed: 'success',
    paused: 'info',
    cancelled: 'danger',
    archived: 'info'
  }
  return statusTypes[status] || 'info'
}

const canStart = (task: TaskListItem) => {
  return ['draft', 'pending', 'paused'].includes(task.status)
}

const canComplete = (task: TaskListItem) => {
  return ['in_progress', 'review', 'testing'].includes(task.status)
}

// 工具方法
const formatDate = (dateStr?: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleDateString()
}

// 生命周期
onMounted(() => {
  loadTaskList()
  loadTaskStatuses()
})
</script>

<style scoped>
.my-tasks-container {
  padding: 20px;
}

.stats-row {
  margin-bottom: 20px;
}

.stats-card {
  cursor: pointer;
  transition: all 0.3s;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.stats-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stats-icon {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.stats-icon.pending {
  background: linear-gradient(135deg, #f39c12, #e67e22);
}

.stats-icon.in-progress {
  background: linear-gradient(135deg, #3498db, #2980b9);
}

.stats-icon.completed {
  background: linear-gradient(135deg, #27ae60, #229954);
}

.stats-icon.overdue {
  background: linear-gradient(135deg, #e74c3c, #c0392b);
}

.stats-info {
  flex: 1;
}

.stats-number {
  font-size: 24px;
  font-weight: bold;
  color: #333;
  line-height: 1;
}

.stats-label {
  font-size: 14px;
  color: #666;
  margin-top: 4px;
}

.filter-card,
.task-list-card {
  margin-bottom: 20px;
}

.filter-controls {
  margin-top: 16px;
  display: flex;
  align-items: center;
}

.task-list {
  min-height: 400px;
}

.task-item {
  padding: 16px;
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  margin-bottom: 12px;
  cursor: pointer;
  transition: all 0.3s;
}

.task-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.task-title {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.title-text {
  font-weight: 500;
  color: #333;
}

.priority-tag {
  margin-left: auto;
}

.task-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.task-info {
  display: flex;
  align-items: center;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  color: #666;
}

.task-progress {
  display: flex;
  align-items: center;
  gap: 8px;
  min-width: 120px;
}

.progress-text {
  font-size: 12px;
  color: #666;
  min-width: 30px;
}

.task-tags {
  display: flex;
  align-items: center;
  gap: 6px;
}

.tag-item {
  font-size: 11px;
}

.more-tags {
  font-size: 11px;
  color: #999;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
