package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 权限实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("permissions")
@Schema(description = "权限实体")
public class Permission implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "权限ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "权限编码")
    @TableField("code")
    private String code;

    @Schema(description = "权限名称")
    @TableField("name")
    private String name;

    @Schema(description = "权限描述")
    @TableField("description")
    private String description;

    @Schema(description = "权限类型")
    @TableField("type")
    private String type;

    @Schema(description = "权限分组")
    @TableField("group_name")
    private String groupName;

    @Schema(description = "父权限ID")
    @TableField("parent_id")
    private String parentId;

    @Schema(description = "权限路径")
    @TableField("path")
    private String path;

    @Schema(description = "权限层级")
    @TableField("level")
    private Integer level;

    @Schema(description = "排序")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "资源标识")
    @TableField("resource")
    private String resource;

    @Schema(description = "操作标识")
    @TableField("action")
    private String action;

    @Schema(description = "权限表达式")
    @TableField("expression")
    private String expression;

    @Schema(description = "是否启用")
    @TableField("is_enabled")
    private Boolean isEnabled;

    @Schema(description = "是否系统权限")
    @TableField("is_system")
    private Boolean isSystem;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "父权限")
    @TableField(exist = false)
    private Permission parent;

    @Schema(description = "子权限列表")
    @TableField(exist = false)
    private List<Permission> children;

    /**
     * 权限类型枚举
     */
    public enum Type {
        MENU("menu", "菜单权限"),
        BUTTON("button", "按钮权限"),
        API("api", "接口权限"),
        DATA("data", "数据权限");

        private final String code;
        private final String description;

        Type(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Type fromCode(String code) {
            for (Type type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return MENU;
        }
    }

    /**
     * 权限分组枚举
     */
    public enum Group {
        SYSTEM("system", "系统管理"),
        USER("user", "用户管理"),
        TASK("task", "任务管理"),
        PROJECT("project", "项目管理"),
        REPORT("report", "报表管理"),
        SETTING("setting", "系统设置");

        private final String code;
        private final String description;

        Group(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查权限是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.isEnabled);
    }

    /**
     * 检查是否为系统权限
     */
    public boolean isSystem() {
        return Boolean.TRUE.equals(this.isSystem);
    }

    /**
     * 获取完整权限表达式
     */
    public String getFullExpression() {
        if (expression != null) {
            return expression;
        }
        if (resource != null && action != null) {
            return resource + ":" + action;
        }
        return code;
    }

    /**
     * 检查是否为叶子节点权限
     */
    public boolean isLeaf() {
        return children == null || children.isEmpty();
    }

    /**
     * 检查是否为根权限
     */
    public boolean isRoot() {
        return parentId == null || parentId.isEmpty();
    }
}
