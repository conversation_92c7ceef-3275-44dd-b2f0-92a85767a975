#!/bin/bash

# WTMS 系统启动验证脚本
# 用于验证系统各个组件是否正常启动和运行

echo "🚀 WTMS 系统启动验证开始..."
echo "=================================="

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 配置信息
FRONTEND_URL="http://localhost:33335"
BACKEND_URL="http://localhost:55557/api/v1"
DB_HOST="localhost"
DB_PORT="3308"
DB_USER="root"
DB_PASS="ankaixin.docker.mysql"
DB_NAME="wtms"
REDIS_HOST="localhost"
REDIS_PORT="6379"

# 检查函数
check_service() {
    local service_name=$1
    local url=$2
    local timeout=${3:-10}
    
    echo -n "检查 $service_name... "
    
    if curl -s --max-time $timeout "$url" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 正常${NC}"
        return 0
    else
        echo -e "${RED}✗ 异常${NC}"
        return 1
    fi
}

check_port() {
    local service_name=$1
    local host=$2
    local port=$3
    
    echo -n "检查 $service_name 端口 $port... "
    
    if nc -z "$host" "$port" 2>/dev/null; then
        echo -e "${GREEN}✓ 开放${NC}"
        return 0
    else
        echo -e "${RED}✗ 关闭${NC}"
        return 1
    fi
}

check_database() {
    echo -n "检查数据库连接... "
    
    if mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -e "USE $DB_NAME; SELECT 1;" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 连接成功${NC}"
        
        # 检查表数量
        table_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -e "SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA='$DB_NAME';" -s -N 2>/dev/null)
        echo "  数据库表数量: $table_count"
        
        # 检查测试数据
        user_count=$(mysql -h"$DB_HOST" -P"$DB_PORT" -u"$DB_USER" -p"$DB_PASS" -D"$DB_NAME" -e "SELECT COUNT(*) FROM users;" -s -N 2>/dev/null)
        echo "  用户数据量: $user_count"
        
        return 0
    else
        echo -e "${RED}✗ 连接失败${NC}"
        return 1
    fi
}

check_redis() {
    echo -n "检查 Redis 连接... "
    
    if redis-cli -h "$REDIS_HOST" -p "$REDIS_PORT" ping > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 连接成功${NC}"
        return 0
    else
        echo -e "${RED}✗ 连接失败${NC}"
        return 1
    fi
}

check_api_endpoints() {
    echo -e "\n${BLUE}检查 API 接口...${NC}"
    
    # 检查健康检查接口
    check_service "健康检查" "$BACKEND_URL/health" 5
    
    # 检查登录接口
    echo -n "检查登录接口... "
    login_response=$(curl -s -X POST "$BACKEND_URL/auth/login" \
        -H "Content-Type: application/json" \
        -d '{"username":"admin","password":"admin123456"}' \
        --max-time 10 2>/dev/null)
    
    if echo "$login_response" | grep -q "token\|success" 2>/dev/null; then
        echo -e "${GREEN}✓ 正常${NC}"
    else
        echo -e "${RED}✗ 异常${NC}"
        echo "  响应: $login_response"
    fi
    
    # 检查任务列表接口
    check_service "任务列表接口" "$BACKEND_URL/tasks?page=1&size=10" 10
}

check_frontend() {
    echo -e "\n${BLUE}检查前端应用...${NC}"
    
    check_service "前端首页" "$FRONTEND_URL" 10
    
    # 检查前端资源
    echo -n "检查前端资源... "
    if curl -s --max-time 10 "$FRONTEND_URL/assets" > /dev/null 2>&1 || 
       curl -s --max-time 10 "$FRONTEND_URL/static" > /dev/null 2>&1; then
        echo -e "${GREEN}✓ 正常${NC}"
    else
        echo -e "${YELLOW}⚠ 部分资源可能未加载${NC}"
    fi
}

check_docker_containers() {
    echo -e "\n${BLUE}检查 Docker 容器状态...${NC}"
    
    if command -v docker > /dev/null 2>&1; then
        containers=("wtms-mysql" "wtms-redis" "wtms-backend" "wtms-frontend")
        
        for container in "${containers[@]}"; do
            echo -n "检查容器 $container... "
            if docker ps --format "table {{.Names}}" | grep -q "^$container$" 2>/dev/null; then
                status=$(docker inspect --format='{{.State.Status}}' "$container" 2>/dev/null)
                if [ "$status" = "running" ]; then
                    echo -e "${GREEN}✓ 运行中${NC}"
                else
                    echo -e "${RED}✗ 状态: $status${NC}"
                fi
            else
                echo -e "${RED}✗ 未找到${NC}"
            fi
        done
    else
        echo "Docker 未安装，跳过容器检查"
    fi
}

# 主检查流程
main() {
    echo -e "${BLUE}1. 检查基础服务端口...${NC}"
    check_port "MySQL" "$DB_HOST" "$DB_PORT"
    check_port "Redis" "$REDIS_HOST" "$REDIS_PORT"
    check_port "后端服务" "localhost" "55557"
    check_port "前端服务" "localhost" "33335"
    
    echo -e "\n${BLUE}2. 检查数据库...${NC}"
    check_database
    
    echo -e "\n${BLUE}3. 检查 Redis...${NC}"
    check_redis
    
    echo -e "\n${BLUE}4. 检查后端服务...${NC}"
    check_service "后端健康检查" "$BACKEND_URL/actuator/health" 10
    check_api_endpoints
    
    check_frontend
    
    check_docker_containers
    
    echo -e "\n${BLUE}5. 系统配置验证...${NC}"
    echo "前端访问地址: $FRONTEND_URL"
    echo "后端API地址: $BACKEND_URL"
    echo "数据库地址: $DB_HOST:$DB_PORT"
    echo "Redis地址: $REDIS_HOST:$REDIS_PORT"
    
    echo -e "\n${BLUE}6. 快速功能测试...${NC}"
    echo "请手动验证以下功能："
    echo "  1. 访问前端: $FRONTEND_URL"
    echo "  2. 使用账户登录: admin / admin123456"
    echo "  3. 查看任务列表"
    echo "  4. 创建新任务"
    echo "  5. 检查API文档: $BACKEND_URL/../swagger-ui.html"
    
    echo -e "\n=================================="
    echo -e "${GREEN}🎉 WTMS 系统验证完成！${NC}"
    echo -e "\n如果所有检查都通过，系统已准备就绪。"
    echo -e "如有异常，请检查相应服务的日志文件。"
}

# 检查必要的命令
check_dependencies() {
    local missing_deps=()
    
    if ! command -v curl > /dev/null 2>&1; then
        missing_deps+=("curl")
    fi
    
    if ! command -v nc > /dev/null 2>&1; then
        missing_deps+=("netcat")
    fi
    
    if ! command -v mysql > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠ MySQL客户端未安装，将跳过数据库连接测试${NC}"
    fi
    
    if ! command -v redis-cli > /dev/null 2>&1; then
        echo -e "${YELLOW}⚠ Redis客户端未安装，将跳过Redis连接测试${NC}"
    fi
    
    if [ ${#missing_deps[@]} -ne 0 ]; then
        echo -e "${RED}错误: 缺少必要的依赖: ${missing_deps[*]}${NC}"
        echo "请安装缺少的依赖后重新运行此脚本"
        exit 1
    fi
}

# 脚本入口
if [ "$1" = "--help" ] || [ "$1" = "-h" ]; then
    echo "WTMS 系统验证脚本"
    echo "用法: $0 [选项]"
    echo "选项:"
    echo "  -h, --help    显示此帮助信息"
    echo "  --quick       快速检查（跳过详细测试）"
    exit 0
fi

check_dependencies
main
