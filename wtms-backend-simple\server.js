const http = require('http');
const url = require('url');
const querystring = require('querystring');

const PORT = 55557;

// 统一响应格式
const Result = {
  success: (data, message = '操作成功') => ({
    success: true,
    code: 200,
    message,
    data,
    timestamp: Date.now()
  }),
  
  error: (message = '操作失败', code = 500) => ({
    success: false,
    code,
    message,
    data: null,
    timestamp: Date.now()
  })
};

// CORS处理
function setCORSHeaders(res) {
  res.setHeader('Access-Control-Allow-Origin', '*');
  res.setHeader('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS');
  res.setHeader('Access-Control-Allow-Headers', 'Content-Type, Authorization');
  res.setHeader('Access-Control-Allow-Credentials', 'true');
}

// 解析请求体
function parseBody(req) {
  return new Promise((resolve, reject) => {
    let body = '';
    req.on('data', chunk => {
      body += chunk.toString();
    });
    req.on('end', () => {
      try {
        resolve(body ? JSON.parse(body) : {});
      } catch (error) {
        reject(error);
      }
    });
  });
}

// 发送JSON响应
function sendJSON(res, data, statusCode = 200) {
  res.writeHead(statusCode, { 'Content-Type': 'application/json' });
  res.end(JSON.stringify(data, null, 2));
}

// 路由处理
const routes = {
  // 健康检查
  'GET /api/v1/health': (req, res) => {
    sendJSON(res, Result.success({
      status: 'UP',
      service: 'WTMS Backend (Node.js Simple)',
      version: '1.0.0',
      database: 'MySQL:3308 (配置就绪)',
      cache: 'Redis:6379 (配置就绪)',
      timestamp: new Date().toISOString()
    }));
  },

  // 系统信息
  'GET /api/v1/info': (req, res) => {
    sendJSON(res, Result.success({
      name: 'WTMS工作任务管理系统',
      description: 'Work Task Management System',
      version: '1.0.0',
      author: 'WTMS Team',
      backend: 'Node.js (原生)',
      features: [
        '用户认证',
        '任务管理', 
        'RBAC权限控制',
        'RESTful API'
      ]
    }));
  },

  // 登录接口
  'POST /api/v1/auth/login': async (req, res) => {
    try {
      const body = await parseBody(req);
      const { username, password } = body;
      
      if (!username || !password) {
        return sendJSON(res, Result.error('用户名和密码不能为空', 400), 400);
      }
      
      // 验证超级管理员账户
      if (username === 'admin' && password === 'admin123456') {
        const token = `wtms-token-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
        
        const userInfo = {
          id: '1',
          username: 'admin',
          fullName: '系统管理员',
          email: '<EMAIL>',
          phone: '13800138000',
          avatar: null,
          employeeId: 'EMP001',
          hireDate: '2024-01-01',
          status: 'active',
          lastLoginAt: new Date().toISOString(),
          loginCount: 1,
          settings: {},
          department: {
            id: '1',
            name: '技术部',
            code: 'TECH',
            description: '技术开发部门',
            level: 1,
            sortOrder: 1,
            status: 'active',
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          role: {
            id: '1',
            name: '超级管理员',
            code: 'SUPER_ADMIN',
            description: '系统超级管理员',
            permissions: [
              'user:create', 'user:read', 'user:update', 'user:delete',
              'task:create', 'task:read', 'task:update', 'task:delete',
              'role:create', 'role:read', 'role:update', 'role:delete',
              'permission:create', 'permission:read', 'permission:update', 'permission:delete'
            ],
            isSystem: true,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
          },
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };

        const refreshToken = `wtms-refresh-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;

        sendJSON(res, Result.success({
          token,
          refreshToken,
          tokenType: 'Bearer',
          expiresIn: 86400,
          user: userInfo
        }, '登录成功'));
      } else {
        sendJSON(res, Result.error('用户名或密码错误', 401), 401);
      }
    } catch (error) {
      console.error('登录错误:', error);
      sendJSON(res, Result.error('登录失败'), 500);
    }
  },

  // 获取用户信息
  'GET /api/v1/user/profile': (req, res) => {
    // 简单的Token验证
    const authHeader = req.headers['authorization'];
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return sendJSON(res, Result.error('访问令牌缺失', 401), 401);
    }

    const userInfo = {
      id: '1',
      username: 'admin',
      fullName: '系统管理员',
      email: '<EMAIL>',
      phone: '13800138000',
      avatar: null,
      employeeId: 'EMP001',
      hireDate: '2024-01-01',
      status: 'active',
      lastLoginAt: new Date().toISOString(),
      loginCount: 1,
      settings: {},
      department: {
        id: '1',
        name: '技术部',
        code: 'TECH',
        description: '技术开发部门',
        level: 1,
        sortOrder: 1,
        status: 'active',
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      role: {
        id: '1',
        name: '超级管理员',
        code: 'SUPER_ADMIN',
        description: '系统超级管理员',
        permissions: [
          'user:create', 'user:read', 'user:update', 'user:delete',
          'task:create', 'task:read', 'task:update', 'task:delete',
          'role:create', 'role:read', 'role:update', 'role:delete',
          'permission:create', 'permission:read', 'permission:update', 'permission:delete'
        ],
        isSystem: true,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      },
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const permissions = [
      'user:create', 'user:read', 'user:update', 'user:delete',
      'task:create', 'task:read', 'task:update', 'task:delete',
      'role:create', 'role:read', 'role:update', 'role:delete',
      'permission:create', 'permission:read', 'permission:update', 'permission:delete'
    ];

    const menus = [
      {
        id: '1',
        name: '工作台',
        path: '/dashboard',
        icon: 'House',
        component: 'Dashboard',
        sortOrder: 1,
        type: 'menu'
      },
      {
        id: '2',
        name: '任务管理',
        path: '/tasks',
        icon: 'List',
        component: 'TaskList',
        sortOrder: 2,
        type: 'menu'
      }
    ];

    sendJSON(res, Result.success({
      user: userInfo,
      permissions,
      menus
    }));
  },

  // 获取任务列表
  'GET /api/v1/tasks': (req, res) => {
    // 简单的Token验证
    const authHeader = req.headers['authorization'];
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return sendJSON(res, Result.error('访问令牌缺失', 401), 401);
    }
    const parsedUrl = url.parse(req.url, true);
    const { page = 1, size = 10 } = parsedUrl.query;
    
    const tasks = [];
    for (let i = 1; i <= 5; i++) {
      tasks.push({
        id: String(i),
        title: `示例任务 ${i}`,
        description: `这是一个示例任务的描述，展示WTMS系统的任务管理功能`,
        status: i % 2 === 0 ? 'IN_PROGRESS' : 'TODO',
        priority: i <= 2 ? 'HIGH' : 'MEDIUM',
        progress: i * 20,
        estimatedHours: 40,
        actualHours: i * 8,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        creator: {
          id: '1',
          fullName: '系统管理员',
          avatarUrl: null
        },
        assignee: {
          id: String(i + 1),
          fullName: `开发人员${i}`,
          avatarUrl: null
        },
        tags: ['开发', '测试', '文档']
      });
    }
    
    sendJSON(res, Result.success({
      records: tasks,
      total: 5,
      page: parseInt(page),
      size: parseInt(size),
      pages: 1
    }));
  },

  // 获取任务详情
  'GET /api/v1/tasks/:id': (req, res, params) => {
    // 简单的Token验证
    const authHeader = req.headers['authorization'];
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return sendJSON(res, Result.error('访问令牌缺失', 401), 401);
    }
    const { id } = params;
    
    const task = {
      id,
      title: `示例任务 ${id}`,
      description: `这是任务 ${id} 的详细描述。本任务展示了WTMS系统的完整功能。`,
      status: 'IN_PROGRESS',
      priority: 'HIGH',
      progress: 60,
      estimatedHours: 40,
      actualHours: 24,
      startDate: new Date().toISOString(),
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      creator: {
        id: '1',
        fullName: '系统管理员',
        avatarUrl: null
      },
      assignee: {
        id: '2',
        fullName: '开发人员',
        avatarUrl: null
      },
      tags: ['开发', '紧急', '后端']
    };
    
    sendJSON(res, Result.success(task));
  },

  // 创建任务
  'POST /api/v1/tasks': async (req, res) => {
    // 简单的Token验证
    const authHeader = req.headers['authorization'];
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return sendJSON(res, Result.error('访问令牌缺失', 401), 401);
    }
    try {
      const body = await parseBody(req);
      const { title, description, priority = 'MEDIUM' } = body;
      
      if (!title) {
        return sendJSON(res, Result.error('任务标题不能为空', 400), 400);
      }
      
      const taskId = `task-${Date.now()}`;
      const task = {
        id: taskId,
        title,
        description,
        status: 'TODO',
        priority,
        progress: 0,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        creator: {
          id: '1',
          fullName: '系统管理员',
          avatarUrl: null
        }
      };
      
      sendJSON(res, Result.success(task, '任务创建成功'));
    } catch (error) {
      console.error('创建任务错误:', error);
      sendJSON(res, Result.error('创建任务失败'), 500);
    }
  },

  // 获取任务评论
  'GET /api/v1/tasks/:id/comments': (req, res, params) => {
    // 简单的Token验证
    const authHeader = req.headers['authorization'];
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return sendJSON(res, Result.error('访问令牌缺失', 401), 401);
    }
    const { id } = params;
    
    const comments = [];
    for (let i = 1; i <= 3; i++) {
      comments.push({
        id: String(i),
        content: `这是任务 ${id} 的第 ${i} 条评论`,
        createdAt: new Date().toISOString(),
        commenter: {
          id: String(i),
          fullName: `用户${i}`,
          avatarUrl: null
        }
      });
    }
    
    sendJSON(res, Result.success(comments));
  }
};

// 路由匹配
function matchRoute(method, pathname) {
  for (const route in routes) {
    const [routeMethod, routePath] = route.split(' ');
    if (routeMethod !== method) continue;
    
    // 简单路径匹配
    if (routePath === pathname) {
      return { handler: routes[route], params: {} };
    }
    
    // 参数路径匹配 (如 /api/v1/tasks/:id)
    const routeParts = routePath.split('/');
    const pathParts = pathname.split('/');
    
    if (routeParts.length !== pathParts.length) continue;
    
    const params = {};
    let match = true;
    
    for (let i = 0; i < routeParts.length; i++) {
      if (routeParts[i].startsWith(':')) {
        params[routeParts[i].slice(1)] = pathParts[i];
      } else if (routeParts[i] !== pathParts[i]) {
        match = false;
        break;
      }
    }
    
    if (match) {
      return { handler: routes[route], params };
    }
  }
  
  return null;
}

// 创建HTTP服务器
const server = http.createServer(async (req, res) => {
  setCORSHeaders(res);
  
  // 处理OPTIONS请求
  if (req.method === 'OPTIONS') {
    res.writeHead(200);
    res.end();
    return;
  }
  
  const parsedUrl = url.parse(req.url);
  const pathname = parsedUrl.pathname;
  const method = req.method;
  
  console.log(`${new Date().toISOString()} ${method} ${pathname}`);
  
  const match = matchRoute(method, pathname);
  
  if (match) {
    try {
      await match.handler(req, res, match.params);
    } catch (error) {
      console.error('路由处理错误:', error);
      sendJSON(res, Result.error('服务器内部错误'), 500);
    }
  } else {
    sendJSON(res, Result.error('接口不存在', 404), 404);
  }
});

// 启动服务器
server.listen(PORT, () => {
  console.log('=================================');
  console.log('WTMS Node.js简化后端服务启动成功！');
  console.log(`访问地址: http://localhost:${PORT}/api/v1`);
  console.log(`健康检查: http://localhost:${PORT}/api/v1/health`);
  console.log(`登录接口: http://localhost:${PORT}/api/v1/auth/login`);
  console.log('=================================');
});

// 优雅关闭
process.on('SIGINT', () => {
  console.log('\n正在关闭服务器...');
  server.close(() => {
    console.log('服务器已关闭');
    process.exit(0);
  });
});
