import request from '@/utils/request'

/**
 * 工作流相关接口
 */

// 工作流定义类型定义
export interface WorkflowDefinition {
  id: string
  name: string
  code: string
  description?: string
  category: 'task' | 'approval' | 'notification' | 'automation' | 'custom'
  version: string
  status: 'draft' | 'published' | 'deprecated' | 'archived'
  definitionJson: string
  graphConfig?: string
  isEnabled: boolean
  isDefault: boolean
  sortOrder?: number
  creatorId: string
  publisherId?: string
  publishedAt?: string
  createdAt: string
  updatedAt: string
  creator?: {
    id: string
    username: string
    fullName: string
  }
  publisher?: {
    id: string
    username: string
    fullName: string
  }
  instanceCount?: number
}

// 工作流节点类型定义
export interface WorkflowNode {
  id: string
  workflowDefinitionId: string
  nodeCode: string
  nodeName: string
  nodeType: string
  description?: string
  configJson?: string
  positionX?: number
  positionY?: number
  width?: number
  height?: number
  isStart: boolean
  isEnd: boolean
  isEnabled: boolean
  sortOrder?: number
  createdAt: string
  updatedAt: string
}

// 工作流连接线类型定义
export interface WorkflowEdge {
  id: string
  workflowDefinitionId: string
  edgeCode: string
  edgeName: string
  sourceNodeId: string
  targetNodeId: string
  edgeType: 'sequence_flow' | 'conditional_flow' | 'default_flow' | 'message_flow'
  conditionExpression?: string
  configJson?: string
  pathPoints?: string
  isDefault: boolean
  isEnabled: boolean
  sortOrder?: number
  createdAt: string
  updatedAt: string
}

// 工作流实例类型定义
export interface WorkflowInstance {
  id: string
  workflowDefinitionId: string
  businessId: string
  businessType: string
  instanceName: string
  status: 'running' | 'completed' | 'suspended' | 'terminated' | 'cancelled' | 'error' | 'pending'
  currentNodeId?: string
  starterId: string
  startedAt?: string
  endedAt?: string
  suspendedAt?: string
  variablesJson?: string
  priority?: number
  dueDate?: string
  parentInstanceId?: string
  rootInstanceId?: string
  tenantId?: string
  createdAt: string
  updatedAt: string
  workflowDefinition?: WorkflowDefinition
  currentNode?: WorkflowNode
  starter?: {
    id: string
    username: string
    fullName: string
  }
}

// 工作流任务类型定义
export interface WorkflowTask {
  id: string
  workflowInstanceId: string
  workflowNodeId: string
  taskName: string
  taskType: string
  status: 'created' | 'ready' | 'reserved' | 'in_progress' | 'suspended' | 'completed' | 'failed' | 'error'
  assigneeId?: string
  candidateUsers?: string
  candidateGroups?: string
  delegatorId?: string
  ownerId?: string
  description?: string
  formKey?: string
  variablesJson?: string
  priority?: number
  dueDate?: string
  followUpDate?: string
  createdAt: string
  claimedAt?: string
  startedAt?: string
  completedAt?: string
  updatedAt: string
  workflowInstance?: WorkflowInstance
  workflowNode?: WorkflowNode
  assignee?: {
    id: string
    username: string
    fullName: string
  }
}

// 创建工作流定义请求
export interface CreateWorkflowDefinitionRequest {
  name: string
  code: string
  description?: string
  category: string
  definitionJson: string
  graphConfig?: string
  isEnabled?: boolean
  isDefault?: boolean
  sortOrder?: number
}

// 更新工作流定义请求
export interface UpdateWorkflowDefinitionRequest {
  name?: string
  description?: string
  category?: string
  definitionJson?: string
  graphConfig?: string
  isEnabled?: boolean
  isDefault?: boolean
  sortOrder?: number
}

// 启动工作流实例请求
export interface StartWorkflowInstanceRequest {
  definitionId: string
  businessId: string
  businessType: string
  instanceName?: string
  variables?: Record<string, any>
  priority?: number
  dueDate?: string
}

// API 接口函数

/**
 * 工作流定义管理
 */
export function createWorkflowDefinition(data: CreateWorkflowDefinitionRequest) {
  return request.post<WorkflowDefinition>('/api/v1/workflow-definitions', data)
}

export function updateWorkflowDefinition(definitionId: string, data: UpdateWorkflowDefinitionRequest) {
  return request.put<WorkflowDefinition>(`/api/v1/workflow-definitions/${definitionId}`, data)
}

export function deleteWorkflowDefinition(definitionId: string) {
  return request.delete(`/api/v1/workflow-definitions/${definitionId}`)
}

export function getWorkflowDefinition(definitionId: string) {
  return request.get<WorkflowDefinition>(`/api/v1/workflow-definitions/${definitionId}`)
}

export function getWorkflowDefinitionsByCode(code: string) {
  return request.get<WorkflowDefinition[]>(`/api/v1/workflow-definitions/code/${code}`)
}

export function getWorkflowDefinitionByCodeAndVersion(code: string, version: string) {
  return request.get<WorkflowDefinition>(`/api/v1/workflow-definitions/code/${code}/version/${version}`)
}

export function getLatestWorkflowDefinitionByCode(code: string) {
  return request.get<WorkflowDefinition>(`/api/v1/workflow-definitions/code/${code}/latest`)
}

export function getWorkflowDefinitionsByCategory(category: string) {
  return request.get<WorkflowDefinition[]>(`/api/v1/workflow-definitions/category/${category}`)
}

export function getWorkflowDefinitionsByStatus(status: string) {
  return request.get<WorkflowDefinition[]>(`/api/v1/workflow-definitions/status/${status}`)
}

export function getPublishedWorkflowDefinitions() {
  return request.get<WorkflowDefinition[]>('/api/v1/workflow-definitions/published')
}

export function getEnabledWorkflowDefinitions() {
  return request.get<WorkflowDefinition[]>('/api/v1/workflow-definitions/enabled')
}

export function getDefaultWorkflowDefinitions() {
  return request.get<WorkflowDefinition[]>('/api/v1/workflow-definitions/default')
}

export function getWorkflowDefinitionsByCreator(creatorId: string, params?: {
  status?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: WorkflowDefinition[]
    total: number
    size: number
    current: number
    pages: number
  }>(`/api/v1/workflow-definitions/creator/${creatorId}`, { params })
}

export function searchWorkflowDefinitions(params: {
  keyword?: string
  category?: string
  status?: string
  creatorId?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: WorkflowDefinition[]
    total: number
    size: number
    current: number
    pages: number
  }>('/api/v1/workflow-definitions/search', { params })
}

export function publishWorkflowDefinition(definitionId: string, publisherId: string) {
  return request.put(`/api/v1/workflow-definitions/${definitionId}/publish`, null, {
    params: { publisherId }
  })
}

export function deprecateWorkflowDefinition(definitionId: string) {
  return request.put(`/api/v1/workflow-definitions/${definitionId}/deprecate`)
}

export function enableWorkflowDefinition(definitionId: string) {
  return request.put(`/api/v1/workflow-definitions/${definitionId}/enable`)
}

export function disableWorkflowDefinition(definitionId: string) {
  return request.put(`/api/v1/workflow-definitions/${definitionId}/disable`)
}

export function copyWorkflowDefinition(definitionId: string, newCode: string, newName: string) {
  return request.post<WorkflowDefinition>(`/api/v1/workflow-definitions/${definitionId}/copy`, null, {
    params: { newCode, newName }
  })
}

export function createNewWorkflowVersion(definitionId: string) {
  return request.post<WorkflowDefinition>(`/api/v1/workflow-definitions/${definitionId}/new-version`)
}

export function validateWorkflowDefinition(definitionId: string) {
  return request.post(`/api/v1/workflow-definitions/${definitionId}/validate`)
}

export function importWorkflowDefinition(definitionJson: string, creatorId: string) {
  return request.post<WorkflowDefinition>('/api/v1/workflow-definitions/import', definitionJson, {
    params: { creatorId },
    headers: { 'Content-Type': 'text/plain' }
  })
}

export function exportWorkflowDefinition(definitionId: string) {
  return request.get<string>(`/api/v1/workflow-definitions/${definitionId}/export`)
}

export function batchDeleteWorkflowDefinitions(definitionIds: string[]) {
  return request.delete('/api/v1/workflow-definitions/batch', { data: definitionIds })
}

export function getWorkflowDefinitionStatistics() {
  return request.get('/api/v1/workflow-definitions/statistics')
}

export function getPopularWorkflowDefinitions(limit = 10) {
  return request.get<WorkflowDefinition[]>('/api/v1/workflow-definitions/popular', {
    params: { limit }
  })
}

export function getRecentWorkflowDefinitions(limit = 10) {
  return request.get<WorkflowDefinition[]>('/api/v1/workflow-definitions/recent', {
    params: { limit }
  })
}

/**
 * 工作流实例管理
 */
export function startWorkflowInstance(data: StartWorkflowInstanceRequest) {
  return request.post<WorkflowInstance>('/api/v1/workflow-instances/start', data)
}

export function getWorkflowInstance(instanceId: string) {
  return request.get<WorkflowInstance>(`/api/v1/workflow-instances/${instanceId}`)
}

export function getWorkflowInstancesByDefinition(definitionId: string) {
  return request.get<WorkflowInstance[]>(`/api/v1/workflow-instances/definition/${definitionId}`)
}

export function getWorkflowInstancesByBusiness(businessId: string, businessType?: string) {
  const params = businessType ? { businessType } : undefined
  return request.get<WorkflowInstance[]>(`/api/v1/workflow-instances/business/${businessId}`, { params })
}

export function getWorkflowInstancesByStatus(status: string) {
  return request.get<WorkflowInstance[]>(`/api/v1/workflow-instances/status/${status}`)
}

export function getWorkflowInstancesByStarter(starterId: string, params?: {
  status?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: WorkflowInstance[]
    total: number
    size: number
    current: number
    pages: number
  }>(`/api/v1/workflow-instances/starter/${starterId}`, { params })
}

export function getActiveWorkflowInstances() {
  return request.get<WorkflowInstance[]>('/api/v1/workflow-instances/active')
}

export function getCompletedWorkflowInstances() {
  return request.get<WorkflowInstance[]>('/api/v1/workflow-instances/completed')
}

export function getOverdueWorkflowInstances() {
  return request.get<WorkflowInstance[]>('/api/v1/workflow-instances/overdue')
}

export function searchWorkflowInstances(params: {
  keyword?: string
  definitionId?: string
  status?: string
  businessType?: string
  starterId?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: WorkflowInstance[]
    total: number
    size: number
    current: number
    pages: number
  }>('/api/v1/workflow-instances/search', { params })
}

export function suspendWorkflowInstance(instanceId: string, userId: string) {
  return request.put(`/api/v1/workflow-instances/${instanceId}/suspend`, null, {
    params: { userId }
  })
}

export function resumeWorkflowInstance(instanceId: string, userId: string) {
  return request.put(`/api/v1/workflow-instances/${instanceId}/resume`, null, {
    params: { userId }
  })
}

export function terminateWorkflowInstance(instanceId: string, reason: string, userId: string) {
  return request.put(`/api/v1/workflow-instances/${instanceId}/terminate`, null, {
    params: { reason, userId }
  })
}

export function cancelWorkflowInstance(instanceId: string, reason: string, userId: string) {
  return request.put(`/api/v1/workflow-instances/${instanceId}/cancel`, null, {
    params: { reason, userId }
  })
}

/**
 * 工作流任务管理
 */
export function getUserTodoTasks(userId: string) {
  return request.get<WorkflowTask[]>(`/api/v1/workflow-tasks/user/${userId}/todo`)
}

export function getUserDoneTasks(userId: string, params?: {
  page?: number
  size?: number
}) {
  return request.get<{
    records: WorkflowTask[]
    total: number
    size: number
    current: number
    pages: number
  }>(`/api/v1/workflow-tasks/user/${userId}/done`, { params })
}

export function getUserCandidateTasks(userId: string) {
  return request.get<WorkflowTask[]>(`/api/v1/workflow-tasks/user/${userId}/candidate`)
}

export function getWorkflowTask(taskId: string) {
  return request.get<WorkflowTask>(`/api/v1/workflow-tasks/${taskId}`)
}

export function claimWorkflowTask(taskId: string, userId: string) {
  return request.post(`/api/v1/workflow-tasks/${taskId}/claim`, null, {
    params: { userId }
  })
}

export function unclaimWorkflowTask(taskId: string, userId: string) {
  return request.post(`/api/v1/workflow-tasks/${taskId}/unclaim`, null, {
    params: { userId }
  })
}

export function completeWorkflowTask(taskId: string, variables: Record<string, any>, userId: string) {
  return request.post(`/api/v1/workflow-tasks/${taskId}/complete`, { variables, userId })
}

export function delegateWorkflowTask(taskId: string, delegateId: string, userId: string) {
  return request.post(`/api/v1/workflow-tasks/${taskId}/delegate`, null, {
    params: { delegateId, userId }
  })
}

export function assignWorkflowTask(taskId: string, assigneeId: string, userId: string) {
  return request.post(`/api/v1/workflow-tasks/${taskId}/assign`, null, {
    params: { assigneeId, userId }
  })
}

export function searchWorkflowTasks(params: {
  keyword?: string
  instanceId?: string
  status?: string
  assigneeId?: string
  taskType?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: WorkflowTask[]
    total: number
    size: number
    current: number
    pages: number
  }>('/api/v1/workflow-tasks/search', { params })
}

export function getWorkflowTaskStatistics() {
  return request.get('/api/v1/workflow-tasks/statistics')
}

export function getWorkflowInstanceStatistics() {
  return request.get('/api/v1/workflow-instances/statistics')
}
