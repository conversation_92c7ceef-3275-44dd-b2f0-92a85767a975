package com.wtms.controller;

import com.wtms.common.result.Result;
import com.wtms.dto.request.AssignUserRoleRequest;
import com.wtms.entity.Role;
import com.wtms.entity.UserRole;
import com.wtms.service.UserRoleService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 用户角色管理控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/user-roles")
@Tag(name = "用户角色管理", description = "用户角色关联管理相关接口")
public class UserRoleController {

    @Autowired
    private UserRoleService userRoleService;

    @PostMapping("/assign")
    @Operation(summary = "分配用户角色", description = "为用户分配角色")
    @PreAuthorize("hasAuthority('user:assign_role') or hasRole('ADMIN')")
    public Result<List<UserRole>> assignRolesToUser(@Valid @RequestBody AssignUserRoleRequest request) {
        log.info("Assigning roles to user: {} -> {}", request.getUserId(), request.getRoleIds());
        
        List<UserRole> assignments = userRoleService.assignRolesToUser(request);
        return Result.success("用户角色分配成功", assignments);
    }

    @DeleteMapping("/remove")
    @Operation(summary = "移除用户角色", description = "移除用户的指定角色")
    @PreAuthorize("hasAuthority('user:remove_role') or hasRole('ADMIN')")
    public Result<String> removeRolesFromUser(
            @Parameter(description = "用户ID", required = true)
            @RequestParam @NotBlank(message = "用户ID不能为空") String userId,
            @Parameter(description = "角色ID列表", required = true)
            @RequestBody @NotEmpty(message = "角色ID列表不能为空") List<String> roleIds) {
        
        log.info("Removing roles from user: {} -> {}", userId, roleIds);
        
        userRoleService.removeRolesFromUser(userId, roleIds);
        return Result.success("用户角色移除成功");
    }

    @DeleteMapping("/users/{userId}/clear")
    @Operation(summary = "清空用户角色", description = "清空指定用户的所有角色")
    @PreAuthorize("hasAuthority('user:clear_role') or hasRole('ADMIN')")
    public Result<String> clearUserRoles(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId) {
        
        log.info("Clearing all roles for user: {}", userId);
        
        userRoleService.clearUserRoles(userId);
        return Result.success("用户角色清空成功");
    }

    @GetMapping("/users/{userId}/roles")
    @Operation(summary = "获取用户角色", description = "获取指定用户的角色列表")
    @PreAuthorize("hasAuthority('user:view_role') or hasRole('ADMIN') or #userId == authentication.principal.id")
    public Result<List<Role>> getUserRoles(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId) {
        
        List<Role> roles = userRoleService.getUserRoles(userId);
        return Result.success(roles);
    }

    @GetMapping("/users/{userId}/assignments")
    @Operation(summary = "获取用户角色关联", description = "获取指定用户的角色关联详情")
    @PreAuthorize("hasAuthority('user:view_role') or hasRole('ADMIN') or #userId == authentication.principal.id")
    public Result<List<UserRole>> getUserRoleAssignments(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId) {
        
        List<UserRole> assignments = userRoleService.getUserRoleAssignments(userId);
        return Result.success(assignments);
    }

    @GetMapping("/roles/{roleId}/assignments")
    @Operation(summary = "获取角色用户关联", description = "获取指定角色的用户关联详情")
    @PreAuthorize("hasAuthority('role:view_user') or hasRole('ADMIN')")
    public Result<List<UserRole>> getRoleUserAssignments(
            @Parameter(description = "角色ID", required = true)
            @PathVariable @NotBlank(message = "角色ID不能为空") String roleId) {
        
        List<UserRole> assignments = userRoleService.getRoleUserAssignments(roleId);
        return Result.success(assignments);
    }

    @GetMapping("/check")
    @Operation(summary = "检查用户角色", description = "检查用户是否拥有指定角色")
    @PreAuthorize("hasAuthority('user:check_role') or hasRole('ADMIN')")
    public Result<Boolean> checkUserRole(
            @Parameter(description = "用户ID", required = true)
            @RequestParam @NotBlank(message = "用户ID不能为空") String userId,
            @Parameter(description = "角色ID", required = true)
            @RequestParam @NotBlank(message = "角色ID不能为空") String roleId) {
        
        boolean hasRole = userRoleService.hasRole(userId, roleId);
        return Result.success(hasRole);
    }

    @GetMapping("/check-code")
    @Operation(summary = "检查用户角色编码", description = "检查用户是否拥有指定角色编码")
    @PreAuthorize("hasAuthority('user:check_role') or hasRole('ADMIN')")
    public Result<Boolean> checkUserRoleByCode(
            @Parameter(description = "用户ID", required = true)
            @RequestParam @NotBlank(message = "用户ID不能为空") String userId,
            @Parameter(description = "角色编码", required = true)
            @RequestParam @NotBlank(message = "角色编码不能为空") String roleCode) {
        
        boolean hasRole = userRoleService.hasRoleByCode(userId, roleCode);
        return Result.success(hasRole);
    }

    @PutMapping("/enable")
    @Operation(summary = "启用用户角色关联", description = "启用指定的用户角色关联")
    @PreAuthorize("hasAuthority('user:manage_role') or hasRole('ADMIN')")
    public Result<String> enableUserRole(
            @Parameter(description = "用户ID", required = true)
            @RequestParam @NotBlank(message = "用户ID不能为空") String userId,
            @Parameter(description = "角色ID", required = true)
            @RequestParam @NotBlank(message = "角色ID不能为空") String roleId) {
        
        log.info("Enabling user role: {} -> {}", userId, roleId);
        
        userRoleService.enableUserRole(userId, roleId);
        return Result.success("用户角色关联启用成功");
    }

    @PutMapping("/disable")
    @Operation(summary = "禁用用户角色关联", description = "禁用指定的用户角色关联")
    @PreAuthorize("hasAuthority('user:manage_role') or hasRole('ADMIN')")
    public Result<String> disableUserRole(
            @Parameter(description = "用户ID", required = true)
            @RequestParam @NotBlank(message = "用户ID不能为空") String userId,
            @Parameter(description = "角色ID", required = true)
            @RequestParam @NotBlank(message = "角色ID不能为空") String roleId) {
        
        log.info("Disabling user role: {} -> {}", userId, roleId);
        
        userRoleService.disableUserRole(userId, roleId);
        return Result.success("用户角色关联禁用成功");
    }

    @PostMapping("/copy")
    @Operation(summary = "复制用户角色", description = "将源用户的角色复制到目标用户")
    @PreAuthorize("hasAuthority('user:copy_role') or hasRole('ADMIN')")
    public Result<String> copyUserRoles(
            @Parameter(description = "源用户ID", required = true)
            @RequestParam @NotBlank(message = "源用户ID不能为空") String sourceUserId,
            @Parameter(description = "目标用户ID", required = true)
            @RequestParam @NotBlank(message = "目标用户ID不能为空") String targetUserId) {
        
        log.info("Copying user roles: {} -> {}", sourceUserId, targetUserId);
        
        userRoleService.copyUserRoles(sourceUserId, targetUserId);
        return Result.success("用户角色复制成功");
    }

    @PostMapping("/sync")
    @Operation(summary = "同步用户角色", description = "同步用户的角色列表")
    @PreAuthorize("hasAuthority('user:sync_role') or hasRole('ADMIN')")
    public Result<String> syncUserRoles(
            @Parameter(description = "用户ID", required = true)
            @RequestParam @NotBlank(message = "用户ID不能为空") String userId,
            @Parameter(description = "角色ID列表", required = true)
            @RequestBody @NotEmpty(message = "角色ID列表不能为空") List<String> roleIds) {
        
        log.info("Syncing user roles: {} -> {}", userId, roleIds);
        
        userRoleService.syncUserRoles(userId, roleIds);
        return Result.success("用户角色同步成功");
    }

    @PostMapping("/handle-expired")
    @Operation(summary = "处理过期角色", description = "处理过期的用户角色关联")
    @PreAuthorize("hasRole('ADMIN')")
    public Result<String> handleExpiredRoles() {
        log.info("Handling expired user roles");
        
        userRoleService.handleExpiredRoles();
        return Result.success("过期角色处理完成");
    }

    @GetMapping("/users/{userId}/statistics")
    @Operation(summary = "获取用户角色统计", description = "获取指定用户的角色统计信息")
    @PreAuthorize("hasAuthority('user:statistics') or hasRole('ADMIN') or #userId == authentication.principal.id")
    public Result<Object> getUserRoleStatistics(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId) {
        
        Object statistics = userRoleService.getUserRoleStatistics(userId);
        return Result.success(statistics);
    }

    @GetMapping("/users/{userId}/is-super-admin")
    @Operation(summary = "检查超级管理员", description = "检查用户是否为超级管理员")
    @PreAuthorize("hasAuthority('user:check_admin') or hasRole('ADMIN')")
    public Result<Boolean> isSuperAdmin(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId) {
        
        boolean isSuperAdmin = userRoleService.isSuperAdmin(userId);
        return Result.success(isSuperAdmin);
    }

    @GetMapping("/users/{userId}/is-admin")
    @Operation(summary = "检查管理员权限", description = "检查用户是否有管理员权限")
    @PreAuthorize("hasAuthority('user:check_admin') or hasRole('ADMIN')")
    public Result<Boolean> isAdmin(
            @Parameter(description = "用户ID", required = true)
            @PathVariable @NotBlank(message = "用户ID不能为空") String userId) {
        
        boolean isAdmin = userRoleService.isAdmin(userId);
        return Result.success(isAdmin);
    }
}
