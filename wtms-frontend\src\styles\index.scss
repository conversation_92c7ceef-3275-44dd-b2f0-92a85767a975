@use 'sass:color';
@use './variables.scss' as *;

// WTMS 全局样式

// 重置样式
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  height: 100%;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: $font-size-base;
  line-height: $line-height-base;
  color: $text-color-primary;
  background-color: $bg-color;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

// 清除默认样式
ul,
ol {
  margin: 0;
  padding: 0;
  list-style: none;
}

a {
  color: $primary-color;
  text-decoration: none;
  
  &:hover {
    color: color.adjust($primary-color, $lightness: 10%);
  }
}

// 工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-primary {
  color: $primary-color;
}

.text-success {
  color: $success-color;
}

.text-warning {
  color: $warning-color;
}

.text-danger {
  color: $danger-color;
}

.text-info {
  color: $info-color;
}

.text-muted {
  color: $text-color-secondary;
}

// 间距工具类
@for $i from 0 through 5 {
  .m-#{$i} {
    margin: #{$i * 8}px;
  }
  
  .mt-#{$i} {
    margin-top: #{$i * 8}px;
  }
  
  .mr-#{$i} {
    margin-right: #{$i * 8}px;
  }
  
  .mb-#{$i} {
    margin-bottom: #{$i * 8}px;
  }
  
  .ml-#{$i} {
    margin-left: #{$i * 8}px;
  }
  
  .p-#{$i} {
    padding: #{$i * 8}px;
  }
  
  .pt-#{$i} {
    padding-top: #{$i * 8}px;
  }
  
  .pr-#{$i} {
    padding-right: #{$i * 8}px;
  }
  
  .pb-#{$i} {
    padding-bottom: #{$i * 8}px;
  }
  
  .pl-#{$i} {
    padding-left: #{$i * 8}px;
  }
}

// 布局工具类
.d-flex {
  display: flex;
}

.d-inline-flex {
  display: inline-flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.justify-content-start {
  justify-content: flex-start;
}

.justify-content-end {
  justify-content: flex-end;
}

.justify-content-center {
  justify-content: center;
}

.justify-content-between {
  justify-content: space-between;
}

.justify-content-around {
  justify-content: space-around;
}

.align-items-start {
  align-items: flex-start;
}

.align-items-end {
  align-items: flex-end;
}

.align-items-center {
  align-items: center;
}

.align-items-baseline {
  align-items: baseline;
}

.align-items-stretch {
  align-items: stretch;
}

.flex-1 {
  flex: 1;
}

.flex-grow-1 {
  flex-grow: 1;
}

.flex-shrink-0 {
  flex-shrink: 0;
}

// 显示/隐藏工具类
.d-none {
  display: none;
}

.d-block {
  display: block;
}

.d-inline {
  display: inline;
}

.d-inline-block {
  display: inline-block;
}

// 位置工具类
.position-relative {
  position: relative;
}

.position-absolute {
  position: absolute;
}

.position-fixed {
  position: fixed;
}

.position-sticky {
  position: sticky;
}

// 溢出处理
.overflow-hidden {
  overflow: hidden;
}

.overflow-auto {
  overflow: auto;
}

.text-overflow {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

// 圆角工具类
.rounded {
  border-radius: $border-radius-base;
}

.rounded-sm {
  border-radius: $border-radius-small;
}

.rounded-lg {
  border-radius: $border-radius-large;
}

.rounded-circle {
  border-radius: 50%;
}

// 阴影工具类
.shadow {
  box-shadow: $box-shadow-base;
}

.shadow-light {
  box-shadow: $box-shadow-light;
}

.shadow-dark {
  box-shadow: $box-shadow-dark;
}

// 边框工具类
.border {
  border: 1px solid $border-color-base;
}

.border-top {
  border-top: 1px solid $border-color-base;
}

.border-right {
  border-right: 1px solid $border-color-base;
}

.border-bottom {
  border-bottom: 1px solid $border-color-base;
}

.border-left {
  border-left: 1px solid $border-color-base;
}

.border-0 {
  border: 0;
}

// 宽高工具类
.w-100 {
  width: 100%;
}

.h-100 {
  height: 100%;
}

.w-auto {
  width: auto;
}

.h-auto {
  height: auto;
}

// 自定义滚动条
.custom-scrollbar {
  &::-webkit-scrollbar {
    width: 6px;
    height: 6px;
  }

  &::-webkit-scrollbar-track {
    background: $bg-color-light;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: $border-color-base;
    border-radius: 3px;
    
    &:hover {
      background: $text-color-secondary;
    }
  }
}

// 动画类
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateX(20px);
  opacity: 0;
}
