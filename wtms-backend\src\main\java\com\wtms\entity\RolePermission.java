package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 角色权限关联实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("role_permissions")
@Schema(description = "角色权限关联实体")
public class RolePermission implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "角色ID")
    @TableField("role_id")
    private String roleId;

    @Schema(description = "权限ID")
    @TableField("permission_id")
    private String permissionId;

    @Schema(description = "授权类型")
    @TableField("grant_type")
    private String grantType;

    @Schema(description = "是否启用")
    @TableField("is_enabled")
    private Boolean isEnabled;

    @Schema(description = "授权时间")
    @TableField("granted_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime grantedAt;

    @Schema(description = "授权人ID")
    @TableField("granted_by")
    private String grantedBy;

    @Schema(description = "过期时间")
    @TableField("expires_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // 非数据库字段
    @Schema(description = "角色信息")
    @TableField(exist = false)
    private Role role;

    @Schema(description = "权限信息")
    @TableField(exist = false)
    private Permission permission;

    @Schema(description = "授权人信息")
    @TableField(exist = false)
    private User grantedByUser;

    /**
     * 授权类型枚举
     */
    public enum GrantType {
        DIRECT("direct", "直接授权"),
        INHERIT("inherit", "继承授权"),
        TEMPORARY("temporary", "临时授权");

        private final String code;
        private final String description;

        GrantType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static GrantType fromCode(String code) {
            for (GrantType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return DIRECT;
        }
    }

    /**
     * 检查权限是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.isEnabled);
    }

    /**
     * 检查权限是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(LocalDateTime.now());
    }

    /**
     * 检查权限是否有效
     */
    public boolean isValid() {
        return isEnabled() && !isExpired();
    }
}
