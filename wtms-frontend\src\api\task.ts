import request from '@/utils/request'

// 任务相关接口

/**
 * 任务查询参数
 */
export interface TaskQueryParams {
  search?: string
  status?: string
  statusList?: string[]
  priority?: number
  priorityList?: number[]
  difficultyLevel?: number
  categoryId?: string
  categoryIds?: string[]
  creatorId?: string
  assigneeId?: string
  assigneeIds?: string[]
  reviewerId?: string
  parentId?: string
  projectId?: string
  tag?: string
  tags?: string[]
  createdAtStart?: string
  createdAtEnd?: string
  plannedStartDateStart?: string
  plannedStartDateEnd?: string
  plannedEndDateStart?: string
  plannedEndDateEnd?: string
  includeSubTasks?: boolean
  onlyMyTasks?: boolean
  onlyMyCreated?: boolean
  includeArchived?: boolean
  sortBy?: string
  sortOrder?: string
}

/**
 * 创建任务请求
 */
export interface CreateTaskRequest {
  title: string
  description?: string
  categoryId: string
  priority?: number
  difficultyLevel?: number
  estimatedHours?: number
  assigneeId?: string
  reviewerId?: string
  parentId?: string
  projectId?: string
  plannedStartDate?: string
  plannedEndDate?: string
  tags?: string[]
  customFields?: string
}

/**
 * 更新任务请求
 */
export interface UpdateTaskRequest {
  title?: string
  description?: string
  categoryId?: string
  priority?: number
  difficultyLevel?: number
  estimatedHours?: number
  actualHours?: number
  progress?: number
  assigneeId?: string
  reviewerId?: string
  parentId?: string
  projectId?: string
  plannedStartDate?: string
  plannedEndDate?: string
  actualStartDate?: string
  actualEndDate?: string
  tags?: string[]
  customFields?: string
}

/**
 * 任务列表响应
 */
export interface TaskListItem {
  id: string
  taskCode: string
  title: string
  description: string
  status: string
  statusText: string
  priority: number
  priorityText: string
  difficultyLevel: number
  estimatedHours: number
  actualHours: number
  progress: number
  category: {
    id: string
    name: string
    code: string
    color: string
    icon: string
  }
  creator: {
    id: string
    username: string
    fullName: string
    avatar: string
  }
  assignee?: {
    id: string
    username: string
    fullName: string
    avatar: string
  }
  reviewer?: {
    id: string
    username: string
    fullName: string
    avatar: string
  }
  plannedStartDate?: string
  plannedEndDate?: string
  actualStartDate?: string
  actualEndDate?: string
  tags: string[]
  isArchived: boolean
  createdAt: string
  updatedAt: string
}

/**
 * 任务详情响应
 */
export interface TaskDetail extends TaskListItem {
  parent?: {
    id: string
    taskCode: string
    title: string
    status: string
    statusText: string
  }
  children?: Array<{
    id: string
    taskCode: string
    title: string
    status: string
    statusText: string
    assigneeName?: string
    progress: number
  }>
  projectId?: string
  attachments?: string
  customFields?: string
  isEditable: boolean
}

/**
 * 分页结果
 */
export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// API 接口函数

/**
 * 获取任务列表
 */
export function getTaskList(params: TaskQueryParams & { page?: number; size?: number }) {
  return request.get<PageResult<TaskListItem>>('/tasks', { params })
}

/**
 * 获取任务详情
 */
export function getTaskDetail(taskId: string) {
  return request.get<TaskDetail>(`/tasks/${taskId}`)
}

/**
 * 创建任务
 */
export function createTask(data: CreateTaskRequest) {
  return request.post<TaskDetail>('/tasks', data)
}

/**
 * 更新任务
 */
export function updateTask(taskId: string, data: UpdateTaskRequest) {
  return request.put<TaskDetail>(`/api/v1/tasks/${taskId}`, data)
}

/**
 * 删除任务
 */
export function deleteTask(taskId: string) {
  return request.delete(`/api/v1/tasks/${taskId}`)
}

/**
 * 更新任务状态
 */
export function updateTaskStatus(taskId: string, status: string) {
  return request.put<TaskDetail>(`/api/v1/tasks/${taskId}/status`, null, {
    params: { status }
  })
}

/**
 * 更新任务进度
 */
export function updateTaskProgress(taskId: string, progress: number) {
  return request.put<TaskDetail>(`/api/v1/tasks/${taskId}/progress`, null, {
    params: { progress }
  })
}

/**
 * 分配任务
 */
export function assignTask(taskId: string, assigneeId: string) {
  return request.put<TaskDetail>(`/api/v1/tasks/${taskId}/assign`, null, {
    params: { assigneeId }
  })
}

/**
 * 开始任务
 */
export function startTask(taskId: string) {
  return request.put<TaskDetail>(`/api/v1/tasks/${taskId}/start`)
}

/**
 * 完成任务
 */
export function completeTask(taskId: string) {
  return request.put<TaskDetail>(`/api/v1/tasks/${taskId}/complete`)
}

/**
 * 暂停任务
 */
export function pauseTask(taskId: string) {
  return request.put<TaskDetail>(`/api/v1/tasks/${taskId}/pause`)
}

/**
 * 取消任务
 */
export function cancelTask(taskId: string) {
  return request.put<TaskDetail>(`/api/v1/tasks/${taskId}/cancel`)
}

/**
 * 归档任务
 */
export function archiveTask(taskId: string) {
  return request.put(`/api/v1/tasks/${taskId}/archive`)
}

/**
 * 取消归档
 */
export function unarchiveTask(taskId: string) {
  return request.put(`/api/v1/tasks/${taskId}/unarchive`)
}

/**
 * 复制任务
 */
export function copyTask(taskId: string) {
  return request.post<TaskDetail>(`/api/v1/tasks/${taskId}/copy`)
}

/**
 * 获取子任务
 */
export function getSubTasks(taskId: string) {
  return request.get<TaskListItem[]>(`/api/v1/tasks/${taskId}/subtasks`)
}

/**
 * 获取我的任务
 */
export function getMyTasks(params: { status?: string; page?: number; size?: number }) {
  return request.get<PageResult<TaskListItem>>('/api/v1/tasks/my', { params })
}

/**
 * 获取我创建的任务
 */
export function getMyCreatedTasks(params: { status?: string; page?: number; size?: number }) {
  return request.get<PageResult<TaskListItem>>('/api/v1/tasks/my-created', { params })
}

/**
 * 获取任务统计
 */
export function getTaskStatistics() {
  return request.get('/api/v1/tasks/statistics')
}

/**
 * 批量更新任务状态
 */
export function batchUpdateTaskStatus(taskIds: string[], status: string) {
  return request.put('/api/v1/tasks/batch/status', null, {
    params: { taskIds, status }
  })
}

/**
 * 批量分配任务
 */
export function batchAssignTasks(taskIds: string[], assigneeId: string) {
  return request.put('/api/v1/tasks/batch/assign', null, {
    params: { taskIds, assigneeId }
  })
}

/**
 * 批量删除任务
 */
export function batchDeleteTasks(taskIds: string[]) {
  return request.delete('/api/v1/tasks/batch', {
    params: { taskIds }
  })
}
