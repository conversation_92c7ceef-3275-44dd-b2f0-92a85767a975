#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WTMS数据库初始化执行脚本
使用Python执行SQL初始化脚本
"""

import pymysql
import os
import sys
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3308,
    'user': 'root',
    'password': 'ankaixin.docker.mysql',
    'charset': 'utf8mb4'
}

def execute_sql_file(connection, file_path):
    """执行SQL文件"""
    print(f"📄 执行SQL文件: {file_path}")

    if not os.path.exists(file_path):
        print(f"❌ 文件不存在: {file_path}")
        return False

    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            sql_content = f.read()

        # 移除注释和空行
        lines = sql_content.split('\n')
        clean_lines = []
        in_comment = False

        for line in lines:
            line = line.strip()
            if not line:
                continue
            if line.startswith('--'):
                continue
            if line.startswith('/*'):
                in_comment = True
                continue
            if line.endswith('*/'):
                in_comment = False
                continue
            if in_comment:
                continue
            clean_lines.append(line)

        # 重新组合SQL内容
        clean_sql = ' '.join(clean_lines)

        # 分割SQL语句
        sql_statements = []
        current_statement = ""

        for part in clean_sql.split(';'):
            part = part.strip()
            if not part:
                continue
            current_statement += part
            if not part.endswith(',') and current_statement:
                sql_statements.append(current_statement)
                current_statement = ""

        cursor = connection.cursor()
        success_count = 0

        for i, statement in enumerate(sql_statements):
            statement = statement.strip()
            if not statement:
                continue
            if statement.upper().startswith(('SET NAMES', 'SET FOREIGN_KEY_CHECKS', 'SET TIME_ZONE')):
                try:
                    cursor.execute(statement)
                    success_count += 1
                except:
                    pass
                continue

            try:
                cursor.execute(statement)
                success_count += 1
                print(f"  ✓ 执行语句 {i+1}: {statement[:50]}...")
            except Exception as e:
                error_msg = str(e).lower()
                if 'already exists' not in error_msg and 'duplicate' not in error_msg:
                    print(f"⚠️  SQL执行警告 (语句 {i+1}): {str(e)[:100]}...")
                    print(f"     语句内容: {statement[:100]}...")

        connection.commit()
        print(f"✅ 成功执行 {success_count} 条SQL语句")
        return True

    except Exception as e:
        print(f"❌ 执行SQL文件失败: {str(e)}")
        return False

def create_database():
    """创建数据库"""
    print("🔧 创建WTMS数据库...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 删除已存在的数据库
        cursor.execute("DROP DATABASE IF EXISTS wtms")
        print("🗑️  删除已存在的wtms数据库")
        
        # 创建新数据库
        cursor.execute("CREATE DATABASE wtms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
        print("✅ 创建wtms数据库成功")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {str(e)}")
        return False

def connect_to_wtms():
    """连接到WTMS数据库"""
    config = DB_CONFIG.copy()
    config['database'] = 'wtms'
    return pymysql.connect(**config)

def verify_initialization():
    """验证初始化结果"""
    print("\n🔍 验证数据库初始化结果...")
    
    try:
        connection = connect_to_wtms()
        cursor = connection.cursor()
        
        # 检查表数量
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"📊 创建表数量: {len(tables)}")
        
        # 检查各表数据量
        table_stats = []
        for (table_name,) in tables:
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]
            table_stats.append((table_name, count))
        
        print("\n📈 各表数据统计:")
        for table_name, count in table_stats:
            print(f"  {table_name}: {count} 条记录")
        
        # 验证超级管理员
        cursor.execute("""
            SELECT u.username, u.full_name, r.name as role_name 
            FROM users u 
            JOIN user_roles ur ON u.id = ur.user_id 
            JOIN roles r ON ur.role_id = r.id 
            WHERE u.username = 'admin'
        """)
        admin_info = cursor.fetchone()
        
        if admin_info:
            print(f"\n👤 超级管理员验证: ✅")
            print(f"   用户名: {admin_info[0]}")
            print(f"   姓名: {admin_info[1]}")
            print(f"   角色: {admin_info[2]}")
        else:
            print(f"\n👤 超级管理员验证: ❌")
        
        # 验证权限数据
        cursor.execute("SELECT COUNT(*) FROM permissions WHERE is_system = 1")
        perm_count = cursor.fetchone()[0]
        print(f"\n🔐 系统权限数量: {perm_count}")
        
        # 验证角色权限关联
        cursor.execute("SELECT COUNT(*) FROM role_permissions")
        role_perm_count = cursor.fetchone()[0]
        print(f"🔗 角色权限关联数量: {role_perm_count}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 验证失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("🚀 WTMS 数据库初始化脚本")
    print("=" * 60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 获取脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 1. 创建数据库
    if not create_database():
        sys.exit(1)
    
    # 2. 连接到WTMS数据库
    try:
        connection = connect_to_wtms()
        print("✅ 连接到WTMS数据库成功")
    except Exception as e:
        print(f"❌ 连接数据库失败: {str(e)}")
        sys.exit(1)
    
    # 3. 执行建表脚本
    table_script = os.path.join(script_dir, '01_create_tables.sql')
    if not execute_sql_file(connection, table_script):
        connection.close()
        sys.exit(1)
    
    # 4. 执行数据初始化脚本
    data_script = os.path.join(script_dir, '02_init_data.sql')
    if not execute_sql_file(connection, data_script):
        connection.close()
        sys.exit(1)
    
    connection.close()
    
    # 5. 验证初始化结果
    if not verify_initialization():
        sys.exit(1)
    
    print("\n" + "=" * 60)
    print("🎉 WTMS 数据库初始化完成！")
    print("=" * 60)
    print("📋 登录信息:")
    print("   URL: http://localhost:55557/api/v1")
    print("   用户名: admin")
    print("   密码: admin123")
    print("   Swagger: http://localhost:55557/api/v1/swagger-ui.html")
    print("   Druid: http://localhost:55557/api/v1/druid/")
    print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)

if __name__ == "__main__":
    try:
        import pymysql
    except ImportError:
        print("❌ 缺少pymysql模块，请安装: pip install pymysql")
        sys.exit(1)
    
    main()
