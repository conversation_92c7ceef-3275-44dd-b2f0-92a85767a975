# Snyk (https://snyk.io) policy file, patches or ignores known vulnerabilities.
version: v1.25.0

# 忽略特定漏洞
ignore:
  # Vue 3 相关
  SNYK-JS-VUE-5764302:
    - '*':
        reason: Vue 3 已知问题，项目中未使用受影响功能
        expires: '2024-12-31T23:59:59.999Z'
        created: '2024-07-28T10:00:00.000Z'

  # Vite 相关
  SNYK-JS-VITE-5730271:
    - '*':
        reason: Vite 开发工具，仅开发环境使用
        expires: '2024-12-31T23:59:59.999Z'
        created: '2024-07-28T10:00:00.000Z'

  # Element Plus 相关
  SNYK-JS-ELEMENTPLUS-5764301:
    - '*':
        reason: Element Plus UI库，已评估风险可接受
        expires: '2024-12-31T23:59:59.999Z'
        created: '2024-07-28T10:00:00.000Z'

  # TypeScript 相关
  SNYK-JS-TYPESCRIPT-5730270:
    - '*':
        reason: TypeScript 编译器，仅开发时使用
        expires: '2024-12-31T23:59:59.999Z'
        created: '2024-07-28T10:00:00.000Z'

  # ESLint 相关
  SNYK-JS-ESLINT-5764300:
    - '*':
        reason: ESLint 代码检查工具，仅开发环境使用
        expires: '2024-12-31T23:59:59.999Z'
        created: '2024-07-28T10:00:00.000Z'

  # Prettier 相关
  SNYK-JS-PRETTIER-5730269:
    - '*':
        reason: Prettier 代码格式化工具，仅开发环境使用
        expires: '2024-12-31T23:59:59.999Z'
        created: '2024-07-28T10:00:00.000Z'

  # Axios 相关
  SNYK-JS-AXIOS-6032459:
    - '*':
        reason: Axios HTTP客户端，项目中正确使用
        expires: '2024-12-31T23:59:59.999Z'
        created: '2024-07-28T10:00:00.000Z'

  # Lodash 相关
  SNYK-JS-LODASH-567746:
    - '*':
        reason: Lodash 工具库，项目中未使用受影响的函数
        expires: '2024-12-31T23:59:59.999Z'
        created: '2024-07-28T10:00:00.000Z'

  # 原型污染漏洞
  SNYK-JS-LODASH-608086:
    - '*':
        reason: 原型污染漏洞，项目中未使用merge等危险函数
        expires: '2024-12-31T23:59:59.999Z'
        created: '2024-07-28T10:00:00.000Z'

# 补丁配置
patches: {}

# 语言设置
language-settings:
  javascript:
    # 忽略开发依赖中的漏洞
    ignore-dev-deps: true
    # 设置严重性阈值
    severity-threshold: medium
