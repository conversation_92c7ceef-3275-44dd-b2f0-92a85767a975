# WTMS 工作任务管理系统部署指南

## 目录
- [系统要求](#系统要求)
- [环境准备](#环境准备)
- [快速部署](#快速部署)
- [手动部署](#手动部署)
- [配置说明](#配置说明)
- [常见问题](#常见问题)
- [维护指南](#维护指南)

## 系统要求

### 硬件要求
- **CPU**: 2核心及以上
- **内存**: 4GB及以上（推荐8GB）
- **存储**: 20GB及以上可用空间
- **网络**: 稳定的网络连接

### 软件要求
- **操作系统**: Linux (Ubuntu 20.04+/CentOS 7+) / Windows 10+ / macOS 10.15+
- **Docker**: 20.10.0+
- **Docker Compose**: 1.29.0+
- **Git**: 2.25.0+

## 环境准备

### 1. 安装Docker和Docker Compose

#### Ubuntu/Debian
```bash
# 更新包索引
sudo apt update

# 安装Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# 验证安装
docker --version
docker-compose --version
```

#### CentOS/RHEL
```bash
# 安装Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io

# 启动Docker服务
sudo systemctl start docker
sudo systemctl enable docker

# 安装Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/download/v2.20.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose
```

#### Windows
1. 下载并安装 [Docker Desktop for Windows](https://www.docker.com/products/docker-desktop)
2. 启动Docker Desktop并确保WSL2后端已启用

#### macOS
1. 下载并安装 [Docker Desktop for Mac](https://www.docker.com/products/docker-desktop)
2. 启动Docker Desktop

### 2. 克隆项目代码
```bash
git clone https://github.com/your-org/wtms.git
cd wtms
```

## 快速部署

### 使用Docker Compose一键部署

1. **配置环境变量**
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量（可选，使用默认配置即可快速启动）
vim .env
```

2. **启动所有服务**
```bash
# 构建并启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

3. **初始化数据库**
```bash
# 等待MySQL服务完全启动（约30秒）
sleep 30

# 执行数据库初始化脚本
docker-compose exec mysql mysql -uroot -pwtms123456 wtms < /docker-entrypoint-initdb.d/init.sql
```

4. **访问系统**
- 前端地址: http://localhost:33335
- 后端API: http://localhost:55557/api/v1
- 数据库: localhost:3308
- Redis: localhost:6379

### 默认账户信息
- **管理员账户**: admin / admin123456
- **普通用户**: user / user123456

## 手动部署

### 1. 数据库部署

#### MySQL部署
```bash
# 创建MySQL容器
docker run -d \
  --name wtms-mysql \
  -p 3306:3306 \
  -e MYSQL_ROOT_PASSWORD=wtms123456 \
  -e MYSQL_DATABASE=wtms \
  -e MYSQL_USER=wtms \
  -e MYSQL_PASSWORD=wtms123456 \
  -v wtms_mysql_data:/var/lib/mysql \
  mysql:8.0

# 导入数据库结构
docker exec -i wtms-mysql mysql -uroot -pwtms123456 wtms < database/init.sql
```

#### Redis部署
```bash
# 创建Redis容器
docker run -d \
  --name wtms-redis \
  -p 6379:6379 \
  -v wtms_redis_data:/data \
  redis:7.0 redis-server --appendonly yes
```

### 2. 后端部署

#### 方式一：Docker部署
```bash
# 构建后端镜像
cd wtms-backend
docker build -t wtms-backend:latest .

# 运行后端容器
docker run -d \
  --name wtms-backend \
  -p 8080:8080 \
  --link wtms-mysql:mysql \
  --link wtms-redis:redis \
  -e SPRING_PROFILES_ACTIVE=prod \
  -e MYSQL_HOST=mysql \
  -e REDIS_HOST=redis \
  wtms-backend:latest
```

#### 方式二：JAR包部署
```bash
# 编译项目
cd wtms-backend
./mvnw clean package -DskipTests

# 运行JAR包
java -jar target/wtms-backend-1.0.0.jar \
  --spring.profiles.active=prod \
  --spring.datasource.url=******************************** \
  --spring.redis.host=localhost
```

### 3. 前端部署

#### 方式一：Docker部署
```bash
# 构建前端镜像
cd wtms-frontend
docker build -t wtms-frontend:latest .

# 运行前端容器
docker run -d \
  --name wtms-frontend \
  -p 3000:80 \
  wtms-frontend:latest
```

#### 方式二：Nginx部署
```bash
# 构建前端项目
cd wtms-frontend
npm install
npm run build

# 配置Nginx
sudo cp nginx.conf /etc/nginx/sites-available/wtms
sudo ln -s /etc/nginx/sites-available/wtms /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx
```

## 配置说明

### 环境变量配置

#### 后端配置 (.env)
```bash
# 数据库配置
MYSQL_HOST=localhost
MYSQL_PORT=3306
MYSQL_DATABASE=wtms
MYSQL_USERNAME=wtms
MYSQL_PASSWORD=wtms123456

# Redis配置
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# JWT配置
JWT_SECRET=wtms-jwt-secret-key-2024
JWT_EXPIRATION=86400

# 文件上传配置
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=10MB

# 邮件配置（可选）
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
```

#### 前端配置 (.env.production)
```bash
# API基础URL
VITE_API_BASE_URL=http://localhost:55557/api/v1

# 应用配置
VITE_APP_TITLE=WTMS工作任务管理系统
VITE_APP_VERSION=1.0.0

# 文件上传配置
VITE_UPLOAD_URL=http://localhost:55557/api/v1/files/upload
VITE_MAX_FILE_SIZE=10485760
```

### 数据库配置

#### MySQL配置优化
```sql
-- 创建数据库
CREATE DATABASE wtms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'wtms'@'%' IDENTIFIED BY 'wtms123456';
GRANT ALL PRIVILEGES ON wtms.* TO 'wtms'@'%';
FLUSH PRIVILEGES;

-- 性能优化配置
SET GLOBAL innodb_buffer_pool_size = 1073741824; -- 1GB
SET GLOBAL max_connections = 200;
SET GLOBAL query_cache_size = 67108864; -- 64MB
```

#### Redis配置优化
```bash
# redis.conf 关键配置
maxmemory 512mb
maxmemory-policy allkeys-lru
save 900 1
save 300 10
save 60 10000
```

### Nginx配置

#### 前端代理配置
```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/wtms/dist;
    index index.html;

    # 前端路由支持
    location / {
        try_files $uri $uri/ /index.html;
    }

    # API代理
    location /api/ {
        proxy_pass http://localhost:55557/api/v1/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # 文件上传大小限制
    client_max_body_size 10M;

    # 静态资源缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

## 常见问题

### 1. 数据库连接失败
**问题**: 后端无法连接到MySQL数据库

**解决方案**:
```bash
# 检查MySQL服务状态
docker-compose ps mysql

# 查看MySQL日志
docker-compose logs mysql

# 检查网络连接
docker-compose exec backend ping mysql

# 重启MySQL服务
docker-compose restart mysql
```

### 2. 前端无法访问后端API
**问题**: 前端请求后端API时出现跨域错误

**解决方案**:
1. 检查后端CORS配置
2. 确认API基础URL配置正确
3. 检查防火墙设置

### 3. 文件上传失败
**问题**: 文件上传时出现错误

**解决方案**:
```bash
# 检查上传目录权限
docker-compose exec backend ls -la /app/uploads

# 创建上传目录
docker-compose exec backend mkdir -p /app/uploads
docker-compose exec backend chmod 755 /app/uploads
```

### 4. 内存不足
**问题**: 系统运行缓慢或出现内存错误

**解决方案**:
```bash
# 调整Docker内存限制
# 在docker-compose.yml中添加：
services:
  backend:
    mem_limit: 1g
  frontend:
    mem_limit: 512m
```

## 维护指南

### 日常维护

#### 1. 日志管理
```bash
# 查看所有服务日志
docker-compose logs

# 查看特定服务日志
docker-compose logs backend
docker-compose logs frontend
docker-compose logs mysql

# 清理日志
docker-compose logs --tail=0 -f
```

#### 2. 数据备份
```bash
# 数据库备份
docker-compose exec mysql mysqldump -uroot -pwtms123456 wtms > backup_$(date +%Y%m%d_%H%M%S).sql

# Redis备份
docker-compose exec redis redis-cli BGSAVE
```

#### 3. 系统更新
```bash
# 拉取最新代码
git pull origin main

# 重新构建并启动服务
docker-compose down
docker-compose build
docker-compose up -d
```

### 性能监控

#### 1. 系统资源监控
```bash
# 查看容器资源使用情况
docker stats

# 查看磁盘使用情况
df -h

# 查看内存使用情况
free -h
```

#### 2. 应用监控
```bash
# 检查应用健康状态
curl http://localhost:8080/actuator/health

# 查看应用指标
curl http://localhost:8080/actuator/metrics
```

### 故障排除

#### 1. 服务无法启动
```bash
# 检查端口占用
netstat -tulpn | grep :8080

# 检查Docker服务
sudo systemctl status docker

# 重启Docker服务
sudo systemctl restart docker
```

#### 2. 数据库问题
```bash
# 检查数据库连接
docker-compose exec mysql mysql -uroot -pwtms123456 -e "SELECT 1"

# 修复数据库表
docker-compose exec mysql mysqlcheck -uroot -pwtms123456 --auto-repair wtms
```

## 安全建议

### 1. 密码安全
- 修改默认密码
- 使用强密码策略
- 定期更换密码

### 2. 网络安全
- 配置防火墙规则
- 使用HTTPS协议
- 限制访问IP范围

### 3. 数据安全
- 定期备份数据
- 加密敏感数据
- 监控异常访问

---

**技术支持**: 如有问题，请联系技术支持团队或查看项目文档。
