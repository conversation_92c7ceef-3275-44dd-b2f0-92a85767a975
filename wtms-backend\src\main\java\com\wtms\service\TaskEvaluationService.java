package com.wtms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wtms.dto.request.CreateEvaluationRequest;
import com.wtms.entity.TaskEvaluation;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 任务评价服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface TaskEvaluationService {

    /**
     * 创建任务评价
     *
     * @param request 创建评价请求
     * @param evaluatorId 评价者ID
     * @return 任务评价
     */
    TaskEvaluation createEvaluation(CreateEvaluationRequest request, String evaluatorId);

    /**
     * 更新任务评价
     *
     * @param evaluationId 评价ID
     * @param request 更新评价请求
     * @param userId 用户ID
     * @return 任务评价
     */
    TaskEvaluation updateEvaluation(String evaluationId, CreateEvaluationRequest request, String userId);

    /**
     * 删除任务评价
     *
     * @param evaluationId 评价ID
     * @param userId 用户ID
     */
    void deleteEvaluation(String evaluationId, String userId);

    /**
     * 根据ID获取任务评价
     *
     * @param evaluationId 评价ID
     * @return 任务评价
     */
    TaskEvaluation getEvaluationById(String evaluationId);

    /**
     * 根据任务ID获取评价列表
     *
     * @param taskId 任务ID
     * @return 评价列表
     */
    List<TaskEvaluation> getEvaluationsByTaskId(String taskId);

    /**
     * 根据评价者ID获取评价列表
     *
     * @param evaluatorId 评价者ID
     * @param status 状态
     * @param page 页码
     * @param size 每页大小
     * @return 评价分页结果
     */
    IPage<TaskEvaluation> getEvaluationsByEvaluator(String evaluatorId, String status, Integer page, Integer size);

    /**
     * 根据被评价者ID获取评价列表
     *
     * @param evaluateeId 被评价者ID
     * @param status 状态
     * @param page 页码
     * @param size 每页大小
     * @return 评价分页结果
     */
    IPage<TaskEvaluation> getEvaluationsByEvaluatee(String evaluateeId, String status, Integer page, Integer size);

    /**
     * 根据评价类型获取评价列表
     *
     * @param evaluationType 评价类型
     * @return 评价列表
     */
    List<TaskEvaluation> getEvaluationsByType(String evaluationType);

    /**
     * 根据评价阶段获取评价列表
     *
     * @param evaluationStage 评价阶段
     * @return 评价列表
     */
    List<TaskEvaluation> getEvaluationsByStage(String evaluationStage);

    /**
     * 根据状态获取评价列表
     *
     * @param status 状态
     * @return 评价列表
     */
    List<TaskEvaluation> getEvaluationsByStatus(String status);

    /**
     * 获取超期评价列表
     *
     * @return 超期评价列表
     */
    List<TaskEvaluation> getOverdueEvaluations();

    /**
     * 获取待提交评价列表
     *
     * @return 待提交评价列表
     */
    List<TaskEvaluation> getPendingEvaluations();

    /**
     * 获取已发布评价列表
     *
     * @return 已发布评价列表
     */
    List<TaskEvaluation> getPublishedEvaluations();

    /**
     * 根据任务和评价者获取评价
     *
     * @param taskId 任务ID
     * @param evaluatorId 评价者ID
     * @return 任务评价
     */
    TaskEvaluation getEvaluationByTaskAndEvaluator(String taskId, String evaluatorId);

    /**
     * 根据任务和被评价者获取评价列表
     *
     * @param taskId 任务ID
     * @param evaluateeId 被评价者ID
     * @return 评价列表
     */
    List<TaskEvaluation> getEvaluationsByTaskAndEvaluatee(String taskId, String evaluateeId);

    /**
     * 搜索评价
     *
     * @param keyword 关键词
     * @param taskId 任务ID
     * @param evaluatorId 评价者ID
     * @param evaluateeId 被评价者ID
     * @param evaluationType 评价类型
     * @param status 状态
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    IPage<TaskEvaluation> searchEvaluations(String keyword, String taskId, String evaluatorId, 
                                           String evaluateeId, String evaluationType, String status, 
                                           Integer page, Integer size);

    /**
     * 提交评价
     *
     * @param evaluationId 评价ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean submitEvaluation(String evaluationId, String userId);

    /**
     * 审核评价
     *
     * @param evaluationId 评价ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean reviewEvaluation(String evaluationId, String userId);

    /**
     * 发布评价
     *
     * @param evaluationId 评价ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean publishEvaluation(String evaluationId, String userId);

    /**
     * 归档评价
     *
     * @param evaluationId 评价ID
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean archiveEvaluation(String evaluationId, String userId);

    /**
     * 拒绝评价
     *
     * @param evaluationId 评价ID
     * @param reason 拒绝原因
     * @param userId 用户ID
     * @return 是否成功
     */
    boolean rejectEvaluation(String evaluationId, String reason, String userId);

    /**
     * 批量删除评价
     *
     * @param evaluationIds 评价ID列表
     * @param userId 用户ID
     */
    void batchDeleteEvaluations(List<String> evaluationIds, String userId);

    /**
     * 批量更新评价状态
     *
     * @param evaluationIds 评价ID列表
     * @param status 状态
     * @param userId 用户ID
     */
    void batchUpdateStatus(List<String> evaluationIds, String status, String userId);

    /**
     * 计算任务平均评分
     *
     * @param taskId 任务ID
     * @return 平均评分
     */
    BigDecimal calculateTaskAverageScore(String taskId);

    /**
     * 计算用户平均评分
     *
     * @param evaluateeId 被评价者ID
     * @return 平均评分
     */
    BigDecimal calculateUserAverageScore(String evaluateeId);

    /**
     * 计算用户各维度平均评分
     *
     * @param evaluateeId 被评价者ID
     * @return 各维度平均评分
     */
    Map<String, BigDecimal> calculateUserDimensionScores(String evaluateeId);

    /**
     * 检查用户是否可以评价任务
     *
     * @param taskId 任务ID
     * @param evaluatorId 评价者ID
     * @param evaluateeId 被评价者ID
     * @return 是否可以评价
     */
    boolean canEvaluateTask(String taskId, String evaluatorId, String evaluateeId);

    /**
     * 检查用户是否可以编辑评价
     *
     * @param evaluationId 评价ID
     * @param userId 用户ID
     * @return 是否可以编辑
     */
    boolean canEditEvaluation(String evaluationId, String userId);

    /**
     * 检查用户是否可以删除评价
     *
     * @param evaluationId 评价ID
     * @param userId 用户ID
     * @return 是否可以删除
     */
    boolean canDeleteEvaluation(String evaluationId, String userId);

    /**
     * 检查用户是否可以查看评价
     *
     * @param evaluationId 评价ID
     * @param userId 用户ID
     * @return 是否可以查看
     */
    boolean canViewEvaluation(String evaluationId, String userId);

    /**
     * 统计评价数量
     *
     * @return 总数量
     */
    int countAllEvaluations();

    /**
     * 根据状态统计评价数量
     *
     * @param status 状态
     * @return 数量
     */
    int countEvaluationsByStatus(String status);

    /**
     * 根据评价类型统计评价数量
     *
     * @param evaluationType 评价类型
     * @return 数量
     */
    int countEvaluationsByType(String evaluationType);

    /**
     * 根据任务统计评价数量
     *
     * @param taskId 任务ID
     * @return 数量
     */
    int countEvaluationsByTask(String taskId);

    /**
     * 根据评价者统计评价数量
     *
     * @param evaluatorId 评价者ID
     * @return 数量
     */
    int countEvaluationsByEvaluator(String evaluatorId);

    /**
     * 根据被评价者统计评价数量
     *
     * @param evaluateeId 被评价者ID
     * @return 数量
     */
    int countEvaluationsByEvaluatee(String evaluateeId);

    /**
     * 获取高分评价列表
     *
     * @param minScore 最低分数
     * @param limit 数量限制
     * @return 高分评价列表
     */
    List<TaskEvaluation> getHighScoreEvaluations(BigDecimal minScore, Integer limit);

    /**
     * 获取低分评价列表
     *
     * @param maxScore 最高分数
     * @param limit 数量限制
     * @return 低分评价列表
     */
    List<TaskEvaluation> getLowScoreEvaluations(BigDecimal maxScore, Integer limit);

    /**
     * 获取最近评价列表
     *
     * @param limit 数量限制
     * @return 最近评价列表
     */
    List<TaskEvaluation> getRecentEvaluations(Integer limit);

    /**
     * 获取最近提交的评价列表
     *
     * @param limit 数量限制
     * @return 最近提交的评价列表
     */
    List<TaskEvaluation> getRecentSubmittedEvaluations(Integer limit);

    /**
     * 获取需要关注的评价列表
     *
     * @param limit 数量限制
     * @return 需要关注的评价列表
     */
    List<TaskEvaluation> getAttentionRequiredEvaluations(Integer limit);

    /**
     * 清理过期草稿评价
     *
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanupExpiredDrafts(Integer days);

    /**
     * 获取评价完成率
     *
     * @param taskId 任务ID
     * @return 完成率统计
     */
    Map<String, Object> getEvaluationCompletionRate(String taskId);

    /**
     * 获取用户评价活跃度
     *
     * @param days 天数
     * @param limit 数量限制
     * @return 用户评价活跃度
     */
    List<Map<String, Object>> getUserEvaluationActivity(Integer days, Integer limit);

    /**
     * 获取评价质量分析
     *
     * @param evaluateeId 被评价者ID
     * @return 评价质量分析
     */
    Map<String, Object> getEvaluationQualityAnalysis(String evaluateeId);

    /**
     * 获取评价统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getEvaluationStatistics();

    /**
     * 获取评价类型统计
     *
     * @return 评价类型统计
     */
    List<Map<String, Object>> getEvaluationTypeStatistics();

    /**
     * 获取评价状态统计
     *
     * @return 评价状态统计
     */
    List<Map<String, Object>> getEvaluationStatusStatistics();

    /**
     * 获取评价者统计
     *
     * @param limit 数量限制
     * @return 评价者统计
     */
    List<Map<String, Object>> getEvaluatorStatistics(Integer limit);

    /**
     * 获取被评价者统计
     *
     * @param limit 数量限制
     * @return 被评价者统计
     */
    List<Map<String, Object>> getEvaluateeStatistics(Integer limit);

    /**
     * 获取评分分布统计
     *
     * @return 评分分布统计
     */
    List<Map<String, Object>> getScoreDistributionStatistics();

    /**
     * 获取每日评价统计
     *
     * @param days 天数
     * @return 每日评价统计
     */
    List<Map<String, Object>> getDailyEvaluationStatistics(Integer days);

    /**
     * 获取月度评价统计
     *
     * @param months 月数
     * @return 月度评价统计
     */
    List<Map<String, Object>> getMonthlyEvaluationStatistics(Integer months);

    /**
     * 获取评价趋势统计
     *
     * @param evaluateeId 被评价者ID
     * @param days 天数
     * @return 评价趋势统计
     */
    List<Map<String, Object>> getEvaluationTrendStatistics(String evaluateeId, Integer days);
}
