package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.entity.TaskEvaluation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务评价Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface TaskEvaluationMapper extends BaseMapper<TaskEvaluation> {

    /**
     * 根据任务ID查询评价
     */
    @Select("SELECT * FROM task_evaluations WHERE task_id = #{taskId} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskEvaluation> selectByTaskId(@Param("taskId") String taskId);

    /**
     * 根据评价者ID查询评价
     */
    IPage<TaskEvaluation> selectByEvaluatorId(Page<TaskEvaluation> page, @Param("evaluatorId") String evaluatorId, 
                                             @Param("status") String status);

    /**
     * 根据被评价者ID查询评价
     */
    IPage<TaskEvaluation> selectByEvaluateeId(Page<TaskEvaluation> page, @Param("evaluateeId") String evaluateeId, 
                                             @Param("status") String status);

    /**
     * 根据评价类型查询评价
     */
    @Select("SELECT * FROM task_evaluations WHERE evaluation_type = #{evaluationType} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskEvaluation> selectByEvaluationType(@Param("evaluationType") String evaluationType);

    /**
     * 根据评价阶段查询评价
     */
    @Select("SELECT * FROM task_evaluations WHERE evaluation_stage = #{evaluationStage} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskEvaluation> selectByEvaluationStage(@Param("evaluationStage") String evaluationStage);

    /**
     * 根据状态查询评价
     */
    @Select("SELECT * FROM task_evaluations WHERE status = #{status} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskEvaluation> selectByStatus(@Param("status") String status);

    /**
     * 查询超期评价
     */
    @Select("SELECT * FROM task_evaluations WHERE deadline IS NOT NULL AND deadline < NOW() AND status = 'draft' AND deleted_at IS NULL ORDER BY deadline ASC")
    List<TaskEvaluation> selectOverdueEvaluations();

    /**
     * 查询待提交评价
     */
    @Select("SELECT * FROM task_evaluations WHERE status = 'draft' AND deleted_at IS NULL ORDER BY created_at ASC")
    List<TaskEvaluation> selectPendingEvaluations();

    /**
     * 查询已发布评价
     */
    @Select("SELECT * FROM task_evaluations WHERE status = 'published' AND is_public = TRUE AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskEvaluation> selectPublishedEvaluations();

    /**
     * 根据任务和评价者查询评价
     */
    @Select("SELECT * FROM task_evaluations WHERE task_id = #{taskId} AND evaluator_id = #{evaluatorId} AND deleted_at IS NULL")
    TaskEvaluation selectByTaskAndEvaluator(@Param("taskId") String taskId, @Param("evaluatorId") String evaluatorId);

    /**
     * 根据任务和被评价者查询评价
     */
    @Select("SELECT * FROM task_evaluations WHERE task_id = #{taskId} AND evaluatee_id = #{evaluateeId} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskEvaluation> selectByTaskAndEvaluatee(@Param("taskId") String taskId, @Param("evaluateeId") String evaluateeId);

    /**
     * 搜索评价
     */
    IPage<TaskEvaluation> searchEvaluations(Page<TaskEvaluation> page, @Param("keyword") String keyword,
                                           @Param("taskId") String taskId, @Param("evaluatorId") String evaluatorId,
                                           @Param("evaluateeId") String evaluateeId, @Param("evaluationType") String evaluationType,
                                           @Param("status") String status);

    /**
     * 统计评价数量
     */
    @Select("SELECT COUNT(*) FROM task_evaluations WHERE deleted_at IS NULL")
    int countAll();

    /**
     * 根据状态统计评价数量
     */
    @Select("SELECT COUNT(*) FROM task_evaluations WHERE status = #{status} AND deleted_at IS NULL")
    int countByStatus(@Param("status") String status);

    /**
     * 根据评价类型统计评价数量
     */
    @Select("SELECT COUNT(*) FROM task_evaluations WHERE evaluation_type = #{evaluationType} AND deleted_at IS NULL")
    int countByEvaluationType(@Param("evaluationType") String evaluationType);

    /**
     * 根据任务统计评价数量
     */
    @Select("SELECT COUNT(*) FROM task_evaluations WHERE task_id = #{taskId} AND deleted_at IS NULL")
    int countByTaskId(@Param("taskId") String taskId);

    /**
     * 根据评价者统计评价数量
     */
    @Select("SELECT COUNT(*) FROM task_evaluations WHERE evaluator_id = #{evaluatorId} AND deleted_at IS NULL")
    int countByEvaluatorId(@Param("evaluatorId") String evaluatorId);

    /**
     * 根据被评价者统计评价数量
     */
    @Select("SELECT COUNT(*) FROM task_evaluations WHERE evaluatee_id = #{evaluateeId} AND deleted_at IS NULL")
    int countByEvaluateeId(@Param("evaluateeId") String evaluateeId);

    /**
     * 提交评价
     */
    @Update("UPDATE task_evaluations SET status = 'submitted', updated_at = NOW() WHERE id = #{evaluationId}")
    int submitEvaluation(@Param("evaluationId") String evaluationId);

    /**
     * 审核评价
     */
    @Update("UPDATE task_evaluations SET status = 'reviewed', updated_at = NOW() WHERE id = #{evaluationId}")
    int reviewEvaluation(@Param("evaluationId") String evaluationId);

    /**
     * 发布评价
     */
    @Update("UPDATE task_evaluations SET status = 'published', updated_at = NOW() WHERE id = #{evaluationId}")
    int publishEvaluation(@Param("evaluationId") String evaluationId);

    /**
     * 归档评价
     */
    @Update("UPDATE task_evaluations SET status = 'archived', updated_at = NOW() WHERE id = #{evaluationId}")
    int archiveEvaluation(@Param("evaluationId") String evaluationId);

    /**
     * 拒绝评价
     */
    @Update("UPDATE task_evaluations SET status = 'rejected', updated_at = NOW() WHERE id = #{evaluationId}")
    int rejectEvaluation(@Param("evaluationId") String evaluationId);

    /**
     * 批量删除评价
     */
    int batchDeleteEvaluations(@Param("evaluationIds") List<String> evaluationIds);

    /**
     * 批量更新评价状态
     */
    int batchUpdateStatus(@Param("evaluationIds") List<String> evaluationIds, @Param("status") String status);

    /**
     * 计算任务平均评分
     */
    @Select("SELECT AVG(overall_score) FROM task_evaluations WHERE task_id = #{taskId} AND status = 'published' AND deleted_at IS NULL")
    BigDecimal calculateTaskAverageScore(@Param("taskId") String taskId);

    /**
     * 计算用户平均评分
     */
    @Select("SELECT AVG(overall_score) FROM task_evaluations WHERE evaluatee_id = #{evaluateeId} AND status = 'published' AND deleted_at IS NULL")
    BigDecimal calculateUserAverageScore(@Param("evaluateeId") String evaluateeId);

    /**
     * 计算用户各维度平均评分
     */
    Object calculateUserDimensionScores(@Param("evaluateeId") String evaluateeId);

    /**
     * 查询评价统计信息
     */
    Object selectEvaluationStatistics();

    /**
     * 查询评价类型统计
     */
    List<Object> selectEvaluationTypeStatistics();

    /**
     * 查询评价状态统计
     */
    List<Object> selectEvaluationStatusStatistics();

    /**
     * 查询评价者统计
     */
    List<Object> selectEvaluatorStatistics(@Param("limit") Integer limit);

    /**
     * 查询被评价者统计
     */
    List<Object> selectEvaluateeStatistics(@Param("limit") Integer limit);

    /**
     * 查询评分分布统计
     */
    List<Object> selectScoreDistributionStatistics();

    /**
     * 查询每日评价统计
     */
    List<Object> selectDailyEvaluationStatistics(@Param("days") Integer days);

    /**
     * 查询月度评价统计
     */
    List<Object> selectMonthlyEvaluationStatistics(@Param("months") Integer months);

    /**
     * 查询评价趋势统计
     */
    List<Object> selectEvaluationTrendStatistics(@Param("evaluateeId") String evaluateeId, @Param("days") Integer days);

    /**
     * 查询高分评价
     */
    @Select("SELECT * FROM task_evaluations WHERE overall_score >= #{minScore} AND status = 'published' AND deleted_at IS NULL ORDER BY overall_score DESC LIMIT #{limit}")
    List<TaskEvaluation> selectHighScoreEvaluations(@Param("minScore") BigDecimal minScore, @Param("limit") Integer limit);

    /**
     * 查询低分评价
     */
    @Select("SELECT * FROM task_evaluations WHERE overall_score <= #{maxScore} AND status = 'published' AND deleted_at IS NULL ORDER BY overall_score ASC LIMIT #{limit}")
    List<TaskEvaluation> selectLowScoreEvaluations(@Param("maxScore") BigDecimal maxScore, @Param("limit") Integer limit);

    /**
     * 查询最近评价
     */
    @Select("SELECT * FROM task_evaluations WHERE deleted_at IS NULL ORDER BY created_at DESC LIMIT #{limit}")
    List<TaskEvaluation> selectRecentEvaluations(@Param("limit") Integer limit);

    /**
     * 查询最近提交的评价
     */
    @Select("SELECT * FROM task_evaluations WHERE status = 'submitted' AND deleted_at IS NULL ORDER BY updated_at DESC LIMIT #{limit}")
    List<TaskEvaluation> selectRecentSubmittedEvaluations(@Param("limit") Integer limit);

    /**
     * 查询需要关注的评价（低分或有改进建议）
     */
    List<TaskEvaluation> selectAttentionRequiredEvaluations(@Param("limit") Integer limit);

    /**
     * 清理过期草稿评价
     */
    @Update("UPDATE task_evaluations SET deleted_at = NOW() WHERE status = 'draft' AND deadline < #{beforeDate}")
    int cleanupExpiredDrafts(@Param("beforeDate") LocalDateTime beforeDate);

    /**
     * 查询评价完成率
     */
    Object selectEvaluationCompletionRate(@Param("taskId") String taskId);

    /**
     * 查询用户评价活跃度
     */
    List<Object> selectUserEvaluationActivity(@Param("days") Integer days, @Param("limit") Integer limit);

    /**
     * 查询评价质量分析
     */
    Object selectEvaluationQualityAnalysis(@Param("evaluateeId") String evaluateeId);
}
