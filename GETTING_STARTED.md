# WTMS 快速启动指南

## 🎯 项目概述

WTMS（Work Task Management System）工作任务管理平台已经完成基础架构搭建，包含：

✅ **前端项目** - Vue.js 3 + Element Plus + TypeScript  
✅ **后端项目** - Spring Boot 2.7.18 + MyBatis-Plus + Spring Security  
✅ **数据库设计** - MySQL 8.0 完整数据库结构  
✅ **开发环境** - Docker Compose 一键启动  
✅ **用户认证** - JWT Token 认证系统  

## 🚀 立即开始

### 方式一：使用Docker（推荐）

```bash
# 1. 启动开发环境基础服务
./scripts/start-dev.sh

# 2. 启动后端服务
cd wtms-backend
mvn spring-boot:run

# 3. 启动前端服务
cd wtms-frontend  
npm install
npm run dev
```

### 方式二：手动启动

```bash
# 1. 启动MySQL和Redis
docker-compose -f docker-compose.dev.yml up -d mysql-dev redis-dev

# 2. 等待数据库初始化完成（约30秒）

# 3. 启动后端
cd wtms-backend
mvn clean compile
mvn spring-boot:run

# 4. 启动前端
cd wtms-frontend
npm install  
npm run dev
```

## 🌐 访问地址

启动成功后，可以访问以下地址：

- **前端应用**: http://localhost:3000
- **后端API**: http://localhost:8080  
- **API文档**: http://localhost:8080/swagger-ui.html
- **数据库管理**: http://localhost:8081 (phpMyAdmin)
- **Redis管理**: http://localhost:8082 (Redis Commander)

## 🔑 默认登录信息

- **用户名**: admin
- **密码**: 123456

## 📁 项目结构说明

```
wtms/
├── wtms-frontend/              # Vue.js 3前端项目
│   ├── src/
│   │   ├── views/auth/Login.vue        # 登录页面 ✅
│   │   ├── layouts/MainLayout.vue      # 主布局 ✅  
│   │   ├── views/dashboard/            # 工作台 ✅
│   │   ├── stores/user.ts              # 用户状态管理 ✅
│   │   ├── api/auth.ts                 # 认证API ✅
│   │   └── utils/request.ts            # HTTP请求封装 ✅
│   └── package.json
├── wtms-backend/               # Spring Boot后端项目  
│   ├── src/main/java/com/wtms/
│   │   ├── controller/AuthController.java     # 认证控制器 ✅
│   │   ├── service/AuthService.java           # 认证服务 ✅
│   │   ├── security/JwtTokenProvider.java     # JWT工具 ✅
│   │   ├── entity/User.java                   # 用户实体 ✅
│   │   └── config/SecurityConfig.java         # 安全配置 ✅
│   └── pom.xml
├── MySQL数据库设计.sql          # 完整数据库结构 ✅
├── docker-compose.dev.yml      # 开发环境Docker配置 ✅
└── README.md                   # 项目文档 ✅
```

## ✨ 已实现功能

### 🔐 用户认证系统
- [x] JWT Token认证
- [x] 用户登录/登出
- [x] 密码加密存储
- [x] Token刷新机制
- [x] 权限控制

### 🎨 前端界面
- [x] 响应式登录页面
- [x] 主布局框架
- [x] 侧边栏导航
- [x] 工作台仪表板
- [x] 路由守卫
- [x] 全局状态管理

### 🔧 后端API
- [x] RESTful API设计
- [x] 统一响应格式
- [x] 全局异常处理
- [x] Swagger API文档
- [x] 数据库连接池
- [x] Redis缓存

### 🗄️ 数据库设计
- [x] 用户管理表结构
- [x] 角色权限体系
- [x] 部门组织架构
- [x] 任务管理表结构
- [x] 技能管理体系
- [x] 初始化数据

## 🔄 下一步开发计划

### 第一阶段：完善用户认证
- [ ] 用户注册功能
- [ ] 忘记密码功能
- [ ] 用户资料管理
- [ ] 头像上传

### 第二阶段：任务管理核心功能
- [ ] 任务CRUD操作
- [ ] 任务状态流转
- [ ] 任务分配功能
- [ ] 任务评论系统

### 第三阶段：工作流引擎
- [ ] 工作流设计器
- [ ] 工作流执行引擎
- [ ] 审批流程
- [ ] 流程监控

### 第四阶段：质量评价系统
- [ ] 评价标准定义
- [ ] 评价流程管理
- [ ] 评价结果分析
- [ ] 评价报告生成

## 🐛 常见问题

### 1. 端口冲突
如果遇到端口被占用，请检查以下端口：
- 3000 (前端)
- 8080 (后端)  
- 3306 (MySQL)
- 6379 (Redis)

### 2. 数据库连接失败
```bash
# 检查MySQL容器状态
docker-compose -f docker-compose.dev.yml ps

# 查看MySQL日志
docker-compose -f docker-compose.dev.yml logs mysql-dev
```

### 3. 前端编译错误
```bash
# 清除node_modules重新安装
cd wtms-frontend
rm -rf node_modules package-lock.json
npm install
```

### 4. 后端启动失败
```bash
# 检查Java版本（需要JDK 8+）
java -version

# 清理Maven缓存
cd wtms-backend  
mvn clean
```

## 📞 技术支持

如果在启动过程中遇到问题，请：

1. 查看控制台错误信息
2. 检查Docker容器状态
3. 查看应用日志文件
4. 参考项目文档

---

## 🎉 恭喜！

如果您看到登录页面并能成功登录，说明WTMS基础架构已经搭建完成！

接下来可以开始开发具体的业务功能模块。项目采用现代化的技术栈，具有良好的扩展性和维护性。

**Happy Coding! 🚀**
