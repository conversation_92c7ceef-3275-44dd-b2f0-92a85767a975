package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务评价实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("task_evaluations")
@Schema(description = "任务评价实体")
public class TaskEvaluation implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "评价ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "任务ID")
    @TableField("task_id")
    private String taskId;

    @Schema(description = "评价者ID")
    @TableField("evaluator_id")
    private String evaluatorId;

    @Schema(description = "被评价者ID")
    @TableField("evaluatee_id")
    private String evaluateeId;

    @Schema(description = "评价类型")
    @TableField("evaluation_type")
    private String evaluationType;

    @Schema(description = "评价阶段")
    @TableField("evaluation_stage")
    private String evaluationStage;

    @Schema(description = "总体评分")
    @TableField("overall_score")
    private BigDecimal overallScore;

    @Schema(description = "质量评分")
    @TableField("quality_score")
    private BigDecimal qualityScore;

    @Schema(description = "效率评分")
    @TableField("efficiency_score")
    private BigDecimal efficiencyScore;

    @Schema(description = "沟通评分")
    @TableField("communication_score")
    private BigDecimal communicationScore;

    @Schema(description = "创新评分")
    @TableField("innovation_score")
    private BigDecimal innovationScore;

    @Schema(description = "团队协作评分")
    @TableField("teamwork_score")
    private BigDecimal teamworkScore;

    @Schema(description = "评价内容")
    @TableField("evaluation_content")
    private String evaluationContent;

    @Schema(description = "优点描述")
    @TableField("strengths")
    private String strengths;

    @Schema(description = "改进建议")
    @TableField("improvements")
    private String improvements;

    @Schema(description = "评价标签")
    @TableField("evaluation_tags")
    private String evaluationTags;

    @Schema(description = "是否匿名")
    @TableField("is_anonymous")
    private Boolean isAnonymous;

    @Schema(description = "是否公开")
    @TableField("is_public")
    private Boolean isPublic;

    @Schema(description = "评价状态")
    @TableField("status")
    private String status;

    @Schema(description = "权重")
    @TableField("weight")
    private BigDecimal weight;

    @Schema(description = "评价截止时间")
    @TableField("deadline")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deadline;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "任务信息")
    @TableField(exist = false)
    private Task task;

    @Schema(description = "评价者信息")
    @TableField(exist = false)
    private User evaluator;

    @Schema(description = "被评价者信息")
    @TableField(exist = false)
    private User evaluatee;

    @Schema(description = "评价维度列表")
    @TableField(exist = false)
    private List<EvaluationDimension> dimensions;

    @Schema(description = "评价附件列表")
    @TableField(exist = false)
    private List<TaskAttachment> attachments;

    /**
     * 评价类型枚举
     */
    public enum EvaluationType {
        SELF("self", "自评"),
        PEER("peer", "同事评价"),
        SUPERVISOR("supervisor", "上级评价"),
        SUBORDINATE("subordinate", "下级评价"),
        CUSTOMER("customer", "客户评价"),
        SYSTEM("system", "系统评价");

        private final String code;
        private final String description;

        EvaluationType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static EvaluationType fromCode(String code) {
            for (EvaluationType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return SELF;
        }
    }

    /**
     * 评价阶段枚举
     */
    public enum EvaluationStage {
        PLANNING("planning", "计划阶段"),
        EXECUTION("execution", "执行阶段"),
        COMPLETION("completion", "完成阶段"),
        REVIEW("review", "回顾阶段"),
        FINAL("final", "最终评价");

        private final String code;
        private final String description;

        EvaluationStage(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static EvaluationStage fromCode(String code) {
            for (EvaluationStage stage : values()) {
                if (stage.code.equals(code)) {
                    return stage;
                }
            }
            return FINAL;
        }
    }

    /**
     * 评价状态枚举
     */
    public enum Status {
        DRAFT("draft", "草稿"),
        SUBMITTED("submitted", "已提交"),
        REVIEWED("reviewed", "已审核"),
        PUBLISHED("published", "已发布"),
        ARCHIVED("archived", "已归档"),
        REJECTED("rejected", "已拒绝");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return DRAFT;
        }
    }

    /**
     * 检查是否匿名
     */
    public boolean isAnonymous() {
        return Boolean.TRUE.equals(this.isAnonymous);
    }

    /**
     * 检查是否公开
     */
    public boolean isPublic() {
        return Boolean.TRUE.equals(this.isPublic);
    }

    /**
     * 检查是否已提交
     */
    public boolean isSubmitted() {
        return Status.SUBMITTED.getCode().equals(this.status) ||
               Status.REVIEWED.getCode().equals(this.status) ||
               Status.PUBLISHED.getCode().equals(this.status);
    }

    /**
     * 检查是否已发布
     */
    public boolean isPublished() {
        return Status.PUBLISHED.getCode().equals(this.status);
    }

    /**
     * 检查是否超期
     */
    public boolean isOverdue() {
        return deadline != null && LocalDateTime.now().isAfter(deadline) && !isSubmitted();
    }

    /**
     * 获取评价类型描述
     */
    public String getEvaluationTypeText() {
        return EvaluationType.fromCode(this.evaluationType).getDescription();
    }

    /**
     * 获取评价阶段描述
     */
    public String getEvaluationStageText() {
        return EvaluationStage.fromCode(this.evaluationStage).getDescription();
    }

    /**
     * 获取状态描述
     */
    public String getStatusText() {
        return Status.fromCode(this.status).getDescription();
    }

    /**
     * 计算加权总分
     */
    public BigDecimal calculateWeightedScore() {
        if (overallScore == null || weight == null) {
            return overallScore;
        }
        return overallScore.multiply(weight);
    }

    /**
     * 获取评分等级
     */
    public String getScoreGrade() {
        if (overallScore == null) {
            return "未评分";
        }
        
        BigDecimal score = overallScore;
        if (score.compareTo(new BigDecimal("90")) >= 0) {
            return "优秀";
        } else if (score.compareTo(new BigDecimal("80")) >= 0) {
            return "良好";
        } else if (score.compareTo(new BigDecimal("70")) >= 0) {
            return "中等";
        } else if (score.compareTo(new BigDecimal("60")) >= 0) {
            return "及格";
        } else {
            return "不及格";
        }
    }

    /**
     * 检查评价是否完整
     */
    public boolean isComplete() {
        return overallScore != null && 
               qualityScore != null && 
               efficiencyScore != null && 
               evaluationContent != null && 
               !evaluationContent.trim().isEmpty();
    }
}
