#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pymysql

# 数据库配置
config = {
    'host': 'localhost',
    'port': 3308,
    'user': 'root',
    'password': 'ankaixin.docker.mysql',
    'database': 'wtms',
    'charset': 'utf8mb4'
}

try:
    connection = pymysql.connect(**config)
    cursor = connection.cursor()
    
    print('🔍 1. 检查admin用户基本信息...')
    cursor.execute('''
        SELECT id, username, password, email, status, enabled, 
               created_at, updated_at, last_login_at
        FROM users 
        WHERE username = 'admin'
    ''')
    
    user_info = cursor.fetchone()
    if user_info:
        print('✅ admin用户存在:')
        print(f'  ID: {user_info[0]}')
        print(f'  用户名: {user_info[1]}')
        print(f'  密码哈希: {user_info[2][:50]}...')
        print(f'  邮箱: {user_info[3]}')
        print(f'  状态: {user_info[4]}')
        print(f'  启用状态: {user_info[5]}')
        print(f'  创建时间: {user_info[6]}')
        print(f'  更新时间: {user_info[7]}')
        print(f'  最后登录: {user_info[8]}')
        
        user_id = user_info[0]
        
        print('\n🔍 2. 检查admin用户角色分配...')
        cursor.execute('''
            SELECT ur.id, ur.user_id, ur.role_id, ur.status, ur.assigned_at, ur.expires_at,
                   r.name as role_name, r.code as role_code, r.status as role_status
            FROM user_roles ur
            JOIN roles r ON ur.role_id = r.id
            WHERE ur.user_id = %s
        ''', (user_id,))
        
        roles = cursor.fetchall()
        if roles:
            print(f'✅ admin用户已分配 {len(roles)} 个角色:')
            for role in roles:
                print(f'  角色: {role[6]} ({role[7]})')
                print(f'    分配状态: {role[3]}')
                print(f'    角色状态: {role[8]}')
                print(f'    分配时间: {role[4]}')
                print(f'    过期时间: {role[5]}')
                print('  ---')
        else:
            print('❌ admin用户未分配任何角色!')
        
        print('\n🔍 3. 检查SUPER_ADMIN角色...')
        cursor.execute('''
            SELECT id, name, code, description, status, created_at
            FROM roles 
            WHERE code = 'SUPER_ADMIN'
        ''')
        
        role_info = cursor.fetchone()
        if role_info:
            print('✅ SUPER_ADMIN角色存在:')
            print(f'  ID: {role_info[0]}')
            print(f'  名称: {role_info[1]}')
            print(f'  代码: {role_info[2]}')
            print(f'  描述: {role_info[3]}')
            print(f'  状态: {role_info[4]}')
            print(f'  创建时间: {role_info[5]}')
        else:
            print('❌ SUPER_ADMIN角色不存在!')
            
    else:
        print('❌ 未找到admin用户!')
    
    connection.close()
    
except Exception as e:
    print(f'❌ 数据库操作失败: {e}')
