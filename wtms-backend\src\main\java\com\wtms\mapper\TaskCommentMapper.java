package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.entity.TaskComment;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 * 任务评论Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface TaskCommentMapper extends BaseMapper<TaskComment> {

    /**
     * 根据任务ID查询评论（树形结构）
     */
    List<TaskComment> selectCommentsByTaskId(@Param("taskId") String taskId);

    /**
     * 根据任务ID查询评论（分页）
     */
    IPage<TaskComment> selectCommentsByTaskIdWithPage(Page<TaskComment> page, @Param("taskId") String taskId, 
                                                     @Param("commentType") String commentType, 
                                                     @Param("status") String status);

    /**
     * 根据父评论ID查询子评论
     */
    @Select("SELECT * FROM task_comments WHERE parent_id = #{parentId} AND deleted_at IS NULL ORDER BY created_at ASC")
    List<TaskComment> selectByParentId(@Param("parentId") String parentId);

    /**
     * 查询根评论
     */
    @Select("SELECT * FROM task_comments WHERE task_id = #{taskId} AND parent_id IS NULL AND deleted_at IS NULL ORDER BY is_pinned DESC, created_at DESC")
    List<TaskComment> selectRootComments(@Param("taskId") String taskId);

    /**
     * 根据评论者ID查询评论
     */
    IPage<TaskComment> selectCommentsByCommenterId(Page<TaskComment> page, @Param("commenterId") String commenterId, 
                                                  @Param("status") String status);

    /**
     * 根据评论类型查询评论
     */
    @Select("SELECT * FROM task_comments WHERE task_id = #{taskId} AND comment_type = #{commentType} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskComment> selectByTaskIdAndType(@Param("taskId") String taskId, @Param("commentType") String commentType);

    /**
     * 查询置顶评论
     */
    @Select("SELECT * FROM task_comments WHERE task_id = #{taskId} AND is_pinned = TRUE AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskComment> selectPinnedComments(@Param("taskId") String taskId);

    /**
     * 查询私有评论
     */
    @Select("SELECT * FROM task_comments WHERE task_id = #{taskId} AND is_private = TRUE AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskComment> selectPrivateComments(@Param("taskId") String taskId);

    /**
     * 统计任务评论数量
     */
    @Select("SELECT COUNT(*) FROM task_comments WHERE task_id = #{taskId} AND deleted_at IS NULL")
    int countByTaskId(@Param("taskId") String taskId);

    /**
     * 统计用户评论数量
     */
    @Select("SELECT COUNT(*) FROM task_comments WHERE commenter_id = #{commenterId} AND deleted_at IS NULL")
    int countByCommenterId(@Param("commenterId") String commenterId);

    /**
     * 统计子评论数量
     */
    @Select("SELECT COUNT(*) FROM task_comments WHERE parent_id = #{parentId} AND deleted_at IS NULL")
    int countChildren(@Param("parentId") String parentId);

    /**
     * 更新回复数量
     */
    @Update("UPDATE task_comments SET reply_count = #{replyCount}, updated_at = NOW() WHERE id = #{commentId}")
    int updateReplyCount(@Param("commentId") String commentId, @Param("replyCount") Integer replyCount);

    /**
     * 更新点赞数量
     */
    @Update("UPDATE task_comments SET like_count = #{likeCount}, updated_at = NOW() WHERE id = #{commentId}")
    int updateLikeCount(@Param("commentId") String commentId, @Param("likeCount") Integer likeCount);

    /**
     * 置顶评论
     */
    @Update("UPDATE task_comments SET is_pinned = TRUE, updated_at = NOW() WHERE id = #{commentId}")
    int pinComment(@Param("commentId") String commentId);

    /**
     * 取消置顶评论
     */
    @Update("UPDATE task_comments SET is_pinned = FALSE, updated_at = NOW() WHERE id = #{commentId}")
    int unpinComment(@Param("commentId") String commentId);

    /**
     * 隐藏评论
     */
    @Update("UPDATE task_comments SET status = 'hidden', updated_at = NOW() WHERE id = #{commentId}")
    int hideComment(@Param("commentId") String commentId);

    /**
     * 显示评论
     */
    @Update("UPDATE task_comments SET status = 'active', updated_at = NOW() WHERE id = #{commentId}")
    int showComment(@Param("commentId") String commentId);

    /**
     * 批量删除评论
     */
    int batchDeleteComments(@Param("commentIds") List<String> commentIds);

    /**
     * 批量更新评论状态
     */
    int batchUpdateStatus(@Param("commentIds") List<String> commentIds, @Param("status") String status);

    /**
     * 查询最新评论
     */
    @Select("SELECT * FROM task_comments WHERE task_id = #{taskId} AND deleted_at IS NULL ORDER BY created_at DESC LIMIT #{limit}")
    List<TaskComment> selectLatestComments(@Param("taskId") String taskId, @Param("limit") Integer limit);

    /**
     * 查询热门评论（按点赞数排序）
     */
    @Select("SELECT * FROM task_comments WHERE task_id = #{taskId} AND deleted_at IS NULL ORDER BY like_count DESC, created_at DESC LIMIT #{limit}")
    List<TaskComment> selectPopularComments(@Param("taskId") String taskId, @Param("limit") Integer limit);

    /**
     * 搜索评论
     */
    IPage<TaskComment> searchComments(Page<TaskComment> page, @Param("taskId") String taskId, 
                                     @Param("keyword") String keyword, @Param("commenterId") String commenterId,
                                     @Param("commentType") String commentType, @Param("status") String status);

    /**
     * 查询用户在任务中的评论
     */
    @Select("SELECT * FROM task_comments WHERE task_id = #{taskId} AND commenter_id = #{commenterId} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<TaskComment> selectUserCommentsInTask(@Param("taskId") String taskId, @Param("commenterId") String commenterId);

    /**
     * 查询评论统计信息
     */
    Object selectCommentStatistics(@Param("taskId") String taskId);

    /**
     * 查询评论活跃度统计
     */
    List<Object> selectCommentActivityStats(@Param("taskId") String taskId, @Param("days") Integer days);
}
