package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {

    /**
     * 根据用户名查询用户（包含角色信息）
     */
    @Select("SELECT u.*, r.id as role_id, r.name as role_name, r.code as role_code, " +
            "r.permissions as role_permissions, d.id as dept_id, d.name as dept_name " +
            "FROM users u " +
            "LEFT JOIN roles r ON u.role_id = r.id " +
            "LEFT JOIN departments d ON u.department_id = d.id " +
            "WHERE u.username = #{username} AND u.deleted_at IS NULL")
    User findByUsernameWithRole(@Param("username") String username);

    /**
     * 根据用户ID查询用户（包含角色信息）
     */
    @Select("SELECT u.*, r.id as role_id, r.name as role_name, r.code as role_code, " +
            "r.permissions as role_permissions, d.id as dept_id, d.name as dept_name " +
            "FROM users u " +
            "LEFT JOIN roles r ON u.role_id = r.id " +
            "LEFT JOIN departments d ON u.department_id = d.id " +
            "WHERE u.id = #{userId} AND u.deleted_at IS NULL")
    User findByIdWithRole(@Param("userId") String userId);

    /**
     * 根据邮箱查询用户
     */
    @Select("SELECT * FROM users WHERE email = #{email} AND deleted_at IS NULL")
    User findByEmail(@Param("email") String email);

    /**
     * 根据工号查询用户
     */
    @Select("SELECT * FROM users WHERE employee_id = #{employeeId} AND deleted_at IS NULL")
    User findByEmployeeId(@Param("employeeId") String employeeId);

    /**
     * 检查用户名是否存在
     */
    @Select("SELECT COUNT(*) FROM users WHERE username = #{username} AND deleted_at IS NULL")
    int countByUsername(@Param("username") String username);

    /**
     * 检查邮箱是否存在
     */
    @Select("SELECT COUNT(*) FROM users WHERE email = #{email} AND deleted_at IS NULL")
    int countByEmail(@Param("email") String email);

    /**
     * 检查工号是否存在
     */
    @Select("SELECT COUNT(*) FROM users WHERE employee_id = #{employeeId} AND deleted_at IS NULL")
    int countByEmployeeId(@Param("employeeId") String employeeId);

    /**
     * 更新用户最后登录时间
     */
    @Update("UPDATE users SET last_login_at = #{loginTime}, login_count = login_count + 1 " +
            "WHERE id = #{userId}")
    int updateLastLoginTime(@Param("userId") String userId, @Param("loginTime") LocalDateTime loginTime);

    /**
     * 更新用户密码
     */
    @Update("UPDATE users SET password_hash = #{passwordHash}, salt = #{salt}, updated_at = NOW() " +
            "WHERE id = #{userId}")
    int updatePassword(@Param("userId") String userId, 
                      @Param("passwordHash") String passwordHash, 
                      @Param("salt") String salt);

    /**
     * 更新用户状态
     */
    @Update("UPDATE users SET status = #{status}, updated_at = NOW() WHERE id = #{userId}")
    int updateStatus(@Param("userId") String userId, @Param("status") String status);

    /**
     * 分页查询用户列表（包含部门和角色信息）
     */
    IPage<User> selectUserPageWithDeptAndRole(Page<User> page, 
                                             @Param("search") String search,
                                             @Param("departmentId") String departmentId,
                                             @Param("roleId") String roleId,
                                             @Param("status") String status);

    /**
     * 根据部门ID查询用户列表
     */
    @Select("SELECT * FROM users WHERE department_id = #{departmentId} AND deleted_at IS NULL")
    List<User> findByDepartmentId(@Param("departmentId") String departmentId);

    /**
     * 根据角色ID查询用户列表
     */
    @Select("SELECT * FROM users WHERE role_id = #{roleId} AND deleted_at IS NULL")
    List<User> findByRoleId(@Param("roleId") String roleId);

    /**
     * 获取用户统计信息
     */
    @Select("SELECT " +
            "COUNT(*) as total, " +
            "SUM(CASE WHEN status = 'active' THEN 1 ELSE 0 END) as active, " +
            "SUM(CASE WHEN status = 'inactive' THEN 1 ELSE 0 END) as inactive, " +
            "SUM(CASE WHEN status = 'locked' THEN 1 ELSE 0 END) as locked " +
            "FROM users WHERE deleted_at IS NULL")
    UserStatistics getUserStatistics();

    /**
     * 用户统计信息内部类
     */
    class UserStatistics {
        private Long total;
        private Long active;
        private Long inactive;
        private Long locked;

        // getters and setters
        public Long getTotal() { return total; }
        public void setTotal(Long total) { this.total = total; }
        public Long getActive() { return active; }
        public void setActive(Long active) { this.active = active; }
        public Long getInactive() { return inactive; }
        public void setInactive(Long inactive) { this.inactive = inactive; }
        public Long getLocked() { return locked; }
        public void setLocked(Long locked) { this.locked = locked; }
    }
}
