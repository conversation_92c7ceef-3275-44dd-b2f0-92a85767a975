package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("tasks")
@Schema(description = "任务实体")
public class Task implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "任务ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "任务编号")
    @TableField("task_code")
    private String taskCode;

    @Schema(description = "任务标题")
    @TableField("title")
    private String title;

    @Schema(description = "任务描述")
    @TableField("description")
    private String description;

    @Schema(description = "任务分类ID")
    @TableField("category_id")
    private String categoryId;

    @Schema(description = "任务状态")
    @TableField("status")
    private String status;

    @Schema(description = "优先级")
    @TableField("priority")
    private Integer priority;

    @Schema(description = "难度等级")
    @TableField("difficulty_level")
    private Integer difficultyLevel;

    @Schema(description = "预估工时")
    @TableField("estimated_hours")
    private BigDecimal estimatedHours;

    @Schema(description = "实际工时")
    @TableField("actual_hours")
    private BigDecimal actualHours;

    @Schema(description = "进度百分比")
    @TableField("progress")
    private BigDecimal progress;

    @Schema(description = "创建者ID")
    @TableField("creator_id")
    private String creatorId;

    @Schema(description = "负责人ID")
    @TableField("assignee_id")
    private String assigneeId;

    @Schema(description = "审核人ID")
    @TableField("reviewer_id")
    private String reviewerId;

    @Schema(description = "父任务ID")
    @TableField("parent_id")
    private String parentId;

    @Schema(description = "项目ID")
    @TableField("project_id")
    private String projectId;

    @Schema(description = "计划开始时间")
    @TableField("planned_start_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedStartDate;

    @Schema(description = "计划结束时间")
    @TableField("planned_end_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedEndDate;

    @Schema(description = "实际开始时间")
    @TableField("actual_start_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualStartDate;

    @Schema(description = "实际结束时间")
    @TableField("actual_end_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualEndDate;

    @Schema(description = "标签")
    @TableField("tags")
    private String tags;

    @Schema(description = "附件信息")
    @TableField("attachments")
    private String attachments;

    @Schema(description = "自定义字段")
    @TableField("custom_fields")
    private String customFields;

    @Schema(description = "是否已归档")
    @TableField("is_archived")
    private Boolean isArchived;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "任务分类")
    @TableField(exist = false)
    private TaskCategory category;

    @Schema(description = "创建者")
    @TableField(exist = false)
    private User creator;

    @Schema(description = "负责人")
    @TableField(exist = false)
    private User assignee;

    @Schema(description = "审核人")
    @TableField(exist = false)
    private User reviewer;

    @Schema(description = "父任务")
    @TableField(exist = false)
    private Task parent;

    @Schema(description = "子任务列表")
    @TableField(exist = false)
    private List<Task> children;

    @Schema(description = "标签列表")
    @TableField(exist = false)
    private List<String> tagList;

    /**
     * 任务状态枚举
     */
    public enum Status {
        DRAFT("draft", "草稿"),
        PENDING("pending", "待开始"),
        IN_PROGRESS("in_progress", "进行中"),
        REVIEW("review", "待审核"),
        TESTING("testing", "测试中"),
        COMPLETED("completed", "已完成"),
        PAUSED("paused", "已暂停"),
        CANCELLED("cancelled", "已取消"),
        ARCHIVED("archived", "已归档");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return DRAFT;
        }
    }

    /**
     * 优先级枚举
     */
    public enum Priority {
        LOWEST(1, "最低"),
        LOW(2, "低"),
        MEDIUM(3, "中"),
        HIGH(4, "高"),
        HIGHEST(5, "最高");

        private final Integer level;
        private final String description;

        Priority(Integer level, String description) {
            this.level = level;
            this.description = description;
        }

        public Integer getLevel() {
            return level;
        }

        public String getDescription() {
            return description;
        }

        public static Priority fromLevel(Integer level) {
            for (Priority priority : values()) {
                if (priority.level.equals(level)) {
                    return priority;
                }
            }
            return MEDIUM;
        }
    }

    /**
     * 检查任务是否可以编辑
     */
    public boolean isEditable() {
        return !Status.COMPLETED.getCode().equals(this.status) &&
               !Status.CANCELLED.getCode().equals(this.status) &&
               !Status.ARCHIVED.getCode().equals(this.status);
    }

    /**
     * 检查任务是否已完成
     */
    public boolean isCompleted() {
        return Status.COMPLETED.getCode().equals(this.status);
    }

    /**
     * 检查任务是否进行中
     */
    public boolean isInProgress() {
        return Status.IN_PROGRESS.getCode().equals(this.status);
    }

    /**
     * 检查任务是否已归档
     */
    public boolean isArchived() {
        return Boolean.TRUE.equals(this.isArchived) || Status.ARCHIVED.getCode().equals(this.status);
    }
}
