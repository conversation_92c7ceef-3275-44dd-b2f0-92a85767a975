package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作流历史实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("workflow_histories")
@Schema(description = "工作流历史实体")
public class WorkflowHistory implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "历史ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "工作流实例ID")
    @TableField("workflow_instance_id")
    private String workflowInstanceId;

    @Schema(description = "工作流任务ID")
    @TableField("workflow_task_id")
    private String workflowTaskId;

    @Schema(description = "工作流节点ID")
    @TableField("workflow_node_id")
    private String workflowNodeId;

    @Schema(description = "历史类型")
    @TableField("history_type")
    private String historyType;

    @Schema(description = "操作类型")
    @TableField("action_type")
    private String actionType;

    @Schema(description = "操作者ID")
    @TableField("actor_id")
    private String actorId;

    @Schema(description = "操作时间")
    @TableField("action_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actionTime;

    @Schema(description = "操作描述")
    @TableField("description")
    private String description;

    @Schema(description = "操作结果")
    @TableField("result")
    private String result;

    @Schema(description = "操作前状态")
    @TableField("before_status")
    private String beforeStatus;

    @Schema(description = "操作后状态")
    @TableField("after_status")
    private String afterStatus;

    @Schema(description = "操作变量JSON")
    @TableField("variables_json")
    private String variablesJson;

    @Schema(description = "操作备注")
    @TableField("comment")
    private String comment;

    @Schema(description = "执行时长（毫秒）")
    @TableField("duration")
    private Long duration;

    @Schema(description = "错误信息")
    @TableField("error_message")
    private String errorMessage;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    // 非数据库字段
    @Schema(description = "工作流实例信息")
    @TableField(exist = false)
    private WorkflowInstance workflowInstance;

    @Schema(description = "工作流任务信息")
    @TableField(exist = false)
    private WorkflowTask workflowTask;

    @Schema(description = "工作流节点信息")
    @TableField(exist = false)
    private WorkflowNode workflowNode;

    @Schema(description = "操作者信息")
    @TableField(exist = false)
    private User actor;

    /**
     * 历史类型枚举
     */
    public enum HistoryType {
        PROCESS_INSTANCE("process_instance", "流程实例"),
        ACTIVITY_INSTANCE("activity_instance", "活动实例"),
        TASK_INSTANCE("task_instance", "任务实例"),
        VARIABLE_INSTANCE("variable_instance", "变量实例"),
        INCIDENT("incident", "事件"),
        JOB_LOG("job_log", "作业日志"),
        EXTERNAL_TASK_LOG("external_task_log", "外部任务日志"),
        USER_OPERATION_LOG("user_operation_log", "用户操作日志"),
        IDENTITY_LINK_LOG("identity_link_log", "身份链接日志"),
        HISTORIC_DETAIL("historic_detail", "历史详情");

        private final String code;
        private final String description;

        HistoryType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static HistoryType fromCode(String code) {
            for (HistoryType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return PROCESS_INSTANCE;
        }
    }

    /**
     * 操作类型枚举
     */
    public enum ActionType {
        START("start", "启动"),
        COMPLETE("complete", "完成"),
        CLAIM("claim", "认领"),
        UNCLAIM("unclaim", "取消认领"),
        DELEGATE("delegate", "委托"),
        RESOLVE("resolve", "解决"),
        ASSIGN("assign", "分配"),
        SUSPEND("suspend", "暂停"),
        RESUME("resume", "恢复"),
        TERMINATE("terminate", "终止"),
        CANCEL("cancel", "取消"),
        DELETE("delete", "删除"),
        UPDATE("update", "更新"),
        CREATE("create", "创建"),
        MIGRATE("migrate", "迁移"),
        MODIFY("modify", "修改");

        private final String code;
        private final String description;

        ActionType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static ActionType fromCode(String code) {
            for (ActionType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return UPDATE;
        }
    }

    /**
     * 操作结果枚举
     */
    public enum Result {
        SUCCESS("success", "成功"),
        FAILED("failed", "失败"),
        ERROR("error", "错误"),
        TIMEOUT("timeout", "超时"),
        CANCELLED("cancelled", "已取消"),
        SKIPPED("skipped", "已跳过");

        private final String code;
        private final String description;

        Result(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Result fromCode(String code) {
            for (Result result : values()) {
                if (result.code.equals(code)) {
                    return result;
                }
            }
            return SUCCESS;
        }
    }

    /**
     * 获取历史类型描述
     */
    public String getHistoryTypeText() {
        return HistoryType.fromCode(this.historyType).getDescription();
    }

    /**
     * 获取操作类型描述
     */
    public String getActionTypeText() {
        return ActionType.fromCode(this.actionType).getDescription();
    }

    /**
     * 获取操作结果描述
     */
    public String getResultText() {
        return Result.fromCode(this.result).getDescription();
    }

    /**
     * 检查操作是否成功
     */
    public boolean isSuccess() {
        return Result.SUCCESS.getCode().equals(this.result);
    }

    /**
     * 检查操作是否失败
     */
    public boolean isFailed() {
        return Result.FAILED.getCode().equals(this.result) || Result.ERROR.getCode().equals(this.result);
    }

    /**
     * 检查是否有错误信息
     */
    public boolean hasError() {
        return errorMessage != null && !errorMessage.trim().isEmpty();
    }

    /**
     * 获取格式化的执行时长
     */
    public String getFormattedDuration() {
        if (duration == null) {
            return "未知";
        }
        
        if (duration < 1000) {
            return duration + "ms";
        } else if (duration < 60000) {
            return String.format("%.1fs", duration / 1000.0);
        } else if (duration < 3600000) {
            return String.format("%.1fm", duration / 60000.0);
        } else {
            return String.format("%.1fh", duration / 3600000.0);
        }
    }
}
