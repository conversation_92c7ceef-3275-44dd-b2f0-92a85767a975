package com.wtms.controller;

import com.wtms.common.result.Result;
import com.wtms.entity.TaskCategory;
import com.wtms.service.TaskCategoryService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 任务分类管理控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/task-categories")
@Tag(name = "任务分类管理", description = "任务分类管理相关接口")
public class TaskCategoryController {

    @Autowired
    private TaskCategoryService taskCategoryService;

    @GetMapping
    @Operation(summary = "获取任务分类列表", description = "获取所有启用的任务分类")
    @PreAuthorize("hasAuthority('task:view') or hasRole('USER')")
    public Result<List<TaskCategory>> getTaskCategories() {
        List<TaskCategory> categories = taskCategoryService.getEnabledCategories();
        return Result.success(categories);
    }

    @GetMapping("/{categoryId}")
    @Operation(summary = "获取任务分类详情", description = "根据ID获取任务分类详情")
    @PreAuthorize("hasAuthority('task:view') or hasRole('USER')")
    public Result<TaskCategory> getTaskCategoryById(
            @Parameter(description = "分类ID", required = true)
            @PathVariable @NotBlank(message = "分类ID不能为空") String categoryId) {
        
        TaskCategory category = taskCategoryService.getCategoryById(categoryId);
        return Result.success(category);
    }

    @PostMapping
    @Operation(summary = "创建任务分类", description = "创建新的任务分类")
    @PreAuthorize("hasAuthority('task_category:create') or hasRole('ADMIN')")
    public Result<TaskCategory> createTaskCategory(@Valid @RequestBody TaskCategory category) {
        log.info("Creating task category: {}", category.getName());
        
        TaskCategory created = taskCategoryService.createCategory(category);
        return Result.success("任务分类创建成功", created);
    }

    @PutMapping("/{categoryId}")
    @Operation(summary = "更新任务分类", description = "更新任务分类信息")
    @PreAuthorize("hasAuthority('task_category:update') or hasRole('ADMIN')")
    public Result<TaskCategory> updateTaskCategory(
            @Parameter(description = "分类ID", required = true)
            @PathVariable @NotBlank(message = "分类ID不能为空") String categoryId,
            @Valid @RequestBody TaskCategory category) {
        
        log.info("Updating task category: {}", categoryId);
        
        TaskCategory updated = taskCategoryService.updateCategory(categoryId, category);
        return Result.success("任务分类更新成功", updated);
    }

    @DeleteMapping("/{categoryId}")
    @Operation(summary = "删除任务分类", description = "删除指定任务分类")
    @PreAuthorize("hasAuthority('task_category:delete') or hasRole('ADMIN')")
    public Result<String> deleteTaskCategory(
            @Parameter(description = "分类ID", required = true)
            @PathVariable @NotBlank(message = "分类ID不能为空") String categoryId) {
        
        log.info("Deleting task category: {}", categoryId);
        
        taskCategoryService.deleteCategory(categoryId);
        return Result.success("任务分类删除成功");
    }

    @PutMapping("/{categoryId}/enable")
    @Operation(summary = "启用任务分类", description = "启用指定任务分类")
    @PreAuthorize("hasAuthority('task_category:update') or hasRole('ADMIN')")
    public Result<String> enableTaskCategory(
            @Parameter(description = "分类ID", required = true)
            @PathVariable @NotBlank(message = "分类ID不能为空") String categoryId) {
        
        log.info("Enabling task category: {}", categoryId);
        
        taskCategoryService.enableCategory(categoryId);
        return Result.success("任务分类已启用");
    }

    @PutMapping("/{categoryId}/disable")
    @Operation(summary = "禁用任务分类", description = "禁用指定任务分类")
    @PreAuthorize("hasAuthority('task_category:update') or hasRole('ADMIN')")
    public Result<String> disableTaskCategory(
            @Parameter(description = "分类ID", required = true)
            @PathVariable @NotBlank(message = "分类ID不能为空") String categoryId) {
        
        log.info("Disabling task category: {}", categoryId);
        
        taskCategoryService.disableCategory(categoryId);
        return Result.success("任务分类已禁用");
    }
}
