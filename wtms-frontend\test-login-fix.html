<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WTMS登录功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 30px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        button:hover {
            background: #2563eb;
        }
        .result {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            font-family: monospace;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .error {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .info {
            background-color: #d1ecf1;
            border: 1px solid #bee5eb;
            color: #0c5460;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-online { background-color: #10b981; }
        .status-offline { background-color: #ef4444; }
    </style>
</head>
<body>
    <h1>🔐 WTMS登录功能测试</h1>
    
    <div class="test-container">
        <h2>📊 服务状态</h2>
        <p><span id="frontend-status" class="status-indicator status-offline"></span>前端服务状态</p>
        <p><span id="backend-status" class="status-indicator status-offline"></span>后端服务状态</p>
        <button onclick="checkServices()">检查服务状态</button>
    </div>

    <div class="test-container">
        <h2>🧪 API连接测试</h2>
        <button onclick="testApiConnection()">测试API连接</button>
        <button onclick="testHealthCheck()">健康检查</button>
        <div id="api-results"></div>
    </div>

    <div class="test-container">
        <h2>🔑 登录功能测试</h2>
        <div class="form-group">
            <label for="username">用户名:</label>
            <input type="text" id="username" value="admin" placeholder="请输入用户名">
        </div>
        <div class="form-group">
            <label for="password">密码:</label>
            <input type="password" id="password" value="admin123456" placeholder="请输入密码">
        </div>
        <button onclick="testLogin()">测试登录</button>
        <button onclick="testInvalidLogin()">测试错误登录</button>
        <button onclick="testUserProfile()">获取用户信息</button>
        <div id="login-results"></div>
    </div>

    <div class="test-container">
        <h2>🔧 问题诊断</h2>
        <button onclick="diagnoseIssues()">诊断连接问题</button>
        <button onclick="showApiConfig()">显示API配置</button>
        <div id="diagnosis-results"></div>
    </div>

    <script>
        // API配置
        const API_BASE = 'http://localhost:55557/api/v1';
        const FRONTEND_URL = 'http://localhost:33335';
        let authToken = null;

        // 工具函数
        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            container.appendChild(div);
            container.scrollTop = container.scrollHeight;
        }

        function clearResults(containerId) {
            document.getElementById(containerId).innerHTML = '';
        }

        // 检查服务状态
        async function checkServices() {
            // 检查前端服务
            try {
                const response = await fetch(FRONTEND_URL);
                document.getElementById('frontend-status').className = 
                    'status-indicator ' + (response.ok ? 'status-online' : 'status-offline');
                addResult('api-results', `前端服务: ${response.ok ? '✅ 在线' : '❌ 离线'}`, 
                    response.ok ? 'success' : 'error');
            } catch (error) {
                document.getElementById('frontend-status').className = 'status-indicator status-offline';
                addResult('api-results', `前端服务: ❌ 离线 (${error.message})`, 'error');
            }

            // 检查后端服务
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                document.getElementById('backend-status').className = 
                    'status-indicator ' + (response.ok ? 'status-online' : 'status-offline');
                
                if (response.ok) {
                    addResult('api-results', `后端服务: ✅ 在线 (${data.data.service})`, 'success');
                } else {
                    addResult('api-results', `后端服务: ❌ 响应异常`, 'error');
                }
            } catch (error) {
                document.getElementById('backend-status').className = 'status-indicator status-offline';
                addResult('api-results', `后端服务: ❌ 离线 (${error.message})`, 'error');
            }
        }

        // 测试API连接
        async function testApiConnection() {
            clearResults('api-results');
            addResult('api-results', '🔍 测试API连接...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/info`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': FRONTEND_URL
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('api-results', `✅ API连接成功`, 'success');
                    addResult('api-results', `服务名称: ${data.data.name}`, 'info');
                    addResult('api-results', `服务版本: ${data.data.version}`, 'info');
                    addResult('api-results', `后端类型: ${data.data.backend}`, 'info');
                } else {
                    addResult('api-results', `❌ API响应异常: ${data.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                addResult('api-results', `❌ API连接失败: ${error.message}`, 'error');
            }
        }

        // 健康检查
        async function testHealthCheck() {
            addResult('api-results', '🔍 执行健康检查...', 'info');
            
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('api-results', `✅ 健康检查通过`, 'success');
                    addResult('api-results', `状态: ${data.data.status}`, 'info');
                    addResult('api-results', `数据库: ${data.data.database}`, 'info');
                    addResult('api-results', `缓存: ${data.data.cache}`, 'info');
                } else {
                    addResult('api-results', `❌ 健康检查失败: ${data.message}`, 'error');
                }
            } catch (error) {
                addResult('api-results', `❌ 健康检查错误: ${error.message}`, 'error');
            }
        }

        // 测试登录
        async function testLogin() {
            clearResults('login-results');
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            
            addResult('login-results', `🔍 测试登录: ${username}`, 'info');
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': FRONTEND_URL
                    },
                    body: JSON.stringify({ username, password })
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    authToken = data.data.token;
                    addResult('login-results', `✅ 登录成功!`, 'success');
                    addResult('login-results', `用户: ${data.data.user.fullName}`, 'info');
                    addResult('login-results', `角色: ${data.data.user.roles.join(', ')}`, 'info');
                    addResult('login-results', `权限数量: ${data.data.user.permissions.length}`, 'info');
                    addResult('login-results', `Token: ${authToken.substring(0, 30)}...`, 'info');
                } else {
                    addResult('login-results', `❌ 登录失败: ${data.message}`, 'error');
                    addResult('login-results', `状态码: ${response.status}`, 'error');
                }
            } catch (error) {
                addResult('login-results', `❌ 登录请求失败: ${error.message}`, 'error');
            }
        }

        // 测试错误登录
        async function testInvalidLogin() {
            addResult('login-results', `🔍 测试错误登录...`, 'info');
            
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Origin': FRONTEND_URL
                    },
                    body: JSON.stringify({ 
                        username: 'admin', 
                        password: 'wrongpassword' 
                    })
                });

                const data = await response.json();
                
                if (!response.ok || !data.success) {
                    addResult('login-results', `✅ 错误登录正确被拒绝`, 'success');
                    addResult('login-results', `错误信息: ${data.message}`, 'info');
                } else {
                    addResult('login-results', `❌ 错误登录应该被拒绝`, 'error');
                }
            } catch (error) {
                addResult('login-results', `❌ 错误登录测试失败: ${error.message}`, 'error');
            }
        }

        // 测试获取用户信息
        async function testUserProfile() {
            if (!authToken) {
                addResult('login-results', `⚠️ 请先登录获取Token`, 'error');
                return;
            }

            addResult('login-results', `🔍 获取用户信息...`, 'info');
            
            try {
                const response = await fetch(`${API_BASE}/user/profile`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${authToken}`,
                        'Origin': FRONTEND_URL
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.success) {
                    addResult('login-results', `✅ 获取用户信息成功`, 'success');
                    addResult('login-results', `用户ID: ${data.data.id}`, 'info');
                    addResult('login-results', `用户名: ${data.data.username}`, 'info');
                    addResult('login-results', `邮箱: ${data.data.email}`, 'info');
                    addResult('login-results', `状态: ${data.data.status}`, 'info');
                } else {
                    addResult('login-results', `❌ 获取用户信息失败: ${data.message}`, 'error');
                }
            } catch (error) {
                addResult('login-results', `❌ 用户信息请求失败: ${error.message}`, 'error');
            }
        }

        // 诊断问题
        async function diagnoseIssues() {
            clearResults('diagnosis-results');
            addResult('diagnosis-results', '🔧 开始诊断连接问题...', 'info');
            
            // 1. 检查CORS
            addResult('diagnosis-results', '检查CORS配置...', 'info');
            try {
                const response = await fetch(`${API_BASE}/health`, {
                    method: 'OPTIONS'
                });
                addResult('diagnosis-results', `CORS预检请求: ${response.ok ? '✅ 通过' : '❌ 失败'}`, 
                    response.ok ? 'success' : 'error');
            } catch (error) {
                addResult('diagnosis-results', `CORS预检请求失败: ${error.message}`, 'error');
            }

            // 2. 检查网络连接
            addResult('diagnosis-results', '检查网络连接...', 'info');
            try {
                const start = Date.now();
                const response = await fetch(`${API_BASE}/health`);
                const duration = Date.now() - start;
                addResult('diagnosis-results', `网络延迟: ${duration}ms`, 'info');
                addResult('diagnosis-results', `响应状态: ${response.status} ${response.statusText}`, 'info');
            } catch (error) {
                addResult('diagnosis-results', `网络连接失败: ${error.message}`, 'error');
            }

            // 3. 检查API端点
            const endpoints = ['/health', '/info', '/auth/login'];
            for (const endpoint of endpoints) {
                try {
                    const response = await fetch(`${API_BASE}${endpoint}`, {
                        method: endpoint === '/auth/login' ? 'POST' : 'GET',
                        headers: { 'Content-Type': 'application/json' },
                        body: endpoint === '/auth/login' ? 
                            JSON.stringify({ username: 'test', password: 'test' }) : undefined
                    });
                    addResult('diagnosis-results', 
                        `端点 ${endpoint}: ${response.status} ${response.statusText}`, 
                        response.status < 500 ? 'success' : 'error');
                } catch (error) {
                    addResult('diagnosis-results', `端点 ${endpoint}: 连接失败`, 'error');
                }
            }
        }

        // 显示API配置
        function showApiConfig() {
            clearResults('diagnosis-results');
            addResult('diagnosis-results', '📋 当前API配置:', 'info');
            addResult('diagnosis-results', `API基础地址: ${API_BASE}`, 'info');
            addResult('diagnosis-results', `前端地址: ${FRONTEND_URL}`, 'info');
            addResult('diagnosis-results', `登录端点: ${API_BASE}/auth/login`, 'info');
            addResult('diagnosis-results', `用户信息端点: ${API_BASE}/user/profile`, 'info');
            addResult('diagnosis-results', `健康检查端点: ${API_BASE}/health`, 'info');
        }

        // 页面加载时自动检查服务状态
        window.onload = function() {
            checkServices();
        };
    </script>
</body>
</html>
