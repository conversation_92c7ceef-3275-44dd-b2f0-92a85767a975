<template>
  <el-dialog
    v-model="visible"
    :title="mode === 'create' ? '新建任务' : '编辑任务'"
    width="800px"
    @close="handleClose"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="100px"
      v-loading="loading"
    >
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="任务标题" prop="title">
            <el-input
              v-model="form.title"
              placeholder="请输入任务标题"
              maxlength="200"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="任务分类" prop="categoryId">
            <el-select
              v-model="form.categoryId"
              placeholder="请选择任务分类"
              style="width: 100%"
            >
              <el-option
                v-for="category in taskCategories"
                :key="category.id"
                :label="category.name"
                :value="category.id"
              >
                <div class="category-option">
                  <el-tag :color="category.color" size="small">
                    {{ category.name }}
                  </el-tag>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="任务描述">
        <el-input
          v-model="form.description"
          type="textarea"
          :rows="4"
          placeholder="请输入任务描述"
          maxlength="2000"
          show-word-limit
        />
      </el-form-item>

      <el-row :gutter="20">
        <el-col :span="8">
          <el-form-item label="优先级">
            <el-select v-model="form.priority" placeholder="选择优先级">
              <el-option label="最低" :value="1" />
              <el-option label="低" :value="2" />
              <el-option label="中" :value="3" />
              <el-option label="高" :value="4" />
              <el-option label="最高" :value="5" />
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="难度等级">
            <el-rate
              v-model="form.difficultyLevel"
              :max="5"
              show-score
              text-color="#ff9900"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="8">
          <el-form-item label="预估工时">
            <el-input-number
              v-model="form.estimatedHours"
              :min="0.1"
              :max="999.9"
              :step="0.5"
              :precision="1"
              placeholder="小时"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="负责人">
            <el-select
              v-model="form.assigneeId"
              placeholder="选择负责人"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.fullName"
                :value="user.id"
              >
                <div class="user-option">
                  <el-avatar :size="20" :src="user.avatar">
                    {{ user.fullName?.charAt(0) }}
                  </el-avatar>
                  <span>{{ user.fullName }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="审核人">
            <el-select
              v-model="form.reviewerId"
              placeholder="选择审核人"
              filterable
              clearable
              style="width: 100%"
            >
              <el-option
                v-for="user in users"
                :key="user.id"
                :label="user.fullName"
                :value="user.id"
              >
                <div class="user-option">
                  <el-avatar :size="20" :src="user.avatar">
                    {{ user.fullName?.charAt(0) }}
                  </el-avatar>
                  <span>{{ user.fullName }}</span>
                </div>
              </el-option>
            </el-select>
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="计划开始">
            <el-date-picker
              v-model="form.plannedStartDate"
              type="datetime"
              placeholder="选择计划开始时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
        
        <el-col :span="12">
          <el-form-item label="计划完成">
            <el-date-picker
              v-model="form.plannedEndDate"
              type="datetime"
              placeholder="选择计划完成时间"
              style="width: 100%"
              format="YYYY-MM-DD HH:mm:ss"
              value-format="YYYY-MM-DD HH:mm:ss"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-form-item label="父任务">
        <el-select
          v-model="form.parentId"
          placeholder="选择父任务（可选）"
          filterable
          clearable
          style="width: 100%"
        >
          <el-option
            v-for="task in parentTasks"
            :key="task.id"
            :label="`${task.taskCode} - ${task.title}`"
            :value="task.id"
          />
        </el-select>
      </el-form-item>

      <el-form-item label="标签">
        <el-select
          v-model="form.tags"
          multiple
          filterable
          allow-create
          default-first-option
          placeholder="添加标签"
          style="width: 100%"
        >
          <el-option
            v-for="tag in commonTags"
            :key="tag"
            :label="tag"
            :value="tag"
          />
        </el-select>
      </el-form-item>

      <!-- 编辑模式下的额外字段 -->
      <template v-if="mode === 'edit'">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实际工时">
              <el-input-number
                v-model="form.actualHours"
                :min="0"
                :max="999.9"
                :step="0.5"
                :precision="1"
                placeholder="小时"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="完成进度">
              <el-slider
                v-model="form.progress"
                :min="0"
                :max="100"
                :step="5"
                show-input
                :show-input-controls="false"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="实际开始">
              <el-date-picker
                v-model="form.actualStartDate"
                type="datetime"
                placeholder="选择实际开始时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="12">
            <el-form-item label="实际完成">
              <el-date-picker
                v-model="form.actualEndDate"
                type="datetime"
                placeholder="选择实际完成时间"
                style="width: 100%"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </template>
    </el-form>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="submitting">
          {{ mode === 'create' ? '创建' : '保存' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { getTaskDetail, createTask, updateTask, type CreateTaskRequest, type UpdateTaskRequest } from '@/api/task'
import { getTaskCategories, type TaskCategory } from '@/api/taskCategory'

// Props
interface Props {
  visible: boolean
  taskId?: string
  mode: 'create' | 'edit'
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:visible': [value: boolean]
  success: []
}>()

// 响应式数据
const formRef = ref<FormInstance>()
const loading = ref(false)
const submitting = ref(false)
const taskCategories = ref<TaskCategory[]>([])
const users = ref<any[]>([]) // TODO: 定义用户类型
const parentTasks = ref<any[]>([]) // TODO: 定义任务类型
const commonTags = ref<string[]>(['前端', '后端', '测试', '设计', '紧急', '重要'])

// 表单数据
const form = reactive<CreateTaskRequest & UpdateTaskRequest>({
  title: '',
  description: '',
  categoryId: '',
  priority: 3,
  difficultyLevel: 3,
  estimatedHours: undefined,
  actualHours: undefined,
  progress: 0,
  assigneeId: '',
  reviewerId: '',
  parentId: '',
  projectId: '',
  plannedStartDate: '',
  plannedEndDate: '',
  actualStartDate: '',
  actualEndDate: '',
  tags: [],
  customFields: ''
})

// 表单验证规则
const rules: FormRules = {
  title: [
    { required: true, message: '请输入任务标题', trigger: 'blur' },
    { min: 1, max: 200, message: '标题长度在 1 到 200 个字符', trigger: 'blur' }
  ],
  categoryId: [
    { required: true, message: '请选择任务分类', trigger: 'change' }
  ]
}

// 计算属性
const visible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

// 方法
const loadTaskCategories = async () => {
  try {
    const { data } = await getTaskCategories()
    taskCategories.value = data
  } catch (error) {
    console.error('加载任务分类失败:', error)
  }
}

const loadTaskDetail = async () => {
  if (!props.taskId || props.mode === 'create') return
  
  try {
    loading.value = true
    const { data } = await getTaskDetail(props.taskId)
    
    // 填充表单数据
    Object.assign(form, {
      title: data.title,
      description: data.description,
      categoryId: data.category?.id || '',
      priority: data.priority,
      difficultyLevel: data.difficultyLevel,
      estimatedHours: data.estimatedHours,
      actualHours: data.actualHours,
      progress: data.progress,
      assigneeId: data.assignee?.id || '',
      reviewerId: data.reviewer?.id || '',
      parentId: data.parent?.id || '',
      projectId: data.projectId || '',
      plannedStartDate: data.plannedStartDate || '',
      plannedEndDate: data.plannedEndDate || '',
      actualStartDate: data.actualStartDate || '',
      actualEndDate: data.actualEndDate || '',
      tags: data.tags || [],
      customFields: data.customFields || ''
    })
  } catch (error) {
    ElMessage.error('加载任务详情失败')
  } finally {
    loading.value = false
  }
}

const resetForm = () => {
  Object.assign(form, {
    title: '',
    description: '',
    categoryId: '',
    priority: 3,
    difficultyLevel: 3,
    estimatedHours: undefined,
    actualHours: undefined,
    progress: 0,
    assigneeId: '',
    reviewerId: '',
    parentId: '',
    projectId: '',
    plannedStartDate: '',
    plannedEndDate: '',
    actualStartDate: '',
    actualEndDate: '',
    tags: [],
    customFields: ''
  })
  
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const handleClose = () => {
  visible.value = false
  resetForm()
}

const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    submitting.value = true
    
    if (props.mode === 'create') {
      await createTask(form as CreateTaskRequest)
      ElMessage.success('任务创建成功')
    } else {
      await updateTask(props.taskId!, form as UpdateTaskRequest)
      ElMessage.success('任务更新成功')
    }
    
    emit('success')
  } catch (error) {
    if (error !== false) { // 不是表单验证错误
      ElMessage.error(props.mode === 'create' ? '创建任务失败' : '更新任务失败')
    }
  } finally {
    submitting.value = false
  }
}

// 监听器
watch(() => props.visible, (visible) => {
  if (visible) {
    loadTaskCategories()
    if (props.mode === 'edit') {
      loadTaskDetail()
    } else {
      resetForm()
    }
  }
})
</script>

<style scoped>
.category-option {
  display: flex;
  align-items: center;
}

.user-option {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dialog-footer {
  text-align: right;
}
</style>
