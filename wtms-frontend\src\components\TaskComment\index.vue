<template>
  <div class="task-comment">
    <!-- 评论输入框 -->
    <div class="comment-input-section">
      <el-card>
        <div class="comment-form">
          <el-input
            v-model="commentForm.content"
            type="textarea"
            :rows="4"
            placeholder="请输入评论内容..."
            maxlength="2000"
            show-word-limit
            @keydown.ctrl.enter="handleSubmitComment"
          />
          <div class="comment-actions">
            <div class="left-actions">
              <el-upload
                ref="uploadRef"
                :action="uploadUrl"
                :headers="uploadHeaders"
                :data="uploadData"
                :on-success="handleUploadSuccess"
                :on-error="handleUploadError"
                :before-upload="beforeUpload"
                :show-file-list="false"
                multiple
              >
                <el-button type="text" :icon="Paperclip">
                  添加附件
                </el-button>
              </el-upload>
              <el-button type="text" :icon="At" @click="showMentionDialog = true">
                @提及
              </el-button>
            </div>
            <div class="right-actions">
              <el-checkbox v-model="commentForm.isPrivate">私有评论</el-checkbox>
              <el-button @click="resetCommentForm">取消</el-button>
              <el-button 
                type="primary" 
                :loading="submitting"
                @click="handleSubmitComment"
              >
                发表评论
              </el-button>
            </div>
          </div>
          <!-- 附件预览 -->
          <div v-if="attachmentList.length > 0" class="attachment-preview">
            <div class="attachment-title">附件列表：</div>
            <div class="attachment-list">
              <div 
                v-for="attachment in attachmentList" 
                :key="attachment.id"
                class="attachment-item"
              >
                <el-icon><Document /></el-icon>
                <span class="attachment-name">{{ attachment.originalName }}</span>
                <span class="attachment-size">{{ attachment.readableFileSize }}</span>
                <el-button 
                  type="text" 
                  size="small" 
                  :icon="Close"
                  @click="removeAttachment(attachment.id)"
                />
              </div>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 评论列表 -->
    <div class="comment-list-section">
      <div class="comment-header">
        <div class="comment-stats">
          <span>共 {{ totalComments }} 条评论</span>
        </div>
        <div class="comment-filters">
          <el-radio-group v-model="sortType" @change="loadComments">
            <el-radio-button label="latest">最新</el-radio-button>
            <el-radio-button label="popular">热门</el-radio-button>
            <el-radio-button label="pinned">置顶</el-radio-button>
          </el-radio-group>
        </div>
      </div>

      <div class="comment-list">
        <CommentItem
          v-for="comment in commentList"
          :key="comment.id"
          :comment="comment"
          :task-id="taskId"
          @reply="handleReply"
          @edit="handleEdit"
          @delete="handleDelete"
          @like="handleLike"
          @pin="handlePin"
          @refresh="loadComments"
        />
      </div>

      <!-- 加载更多 -->
      <div v-if="hasMore" class="load-more">
        <el-button 
          :loading="loading" 
          @click="loadMoreComments"
        >
          加载更多评论
        </el-button>
      </div>
    </div>

    <!-- 提及用户对话框 -->
    <MentionDialog
      v-model:visible="showMentionDialog"
      :task-id="taskId"
      @confirm="handleMentionConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { Paperclip, At, Document, Close } from '@element-plus/icons-vue'
import { 
  getTaskCommentTree, 
  createComment, 
  likeComment, 
  unlikeComment,
  pinComment,
  unpinComment,
  uploadAttachment,
  type CommentResponse,
  type CreateCommentRequest,
  type TaskAttachment
} from '@/api/comment'
import CommentItem from './CommentItem.vue'
import MentionDialog from './MentionDialog.vue'
import { useUserStore } from '@/stores/user'

interface Props {
  taskId: string
}

const props = defineProps<Props>()
const userStore = useUserStore()

// 响应式数据
const loading = ref(false)
const submitting = ref(false)
const commentList = ref<CommentResponse[]>([])
const attachmentList = ref<TaskAttachment[]>([])
const totalComments = ref(0)
const hasMore = ref(false)
const sortType = ref('latest')
const showMentionDialog = ref(false)

// 评论表单
const commentForm = reactive<CreateCommentRequest>({
  taskId: props.taskId,
  content: '',
  commentType: 'normal',
  isPrivate: false,
  attachmentIds: [],
  mentionedUserIds: []
})

// 上传配置
const uploadUrl = computed(() => '/api/v1/task-attachments/upload')
const uploadHeaders = computed(() => ({
  'Authorization': `Bearer ${userStore.token}`
}))
const uploadData = computed(() => ({
  taskId: props.taskId,
  isPublic: true
}))

// 加载评论
const loadComments = async () => {
  try {
    loading.value = true
    
    let response
    switch (sortType.value) {
      case 'popular':
        response = await getPopularComments(props.taskId, 20)
        break
      case 'pinned':
        response = await getPinnedComments(props.taskId)
        break
      default:
        response = await getTaskCommentTree(props.taskId)
    }
    
    commentList.value = response.data
    totalComments.value = response.data.length
    hasMore.value = false // 树形结构暂不支持分页
  } catch (error) {
    console.error('Failed to load comments:', error)
    ElMessage.error('加载评论失败')
  } finally {
    loading.value = false
  }
}

// 加载更多评论
const loadMoreComments = async () => {
  // TODO: 实现分页加载
}

// 提交评论
const handleSubmitComment = async () => {
  if (!commentForm.content.trim()) {
    ElMessage.warning('请输入评论内容')
    return
  }

  try {
    submitting.value = true
    
    // 设置附件ID
    commentForm.attachmentIds = attachmentList.value.map(att => att.id)
    
    await createComment(commentForm)
    ElMessage.success('评论发表成功')
    
    // 重置表单
    resetCommentForm()
    
    // 重新加载评论
    await loadComments()
  } catch (error) {
    console.error('Failed to create comment:', error)
    ElMessage.error('评论发表失败')
  } finally {
    submitting.value = false
  }
}

// 重置评论表单
const resetCommentForm = () => {
  commentForm.content = ''
  commentForm.isPrivate = false
  commentForm.attachmentIds = []
  commentForm.mentionedUserIds = []
  attachmentList.value = []
}

// 处理回复
const handleReply = (comment: CommentResponse) => {
  commentForm.parentId = comment.id
  commentForm.content = `@${comment.commenter.fullName} `
  // 滚动到评论输入框
  document.querySelector('.comment-input-section')?.scrollIntoView({ behavior: 'smooth' })
}

// 处理编辑
const handleEdit = (comment: CommentResponse) => {
  // TODO: 实现编辑功能
  ElMessage.info('编辑功能开发中')
}

// 处理删除
const handleDelete = async (comment: CommentResponse) => {
  // TODO: 实现删除功能
  ElMessage.info('删除功能开发中')
}

// 处理点赞
const handleLike = async (comment: CommentResponse) => {
  try {
    if (comment.isLiked) {
      await unlikeComment(comment.id)
      comment.isLiked = false
      comment.likeCount--
    } else {
      await likeComment(comment.id)
      comment.isLiked = true
      comment.likeCount++
    }
  } catch (error) {
    console.error('Failed to like/unlike comment:', error)
    ElMessage.error('操作失败')
  }
}

// 处理置顶
const handlePin = async (comment: CommentResponse) => {
  try {
    if (comment.isPinned) {
      await unpinComment(comment.id)
      ElMessage.success('取消置顶成功')
    } else {
      await pinComment(comment.id)
      ElMessage.success('置顶成功')
    }
    await loadComments()
  } catch (error) {
    console.error('Failed to pin/unpin comment:', error)
    ElMessage.error('操作失败')
  }
}

// 文件上传前检查
const beforeUpload = (file: File) => {
  const isValidSize = file.size / 1024 / 1024 < 50 // 50MB
  if (!isValidSize) {
    ElMessage.error('文件大小不能超过 50MB!')
    return false
  }
  return true
}

// 文件上传成功
const handleUploadSuccess = (response: any) => {
  if (response.code === 200) {
    attachmentList.value.push(response.data)
    ElMessage.success('文件上传成功')
  } else {
    ElMessage.error(response.message || '文件上传失败')
  }
}

// 文件上传失败
const handleUploadError = () => {
  ElMessage.error('文件上传失败')
}

// 移除附件
const removeAttachment = (attachmentId: string) => {
  const index = attachmentList.value.findIndex(att => att.id === attachmentId)
  if (index > -1) {
    attachmentList.value.splice(index, 1)
  }
}

// 处理提及确认
const handleMentionConfirm = (userIds: string[]) => {
  commentForm.mentionedUserIds = userIds
  showMentionDialog.value = false
}

// 组件挂载时加载评论
onMounted(() => {
  loadComments()
})
</script>

<style scoped>
.task-comment {
  padding: 20px;
}

.comment-input-section {
  margin-bottom: 20px;
}

.comment-form {
  padding: 16px;
}

.comment-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 12px;
}

.left-actions {
  display: flex;
  gap: 12px;
}

.right-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.attachment-preview {
  margin-top: 12px;
  padding: 12px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.attachment-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
}

.attachment-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background-color: white;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
}

.attachment-name {
  flex: 1;
  font-size: 14px;
}

.attachment-size {
  font-size: 12px;
  color: #999;
}

.comment-list-section {
  margin-top: 20px;
}

.comment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid #e0e0e0;
}

.comment-stats {
  font-size: 14px;
  color: #666;
}

.comment-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.load-more {
  text-align: center;
  margin-top: 20px;
}
</style>
