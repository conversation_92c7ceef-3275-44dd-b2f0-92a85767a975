package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 工作流定义实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("workflow_definitions")
@Schema(description = "工作流定义实体")
public class WorkflowDefinition implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "工作流定义ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "工作流名称")
    @TableField("name")
    private String name;

    @Schema(description = "工作流编码")
    @TableField("code")
    private String code;

    @Schema(description = "工作流描述")
    @TableField("description")
    private String description;

    @Schema(description = "工作流分类")
    @TableField("category")
    private String category;

    @Schema(description = "工作流版本")
    @TableField("version")
    private String version;

    @Schema(description = "工作流状态")
    @TableField("status")
    private String status;

    @Schema(description = "工作流定义JSON")
    @TableField("definition_json")
    private String definitionJson;

    @Schema(description = "工作流图形配置")
    @TableField("graph_config")
    private String graphConfig;

    @Schema(description = "是否启用")
    @TableField("is_enabled")
    private Boolean isEnabled;

    @Schema(description = "是否默认")
    @TableField("is_default")
    private Boolean isDefault;

    @Schema(description = "排序号")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "创建者ID")
    @TableField("creator_id")
    private String creatorId;

    @Schema(description = "发布者ID")
    @TableField("publisher_id")
    private String publisherId;

    @Schema(description = "发布时间")
    @TableField("published_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime publishedAt;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "创建者信息")
    @TableField(exist = false)
    private User creator;

    @Schema(description = "发布者信息")
    @TableField(exist = false)
    private User publisher;

    @Schema(description = "工作流节点列表")
    @TableField(exist = false)
    private List<WorkflowNode> nodes;

    @Schema(description = "工作流实例数量")
    @TableField(exist = false)
    private Integer instanceCount;

    /**
     * 工作流状态枚举
     */
    public enum Status {
        DRAFT("draft", "草稿"),
        PUBLISHED("published", "已发布"),
        DEPRECATED("deprecated", "已废弃"),
        ARCHIVED("archived", "已归档");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return DRAFT;
        }
    }

    /**
     * 工作流分类枚举
     */
    public enum Category {
        TASK("task", "任务流程"),
        APPROVAL("approval", "审批流程"),
        NOTIFICATION("notification", "通知流程"),
        AUTOMATION("automation", "自动化流程"),
        CUSTOM("custom", "自定义流程");

        private final String code;
        private final String description;

        Category(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Category fromCode(String code) {
            for (Category category : values()) {
                if (category.code.equals(code)) {
                    return category;
                }
            }
            return CUSTOM;
        }
    }

    /**
     * 检查是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.isEnabled);
    }

    /**
     * 检查是否为默认工作流
     */
    public boolean isDefault() {
        return Boolean.TRUE.equals(this.isDefault);
    }

    /**
     * 检查是否已发布
     */
    public boolean isPublished() {
        return Status.PUBLISHED.getCode().equals(this.status);
    }

    /**
     * 检查是否为草稿
     */
    public boolean isDraft() {
        return Status.DRAFT.getCode().equals(this.status);
    }

    /**
     * 检查是否已废弃
     */
    public boolean isDeprecated() {
        return Status.DEPRECATED.getCode().equals(this.status);
    }

    /**
     * 获取完整的工作流标识
     */
    public String getFullCode() {
        return code + ":" + version;
    }

    /**
     * 获取状态描述
     */
    public String getStatusText() {
        return Status.fromCode(this.status).getDescription();
    }

    /**
     * 获取分类描述
     */
    public String getCategoryText() {
        return Category.fromCode(this.category).getDescription();
    }
}
