package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务分配实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("task_assignments")
@Schema(description = "任务分配实体")
public class TaskAssignment implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分配ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "任务ID")
    @TableField("task_id")
    private String taskId;

    @Schema(description = "分配类型")
    @TableField("assign_type")
    private String assignType;

    @Schema(description = "分配目标类型")
    @TableField("target_type")
    private String targetType;

    @Schema(description = "分配目标ID")
    @TableField("target_id")
    private String targetId;

    @Schema(description = "分配者ID")
    @TableField("assigner_id")
    private String assignerId;

    @Schema(description = "分配状态")
    @TableField("status")
    private String status;

    @Schema(description = "分配备注")
    @TableField("comment")
    private String comment;

    @Schema(description = "分配时间")
    @TableField("assigned_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assignedAt;

    @Schema(description = "接受时间")
    @TableField("accepted_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime acceptedAt;

    @Schema(description = "拒绝时间")
    @TableField("rejected_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime rejectedAt;

    @Schema(description = "拒绝原因")
    @TableField("reject_reason")
    private String rejectReason;

    @Schema(description = "截止时间")
    @TableField("deadline")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deadline;

    @Schema(description = "权重")
    @TableField("weight")
    private Double weight;

    @Schema(description = "优先级")
    @TableField("priority")
    private Integer priority;

    @Schema(description = "是否已通知")
    @TableField("is_notified")
    private Boolean isNotified;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "任务信息")
    @TableField(exist = false)
    private Task task;

    @Schema(description = "分配者信息")
    @TableField(exist = false)
    private User assigner;

    @Schema(description = "目标用户信息")
    @TableField(exist = false)
    private User targetUser;

    @Schema(description = "目标部门信息")
    @TableField(exist = false)
    private Department targetDepartment;

    /**
     * 分配状态枚举
     */
    public enum Status {
        PENDING("pending", "待接受"),
        ACCEPTED("accepted", "已接受"),
        REJECTED("rejected", "已拒绝"),
        CANCELLED("cancelled", "已取消"),
        EXPIRED("expired", "已过期");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return PENDING;
        }
    }

    /**
     * 分配类型枚举
     */
    public enum AssignType {
        DIRECT("direct", "直接分配"),
        REQUEST("request", "请求分配"),
        AUTO("auto", "自动分配");

        private final String code;
        private final String description;

        AssignType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 目标类型枚举
     */
    public enum TargetType {
        USER("user", "用户"),
        DEPARTMENT("department", "部门"),
        ROLE("role", "角色");

        private final String code;
        private final String description;

        TargetType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 检查分配是否有效
     */
    public boolean isValid() {
        return Status.ACCEPTED.getCode().equals(this.status) && 
               (this.deadline == null || this.deadline.isAfter(LocalDateTime.now()));
    }

    /**
     * 检查分配是否已过期
     */
    public boolean isExpired() {
        return this.deadline != null && this.deadline.isBefore(LocalDateTime.now()) &&
               Status.PENDING.getCode().equals(this.status);
    }
}
