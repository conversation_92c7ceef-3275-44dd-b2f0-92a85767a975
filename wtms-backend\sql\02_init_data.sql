-- =====================================================
-- WTMS 工作任务管理系统 - 基础数据初始化脚本
-- 版本: 1.0.0
-- 创建时间: 2025-07-24
-- 说明: 初始化系统基础数据，包括角色、权限、默认用户等
-- =====================================================

-- 设置字符集
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 初始化部门数据
-- =====================================================
INSERT INTO `departments` (`id`, `name`, `code`, `description`, `parent_id`, `level`, `sort_order`, `is_enabled`) VALUES
('dept-001', '总经理办公室', 'CEO_OFFICE', '公司最高管理层', NULL, 1, 1, 1),
('dept-002', '技术部', 'TECH_DEPT', '负责技术研发和系统维护', NULL, 1, 2, 1),
('dept-003', '产品部', 'PRODUCT_DEPT', '负责产品设计和需求管理', NULL, 1, 3, 1),
('dept-004', '测试部', 'QA_DEPT', '负责产品质量保证和测试', NULL, 1, 4, 1),
('dept-005', '运营部', 'OPERATION_DEPT', '负责产品运营和市场推广', NULL, 1, 5, 1),
('dept-006', '前端开发组', 'FRONTEND_GROUP', '前端技术开发', 'dept-002', 2, 1, 1),
('dept-007', '后端开发组', 'BACKEND_GROUP', '后端技术开发', 'dept-002', 2, 2, 1),
('dept-008', '移动开发组', 'MOBILE_GROUP', '移动端技术开发', 'dept-002', 2, 3, 1);

-- =====================================================
-- 2. 初始化角色数据
-- =====================================================
INSERT INTO `roles` (`id`, `name`, `code`, `description`, `is_system`, `is_enabled`) VALUES
('role-001', '超级管理员', 'SUPER_ADMIN', '系统超级管理员，拥有所有权限', 1, 1),
('role-002', '系统管理员', 'ADMIN', '系统管理员，拥有大部分管理权限', 1, 1),
('role-003', '项目经理', 'PM', '项目经理，负责项目管理和任务分配', 1, 1),
('role-004', '开发者', 'DEVELOPER', '开发人员，负责任务开发', 1, 1),
('role-005', '测试员', 'TESTER', '测试人员，负责任务测试', 1, 1),
('role-006', '产品经理', 'PRODUCT_MANAGER', '产品经理，负责产品需求管理', 1, 1),
('role-007', '运营人员', 'OPERATOR', '运营人员，负责产品运营', 1, 1),
('role-008', '普通用户', 'USER', '普通用户，基础权限', 1, 1);

-- =====================================================
-- 3. 初始化权限数据
-- =====================================================

-- 系统管理权限
INSERT INTO `permissions` (`id`, `code`, `name`, `description`, `type`, `group_name`, `parent_id`, `level`, `sort_order`, `resource`, `action`, `is_enabled`, `is_system`) VALUES
('perm-001', 'system', '系统管理', '系统管理根权限', 'menu', 'system', NULL, 1, 1, 'system', '*', 1, 1),
('perm-002', 'system:view', '系统查看', '查看系统信息', 'api', 'system', 'perm-001', 2, 1, 'system', 'view', 1, 1),
('perm-003', 'system:config', '系统配置', '修改系统配置', 'api', 'system', 'perm-001', 2, 2, 'system', 'config', 1, 1);

-- 用户管理权限
INSERT INTO `permissions` (`id`, `code`, `name`, `description`, `type`, `group_name`, `parent_id`, `level`, `sort_order`, `resource`, `action`, `is_enabled`, `is_system`) VALUES
('perm-101', 'user', '用户管理', '用户管理根权限', 'menu', 'user', NULL, 1, 2, 'user', '*', 1, 1),
('perm-102', 'user:view', '用户查看', '查看用户信息', 'api', 'user', 'perm-101', 2, 1, 'user', 'view', 1, 1),
('perm-103', 'user:create', '用户创建', '创建新用户', 'api', 'user', 'perm-101', 2, 2, 'user', 'create', 1, 1),
('perm-104', 'user:update', '用户更新', '更新用户信息', 'api', 'user', 'perm-101', 2, 3, 'user', 'update', 1, 1),
('perm-105', 'user:delete', '用户删除', '删除用户', 'api', 'user', 'perm-101', 2, 4, 'user', 'delete', 1, 1),
('perm-106', 'user:assign_role', '分配角色', '为用户分配角色', 'api', 'user', 'perm-101', 2, 5, 'user', 'assign_role', 1, 1),
('perm-107', 'user:remove_role', '移除角色', '移除用户角色', 'api', 'user', 'perm-101', 2, 6, 'user', 'remove_role', 1, 1),
('perm-108', 'user:view_role', '查看角色', '查看用户角色', 'api', 'user', 'perm-101', 2, 7, 'user', 'view_role', 1, 1),
('perm-109', 'user:manage_role', '管理角色', '管理用户角色状态', 'api', 'user', 'perm-101', 2, 8, 'user', 'manage_role', 1, 1),
('perm-110', 'user:sync_role', '同步角色', '同步用户角色', 'api', 'user', 'perm-101', 2, 9, 'user', 'sync_role', 1, 1),
('perm-111', 'user:clear_role', '清空角色', '清空用户所有角色', 'api', 'user', 'perm-101', 2, 10, 'user', 'clear_role', 1, 1),
('perm-112', 'user:copy_role', '复制角色', '复制用户角色', 'api', 'user', 'perm-101', 2, 11, 'user', 'copy_role', 1, 1),
('perm-113', 'user:check_role', '检查角色', '检查用户角色', 'api', 'user', 'perm-101', 2, 12, 'user', 'check_role', 1, 1),
('perm-114', 'user:check_admin', '检查管理员', '检查管理员权限', 'api', 'user', 'perm-101', 2, 13, 'user', 'check_admin', 1, 1),
('perm-115', 'user:statistics', '用户统计', '查看用户统计信息', 'api', 'user', 'perm-101', 2, 14, 'user', 'statistics', 1, 1);

-- 角色管理权限
INSERT INTO `permissions` (`id`, `code`, `name`, `description`, `type`, `group_name`, `parent_id`, `level`, `sort_order`, `resource`, `action`, `is_enabled`, `is_system`) VALUES
('perm-201', 'role', '角色管理', '角色管理根权限', 'menu', 'role', NULL, 1, 3, 'role', '*', 1, 1),
('perm-202', 'role:view', '角色查看', '查看角色信息', 'api', 'role', 'perm-201', 2, 1, 'role', 'view', 1, 1),
('perm-203', 'role:create', '角色创建', '创建新角色', 'api', 'role', 'perm-201', 2, 2, 'role', 'create', 1, 1),
('perm-204', 'role:update', '角色更新', '更新角色信息', 'api', 'role', 'perm-201', 2, 3, 'role', 'update', 1, 1),
('perm-205', 'role:delete', '角色删除', '删除角色', 'api', 'role', 'perm-201', 2, 4, 'role', 'delete', 1, 1),
('perm-206', 'role:view_user', '查看用户', '查看角色关联用户', 'api', 'role', 'perm-201', 2, 5, 'role', 'view_user', 1, 1);

-- 权限管理权限
INSERT INTO `permissions` (`id`, `code`, `name`, `description`, `type`, `group_name`, `parent_id`, `level`, `sort_order`, `resource`, `action`, `is_enabled`, `is_system`) VALUES
('perm-301', 'permission', '权限管理', '权限管理根权限', 'menu', 'permission', NULL, 1, 4, 'permission', '*', 1, 1),
('perm-302', 'permission:view', '权限查看', '查看权限信息', 'api', 'permission', 'perm-301', 2, 1, 'permission', 'view', 1, 1),
('perm-303', 'permission:create', '权限创建', '创建新权限', 'api', 'permission', 'perm-301', 2, 2, 'permission', 'create', 1, 1),
('perm-304', 'permission:update', '权限更新', '更新权限信息', 'api', 'permission', 'perm-301', 2, 3, 'permission', 'update', 1, 1),
('perm-305', 'permission:delete', '权限删除', '删除权限', 'api', 'permission', 'perm-301', 2, 4, 'permission', 'delete', 1, 1),
('perm-306', 'permission:statistics', '权限统计', '查看权限统计信息', 'api', 'permission', 'perm-301', 2, 5, 'permission', 'statistics', 1, 1);

-- 任务管理权限
INSERT INTO `permissions` (`id`, `code`, `name`, `description`, `type`, `group_name`, `parent_id`, `level`, `sort_order`, `resource`, `action`, `is_enabled`, `is_system`) VALUES
('perm-401', 'task', '任务管理', '任务管理根权限', 'menu', 'task', NULL, 1, 5, 'task', '*', 1, 1),
('perm-402', 'task:view', '任务查看', '查看任务信息', 'api', 'task', 'perm-401', 2, 1, 'task', 'view', 1, 1),
('perm-403', 'task:create', '任务创建', '创建新任务', 'api', 'task', 'perm-401', 2, 2, 'task', 'create', 1, 1),
('perm-404', 'task:update', '任务更新', '更新任务信息', 'api', 'task', 'perm-401', 2, 3, 'task', 'update', 1, 1),
('perm-405', 'task:delete', '任务删除', '删除任务', 'api', 'task', 'perm-401', 2, 4, 'task', 'delete', 1, 1),
('perm-406', 'task:assign', '任务分配', '分配任务给用户', 'api', 'task', 'perm-401', 2, 5, 'task', 'assign', 1, 1),
('perm-407', 'task:update_status', '状态更新', '更新任务状态', 'api', 'task', 'perm-401', 2, 6, 'task', 'update_status', 1, 1),
('perm-408', 'task:comment', '任务评论', '添加任务评论', 'api', 'task', 'perm-401', 2, 7, 'task', 'comment', 1, 1),
('perm-409', 'task:archive', '任务归档', '归档任务', 'api', 'task', 'perm-401', 2, 8, 'task', 'archive', 1, 1),
('perm-410', 'task:export', '任务导出', '导出任务数据', 'api', 'task', 'perm-401', 2, 9, 'task', 'export', 1, 1),
('perm-411', 'task:statistics', '任务统计', '查看任务统计信息', 'api', 'task', 'perm-401', 2, 10, 'task', 'statistics', 1, 1);

-- 项目管理权限
INSERT INTO `permissions` (`id`, `code`, `name`, `description`, `type`, `group_name`, `parent_id`, `level`, `sort_order`, `resource`, `action`, `is_enabled`, `is_system`) VALUES
('perm-501', 'project', '项目管理', '项目管理根权限', 'menu', 'project', NULL, 1, 6, 'project', '*', 1, 1),
('perm-502', 'project:view', '项目查看', '查看项目信息', 'api', 'project', 'perm-501', 2, 1, 'project', 'view', 1, 1),
('perm-503', 'project:create', '项目创建', '创建新项目', 'api', 'project', 'perm-501', 2, 2, 'project', 'create', 1, 1),
('perm-504', 'project:update', '项目更新', '更新项目信息', 'api', 'project', 'perm-501', 2, 3, 'project', 'update', 1, 1),
('perm-505', 'project:delete', '项目删除', '删除项目', 'api', 'project', 'perm-501', 2, 4, 'project', 'delete', 1, 1);

-- 报表管理权限
INSERT INTO `permissions` (`id`, `code`, `name`, `description`, `type`, `group_name`, `parent_id`, `level`, `sort_order`, `resource`, `action`, `is_enabled`, `is_system`) VALUES
('perm-601', 'report', '报表管理', '报表管理根权限', 'menu', 'report', NULL, 1, 7, 'report', '*', 1, 1),
('perm-602', 'report:view', '报表查看', '查看报表', 'api', 'report', 'perm-601', 2, 1, 'report', 'view', 1, 1),
('perm-603', 'report:export', '报表导出', '导出报表数据', 'api', 'report', 'perm-601', 2, 2, 'report', 'export', 1, 1);

-- =====================================================
-- 4. 初始化角色权限关联
-- =====================================================

-- 超级管理员拥有所有权限
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`) 
SELECT CONCAT('rp-', LPAD(ROW_NUMBER() OVER (ORDER BY p.id), 3, '0')), 'role-001', p.id 
FROM `permissions` p WHERE p.is_enabled = 1;

-- 系统管理员权限（除了超级管理员专有权限）
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`) VALUES
('rp-admin-001', 'role-002', 'perm-001'), ('rp-admin-002', 'role-002', 'perm-002'),
('rp-admin-003', 'role-002', 'perm-101'), ('rp-admin-004', 'role-002', 'perm-102'), ('rp-admin-005', 'role-002', 'perm-103'), ('rp-admin-006', 'role-002', 'perm-104'), ('rp-admin-007', 'role-002', 'perm-106'), ('rp-admin-008', 'role-002', 'perm-107'), ('rp-admin-009', 'role-002', 'perm-108'), ('rp-admin-010', 'role-002', 'perm-109'),
('rp-admin-011', 'role-002', 'perm-201'), ('rp-admin-012', 'role-002', 'perm-202'), ('rp-admin-013', 'role-002', 'perm-203'), ('rp-admin-014', 'role-002', 'perm-204'), ('rp-admin-015', 'role-002', 'perm-206'),
('rp-admin-016', 'role-002', 'perm-301'), ('rp-admin-017', 'role-002', 'perm-302'), ('rp-admin-018', 'role-002', 'perm-306'),
('rp-admin-019', 'role-002', 'perm-401'), ('rp-admin-020', 'role-002', 'perm-402'), ('rp-admin-021', 'role-002', 'perm-403'), ('rp-admin-022', 'role-002', 'perm-404'), ('rp-admin-023', 'role-002', 'perm-405'), ('rp-admin-024', 'role-002', 'perm-406'), ('rp-admin-025', 'role-002', 'perm-407'), ('rp-admin-026', 'role-002', 'perm-408'), ('rp-admin-027', 'role-002', 'perm-409'), ('rp-admin-028', 'role-002', 'perm-410'), ('rp-admin-029', 'role-002', 'perm-411'),
('rp-admin-030', 'role-002', 'perm-501'), ('rp-admin-031', 'role-002', 'perm-502'), ('rp-admin-032', 'role-002', 'perm-503'), ('rp-admin-033', 'role-002', 'perm-504'), ('rp-admin-034', 'role-002', 'perm-505'),
('rp-admin-035', 'role-002', 'perm-601'), ('rp-admin-036', 'role-002', 'perm-602'), ('rp-admin-037', 'role-002', 'perm-603');

-- 项目经理权限
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`) VALUES
('rp-pm-001', 'role-003', 'perm-102'), ('rp-pm-002', 'role-003', 'perm-108'),
('rp-pm-003', 'role-003', 'perm-401'), ('rp-pm-004', 'role-003', 'perm-402'), ('rp-pm-005', 'role-003', 'perm-403'), ('rp-pm-006', 'role-003', 'perm-404'), ('rp-pm-007', 'role-003', 'perm-406'), ('rp-pm-008', 'role-003', 'perm-407'), ('rp-pm-009', 'role-003', 'perm-408'), ('rp-pm-010', 'role-003', 'perm-409'), ('rp-pm-011', 'role-003', 'perm-410'), ('rp-pm-012', 'role-003', 'perm-411'),
('rp-pm-013', 'role-003', 'perm-501'), ('rp-pm-014', 'role-003', 'perm-502'), ('rp-pm-015', 'role-003', 'perm-503'), ('rp-pm-016', 'role-003', 'perm-504'),
('rp-pm-017', 'role-003', 'perm-601'), ('rp-pm-018', 'role-003', 'perm-602'), ('rp-pm-019', 'role-003', 'perm-603');

-- 开发者权限
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`) VALUES
('rp-dev-001', 'role-004', 'perm-102'), ('rp-dev-002', 'role-004', 'perm-108'),
('rp-dev-003', 'role-004', 'perm-402'), ('rp-dev-004', 'role-004', 'perm-404'), ('rp-dev-005', 'role-004', 'perm-407'), ('rp-dev-006', 'role-004', 'perm-408'),
('rp-dev-007', 'role-004', 'perm-502');

-- 测试员权限
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`) VALUES
('rp-test-001', 'role-005', 'perm-102'), ('rp-test-002', 'role-005', 'perm-108'),
('rp-test-003', 'role-005', 'perm-402'), ('rp-test-004', 'role-005', 'perm-404'), ('rp-test-005', 'role-005', 'perm-407'), ('rp-test-006', 'role-005', 'perm-408'),
('rp-test-007', 'role-005', 'perm-502');

-- 产品经理权限
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`) VALUES
('rp-prod-001', 'role-006', 'perm-102'), ('rp-prod-002', 'role-006', 'perm-108'),
('rp-prod-003', 'role-006', 'perm-402'), ('rp-prod-004', 'role-006', 'perm-403'), ('rp-prod-005', 'role-006', 'perm-404'), ('rp-prod-006', 'role-006', 'perm-408'), ('rp-prod-007', 'role-006', 'perm-411'),
('rp-prod-008', 'role-006', 'perm-501'), ('rp-prod-009', 'role-006', 'perm-502'), ('rp-prod-010', 'role-006', 'perm-503'), ('rp-prod-011', 'role-006', 'perm-504'),
('rp-prod-012', 'role-006', 'perm-602');

-- 运营人员权限
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`) VALUES
('rp-op-001', 'role-007', 'perm-102'), ('rp-op-002', 'role-007', 'perm-108'),
('rp-op-003', 'role-007', 'perm-402'), ('rp-op-004', 'role-007', 'perm-408'), ('rp-op-005', 'role-007', 'perm-411'),
('rp-op-006', 'role-007', 'perm-502'),
('rp-op-007', 'role-007', 'perm-602');

-- 普通用户权限
INSERT INTO `role_permissions` (`id`, `role_id`, `permission_id`) VALUES
('rp-user-001', 'role-008', 'perm-402'), ('rp-user-002', 'role-008', 'perm-408'),
('rp-user-003', 'role-008', 'perm-502');

-- =====================================================
-- 5. 初始化默认用户
-- =====================================================

-- 创建超级管理员用户 (密码: admin123)
INSERT INTO `users` (`id`, `username`, `email`, `password_hash`, `salt`, `full_name`, `department_id`, `status`, `employee_id`) VALUES
('user-001', 'admin', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR2P2jNbTdKJeGqXEuLOhOa', 'wtms2024', '系统管理员', 'dept-001', 'active', 'EMP001');

-- 创建测试用户
INSERT INTO `users` (`id`, `username`, `email`, `password_hash`, `salt`, `full_name`, `department_id`, `status`, `employee_id`) VALUES
('user-002', 'pm001', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR2P2jNbTdKJeGqXEuLOhOa', 'wtms2024', '项目经理', 'dept-003', 'active', 'EMP002'),
('user-003', 'dev001', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR2P2jNbTdKJeGqXEuLOhOa', 'wtms2024', '前端开发', 'dept-006', 'active', 'EMP003'),
('user-004', 'dev002', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR2P2jNbTdKJeGqXEuLOhOa', 'wtms2024', '后端开发', 'dept-007', 'active', 'EMP004'),
('user-005', 'test001', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR2P2jNbTdKJeGqXEuLOhOa', 'wtms2024', '测试工程师', 'dept-004', 'active', 'EMP005'),
('user-006', 'product001', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR2P2jNbTdKJeGqXEuLOhOa', 'wtms2024', '产品经理', 'dept-003', 'active', 'EMP006'),
('user-007', 'op001', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR2P2jNbTdKJeGqXEuLOhOa', 'wtms2024', '运营专员', 'dept-005', 'active', 'EMP007'),
('user-008', 'user001', '<EMAIL>', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iKXIGfR2P2jNbTdKJeGqXEuLOhOa', 'wtms2024', '普通用户', 'dept-005', 'active', 'EMP008');

-- =====================================================
-- 6. 初始化用户角色关联
-- =====================================================
INSERT INTO `user_roles` (`id`, `user_id`, `role_id`, `assigned_by`, `is_active`) VALUES
('ur-001', 'user-001', 'role-001', 'user-001', 1),
('ur-002', 'user-002', 'role-003', 'user-001', 1),
('ur-003', 'user-003', 'role-004', 'user-001', 1),
('ur-004', 'user-004', 'role-004', 'user-001', 1),
('ur-005', 'user-005', 'role-005', 'user-001', 1),
('ur-006', 'user-006', 'role-006', 'user-001', 1),
('ur-007', 'user-007', 'role-007', 'user-001', 1),
('ur-008', 'user-008', 'role-008', 'user-001', 1);

-- =====================================================
-- 7. 初始化任务分类
-- =====================================================
INSERT INTO `task_categories` (`id`, `name`, `code`, `description`, `parent_id`, `level`, `color`, `icon`, `sort_order`, `is_enabled`) VALUES
('cat-001', '开发任务', 'DEVELOPMENT', '软件开发相关任务', NULL, 1, '#1890ff', 'code', 1, 1),
('cat-002', '测试任务', 'TESTING', '软件测试相关任务', NULL, 1, '#52c41a', 'bug', 2, 1),
('cat-003', '设计任务', 'DESIGN', '产品设计相关任务', NULL, 1, '#722ed1', 'design', 3, 1),
('cat-004', '运营任务', 'OPERATION', '产品运营相关任务', NULL, 1, '#fa8c16', 'team', 4, 1),
('cat-005', '管理任务', 'MANAGEMENT', '项目管理相关任务', NULL, 1, '#eb2f96', 'setting', 5, 1),
('cat-101', '前端开发', 'FRONTEND_DEV', '前端开发任务', 'cat-001', 2, '#1890ff', 'html5', 1, 1),
('cat-102', '后端开发', 'BACKEND_DEV', '后端开发任务', 'cat-001', 2, '#1890ff', 'api', 2, 1),
('cat-103', '移动开发', 'MOBILE_DEV', '移动端开发任务', 'cat-001', 2, '#1890ff', 'mobile', 3, 1),
('cat-201', '功能测试', 'FUNCTION_TEST', '功能测试任务', 'cat-002', 2, '#52c41a', 'check-circle', 1, 1),
('cat-202', '性能测试', 'PERFORMANCE_TEST', '性能测试任务', 'cat-002', 2, '#52c41a', 'dashboard', 2, 1),
('cat-203', '自动化测试', 'AUTO_TEST', '自动化测试任务', 'cat-002', 2, '#52c41a', 'robot', 3, 1);

-- =====================================================
-- 8. 初始化示例任务
-- =====================================================
INSERT INTO `tasks` (`id`, `task_code`, `title`, `description`, `category_id`, `status`, `priority`, `difficulty_level`, `estimated_hours`, `creator_id`, `assignee_id`, `planned_start_date`, `planned_end_date`) VALUES
('task-001', 'TASK-001', '用户登录功能开发', '开发用户登录功能，包括用户名密码登录和第三方登录', 'cat-102', 'completed', 4, 3, 16.00, 'user-002', 'user-004', '2024-07-01 09:00:00', '2024-07-03 18:00:00'),
('task-002', 'TASK-002', '任务管理界面设计', '设计任务管理相关的用户界面，包括任务列表、详情页等', 'cat-003', 'completed', 3, 2, 24.00, 'user-006', 'user-006', '2024-07-02 09:00:00', '2024-07-05 18:00:00'),
('task-003', 'TASK-003', '任务CRUD接口开发', '开发任务的增删改查接口', 'cat-102', 'in_progress', 4, 3, 20.00, 'user-002', 'user-004', '2024-07-04 09:00:00', '2024-07-08 18:00:00'),
('task-004', 'TASK-004', '用户权限测试', '测试用户权限相关功能', 'cat-201', 'pending', 3, 2, 12.00, 'user-002', 'user-005', '2024-07-06 09:00:00', '2024-07-08 18:00:00'),
('task-005', 'TASK-005', '前端任务列表页面', '开发前端任务列表页面', 'cat-101', 'in_progress', 3, 2, 16.00, 'user-002', 'user-003', '2024-07-05 09:00:00', '2024-07-09 18:00:00'),
('task-006', 'TASK-006', '数据库性能优化', '优化数据库查询性能', 'cat-102', 'pending', 5, 4, 32.00, 'user-002', 'user-004', '2024-07-08 09:00:00', '2024-07-15 18:00:00'),
('task-007', 'TASK-007', '系统部署文档编写', '编写系统部署相关文档', 'cat-005', 'draft', 2, 1, 8.00, 'user-002', NULL, '2024-07-10 09:00:00', '2024-07-12 18:00:00'),
('task-008', 'TASK-008', '用户体验优化', '优化系统用户体验', 'cat-004', 'draft', 3, 2, 20.00, 'user-006', NULL, '2024-07-12 09:00:00', '2024-07-18 18:00:00');

-- =====================================================
-- 9. 初始化任务评论
-- =====================================================
INSERT INTO `task_comments` (`id`, `task_id`, `user_id`, `content`, `comment_type`) VALUES
('comment-001', 'task-001', 'user-004', '登录功能开发完成，已提交代码审查', 'comment'),
('comment-002', 'task-001', 'user-002', '代码审查通过，可以部署测试', 'comment'),
('comment-003', 'task-002', 'user-006', '界面设计初稿完成，请查看原型图', 'comment'),
('comment-004', 'task-003', 'user-004', '基础CRUD接口已完成，正在进行单元测试', 'comment'),
('comment-005', 'task-003', 'user-002', '注意添加参数校验和异常处理', 'comment'),
('comment-006', 'task-005', 'user-003', '任务列表页面布局完成，正在对接后端接口', 'comment'),
('comment-007', 'task-004', 'user-005', '权限测试用例已准备完成，等待开发完成后开始测试', 'comment'),
('comment-008', 'task-006', 'user-004', '已分析慢查询，准备添加索引优化', 'comment');

-- 恢复外键检查
SET FOREIGN_KEY_CHECKS = 1;
