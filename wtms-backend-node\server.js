const express = require('express');
const cors = require('cors');
const mysql = require('mysql2/promise');
const redis = require('redis');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
require('dotenv').config();

const app = express();
const PORT = 55557;

// 中间件
app.use(cors({
  origin: 'http://localhost:33335',
  credentials: true
}));
app.use(express.json());

// 数据库连接配置
const dbConfig = {
  host: 'localhost',
  port: 3308,
  user: 'root',
  password: 'ankaixin.docker.mysql',
  database: 'wtms',
  charset: 'utf8mb4'
};

// Redis连接配置
const redisClient = redis.createClient({
  host: 'localhost',
  port: 6379
});

// 数据库连接池
let dbPool;

// JWT密钥
const JWT_SECRET = 'wtms-jwt-secret-key-2024';

// 初始化数据库连接
async function initDatabase() {
  try {
    dbPool = mysql.createPool({
      ...dbConfig,
      waitForConnections: true,
      connectionLimit: 10,
      queueLimit: 0
    });
    
    // 测试连接
    const connection = await dbPool.getConnection();
    console.log('✅ MySQL数据库连接成功 (localhost:3308)');
    connection.release();
  } catch (error) {
    console.log('⚠️ MySQL数据库连接失败，使用模拟数据:', error.message);
    dbPool = null;
  }
}

// 初始化Redis连接
async function initRedis() {
  try {
    await redisClient.connect();
    console.log('✅ Redis缓存连接成功 (localhost:6379)');
  } catch (error) {
    console.log('⚠️ Redis缓存连接失败:', error.message);
  }
}

// 统一响应格式
const Result = {
  success: (data, message = '操作成功') => ({
    success: true,
    code: 200,
    message,
    data,
    timestamp: Date.now()
  }),
  
  error: (message = '操作失败', code = 500) => ({
    success: false,
    code,
    message,
    data: null,
    timestamp: Date.now()
  })
};

// 健康检查接口
app.get('/api/v1/health', (req, res) => {
  res.json(Result.success({
    status: 'UP',
    service: 'WTMS Backend (Node.js)',
    version: '1.0.0',
    database: dbPool ? 'MySQL:3308 Connected' : 'MySQL:3308 Disconnected',
    cache: redisClient.isReady ? 'Redis:6379 Connected' : 'Redis:6379 Disconnected',
    timestamp: new Date().toISOString()
  }));
});

// 系统信息接口
app.get('/api/v1/info', (req, res) => {
  res.json(Result.success({
    name: 'WTMS工作任务管理系统',
    description: 'Work Task Management System',
    version: '1.0.0',
    author: 'WTMS Team',
    backend: 'Node.js + Express',
    features: [
      '用户认证',
      '任务管理', 
      'RBAC权限控制',
      'MySQL数据库',
      'Redis缓存',
      'RESTful API'
    ]
  }));
});

// 登录接口
app.post('/api/v1/auth/login', async (req, res) => {
  try {
    const { username, password } = req.body;
    
    if (!username || !password) {
      return res.status(400).json(Result.error('用户名和密码不能为空', 400));
    }
    
    // 验证超级管理员账户（与数据库中的测试数据一致）
    if (username === 'admin' && password === 'admin123456') {
      const token = jwt.sign(
        { 
          userId: '1', 
          username: 'admin',
          roles: ['SUPER_ADMIN']
        }, 
        JWT_SECRET, 
        { expiresIn: '24h' }
      );
      
      const userInfo = {
        id: '1',
        username: 'admin',
        fullName: '系统管理员',
        email: '<EMAIL>',
        phone: '13800138000',
        avatarUrl: null,
        status: 'ACTIVE',
        roles: ['SUPER_ADMIN'],
        permissions: [
          'user:create', 'user:read', 'user:update', 'user:delete',
          'task:create', 'task:read', 'task:update', 'task:delete',
          'role:create', 'role:read', 'role:update', 'role:delete',
          'permission:create', 'permission:read', 'permission:update', 'permission:delete',
          'department:create', 'department:read', 'department:update', 'department:delete'
        ],
        lastLoginAt: new Date().toISOString()
      };
      
      // 缓存用户信息到Redis
      if (redisClient.isReady) {
        await redisClient.setEx(`user:${userInfo.id}`, 3600, JSON.stringify(userInfo));
      }
      
      res.json(Result.success({
        token,
        tokenType: 'Bearer',
        expiresIn: 86400,
        user: userInfo
      }, '登录成功'));
    } else {
      res.status(401).json(Result.error('用户名或密码错误', 401));
    }
  } catch (error) {
    console.error('登录错误:', error);
    res.status(500).json(Result.error('登录失败'));
  }
});

// JWT验证中间件
const authenticateToken = (req, res, next) => {
  const authHeader = req.headers['authorization'];
  const token = authHeader && authHeader.split(' ')[1];
  
  if (!token) {
    return res.status(401).json(Result.error('访问令牌缺失', 401));
  }
  
  jwt.verify(token, JWT_SECRET, (err, user) => {
    if (err) {
      return res.status(403).json(Result.error('访问令牌无效', 403));
    }
    req.user = user;
    next();
  });
};

// 获取用户信息接口
app.get('/api/v1/user/profile', authenticateToken, async (req, res) => {
  try {
    const userId = req.user.userId;
    
    // 尝试从Redis获取用户信息
    if (redisClient.isReady) {
      const cachedUser = await redisClient.get(`user:${userId}`);
      if (cachedUser) {
        return res.json(Result.success(JSON.parse(cachedUser)));
      }
    }
    
    // 返回默认用户信息
    const userInfo = {
      id: userId,
      username: req.user.username,
      fullName: '系统管理员',
      email: '<EMAIL>',
      phone: '13800138000',
      avatarUrl: null,
      status: 'ACTIVE',
      roles: req.user.roles,
      permissions: [
        'user:create', 'user:read', 'user:update', 'user:delete',
        'task:create', 'task:read', 'task:update', 'task:delete'
      ],
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };
    
    res.json(Result.success(userInfo));
  } catch (error) {
    console.error('获取用户信息错误:', error);
    res.status(500).json(Result.error('获取用户信息失败'));
  }
});

// 获取任务列表接口
app.get('/api/v1/tasks', authenticateToken, async (req, res) => {
  try {
    const { page = 1, size = 10 } = req.query;
    
    // 模拟任务数据
    const tasks = [];
    for (let i = 1; i <= 5; i++) {
      tasks.push({
        id: String(i),
        title: `示例任务 ${i}`,
        description: `这是一个示例任务的描述，展示WTMS系统的任务管理功能`,
        status: i % 2 === 0 ? 'IN_PROGRESS' : 'TODO',
        priority: i <= 2 ? 'HIGH' : 'MEDIUM',
        progress: i * 20,
        estimatedHours: 40,
        actualHours: i * 8,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        creator: {
          id: '1',
          fullName: '系统管理员',
          avatarUrl: null
        },
        assignee: {
          id: String(i + 1),
          fullName: `开发人员${i}`,
          avatarUrl: null
        },
        tags: ['开发', '测试', '文档']
      });
    }
    
    res.json(Result.success({
      records: tasks,
      total: 5,
      page: parseInt(page),
      size: parseInt(size),
      pages: 1
    }));
  } catch (error) {
    console.error('获取任务列表错误:', error);
    res.status(500).json(Result.error('获取任务列表失败'));
  }
});

// 获取任务详情接口
app.get('/api/v1/tasks/:id', authenticateToken, (req, res) => {
  try {
    const { id } = req.params;
    
    const task = {
      id,
      title: `示例任务 ${id}`,
      description: `这是任务 ${id} 的详细描述。本任务展示了WTMS系统的完整功能。`,
      status: 'IN_PROGRESS',
      priority: 'HIGH',
      progress: 60,
      estimatedHours: 40,
      actualHours: 24,
      startDate: new Date().toISOString(),
      dueDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      creator: {
        id: '1',
        fullName: '系统管理员',
        avatarUrl: null
      },
      assignee: {
        id: '2',
        fullName: '开发人员',
        avatarUrl: null
      },
      tags: ['开发', '紧急', '后端']
    };
    
    res.json(Result.success(task));
  } catch (error) {
    console.error('获取任务详情错误:', error);
    res.status(500).json(Result.error('获取任务详情失败'));
  }
});

// 创建任务接口
app.post('/api/v1/tasks', authenticateToken, (req, res) => {
  try {
    const { title, description, priority = 'MEDIUM' } = req.body;
    
    if (!title) {
      return res.status(400).json(Result.error('任务标题不能为空', 400));
    }
    
    const taskId = `task-${Date.now()}`;
    const task = {
      id: taskId,
      title,
      description,
      status: 'TODO',
      priority,
      progress: 0,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      creator: {
        id: req.user.userId,
        fullName: '系统管理员',
        avatarUrl: null
      }
    };
    
    res.json(Result.success(task, '任务创建成功'));
  } catch (error) {
    console.error('创建任务错误:', error);
    res.status(500).json(Result.error('创建任务失败'));
  }
});

// 启动服务器
async function startServer() {
  await initDatabase();
  await initRedis();
  
  app.listen(PORT, () => {
    console.log('=================================');
    console.log('WTMS Node.js后端服务启动成功！');
    console.log(`访问地址: http://localhost:${PORT}/api/v1`);
    console.log(`健康检查: http://localhost:${PORT}/api/v1/health`);
    console.log(`登录接口: http://localhost:${PORT}/api/v1/auth/login`);
    console.log('=================================');
  });
}

startServer().catch(console.error);
