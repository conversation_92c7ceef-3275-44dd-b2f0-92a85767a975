<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WTMS 登录验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background: #f0f2f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        button.success {
            background: #28a745;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 400px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔐 WTMS 登录验证</h1>
        
        <div id="status" class="status info">准备测试登录功能...</div>
        
        <div style="text-align: center;">
            <button onclick="testLogin()">🚀 测试登录</button>
            <button onclick="clearStorage()">🧹 清除存储</button>
            <button onclick="openApp()" class="success">🌐 打开应用</button>
        </div>
        
        <div id="log" class="log">等待测试开始...</div>
    </div>

    <script>
        const API_BASE = 'http://localhost:55557/api/v1';
        const APP_URL = 'http://localhost:33336';
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = `status ${type}`;
        }
        
        function log(message) {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.textContent += `[${timestamp}] ${message}\n`;
            logDiv.scrollTop = logDiv.scrollHeight;
        }
        
        function clearStorage() {
            localStorage.clear();
            sessionStorage.clear();
            // 清除所有可能的cookie
            document.cookie.split(";").forEach(function(c) { 
                document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/"); 
            });
            updateStatus('所有存储已清除', 'info');
            log('🧹 清除了所有本地存储和Cookie');
        }
        
        function openApp() {
            window.open(APP_URL, '_blank');
        }
        
        async function testLogin() {
            updateStatus('正在测试登录...', 'info');
            document.getElementById('log').textContent = '';
            
            try {
                // 步骤1: 清除存储
                log('步骤1: 清除所有存储...');
                clearStorage();
                
                // 步骤2: 测试API连接
                log('步骤2: 测试API连接...');
                const healthResponse = await fetch(`${API_BASE}/health`);
                if (!healthResponse.ok) {
                    throw new Error(`API连接失败: ${healthResponse.status}`);
                }
                const healthData = await healthResponse.json();
                log(`✅ API连接正常: ${healthData.message}`);
                
                // 步骤3: 执行登录
                log('步骤3: 执行登录请求...');
                const loginResponse = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123456'
                    })
                });
                
                log(`登录响应状态: ${loginResponse.status}`);
                
                if (!loginResponse.ok) {
                    throw new Error(`登录请求失败: ${loginResponse.status}`);
                }
                
                const loginData = await loginResponse.json();
                
                if (!loginData.success || !loginData.data) {
                    throw new Error(`登录失败: ${loginData.message || '未知错误'}`);
                }
                
                // 步骤4: 保存登录数据
                log('步骤4: 保存登录数据...');
                const { token, user, refreshToken } = loginData.data;
                
                // 保存到localStorage和Cookie
                localStorage.setItem('wtms_token', token);
                if (refreshToken) {
                    localStorage.setItem('refreshToken', refreshToken);
                }
                
                // 设置Cookie
                document.cookie = `wtms_token=${token}; path=/; max-age=${7*24*60*60}`;
                
                log(`✅ Token已保存: ${token.substring(0, 50)}...`);
                log(`✅ 用户信息: ${user.fullName} (${user.username})`);
                log(`✅ 用户角色: ${user.role?.name} (${user.role?.code})`);
                
                // 步骤5: 验证token
                log('步骤5: 验证token有效性...');
                const profileResponse = await fetch(`${API_BASE}/user/profile`, {
                    headers: {
                        'Authorization': `Bearer ${token}`
                    }
                });
                
                if (profileResponse.ok) {
                    const profileData = await profileResponse.json();
                    log(`✅ Token验证成功`);
                } else {
                    log(`⚠️ Token验证失败: ${profileResponse.status}`);
                }
                
                // 成功
                updateStatus('🎉 登录测试完全成功！现在可以访问主应用了。', 'success');
                log('🎉 登录流程测试完成！');
                log('💡 现在点击"打开应用"按钮，应该能够直接进入主界面。');
                
                // 3秒后自动打开应用
                setTimeout(() => {
                    log('🌐 自动打开主应用...');
                    window.open(APP_URL, '_blank');
                }, 3000);
                
            } catch (error) {
                updateStatus(`❌ 登录测试失败: ${error.message}`, 'error');
                log(`❌ 错误: ${error.message}`);
                console.error('Login test failed:', error);
            }
        }
        
        // 页面加载时检查当前状态
        window.addEventListener('load', function() {
            const token = localStorage.getItem('wtms_token');
            if (token) {
                log(`检测到已保存的token: ${token.substring(0, 50)}...`);
                updateStatus('检测到已保存的登录信息', 'success');
            } else {
                log('当前没有保存的登录信息');
                updateStatus('没有检测到登录信息', 'info');
            }
        });
    </script>
</body>
</html>
