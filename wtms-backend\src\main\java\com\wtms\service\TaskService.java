package com.wtms.service;

import com.wtms.common.result.PageResult;
import com.wtms.dto.request.CreateTaskRequest;
import com.wtms.dto.request.TaskQueryRequest;
import com.wtms.dto.request.UpdateTaskRequest;
import com.wtms.dto.response.TaskDetailResponse;
import com.wtms.dto.response.TaskListResponse;
import com.wtms.entity.Task;

import java.math.BigDecimal;
import java.util.List;

/**
 * 任务服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface TaskService {

    /**
     * 创建任务
     *
     * @param request 创建任务请求
     * @return 任务详情
     */
    TaskDetailResponse createTask(CreateTaskRequest request);

    /**
     * 根据ID获取任务详情
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    TaskDetailResponse getTaskById(String taskId);

    /**
     * 更新任务
     *
     * @param taskId 任务ID
     * @param request 更新任务请求
     * @return 任务详情
     */
    TaskDetailResponse updateTask(String taskId, UpdateTaskRequest request);

    /**
     * 删除任务
     *
     * @param taskId 任务ID
     */
    void deleteTask(String taskId);

    /**
     * 分页查询任务列表
     *
     * @param request 查询请求
     * @param page 页码
     * @param size 每页大小
     * @return 任务列表
     */
    PageResult<TaskListResponse> getTaskList(TaskQueryRequest request, Integer page, Integer size);

    /**
     * 更新任务状态
     *
     * @param taskId 任务ID
     * @param status 新状态
     * @return 任务详情
     */
    TaskDetailResponse updateTaskStatus(String taskId, String status);

    /**
     * 更新任务进度
     *
     * @param taskId 任务ID
     * @param progress 进度百分比
     * @return 任务详情
     */
    TaskDetailResponse updateTaskProgress(String taskId, BigDecimal progress);

    /**
     * 分配任务
     *
     * @param taskId 任务ID
     * @param assigneeId 负责人ID
     * @return 任务详情
     */
    TaskDetailResponse assignTask(String taskId, String assigneeId);

    /**
     * 开始任务
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    TaskDetailResponse startTask(String taskId);

    /**
     * 完成任务
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    TaskDetailResponse completeTask(String taskId);

    /**
     * 暂停任务
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    TaskDetailResponse pauseTask(String taskId);

    /**
     * 取消任务
     *
     * @param taskId 任务ID
     * @return 任务详情
     */
    TaskDetailResponse cancelTask(String taskId);

    /**
     * 归档任务
     *
     * @param taskId 任务ID
     */
    void archiveTask(String taskId);

    /**
     * 取消归档任务
     *
     * @param taskId 任务ID
     */
    void unarchiveTask(String taskId);

    /**
     * 复制任务
     *
     * @param taskId 原任务ID
     * @return 新任务详情
     */
    TaskDetailResponse copyTask(String taskId);

    /**
     * 获取子任务列表
     *
     * @param parentId 父任务ID
     * @return 子任务列表
     */
    List<TaskListResponse> getSubTasks(String parentId);

    /**
     * 获取我的任务列表
     *
     * @param status 任务状态（可选）
     * @param page 页码
     * @param size 每页大小
     * @return 任务列表
     */
    PageResult<TaskListResponse> getMyTasks(String status, Integer page, Integer size);

    /**
     * 获取我创建的任务列表
     *
     * @param status 任务状态（可选）
     * @param page 页码
     * @param size 每页大小
     * @return 任务列表
     */
    PageResult<TaskListResponse> getMyCreatedTasks(String status, Integer page, Integer size);

    /**
     * 获取任务统计信息
     *
     * @return 统计信息
     */
    Object getTaskStatistics();

    /**
     * 批量更新任务状态
     *
     * @param taskIds 任务ID列表
     * @param status 新状态
     */
    void batchUpdateTaskStatus(List<String> taskIds, String status);

    /**
     * 批量分配任务
     *
     * @param taskIds 任务ID列表
     * @param assigneeId 负责人ID
     */
    void batchAssignTasks(List<String> taskIds, String assigneeId);

    /**
     * 批量删除任务
     *
     * @param taskIds 任务ID列表
     */
    void batchDeleteTasks(List<String> taskIds);

    /**
     * 检查任务是否存在
     *
     * @param taskId 任务ID
     * @return 是否存在
     */
    boolean existsById(String taskId);

    /**
     * 检查用户是否有任务权限
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param permission 权限类型
     * @return 是否有权限
     */
    boolean hasTaskPermission(String taskId, String userId, String permission);
}
