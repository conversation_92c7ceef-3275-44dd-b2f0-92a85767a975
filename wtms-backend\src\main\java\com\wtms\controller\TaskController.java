package com.wtms.controller;

import com.wtms.common.result.PageResult;
import com.wtms.common.result.Result;
import com.wtms.dto.request.CreateTaskRequest;
import com.wtms.dto.request.TaskQueryRequest;
import com.wtms.dto.request.UpdateTaskRequest;
import com.wtms.dto.response.TaskDetailResponse;
import com.wtms.dto.response.TaskListResponse;
import com.wtms.service.TaskService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;
import java.util.List;

/**
 * 任务管理控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/tasks")
@Tag(name = "任务管理", description = "任务管理相关接口")
public class TaskController {

    @Autowired
    private TaskService taskService;

    @PostMapping
    @Operation(summary = "创建任务", description = "创建新的任务")
    @PreAuthorize("hasAuthority('task:create') or hasRole('ADMIN') or hasRole('PM')")
    public Result<TaskDetailResponse> createTask(@Valid @RequestBody CreateTaskRequest request) {
        log.info("Creating task: {}", request.getTitle());
        
        TaskDetailResponse response = taskService.createTask(request);
        return Result.success("任务创建成功", response);
    }

    @GetMapping("/{taskId}")
    @Operation(summary = "获取任务详情", description = "根据ID获取任务详细信息")
    @PreAuthorize("hasAuthority('task:view') or hasRole('ADMIN')")
    public Result<TaskDetailResponse> getTaskById(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        TaskDetailResponse response = taskService.getTaskById(taskId);
        return Result.success(response);
    }

    @PutMapping("/{taskId}")
    @Operation(summary = "更新任务", description = "更新任务信息")
    @PreAuthorize("hasAuthority('task:update') or hasRole('ADMIN')")
    public Result<TaskDetailResponse> updateTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Valid @RequestBody UpdateTaskRequest request) {
        
        log.info("Updating task: {}", taskId);
        
        TaskDetailResponse response = taskService.updateTask(taskId, request);
        return Result.success("任务更新成功", response);
    }

    @DeleteMapping("/{taskId}")
    @Operation(summary = "删除任务", description = "删除指定任务")
    @PreAuthorize("hasAuthority('task:delete') or hasRole('ADMIN')")
    public Result<String> deleteTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        log.info("Deleting task: {}", taskId);
        
        taskService.deleteTask(taskId);
        return Result.success("任务删除成功");
    }

    @GetMapping
    @Operation(summary = "获取任务列表", description = "分页查询任务列表")
    @PreAuthorize("hasAuthority('task:view') or hasRole('ADMIN')")
    public Result<PageResult<TaskListResponse>> getTaskList(
            @Parameter(description = "查询条件") TaskQueryRequest request,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") Integer size) {
        
        PageResult<TaskListResponse> result = taskService.getTaskList(request, page, size);
        return Result.success(result);
    }

    @PutMapping("/{taskId}/status")
    @Operation(summary = "更新任务状态", description = "更新任务的状态")
    @PreAuthorize("hasAuthority('task:update_status') or hasRole('ADMIN') or hasRole('PM')")
    public Result<TaskDetailResponse> updateTaskStatus(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "新状态", required = true)
            @RequestParam @NotBlank(message = "状态不能为空") String status) {
        
        log.info("Updating task status: {} -> {}", taskId, status);
        
        TaskDetailResponse response = taskService.updateTaskStatus(taskId, status);
        return Result.success("任务状态更新成功", response);
    }

    @PutMapping("/{taskId}/progress")
    @Operation(summary = "更新任务进度", description = "更新任务的完成进度")
    @PreAuthorize("hasAuthority('task:update') or hasRole('ADMIN')")
    public Result<TaskDetailResponse> updateTaskProgress(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "进度百分比", required = true)
            @RequestParam BigDecimal progress) {
        
        log.info("Updating task progress: {} -> {}%", taskId, progress);
        
        TaskDetailResponse response = taskService.updateTaskProgress(taskId, progress);
        return Result.success("任务进度更新成功", response);
    }

    @PutMapping("/{taskId}/assign")
    @Operation(summary = "分配任务", description = "将任务分配给指定用户")
    @PreAuthorize("hasAuthority('task:assign') or hasRole('ADMIN') or hasRole('PM')")
    public Result<TaskDetailResponse> assignTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "负责人ID", required = true)
            @RequestParam @NotBlank(message = "负责人ID不能为空") String assigneeId) {
        
        log.info("Assigning task: {} -> {}", taskId, assigneeId);
        
        TaskDetailResponse response = taskService.assignTask(taskId, assigneeId);
        return Result.success("任务分配成功", response);
    }

    @PutMapping("/{taskId}/start")
    @Operation(summary = "开始任务", description = "开始执行任务")
    @PreAuthorize("hasAuthority('task:update_status') or hasRole('ADMIN') or hasRole('PM') or @taskService.isTaskAssignee(#taskId, authentication.principal.id)")
    public Result<TaskDetailResponse> startTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        log.info("Starting task: {}", taskId);
        
        TaskDetailResponse response = taskService.startTask(taskId);
        return Result.success("任务已开始", response);
    }

    @PutMapping("/{taskId}/complete")
    @Operation(summary = "完成任务", description = "标记任务为完成状态")
    @PreAuthorize("hasAuthority('task:update_status') or hasRole('ADMIN') or hasRole('PM') or @taskService.isTaskAssignee(#taskId, authentication.principal.id)")
    public Result<TaskDetailResponse> completeTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        log.info("Completing task: {}", taskId);
        
        TaskDetailResponse response = taskService.completeTask(taskId);
        return Result.success("任务已完成", response);
    }

    @PutMapping("/{taskId}/pause")
    @Operation(summary = "暂停任务", description = "暂停任务执行")
    @PreAuthorize("hasAuthority('task:update_status') or hasRole('ADMIN') or hasRole('PM')")
    public Result<TaskDetailResponse> pauseTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        log.info("Pausing task: {}", taskId);
        
        TaskDetailResponse response = taskService.pauseTask(taskId);
        return Result.success("任务已暂停", response);
    }

    @PutMapping("/{taskId}/cancel")
    @Operation(summary = "取消任务", description = "取消任务执行")
    @PreAuthorize("hasAuthority('task:update_status') or hasRole('ADMIN') or hasRole('PM')")
    public Result<TaskDetailResponse> cancelTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        log.info("Cancelling task: {}", taskId);
        
        TaskDetailResponse response = taskService.cancelTask(taskId);
        return Result.success("任务已取消", response);
    }

    @PutMapping("/{taskId}/archive")
    @Operation(summary = "归档任务", description = "将任务归档")
    @PreAuthorize("hasAuthority('task:archive') or hasRole('ADMIN')")
    public Result<String> archiveTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        log.info("Archiving task: {}", taskId);
        
        taskService.archiveTask(taskId);
        return Result.success("任务已归档");
    }

    @PutMapping("/{taskId}/unarchive")
    @Operation(summary = "取消归档", description = "取消任务归档")
    @PreAuthorize("hasAuthority('task:archive') or hasRole('ADMIN')")
    public Result<String> unarchiveTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        log.info("Unarchiving task: {}", taskId);
        
        taskService.unarchiveTask(taskId);
        return Result.success("任务归档已取消");
    }

    @PostMapping("/{taskId}/copy")
    @Operation(summary = "复制任务", description = "复制现有任务创建新任务")
    @PreAuthorize("hasAuthority('task:create') or hasRole('ADMIN') or hasRole('PM')")
    public Result<TaskDetailResponse> copyTask(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        log.info("Copying task: {}", taskId);
        
        TaskDetailResponse response = taskService.copyTask(taskId);
        return Result.success("任务复制成功", response);
    }

    @GetMapping("/{taskId}/subtasks")
    @Operation(summary = "获取子任务", description = "获取指定任务的子任务列表")
    @PreAuthorize("hasAuthority('task:view') or hasRole('ADMIN')")
    public Result<List<TaskListResponse>> getSubTasks(
            @Parameter(description = "父任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        List<TaskListResponse> subTasks = taskService.getSubTasks(taskId);
        return Result.success(subTasks);
    }

    @GetMapping("/my")
    @Operation(summary = "获取我的任务", description = "获取当前用户负责的任务列表")
    @PreAuthorize("isAuthenticated()")
    public Result<PageResult<TaskListResponse>> getMyTasks(
            @Parameter(description = "任务状态") @RequestParam(required = false) String status,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") Integer size) {
        
        PageResult<TaskListResponse> result = taskService.getMyTasks(status, page, size);
        return Result.success(result);
    }

    @GetMapping("/my-created")
    @Operation(summary = "获取我创建的任务", description = "获取当前用户创建的任务列表")
    @PreAuthorize("isAuthenticated()")
    public Result<PageResult<TaskListResponse>> getMyCreatedTasks(
            @Parameter(description = "任务状态") @RequestParam(required = false) String status,
            @Parameter(description = "页码", example = "1")
            @RequestParam(defaultValue = "1") Integer page,
            @Parameter(description = "每页大小", example = "20")
            @RequestParam(defaultValue = "20") Integer size) {
        
        PageResult<TaskListResponse> result = taskService.getMyCreatedTasks(status, page, size);
        return Result.success(result);
    }

    @GetMapping("/statistics")
    @Operation(summary = "获取任务统计", description = "获取任务统计信息")
    @PreAuthorize("hasAuthority('task:statistics') or hasRole('ADMIN') or hasRole('PM')")
    public Result<Object> getTaskStatistics() {
        Object statistics = taskService.getTaskStatistics();
        return Result.success(statistics);
    }

    @PutMapping("/batch/status")
    @Operation(summary = "批量更新状态", description = "批量更新任务状态")
    @PreAuthorize("hasAuthority('task:update_status') or hasRole('ADMIN') or hasRole('PM')")
    public Result<String> batchUpdateTaskStatus(
            @Parameter(description = "任务ID列表", required = true)
            @RequestParam @NotEmpty(message = "任务ID列表不能为空") List<String> taskIds,
            @Parameter(description = "新状态", required = true)
            @RequestParam @NotBlank(message = "状态不能为空") String status) {
        
        log.info("Batch updating task status: {} tasks -> {}", taskIds.size(), status);
        
        taskService.batchUpdateTaskStatus(taskIds, status);
        return Result.success("批量状态更新成功");
    }

    @PutMapping("/batch/assign")
    @Operation(summary = "批量分配任务", description = "批量分配任务给指定用户")
    @PreAuthorize("hasAuthority('task:assign') or hasRole('ADMIN') or hasRole('PM')")
    public Result<String> batchAssignTasks(
            @Parameter(description = "任务ID列表", required = true)
            @RequestParam @NotEmpty(message = "任务ID列表不能为空") List<String> taskIds,
            @Parameter(description = "负责人ID", required = true)
            @RequestParam @NotBlank(message = "负责人ID不能为空") String assigneeId) {
        
        log.info("Batch assigning tasks: {} tasks -> {}", taskIds.size(), assigneeId);
        
        taskService.batchAssignTasks(taskIds, assigneeId);
        return Result.success("批量任务分配成功");
    }

    @DeleteMapping("/batch")
    @Operation(summary = "批量删除任务", description = "批量删除指定任务")
    @PreAuthorize("hasAuthority('task:delete') or hasRole('ADMIN')")
    public Result<String> batchDeleteTasks(
            @Parameter(description = "任务ID列表", required = true)
            @RequestParam @NotEmpty(message = "任务ID列表不能为空") List<String> taskIds) {
        
        log.info("Batch deleting tasks: {} tasks", taskIds.size());
        
        taskService.batchDeleteTasks(taskIds);
        return Result.success("批量任务删除成功");
    }
}
