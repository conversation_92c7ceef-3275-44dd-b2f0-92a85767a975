package com.wtms.service;

import com.wtms.dto.request.LoginRequest;
import com.wtms.dto.response.LoginResponse;
import com.wtms.dto.response.UserInfoResponse;

/**
 * 认证服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface AuthService {

    /**
     * 用户登录
     *
     * @param loginRequest 登录请求
     * @return 登录响应
     */
    LoginResponse login(LoginRequest loginRequest);

    /**
     * 用户登出
     *
     * @param token 访问令牌
     */
    void logout(String token);

    /**
     * 刷新令牌
     *
     * @param refreshToken 刷新令牌
     * @return 新的访问令牌
     */
    String refreshToken(String refreshToken);

    /**
     * 获取当前用户信息
     *
     * @return 用户信息
     */
    UserInfoResponse getCurrentUserInfo();

    /**
     * 修改密码
     *
     * @param oldPassword 旧密码
     * @param newPassword 新密码
     */
    void changePassword(String oldPassword, String newPassword);

    /**
     * 忘记密码
     *
     * @param email 邮箱
     */
    void forgotPassword(String email);

    /**
     * 重置密码
     *
     * @param token 重置令牌
     * @param newPassword 新密码
     */
    void resetPassword(String token, String newPassword);
}
