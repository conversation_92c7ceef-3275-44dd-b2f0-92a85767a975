<template>
  <div class="login-container">
    <div class="login-box">
      <div class="login-header">
        <div class="logo">
          <img src="/logo.svg" alt="WTMS" />
        </div>
        <h1 class="title">WTMS工作任务管理平台</h1>
        <p class="subtitle">Work Task Management System</p>
      </div>

      <el-form
        ref="loginFormRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
        @keyup.enter="handleLogin"
      >
        <el-form-item prop="username">
          <el-input
            v-model="loginForm.username"
            placeholder="请输入用户名"
            size="large"
            prefix-icon="User"
            clearable
          />
        </el-form-item>

        <el-form-item prop="password">
          <el-input
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            size="large"
            prefix-icon="Lock"
            show-password
            clearable
          />
        </el-form-item>

        <el-form-item v-if="showCaptcha" prop="captcha">
          <div class="captcha-container">
            <el-input
              v-model="loginForm.captcha"
              placeholder="请输入验证码"
              size="large"
              prefix-icon="Picture"
              clearable
            />
            <div class="captcha-image" @click="refreshCaptcha">
              <img v-if="captchaUrl" :src="captchaUrl" alt="验证码" />
              <span v-else>点击获取</span>
            </div>
          </div>
        </el-form-item>

        <el-form-item>
          <div class="login-options">
            <el-checkbox v-model="loginForm.rememberMe">
              记住我
            </el-checkbox>
            <el-link type="primary" @click="showForgotPassword">
              忘记密码？
            </el-link>
          </div>
        </el-form-item>

        <el-form-item>
          <el-button
            type="primary"
            size="large"
            class="login-button"
            :loading="loading"
            @click="handleLogin"
          >
            {{ loading ? '登录中...' : '登录' }}
          </el-button>
        </el-form-item>
      </el-form>

      <div class="login-footer">
        <p>&copy; 2024 WTMS工作任务管理平台. All rights reserved.</p>
      </div>
    </div>

    <!-- 忘记密码对话框 -->
    <el-dialog
      v-model="forgotPasswordVisible"
      title="忘记密码"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        ref="forgotPasswordFormRef"
        :model="forgotPasswordForm"
        :rules="forgotPasswordRules"
        label-width="80px"
      >
        <el-form-item label="邮箱" prop="email">
          <el-input
            v-model="forgotPasswordForm.email"
            placeholder="请输入注册邮箱"
            prefix-icon="Message"
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="forgotPasswordVisible = false">取消</el-button>
          <el-button
            type="primary"
            :loading="forgotPasswordLoading"
            @click="handleForgotPassword"
          >
            发送重置邮件
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, type FormInstance, type FormRules } from 'element-plus'
import { User, Lock, Picture } from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import { authApi } from '@/api/auth'
import type { LoginCredentials } from '@/types/user'

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()

// 表单引用
const loginFormRef = ref<FormInstance>()
const forgotPasswordFormRef = ref<FormInstance>()

// 状态
const loading = ref(false)
const showCaptcha = ref(false)
const captchaUrl = ref('')
const forgotPasswordVisible = ref(false)
const forgotPasswordLoading = ref(false)

// 登录表单
const loginForm = reactive<LoginCredentials>({
  username: '',
  password: '',
  captcha: '',
  captchaId: '',
  rememberMe: false
})

// 忘记密码表单
const forgotPasswordForm = reactive({
  email: ''
})

// 表单验证规则
const loginRules: FormRules = {
  username: [
    { required: true, message: '请输入用户名', trigger: 'blur' },
    { min: 3, max: 50, message: '用户名长度在 3 到 50 个字符', trigger: 'blur' }
  ],
  password: [
    { required: true, message: '请输入密码', trigger: 'blur' },
    { min: 6, max: 50, message: '密码长度在 6 到 50 个字符', trigger: 'blur' }
  ],
  captcha: [
    { required: true, message: '请输入验证码', trigger: 'blur' },
    { len: 4, message: '验证码长度为 4 位', trigger: 'blur' }
  ]
}

const forgotPasswordRules: FormRules = {
  email: [
    { required: true, message: '请输入邮箱地址', trigger: 'blur' },
    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
  ]
}

// 处理登录
const handleLogin = async () => {
  if (!loginFormRef.value) return

  try {
    await loginFormRef.value.validate()
    loading.value = true

    const success = await userStore.login(loginForm)

    if (success) {
      console.log('Login successful, preparing to redirect...')
      // 登录成功，等待一小段时间确保状态更新完成
      await new Promise(resolve => setTimeout(resolve, 100))

      // 跳转到目标页面
      const redirect = (route.query.redirect as string) || '/dashboard'
      console.log('Redirecting to:', redirect)

      try {
        await router.push(redirect)
        console.log('Redirect successful')
      } catch (error) {
        console.error('Redirect failed:', error)
        // 如果跳转失败，尝试强制刷新页面
        window.location.href = redirect
      }
    }
  } catch (error) {
    console.error('Login validation failed:', error)
  } finally {
    loading.value = false
  }
}

// 显示忘记密码对话框
const showForgotPassword = () => {
  forgotPasswordVisible.value = true
  forgotPasswordForm.email = ''
}

// 处理忘记密码
const handleForgotPassword = async () => {
  if (!forgotPasswordFormRef.value) return

  try {
    await forgotPasswordFormRef.value.validate()
    forgotPasswordLoading.value = true

    const response = await authApi.forgotPassword(forgotPasswordForm.email)
    
    if (response.success) {
      ElMessage.success('重置密码邮件已发送，请查收邮箱')
      forgotPasswordVisible.value = false
    }
  } catch (error) {
    console.error('Forgot password failed:', error)
  } finally {
    forgotPasswordLoading.value = false
  }
}

// 获取验证码
const getCaptcha = async () => {
  try {
    const response = await authApi.getCaptcha()
    if (response.success && response.data) {
      captchaUrl.value = response.data.imageUrl
      loginForm.captchaId = response.data.captchaId
      showCaptcha.value = true
    }
  } catch (error) {
    console.error('Get captcha failed:', error)
  }
}

// 刷新验证码
const refreshCaptcha = () => {
  getCaptcha()
}

// 组件挂载时的初始化
onMounted(() => {
  // 清除可能存在的过期状态
  if (userStore.token && !userStore.currentUser) {
    console.log('Clearing expired token state')
    userStore.resetState()
  }

  // 如果已经登录，直接跳转
  if (userStore.isLoggedIn) {
    const redirect = (route.query.redirect as string) || '/'
    router.push(redirect)
    return
  }

  // 根据需要显示验证码
  // getCaptcha()
})
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 20px;
}

.login-box {
  width: 100%;
  max-width: 400px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.login-header {
  text-align: center;
  padding: 40px 30px 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;

  .logo {
    margin-bottom: 16px;

    img {
      width: 64px;
      height: 64px;
      border-radius: 50%;
      background: white;
      padding: 8px;
    }
  }

  .title {
    font-size: 24px;
    font-weight: 600;
    margin: 0 0 8px;
  }

  .subtitle {
    font-size: 14px;
    opacity: 0.9;
    margin: 0;
  }
}

.login-form {
  padding: 30px;

  .el-form-item {
    margin-bottom: 24px;
  }

  .captcha-container {
    display: flex;
    gap: 12px;

    .el-input {
      flex: 1;
    }

    .captcha-image {
      width: 100px;
      height: 40px;
      border: 1px solid #dcdfe6;
      border-radius: 4px;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #f5f7fa;

      img {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      span {
        font-size: 12px;
        color: #909399;
      }

      &:hover {
        border-color: #409eff;
      }
    }
  }

  .login-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
  }

  .login-button {
    width: 100%;
    height: 44px;
    font-size: 16px;
    font-weight: 500;
  }
}

.login-footer {
  text-align: center;
  padding: 20px 30px;
  background: #f8f9fa;
  border-top: 1px solid #eee;

  p {
    margin: 0;
    font-size: 12px;
    color: #666;
  }
}

.dialog-footer {
  text-align: right;
}

// 响应式设计
@media (max-width: 480px) {
  .login-container {
    padding: 10px;
  }

  .login-box {
    max-width: 100%;
  }

  .login-header {
    padding: 30px 20px 15px;

    .title {
      font-size: 20px;
    }
  }

  .login-form {
    padding: 20px;
  }
}
</style>
