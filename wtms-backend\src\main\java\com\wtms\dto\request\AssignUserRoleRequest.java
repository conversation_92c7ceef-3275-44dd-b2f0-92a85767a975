package com.wtms.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 用户角色分配请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "用户角色分配请求")
public class AssignUserRoleRequest {

    @Schema(description = "用户ID", example = "user-uuid")
    @NotBlank(message = "用户ID不能为空")
    private String userId;

    @Schema(description = "角色ID列表", example = "[\"role-uuid-1\", \"role-uuid-2\"]")
    @NotEmpty(message = "角色列表不能为空")
    private List<String> roleIds;

    @Schema(description = "分配类型", example = "direct", allowableValues = {"direct", "inherit", "temporary", "default"})
    private String assignType = "direct";

    @Schema(description = "是否启用", example = "true")
    private Boolean isEnabled = true;

    @Schema(description = "过期时间", example = "2024-12-31 23:59:59")
    private LocalDateTime expiresAt;

    @Schema(description = "备注")
    private String comment;
}
