# WTMS 系统部署指南

## 1. 环境要求

### 1.1 硬件要求

#### 最小配置
- **CPU**: 2核心
- **内存**: 4GB RAM
- **存储**: 50GB SSD
- **网络**: 100Mbps

#### 推荐配置
- **CPU**: 4核心以上
- **内存**: 8GB RAM以上
- **存储**: 100GB SSD以上
- **网络**: 1Gbps

### 1.2 软件要求

#### 基础环境
- **操作系统**: Ubuntu 20.04+ / CentOS 8+ / Windows Server 2019+
- **Java**: JDK 8 或 JDK 11
- **Node.js**: 18.x LTS
- **MySQL**: 8.0+
- **Redis**: 7.0+
- **Nginx**: 1.20+ (可选)

#### 开发工具
- **Maven**: 3.6+
- **Git**: 2.30+
- **Docker**: 20.10+ (可选)
- **Docker Compose**: 2.0+ (可选)

## 2. 快速部署

### 2.1 使用Docker Compose部署（推荐）

#### 2.1.1 克隆项目
```bash
git clone https://github.com/your-org/wtms.git
cd wtms
```

#### 2.1.2 配置环境变量
```bash
# 复制环境变量模板
cp .env.example .env

# 编辑环境变量
vim .env
```

#### 2.1.3 启动服务
```bash
# 启动所有服务
docker-compose -f docker-compose.prod.yml up -d

# 查看服务状态
docker-compose -f docker-compose.prod.yml ps

# 查看日志
docker-compose -f docker-compose.prod.yml logs -f
```

#### 2.1.4 初始化数据库
```bash
# 执行数据库初始化脚本
docker-compose -f docker-compose.prod.yml exec backend \
  java -jar app.jar --spring.profiles.active=prod --init-database
```

### 2.2 手动部署

#### 2.2.1 数据库部署

**安装MySQL 8.0**
```bash
# Ubuntu/Debian
sudo apt update
sudo apt install mysql-server-8.0

# CentOS/RHEL
sudo yum install mysql-server
sudo systemctl start mysqld
sudo systemctl enable mysqld
```

**创建数据库和用户**
```sql
-- 创建数据库
CREATE DATABASE wtms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 创建用户
CREATE USER 'wtms'@'%' IDENTIFIED BY 'your_password';
GRANT ALL PRIVILEGES ON wtms.* TO 'wtms'@'%';
FLUSH PRIVILEGES;
```

**导入数据库结构**
```bash
mysql -u wtms -p wtms < wtms-backend/sql/00_execute_all.sql
```

**安装Redis**
```bash
# Ubuntu/Debian
sudo apt install redis-server

# CentOS/RHEL
sudo yum install redis
sudo systemctl start redis
sudo systemctl enable redis
```

#### 2.2.2 后端部署

**编译项目**
```bash
cd wtms-backend
mvn clean package -DskipTests
```

**配置应用**
```bash
# 复制配置文件
cp src/main/resources/application-prod.yml.example \
   src/main/resources/application-prod.yml

# 编辑生产配置
vim src/main/resources/application-prod.yml
```

**启动应用**
```bash
# 方式1：直接运行JAR
java -jar target/wtms-backend-1.0.0.jar --spring.profiles.active=prod

# 方式2：使用systemd服务
sudo cp scripts/wtms-backend.service /etc/systemd/system/
sudo systemctl daemon-reload
sudo systemctl start wtms-backend
sudo systemctl enable wtms-backend
```

#### 2.2.3 前端部署

**构建前端**
```bash
cd wtms-frontend
npm install
npm run build
```

**配置Nginx**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 前端静态文件
    location / {
        root /var/www/wtms/dist;
        try_files $uri $uri/ /index.html;
        
        # 缓存静态资源
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 1y;
            add_header Cache-Control "public, immutable";
        }
    }
    
    # API代理
    location /api/ {
        proxy_pass http://localhost:55557;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 超时设置
        proxy_connect_timeout 30s;
        proxy_send_timeout 30s;
        proxy_read_timeout 30s;
    }
}
```

**部署静态文件**
```bash
sudo mkdir -p /var/www/wtms
sudo cp -r wtms-frontend/dist/* /var/www/wtms/
sudo chown -R www-data:www-data /var/www/wtms
```

## 3. 生产环境配置

### 3.1 数据库优化

#### 3.1.1 MySQL配置优化
```ini
# /etc/mysql/mysql.conf.d/mysqld.cnf
[mysqld]
# 基础配置
port = 3306
bind-address = 0.0.0.0
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# 内存配置
innodb_buffer_pool_size = 2G
innodb_log_file_size = 256M
innodb_log_buffer_size = 64M
key_buffer_size = 256M

# 连接配置
max_connections = 1000
max_connect_errors = 100000
wait_timeout = 28800
interactive_timeout = 28800

# 性能配置
innodb_flush_log_at_trx_commit = 2
innodb_flush_method = O_DIRECT
innodb_file_per_table = 1
query_cache_type = 0
query_cache_size = 0

# 日志配置
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2
log_queries_not_using_indexes = 1
```

#### 3.1.2 Redis配置优化
```conf
# /etc/redis/redis.conf
# 网络配置
bind 0.0.0.0
port 6379
timeout 300
tcp-keepalive 300

# 内存配置
maxmemory 1gb
maxmemory-policy allkeys-lru

# 持久化配置
save 900 1
save 300 10
save 60 10000
rdbcompression yes
rdbchecksum yes

# 安全配置
requirepass your_redis_password
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command CONFIG ""
```

### 3.2 应用配置

#### 3.2.1 JVM参数优化
```bash
# 生产环境JVM参数
JAVA_OPTS="-server \
  -Xms2g -Xmx2g \
  -XX:NewRatio=1 \
  -XX:SurvivorRatio=8 \
  -XX:+UseG1GC \
  -XX:MaxGCPauseMillis=200 \
  -XX:+UseStringDeduplication \
  -XX:+PrintGCDetails \
  -XX:+PrintGCTimeStamps \
  -Xloggc:/var/log/wtms/gc.log \
  -XX:+UseGCLogFileRotation \
  -XX:NumberOfGCLogFiles=5 \
  -XX:GCLogFileSize=10M \
  -Djava.awt.headless=true \
  -Dfile.encoding=UTF-8 \
  -Duser.timezone=Asia/Shanghai"

java $JAVA_OPTS -jar wtms-backend-1.0.0.jar
```

#### 3.2.2 应用配置文件
```yaml
# application-prod.yml
server:
  port: 55557
  tomcat:
    max-threads: 200
    min-spare-threads: 10
    max-connections: 8192
    accept-count: 100
    connection-timeout: 20000

spring:
  datasource:
    url: *************************************************************************
    username: ${DB_USERNAME:wtms}
    password: ${DB_PASSWORD:your_password}
    hikari:
      maximum-pool-size: 20
      minimum-idle: 5
      connection-timeout: 30000
      idle-timeout: 600000
      max-lifetime: 1800000
      
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:your_redis_password}
    timeout: 5000
    lettuce:
      pool:
        max-active: 20
        max-idle: 10
        min-idle: 5
        max-wait: 5000

logging:
  level:
    root: INFO
    com.wtms: INFO
  file:
    name: /var/log/wtms/application.log
    max-size: 100MB
    max-history: 30
```

### 3.3 安全配置

#### 3.3.1 防火墙配置
```bash
# Ubuntu UFW
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw deny 3306/tcp   # MySQL (仅内网访问)
sudo ufw deny 6379/tcp   # Redis (仅内网访问)
sudo ufw enable

# CentOS firewalld
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

#### 3.3.2 SSL证书配置
```nginx
server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL证书配置
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    
    # SSL安全配置
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 安全头
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options DENY always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-XSS-Protection "1; mode=block" always;
    
    # 其他配置...
}

# HTTP重定向到HTTPS
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}
```

## 4. 监控和维护

### 4.1 健康检查

#### 4.1.1 应用健康检查
```bash
# 检查应用状态
curl -f http://localhost:55557/api/v1/actuator/health

# 检查数据库连接
curl -f http://localhost:55557/api/v1/actuator/health/db

# 检查Redis连接
curl -f http://localhost:55557/api/v1/actuator/health/redis
```

#### 4.1.2 系统监控脚本
```bash
#!/bin/bash
# monitor.sh - 系统监控脚本

# 检查服务状态
check_service() {
    local service=$1
    if systemctl is-active --quiet $service; then
        echo "✅ $service is running"
    else
        echo "❌ $service is not running"
        systemctl restart $service
    fi
}

# 检查端口
check_port() {
    local port=$1
    local service=$2
    if netstat -tuln | grep -q ":$port "; then
        echo "✅ $service port $port is open"
    else
        echo "❌ $service port $port is not accessible"
    fi
}

# 检查磁盘空间
check_disk() {
    local usage=$(df / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ $usage -gt 80 ]; then
        echo "⚠️  Disk usage is ${usage}%"
    else
        echo "✅ Disk usage is ${usage}%"
    fi
}

# 执行检查
check_service mysql
check_service redis-server
check_service wtms-backend
check_service nginx

check_port 3306 MySQL
check_port 6379 Redis
check_port 55557 "WTMS Backend"
check_port 80 Nginx
check_port 443 "Nginx SSL"

check_disk
```

### 4.2 日志管理

#### 4.2.1 日志轮转配置
```bash
# /etc/logrotate.d/wtms
/var/log/wtms/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 wtms wtms
    postrotate
        systemctl reload wtms-backend
    endscript
}
```

### 4.3 备份策略

#### 4.3.1 数据库备份脚本
```bash
#!/bin/bash
# backup.sh - 数据库备份脚本

BACKUP_DIR="/backup/mysql"
DATE=$(date +%Y%m%d_%H%M%S)
DB_NAME="wtms"
DB_USER="wtms"
DB_PASS="your_password"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 执行备份
mysqldump -u$DB_USER -p$DB_PASS \
  --single-transaction \
  --routines \
  --triggers \
  $DB_NAME > $BACKUP_DIR/wtms_$DATE.sql

# 压缩备份文件
gzip $BACKUP_DIR/wtms_$DATE.sql

# 删除7天前的备份
find $BACKUP_DIR -name "wtms_*.sql.gz" -mtime +7 -delete

echo "Database backup completed: wtms_$DATE.sql.gz"
```

## 5. 故障排除

### 5.1 常见问题

#### 5.1.1 应用启动失败
```bash
# 检查日志
tail -f /var/log/wtms/application.log

# 检查端口占用
netstat -tuln | grep 55557

# 检查Java进程
ps aux | grep java

# 检查配置文件
java -jar wtms-backend-1.0.0.jar --spring.config.location=application-prod.yml --debug
```

#### 5.1.2 数据库连接问题
```bash
# 测试数据库连接
mysql -h localhost -u wtms -p wtms

# 检查MySQL状态
systemctl status mysql

# 查看MySQL错误日志
tail -f /var/log/mysql/error.log
```

#### 5.1.3 Redis连接问题
```bash
# 测试Redis连接
redis-cli -h localhost -p 6379 -a your_password ping

# 检查Redis状态
systemctl status redis

# 查看Redis日志
tail -f /var/log/redis/redis-server.log
```

### 5.2 性能调优

#### 5.2.1 数据库性能分析
```sql
-- 查看慢查询
SELECT * FROM mysql.slow_log ORDER BY start_time DESC LIMIT 10;

-- 查看连接状态
SHOW PROCESSLIST;

-- 查看表状态
SHOW TABLE STATUS LIKE 'tasks';

-- 分析查询执行计划
EXPLAIN SELECT * FROM tasks WHERE status = 'IN_PROGRESS';
```

#### 5.2.2 应用性能监控
```bash
# JVM内存使用情况
jstat -gc <pid>

# 线程堆栈信息
jstack <pid>

# 堆内存分析
jmap -histo <pid>
```

## 6. 高可用部署架构

### 6.1 生产环境架构图

```
                    ┌─────────────────┐
                    │   Load Balancer │
                    │   (Nginx/HAProxy)│
                    └─────────┬───────┘
                              │
                    ┌─────────┴───────┐
                    │                 │
            ┌───────▼──────┐  ┌──────▼──────┐
            │  Web Server  │  │ Web Server  │
            │   Node 1     │  │   Node 2    │
            │ (Primary)    │  │ (Secondary) │
            └───────┬──────┘  └──────┬──────┘
                    │                │
                    └─────────┬──────┘
                              │
                    ┌─────────▼───────┐
                    │  Database Cluster│
                    │                 │
            ┌───────▼──────┐  ┌──────▼──────┐
            │ MySQL Master │  │ MySQL Slave │
            │   (Write)    │  │   (Read)    │
            └──────────────┘  └─────────────┘
                    │
            ┌───────▼──────┐
            │ Redis Cluster│
            │ (Cache/Session)│
            └──────────────┘
```

### 6.2 Kubernetes部署

#### 6.2.1 命名空间配置
```yaml
# namespace.yaml
apiVersion: v1
kind: Namespace
metadata:
  name: wtms-prod
  labels:
    name: wtms-prod
    environment: production
```

#### 6.2.2 ConfigMap配置
```yaml
# configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: wtms-config
  namespace: wtms-prod
data:
  application.yml: |
    server:
      port: 8080
    spring:
      profiles:
        active: prod
      datasource:
        url: ************************************
        username: ${DB_USERNAME}
        password: ${DB_PASSWORD}
      redis:
        host: redis-service
        port: 6379
        password: ${REDIS_PASSWORD}
```

#### 6.2.3 Secret配置
```yaml
# secret.yaml
apiVersion: v1
kind: Secret
metadata:
  name: wtms-secret
  namespace: wtms-prod
type: Opaque
data:
  db-username: d3Rtcw==  # base64 encoded
  db-password: eW91cl9wYXNzd29yZA==
  redis-password: cmVkaXNfcGFzc3dvcmQ=
  jwt-secret: eW91cl9qd3Rfc2VjcmV0X2tleQ==
```

#### 6.2.4 Deployment配置
```yaml
# deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: wtms-backend
  namespace: wtms-prod
spec:
  replicas: 3
  selector:
    matchLabels:
      app: wtms-backend
  template:
    metadata:
      labels:
        app: wtms-backend
    spec:
      containers:
      - name: wtms-backend
        image: wtms/backend:1.0.0
        ports:
        - containerPort: 8080
        env:
        - name: DB_USERNAME
          valueFrom:
            secretKeyRef:
              name: wtms-secret
              key: db-username
        - name: DB_PASSWORD
          valueFrom:
            secretKeyRef:
              name: wtms-secret
              key: db-password
        - name: REDIS_PASSWORD
          valueFrom:
            secretKeyRef:
              name: wtms-secret
              key: redis-password
        - name: JWT_SECRET
          valueFrom:
            secretKeyRef:
              name: wtms-secret
              key: jwt-secret
        volumeMounts:
        - name: config-volume
          mountPath: /app/config
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10
      volumes:
      - name: config-volume
        configMap:
          name: wtms-config
```

#### 6.2.5 Service配置
```yaml
# service.yaml
apiVersion: v1
kind: Service
metadata:
  name: wtms-backend-service
  namespace: wtms-prod
spec:
  selector:
    app: wtms-backend
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: ClusterIP
```

#### 6.2.6 Ingress配置
```yaml
# ingress.yaml
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: wtms-ingress
  namespace: wtms-prod
  annotations:
    kubernetes.io/ingress.class: nginx
    cert-manager.io/cluster-issuer: letsencrypt-prod
    nginx.ingress.kubernetes.io/ssl-redirect: "true"
    nginx.ingress.kubernetes.io/proxy-body-size: "50m"
spec:
  tls:
  - hosts:
    - wtms.your-domain.com
    secretName: wtms-tls
  rules:
  - host: wtms.your-domain.com
    http:
      paths:
      - path: /api
        pathType: Prefix
        backend:
          service:
            name: wtms-backend-service
            port:
              number: 80
      - path: /
        pathType: Prefix
        backend:
          service:
            name: wtms-frontend-service
            port:
              number: 80
```

### 6.3 CI/CD流水线

#### 6.3.1 GitHub Actions配置
```yaml
# .github/workflows/deploy.yml
name: Deploy to Production

on:
  push:
    branches: [ main ]
    tags: [ 'v*' ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 8
      uses: actions/setup-java@v3
      with:
        java-version: '8'
        distribution: 'temurin'

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Run backend tests
      run: |
        cd wtms-backend
        mvn clean test

    - name: Run frontend tests
      run: |
        cd wtms-frontend
        npm ci
        npm run test:unit

    - name: Security scan
      run: |
        chmod +x scripts/security-scan.sh
        ./scripts/security-scan.sh

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3

    - name: Build backend
      run: |
        cd wtms-backend
        mvn clean package -DskipTests

    - name: Build frontend
      run: |
        cd wtms-frontend
        npm ci
        npm run build

    - name: Build Docker images
      run: |
        docker build -t wtms/backend:${{ github.sha }} wtms-backend/
        docker build -t wtms/frontend:${{ github.sha }} wtms-frontend/

    - name: Push to registry
      run: |
        echo ${{ secrets.DOCKER_PASSWORD }} | docker login -u ${{ secrets.DOCKER_USERNAME }} --password-stdin
        docker push wtms/backend:${{ github.sha }}
        docker push wtms/frontend:${{ github.sha }}

  deploy:
    needs: build
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'
    steps:
    - name: Deploy to Kubernetes
      run: |
        # 更新Kubernetes部署
        kubectl set image deployment/wtms-backend wtms-backend=wtms/backend:${{ github.sha }} -n wtms-prod
        kubectl set image deployment/wtms-frontend wtms-frontend=wtms/frontend:${{ github.sha }} -n wtms-prod

        # 等待部署完成
        kubectl rollout status deployment/wtms-backend -n wtms-prod
        kubectl rollout status deployment/wtms-frontend -n wtms-prod
```

### 6.4 蓝绿部署策略

#### 6.4.1 蓝绿部署脚本
```bash
#!/bin/bash
# blue-green-deploy.sh

set -e

NAMESPACE="wtms-prod"
NEW_VERSION=$1
CURRENT_COLOR=$(kubectl get service wtms-service -n $NAMESPACE -o jsonpath='{.spec.selector.color}')

if [ "$CURRENT_COLOR" = "blue" ]; then
    NEW_COLOR="green"
else
    NEW_COLOR="blue"
fi

echo "当前环境: $CURRENT_COLOR"
echo "部署到: $NEW_COLOR"
echo "版本: $NEW_VERSION"

# 部署新版本到非活跃环境
kubectl set image deployment/wtms-$NEW_COLOR wtms-backend=wtms/backend:$NEW_VERSION -n $NAMESPACE

# 等待部署完成
kubectl rollout status deployment/wtms-$NEW_COLOR -n $NAMESPACE

# 健康检查
echo "执行健康检查..."
for i in {1..30}; do
    if kubectl exec -n $NAMESPACE deployment/wtms-$NEW_COLOR -- curl -f http://localhost:8080/actuator/health; then
        echo "健康检查通过"
        break
    fi
    echo "等待应用启动... ($i/30)"
    sleep 10
done

# 切换流量
echo "切换流量到 $NEW_COLOR 环境"
kubectl patch service wtms-service -n $NAMESPACE -p '{"spec":{"selector":{"color":"'$NEW_COLOR'"}}}'

echo "部署完成！"
echo "如需回滚，请运行: kubectl patch service wtms-service -n $NAMESPACE -p '{\"spec\":{\"selector\":{\"color\":\"$CURRENT_COLOR\"}}}'"
```

### 6.5 灾难恢复

#### 6.5.1 数据备份策略
```bash
#!/bin/bash
# disaster-recovery-backup.sh

BACKUP_DIR="/backup/disaster-recovery/$(date +%Y%m%d_%H%M%S)"
mkdir -p $BACKUP_DIR

echo "开始灾难恢复备份..."

# 1. 数据库备份
echo "备份数据库..."
mysqldump -h mysql-master -u backup_user -p$BACKUP_PASSWORD \
  --single-transaction \
  --routines \
  --triggers \
  --all-databases > $BACKUP_DIR/full-database-backup.sql

# 2. 配置文件备份
echo "备份配置文件..."
kubectl get configmaps -n wtms-prod -o yaml > $BACKUP_DIR/configmaps.yaml
kubectl get secrets -n wtms-prod -o yaml > $BACKUP_DIR/secrets.yaml

# 3. 应用配置备份
echo "备份应用配置..."
cp -r /etc/wtms/ $BACKUP_DIR/app-config/

# 4. 文件存储备份
echo "备份文件存储..."
rsync -av /var/wtms/uploads/ $BACKUP_DIR/uploads/

# 5. 压缩备份
echo "压缩备份文件..."
tar -czf $BACKUP_DIR.tar.gz -C $(dirname $BACKUP_DIR) $(basename $BACKUP_DIR)
rm -rf $BACKUP_DIR

# 6. 上传到云存储
echo "上传到云存储..."
aws s3 cp $BACKUP_DIR.tar.gz s3://wtms-disaster-recovery/

echo "灾难恢复备份完成: $BACKUP_DIR.tar.gz"
```

#### 6.5.2 恢复脚本
```bash
#!/bin/bash
# disaster-recovery-restore.sh

BACKUP_FILE=$1
RESTORE_DIR="/tmp/wtms-restore"

if [ -z "$BACKUP_FILE" ]; then
    echo "用法: $0 <backup-file>"
    exit 1
fi

echo "开始灾难恢复..."

# 1. 解压备份文件
mkdir -p $RESTORE_DIR
tar -xzf $BACKUP_FILE -C $RESTORE_DIR

# 2. 恢复数据库
echo "恢复数据库..."
mysql -h mysql-master -u root -p$MYSQL_ROOT_PASSWORD < $RESTORE_DIR/*/full-database-backup.sql

# 3. 恢复配置
echo "恢复Kubernetes配置..."
kubectl apply -f $RESTORE_DIR/*/configmaps.yaml
kubectl apply -f $RESTORE_DIR/*/secrets.yaml

# 4. 恢复应用配置
echo "恢复应用配置..."
cp -r $RESTORE_DIR/*/app-config/* /etc/wtms/

# 5. 恢复文件存储
echo "恢复文件存储..."
rsync -av $RESTORE_DIR/*/uploads/ /var/wtms/uploads/

# 6. 重启应用
echo "重启应用..."
kubectl rollout restart deployment/wtms-backend -n wtms-prod
kubectl rollout restart deployment/wtms-frontend -n wtms-prod

# 7. 清理临时文件
rm -rf $RESTORE_DIR

echo "灾难恢复完成！"
```

---

**文档版本**: v1.0.0
**最后更新**: 2024-07-28
**维护团队**: WTMS开发团队
