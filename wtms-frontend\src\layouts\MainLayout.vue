<template>
  <div class="main-layout">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="sidebarWidth" class="main-sidebar">
        <div class="sidebar-header">
          <div class="logo">
            <img src="/logo.png" alt="WTMS" />
            <span v-show="!isCollapsed" class="logo-text">WTMS</span>
          </div>
        </div>
        
        <el-scrollbar class="sidebar-scrollbar">
          <el-menu
            :default-active="activeMenu"
            :collapse="isCollapsed"
            :unique-opened="true"
            router
            class="sidebar-menu"
          >
            <template v-for="route in menuRoutes" :key="route.path">
              <el-menu-item
                v-if="!route.children || route.children.length === 0"
                :index="route.path"
                @click="handleMenuClick(route)"
              >
                <el-icon v-if="route.meta?.icon">
                  <component :is="route.meta.icon" />
                </el-icon>
                <template #title>{{ route.meta?.title }}</template>
              </el-menu-item>
              
              <el-sub-menu
                v-else
                :index="route.path"
              >
                <template #title>
                  <el-icon v-if="route.meta?.icon">
                    <component :is="route.meta.icon" />
                  </el-icon>
                  <span>{{ route.meta?.title }}</span>
                </template>
                
                <el-menu-item
                  v-for="child in route.children"
                  :key="child.path"
                  :index="child.path"
                  @click="handleMenuClick(child)"
                >
                  <el-icon v-if="child.meta?.icon">
                    <component :is="child.meta.icon" />
                  </el-icon>
                  <template #title>{{ child.meta?.title }}</template>
                </el-menu-item>
              </el-sub-menu>
            </template>
          </el-menu>
        </el-scrollbar>
      </el-aside>

      <el-container>
        <!-- 顶部导航栏 -->
        <el-header class="main-header">
          <div class="header-left">
            <el-button
              type="text"
              size="large"
              @click="toggleSidebar"
            >
              <el-icon>
                <Expand v-if="isCollapsed" />
                <Fold v-else />
              </el-icon>
            </el-button>
            
            <el-breadcrumb separator="/" class="breadcrumb">
              <el-breadcrumb-item
                v-for="item in breadcrumbList"
                :key="item.path"
                :to="item.path"
              >
                {{ item.title }}
              </el-breadcrumb-item>
            </el-breadcrumb>
          </div>

          <div class="header-center">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索任务、人员、文档..."
              prefix-icon="Search"
              class="search-input"
              clearable
              @keyup.enter="handleSearch"
            />
          </div>

          <div class="header-right">
            <el-button type="primary" @click="createTask">
              <el-icon><Plus /></el-icon>
              新建任务
            </el-button>

            <el-badge :value="unreadCount" :hidden="unreadCount === 0">
              <el-button circle @click="showNotifications">
                <el-icon><Bell /></el-icon>
              </el-button>
            </el-badge>

            <el-dropdown @command="handleUserCommand" class="user-dropdown">
              <div class="user-info">
                <el-avatar :src="userStore.userInfo?.avatar" :size="32">
                  {{ userStore.userInfo?.fullName?.charAt(0) }}
                </el-avatar>
                <span v-show="!isCollapsed" class="username">
                  {{ userStore.userInfo?.fullName }}
                </span>
                <el-icon><ArrowDown /></el-icon>
              </div>
              
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item command="profile">
                    <el-icon><User /></el-icon>
                    个人资料
                  </el-dropdown-item>
                  <el-dropdown-item command="settings">
                    <el-icon><Setting /></el-icon>
                    系统设置
                  </el-dropdown-item>
                  <el-dropdown-item command="help">
                    <el-icon><QuestionFilled /></el-icon>
                    帮助文档
                  </el-dropdown-item>
                  <el-dropdown-item divided command="logout">
                    <el-icon><SwitchButton /></el-icon>
                    退出登录
                  </el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主内容区 -->
        <el-main class="main-content">
          <router-view v-slot="{ Component, route }">
            <transition name="fade-transform" mode="out-in">
              <keep-alive :include="cachedViews">
                <component :is="Component" :key="route.path" />
              </keep-alive>
            </transition>
          </router-view>
        </el-main>

        <!-- 底部状态栏 -->
        <el-footer class="main-footer">
          <div class="footer-content">
            <span>&copy; 2024 WTMS工作任务管理平台</span>
            <span>当前版本：v1.0.0</span>
          </div>
        </el-footer>
      </el-container>
    </el-container>

    <!-- 通知抽屉 -->
    <el-drawer
      v-model="notificationDrawer"
      title="通知中心"
      direction="rtl"
      size="400px"
    >
      <div class="notification-content">
        <!-- 通知内容将在这里显示 -->
        <el-empty description="暂无通知" />
      </div>
    </el-drawer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Fold,
  Expand,
  Search,
  Bell,
  Setting,
  User,
  SwitchButton,
  House,
  List,
  Share,
  Star,
  TrendCharts,
  Close,
  ArrowRight,
  ArrowDown,
  Plus
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import type { BreadcrumbItem } from '@/types/common'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 状态
const isCollapsed = ref(false)
const searchKeyword = ref('')
const unreadCount = ref(0)
const notificationDrawer = ref(false)
const cachedViews = ref<string[]>([])

// 计算属性
const sidebarWidth = computed(() => isCollapsed.value ? '64px' : '240px')

const activeMenu = computed(() => route.path)

const menuRoutes = computed(() => {
  return router.getRoutes()
    .filter(route => {
      return route.path !== '/' && 
             route.meta?.requiresAuth && 
             !route.meta?.hideInMenu &&
             route.path !== '/login'
    })
    .sort((a, b) => (a.meta?.order || 0) - (b.meta?.order || 0))
})

const breadcrumbList = computed((): BreadcrumbItem[] => {
  const matched = route.matched.filter(item => item.meta?.title)
  const breadcrumbs: BreadcrumbItem[] = []
  
  matched.forEach(item => {
    if (item.meta?.title) {
      breadcrumbs.push({
        title: item.meta.title as string,
        path: item.path
      })
    }
  })
  
  return breadcrumbs
})

// 方法
const toggleSidebar = () => {
  isCollapsed.value = !isCollapsed.value
}

const handleMenuClick = (menuRoute: any) => {
  if (menuRoute.path !== route.path) {
    router.push(menuRoute.path)
  }
}

const handleSearch = () => {
  if (searchKeyword.value.trim()) {
    // 实现全局搜索逻辑
    console.log('搜索:', searchKeyword.value)
    ElMessage.info(`搜索功能开发中: ${searchKeyword.value}`)
  }
}

const createTask = () => {
  router.push('/tasks/create')
}

const showNotifications = () => {
  notificationDrawer.value = true
}

const handleUserCommand = async (command: string) => {
  switch (command) {
    case 'profile':
      router.push('/profile')
      break
    case 'settings':
      router.push('/settings')
      break
    case 'help':
      window.open('/help', '_blank')
      break
    case 'logout':
      try {
        await ElMessageBox.confirm(
          '确定要退出登录吗？',
          '系统提示',
          {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          }
        )
        
        await userStore.logout()
        router.push('/login')
      } catch (error) {
        // 用户取消
      }
      break
  }
}

// 监听路由变化，更新面包屑
watch(
  () => route.path,
  () => {
    // 可以在这里添加页面缓存逻辑
  },
  { immediate: true }
)
</script>

<style lang="scss" scoped>
.main-layout {
  height: 100vh;
  
  .el-container {
    height: 100%;
  }
}

.main-sidebar {
  background: #001529;
  transition: width 0.3s;
  
  .sidebar-header {
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-bottom: 1px solid #1f2937;
    
    .logo {
      display: flex;
      align-items: center;
      gap: 12px;
      
      img {
        width: 32px;
        height: 32px;
        border-radius: 4px;
      }
      
      .logo-text {
        color: white;
        font-size: 18px;
        font-weight: 600;
      }
    }
  }
  
  .sidebar-scrollbar {
    height: calc(100vh - 60px);
  }
  
  .sidebar-menu {
    border: none;
    background: transparent;
    
    :deep(.el-menu-item),
    :deep(.el-sub-menu__title) {
      color: rgba(255, 255, 255, 0.8);
      
      &:hover {
        background-color: #1f2937;
        color: white;
      }
      
      &.is-active {
        background-color: #1890ff;
        color: white;
      }
    }
    
    :deep(.el-sub-menu .el-menu-item) {
      background-color: #0c1419;
      
      &:hover {
        background-color: #1f2937;
      }
      
      &.is-active {
        background-color: #1890ff;
      }
    }
  }
}

.main-header {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
  
  .header-left {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .breadcrumb {
      :deep(.el-breadcrumb__item) {
        .el-breadcrumb__inner {
          color: #666;
          
          &:hover {
            color: #1890ff;
          }
        }
        
        &:last-child .el-breadcrumb__inner {
          color: #333;
          font-weight: 500;
        }
      }
    }
  }
  
  .header-center {
    flex: 1;
    max-width: 400px;
    margin: 0 24px;
    
    .search-input {
      width: 100%;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .user-dropdown {
      .user-info {
        display: flex;
        align-items: center;
        gap: 8px;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: background-color 0.3s;
        
        &:hover {
          background-color: #f5f5f5;
        }
        
        .username {
          font-size: 14px;
          color: #333;
        }
      }
    }
  }
}

.main-content {
  background: #f5f5f5;
  padding: 24px;
  overflow-y: auto;
}

.main-footer {
  background: white;
  border-top: 1px solid #e8e8e8;
  height: 50px;
  line-height: 50px;
  padding: 0 24px;
  
  .footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #666;
  }
}

.notification-content {
  padding: 16px;
}

// 页面切换动画
.fade-transform-enter-active,
.fade-transform-leave-active {
  transition: all 0.3s;
}

.fade-transform-enter-from {
  opacity: 0;
  transform: translateX(30px);
}

.fade-transform-leave-to {
  opacity: 0;
  transform: translateX(-30px);
}

// 响应式设计
@media (max-width: 768px) {
  .main-header {
    padding: 0 16px;
    
    .header-center {
      display: none;
    }
    
    .header-right {
      gap: 8px;
    }
  }
  
  .main-content {
    padding: 16px;
  }
}
</style>
