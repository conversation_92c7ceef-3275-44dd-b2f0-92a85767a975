// 用户相关类型定义

export interface User {
  id: string
  username: string
  email: string
  phone?: string
  fullName: string
  avatar?: string
  employeeId?: string
  hireDate?: string
  status: 'active' | 'inactive' | 'locked'
  lastLoginAt?: string
  loginCount: number
  settings?: Record<string, any>
  department?: Department
  role?: Role
  createdAt: string
  updatedAt: string
}

export interface Department {
  id: string
  name: string
  code: string
  description?: string
  parentId?: string
  managerId?: string
  level: number
  sortOrder: number
  status: 'active' | 'inactive'
  createdAt: string
  updatedAt: string
}

export interface Role {
  id: string
  name: string
  code: string
  description?: string
  permissions: string[]
  isSystem: boolean
  createdAt: string
  updatedAt: string
}

export interface LoginCredentials {
  username: string
  password: string
  captcha?: string
  captchaId?: string
  rememberMe?: boolean
}

export interface LoginResponse {
  token: string
  refreshToken: string
  user: User
  expiresIn: number
}

export interface RefreshTokenRequest {
  refreshToken: string
}

export interface TokenResponse {
  token: string
  refreshToken: string
  expiresIn: number
}

export interface UserInfoResponse {
  user: User
  permissions: string[]
  menus: MenuItem[]
}

export interface MenuItem {
  id: string
  name: string
  path: string
  icon?: string
  component?: string
  parentId?: string
  sortOrder: number
  type: 'menu' | 'button'
  permission?: string
  children?: MenuItem[]
}

export interface UpdateUserRequest {
  fullName?: string
  email?: string
  phone?: string
  avatar?: string
  departmentId?: string
  roleId?: string
}

export interface ChangePasswordRequest {
  oldPassword: string
  newPassword: string
  confirmPassword: string
}

export interface UserQueryParams {
  page?: number
  pageSize?: number
  search?: string
  departmentId?: string
  roleId?: string
  status?: string
  sortBy?: string
  sortOrder?: 'asc' | 'desc'
}
