<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WTMS 紧急登录</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 500px;
            margin: 50px auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .login-container {
            background: white;
            padding: 40px;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        h1 {
            text-align: center;
            color: #333;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 20px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: bold;
            color: #555;
        }
        input {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            box-sizing: border-box;
        }
        input:focus {
            border-color: #667eea;
            outline: none;
        }
        button {
            width: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            border: none;
            border-radius: 6px;
            font-size: 18px;
            cursor: pointer;
            margin-top: 10px;
        }
        button:hover {
            opacity: 0.9;
        }
        button:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 14px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .loading {
            text-align: center;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <h1>🚨 WTMS 紧急登录</h1>
        
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin" required>
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="admin123456" required>
            </div>
            <button type="submit" id="loginBtn">🚀 立即登录</button>
        </form>
        
        <div id="result"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:55557/api/v1';
        
        function log(message, type = 'info') {
            const resultDiv = document.getElementById('result');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
        }

        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const loginBtn = document.getElementById('loginBtn');
            
            // 禁用按钮
            loginBtn.disabled = true;
            loginBtn.textContent = '登录中...';
            
            try {
                log('正在尝试登录...', 'info');
                
                // 清除所有存储
                localStorage.clear();
                sessionStorage.clear();
                
                // 发送登录请求
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', [...response.headers.entries()]);
                
                const data = await response.json();
                console.log('Response data:', data);
                
                if (response.ok && data.success && data.data) {
                    const { token, user, refreshToken } = data.data;
                    
                    // 保存token
                    localStorage.setItem('wtms_token', token);
                    if (refreshToken) {
                        localStorage.setItem('refreshToken', refreshToken);
                    }
                    
                    log(`✅ 登录成功！\n用户: ${user.fullName}\n正在跳转到主界面...`, 'success');
                    
                    // 等待2秒后跳转
                    setTimeout(() => {
                        window.location.href = 'http://localhost:33336';
                    }, 2000);
                    
                } else {
                    log(`❌ 登录失败！\n错误信息: ${data.message || '未知错误'}\n状态码: ${response.status}`, 'error');
                }
                
            } catch (error) {
                console.error('Login error:', error);
                log(`❌ 登录请求失败！\n错误: ${error.message}\n\n请检查:\n1. 后端服务是否运行 (http://localhost:55557)\n2. 网络连接是否正常\n3. 浏览器控制台是否有CORS错误`, 'error');
            } finally {
                // 恢复按钮
                loginBtn.disabled = false;
                loginBtn.textContent = '🚀 立即登录';
            }
        });
        
        // 页面加载时检查服务状态
        window.addEventListener('load', async function() {
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                if (data.success) {
                    log('✅ 后端服务连接正常', 'success');
                } else {
                    log('⚠️ 后端服务响应异常', 'error');
                }
            } catch (error) {
                log('❌ 无法连接到后端服务', 'error');
            }
        });
    </script>
</body>
</html>
