# WTMS 系统管理员操作手册

## 目录
- [管理员职责](#管理员职责)
- [系统初始化](#系统初始化)
- [用户管理](#用户管理)
- [权限管理](#权限管理)
- [系统配置](#系统配置)
- [数据管理](#数据管理)
- [监控维护](#监控维护)
- [故障处理](#故障处理)

## 管理员职责

### 系统管理员角色
- **超级管理员**: 拥有系统所有权限，负责系统整体管理
- **用户管理员**: 负责用户账户和权限管理
- **业务管理员**: 负责业务配置和流程管理
- **技术管理员**: 负责系统技术维护和监控

### 主要职责
1. **用户管理**: 创建、修改、删除用户账户
2. **权限控制**: 配置角色权限和数据访问范围
3. **系统配置**: 设置系统参数和业务规则
4. **数据维护**: 数据备份、清理和迁移
5. **监控运维**: 系统性能监控和故障处理
6. **安全管理**: 安全策略制定和执行

## 系统初始化

### 首次登录设置
1. **修改默认密码**
   ```
   默认管理员账户: admin
   默认密码: admin123456
   ```
   - 登录后立即修改密码
   - 设置强密码策略
   - 启用双因素认证（如果支持）

2. **基础信息配置**
   - 系统名称和Logo
   - 组织信息设置
   - 时区和语言配置
   - 联系方式设置

3. **邮件服务配置**
   ```
   SMTP服务器: smtp.your-domain.com
   端口: 587 (TLS) 或 465 (SSL)
   用户名: <EMAIL>
   密码: your-email-password
   ```

### 组织架构设置
1. **部门管理**
   - 创建部门层级结构
   - 设置部门负责人
   - 配置部门权限范围

2. **岗位管理**
   - 定义岗位职责
   - 设置岗位级别
   - 关联岗位权限

3. **团队管理**
   - 创建项目团队
   - 分配团队成员
   - 设置团队权限

## 用户管理

### 用户账户管理

#### 批量创建用户
1. **Excel模板导入**
   ```
   用户名 | 姓名 | 邮箱 | 部门 | 岗位 | 角色
   zhangsan | 张三 | <EMAIL> | 技术部 | 开发工程师 | 普通用户
   lisi | 李四 | <EMAIL> | 产品部 | 产品经理 | 项目经理
   ```

2. **导入步骤**
   - 下载用户导入模板
   - 填写用户信息
   - 上传Excel文件
   - 验证数据格式
   - 确认导入结果

#### 单个用户管理
1. **创建用户**
   - 基本信息：用户名、姓名、邮箱、手机
   - 组织信息：部门、岗位、直属上级
   - 账户设置：角色、状态、有效期

2. **修改用户**
   - 更新用户基本信息
   - 调整组织关系
   - 修改角色权限

3. **用户状态管理**
   - 激活/禁用账户
   - 重置用户密码
   - 解锁被锁定账户

### 用户权限管理

#### 角色权限配置
1. **预定义角色**
   - **超级管理员**: 系统所有权限
   - **部门经理**: 部门内用户和任务管理
   - **项目经理**: 项目相关权限
   - **普通用户**: 基础功能权限

2. **自定义角色**
   - 创建新角色
   - 配置功能权限
   - 设置数据权限
   - 分配给用户

#### 权限矩阵示例
```
功能模块        | 超级管理员 | 部门经理 | 项目经理 | 普通用户
用户管理        |    ✓     |    ✓    |    ✗    |    ✗
任务管理        |    ✓     |    ✓    |    ✓    |    ✓
工作流管理      |    ✓     |    ✓    |    ✓    |    ✗
质量评价        |    ✓     |    ✓    |    ✓    |    ✓
系统配置        |    ✓     |    ✗    |    ✗    |    ✗
```

## 权限管理

### 功能权限配置

#### 菜单权限
1. **权限配置步骤**
   - 进入"系统管理" → "权限管理"
   - 选择角色或用户
   - 勾选可访问的菜单项
   - 保存权限配置

2. **权限继承规则**
   - 子菜单继承父菜单权限
   - 用户权限 = 角色权限 + 个人权限
   - 拒绝权限优先于允许权限

#### 操作权限
1. **CRUD权限**
   - Create: 创建权限
   - Read: 查看权限
   - Update: 修改权限
   - Delete: 删除权限

2. **特殊权限**
   - 审批权限：审核任务和流程
   - 分配权限：分配任务给他人
   - 导出权限：导出数据
   - 管理权限：管理其他用户

### 数据权限配置

#### 数据范围设置
1. **全部数据**: 可以查看所有数据
2. **部门数据**: 只能查看本部门数据
3. **个人数据**: 只能查看个人相关数据
4. **自定义数据**: 根据条件过滤数据

#### 数据权限规则
```sql
-- 部门数据权限示例
SELECT * FROM tasks 
WHERE department_id IN (
    SELECT id FROM departments 
    WHERE id = #{userDepartmentId} 
    OR parent_id = #{userDepartmentId}
)

-- 个人数据权限示例
SELECT * FROM tasks 
WHERE creator_id = #{userId} 
   OR assignee_id = #{userId}
   OR participant_ids LIKE CONCAT('%', #{userId}, '%')
```

## 系统配置

### 基础参数配置

#### 系统参数
1. **系统信息**
   - 系统名称：WTMS工作任务管理系统
   - 版本号：1.0.0
   - 版权信息：© 2024 Your Company

2. **业务参数**
   - 任务状态：待开始、进行中、已完成等
   - 优先级：高、中、低
   - 任务类型：开发、测试、设计等

3. **安全参数**
   - 密码最小长度：8位
   - 密码复杂度要求：包含大小写字母和数字
   - 登录失败锁定次数：5次
   - 会话超时时间：30分钟

#### 业务规则配置
1. **任务规则**
   - 任务自动分配规则
   - 任务超期提醒规则
   - 任务完成验收规则

2. **工作流规则**
   - 流程超时处理规则
   - 节点处理人规则
   - 流程回退规则

3. **评价规则**
   - 评价周期设置
   - 评价维度权重
   - 评价结果计算规则

### 通知配置

#### 邮件通知
1. **通知类型**
   - 任务分配通知
   - 任务截止提醒
   - 流程审批通知
   - 评价邀请通知

2. **邮件模板**
   ```html
   <h2>任务分配通知</h2>
   <p>您好，${userName}！</p>
   <p>您有一个新的任务需要处理：</p>
   <ul>
     <li>任务名称：${taskTitle}</li>
     <li>截止时间：${deadline}</li>
     <li>优先级：${priority}</li>
   </ul>
   <p>请及时登录系统查看详情。</p>
   ```

#### 系统通知
1. **通知渠道**
   - 站内消息
   - 邮件通知
   - 短信通知（可选）
   - 第三方集成（钉钉、企业微信等）

2. **通知规则**
   - 即时通知：重要事件立即通知
   - 定时通知：每日/每周汇总通知
   - 条件通知：满足条件时触发通知

## 数据管理

### 数据备份

#### 自动备份配置
1. **备份策略**
   - 全量备份：每周一次
   - 增量备份：每日一次
   - 日志备份：每小时一次

2. **备份脚本**
   ```bash
   #!/bin/bash
   # WTMS数据库备份脚本
   DATE=$(date +%Y%m%d_%H%M%S)
   BACKUP_DIR="/app/backups"
   DB_NAME="wtms"
   
   # 创建备份目录
   mkdir -p $BACKUP_DIR
   
   # 执行备份
   mysqldump -u root -p$MYSQL_PASSWORD $DB_NAME > $BACKUP_DIR/wtms_$DATE.sql
   
   # 压缩备份文件
   gzip $BACKUP_DIR/wtms_$DATE.sql
   
   # 删除7天前的备份
   find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete
   ```

#### 手动备份
1. **数据库备份**
   - 进入"系统管理" → "数据管理"
   - 点击"立即备份"
   - 选择备份范围
   - 下载备份文件

2. **文件备份**
   - 备份上传的附件文件
   - 备份系统配置文件
   - 备份日志文件

### 数据清理

#### 定期清理任务
1. **清理策略**
   - 删除过期的临时文件
   - 清理超过保留期的日志
   - 归档历史数据

2. **清理配置**
   ```yaml
   cleanup:
     enabled: true
     schedule: "0 0 2 * * ?"  # 每天凌晨2点执行
     retention:
       logs: 30              # 日志保留30天
       temp_files: 7         # 临时文件保留7天
       archived_tasks: 365   # 归档任务保留1年
   ```

#### 数据归档
1. **归档规则**
   - 已完成超过1年的任务
   - 已结束的工作流实例
   - 历史评价数据

2. **归档操作**
   - 将数据移动到归档表
   - 保持数据完整性
   - 提供归档数据查询接口

## 监控维护

### 系统监控

#### 性能监控
1. **关键指标**
   - CPU使用率
   - 内存使用率
   - 磁盘空间使用率
   - 数据库连接数
   - 响应时间

2. **监控工具**
   - Spring Boot Actuator
   - Prometheus + Grafana
   - 自定义监控脚本

#### 业务监控
1. **用户活跃度**
   - 日活跃用户数
   - 登录成功率
   - 功能使用统计

2. **任务统计**
   - 任务创建数量
   - 任务完成率
   - 平均处理时间

### 日志管理

#### 日志分类
1. **应用日志**
   - 业务操作日志
   - 错误异常日志
   - 性能监控日志

2. **访问日志**
   - 用户登录日志
   - API访问日志
   - 文件下载日志

3. **安全日志**
   - 权限变更日志
   - 敏感操作日志
   - 异常访问日志

#### 日志分析
1. **日志查询**
   - 按时间范围查询
   - 按用户查询
   - 按操作类型查询

2. **异常分析**
   - 错误统计分析
   - 性能瓶颈分析
   - 安全威胁分析

## 故障处理

### 常见故障

#### 系统无法访问
1. **检查步骤**
   - 检查服务器状态
   - 检查网络连接
   - 检查防火墙设置
   - 检查应用服务状态

2. **解决方案**
   ```bash
   # 检查服务状态
   docker-compose ps
   
   # 重启服务
   docker-compose restart
   
   # 查看日志
   docker-compose logs -f
   ```

#### 数据库连接失败
1. **故障现象**
   - 登录失败
   - 数据加载错误
   - 操作超时

2. **处理步骤**
   ```bash
   # 检查数据库状态
   docker-compose exec mysql mysql -u root -p -e "SELECT 1"
   
   # 检查连接数
   docker-compose exec mysql mysql -u root -p -e "SHOW PROCESSLIST"
   
   # 重启数据库
   docker-compose restart mysql
   ```

#### 文件上传失败
1. **可能原因**
   - 磁盘空间不足
   - 文件权限问题
   - 文件大小超限

2. **解决方法**
   ```bash
   # 检查磁盘空间
   df -h
   
   # 检查上传目录权限
   ls -la /app/uploads
   
   # 修复权限
   chmod 755 /app/uploads
   chown -R app:app /app/uploads
   ```

### 应急处理

#### 数据恢复
1. **从备份恢复**
   ```bash
   # 停止应用服务
   docker-compose stop backend
   
   # 恢复数据库
   gunzip -c backup_20240101_120000.sql.gz | \
   docker-compose exec -T mysql mysql -u root -p wtms
   
   # 重启服务
   docker-compose start backend
   ```

2. **部分数据恢复**
   - 恢复特定表数据
   - 恢复特定时间点数据
   - 恢复删除的记录

#### 系统回滚
1. **版本回滚**
   ```bash
   # 切换到稳定版本
   git checkout v1.0.0
   
   # 重新构建和部署
   docker-compose down
   docker-compose build
   docker-compose up -d
   ```

2. **配置回滚**
   - 恢复配置文件
   - 重置系统参数
   - 恢复权限设置

---

**文档版本**: v1.0.0  
**更新时间**: 2024年1月  
**维护团队**: WTMS技术团队
