import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { User, LoginCredentials, LoginResponse, UserInfoResponse } from '@/types/user'
import { authApi } from '@/api/auth'
import { getToken, setToken, removeToken } from '@/utils/auth'
import { ElMessage } from 'element-plus'

export const useUserStore = defineStore('user', () => {
  // 状态
  const token = ref<string>(getToken() || '')
  const currentUser = ref<User | null>(null)
  const permissions = ref<string[]>([])
  const isLoading = ref(false)

  // 计算属性
  const isLoggedIn = computed(() => {
    const hasToken = !!token.value
    const hasUser = !!currentUser.value
    console.log('isLoggedIn check:', { hasToken, hasUser, token: token.value?.substring(0, 20) + '...', user: currentUser.value?.username })
    return hasToken && hasUser
  })
  
  const userInfo = computed(() => currentUser.value)
  
  const hasPermission = computed(() => (permission: string) => {
    if (!permission) return true
    return permissions.value.includes(permission) || permissions.value.includes('*')
  })

  const hasRole = computed(() => (role: string) => {
    if (!currentUser.value?.role) return false
    return currentUser.value.role.code === role
  })

  const isAdmin = computed(() => hasRole.value('ADMIN'))

  // 方法
  const login = async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      isLoading.value = true
      const response = await authApi.login(credentials)

      if (response.success && response.data) {
        const { token: accessToken, user, expiresIn, refreshToken } = response.data

        console.log('Login response data:', response.data)
        console.log('User data:', user)

        // 保存token和用户信息
        token.value = accessToken
        currentUser.value = user

        // 设置权限 - 从用户角色中获取
        if (user.role && user.role.permissions) {
          permissions.value = user.role.permissions
        } else {
          permissions.value = []
        }

        // 持久化token - 同时保存到Cookie和localStorage以确保兼容性
        setToken(accessToken)
        localStorage.setItem('wtms_token', accessToken)

        // 保存刷新token
        if (refreshToken) {
          localStorage.setItem('refreshToken', refreshToken)
        }

        console.log('Login successful, user:', user)
        console.log('Permissions set:', permissions.value)
        console.log('isLoggedIn:', !!token.value && !!currentUser.value)

        ElMessage.success('登录成功')
        return true
      } else {
        ElMessage.error(response.message || '登录失败')
        return false
      }
    } catch (error: any) {
      console.error('Login error:', error)
      ElMessage.error(error.message || '登录失败，请稍后重试')
      return false
    } finally {
      isLoading.value = false
    }
  }

  const logout = async (): Promise<void> => {
    try {
      // 调用后端登出接口
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除本地状态
      token.value = ''
      currentUser.value = null
      permissions.value = []
      removeToken()
      
      ElMessage.success('已退出登录')
    }
  }

  const refreshToken = async (): Promise<boolean> => {
    try {
      const refreshToken = localStorage.getItem('refreshToken')
      if (!refreshToken) {
        return false
      }

      const response = await authApi.refreshToken({ refreshToken })
      
      if (response.success && response.data) {
        token.value = response.data.token
        setToken(response.data.token)
        return true
      }
      
      return false
    } catch (error) {
      console.error('Refresh token error:', error)
      return false
    }
  }

  const getCurrentUserInfo = async (): Promise<void> => {
    try {
      if (!token.value) return

      const response = await authApi.getCurrentUser()
      
      if (response.success && response.data) {
        currentUser.value = response.data.user
        permissions.value = response.data.permissions
      }
    } catch (error) {
      console.error('Get user info error:', error)
      // 如果获取用户信息失败，可能token已过期
      await logout()
    }
  }

  const updateUserInfo = (userInfo: Partial<User>): void => {
    if (currentUser.value) {
      currentUser.value = { ...currentUser.value, ...userInfo }
    }
  }

  const checkLoginStatus = async (): Promise<void> => {
    if (token.value && !currentUser.value) {
      try {
        await getCurrentUserInfo()
      } catch (error) {
        console.error('Check login status failed:', error)
        // 如果获取用户信息失败，清除token
        resetState()
      }
    }
  }

  const changePassword = async (oldPassword: string, newPassword: string): Promise<boolean> => {
    try {
      const response = await authApi.changePassword({
        oldPassword,
        newPassword,
        confirmPassword: newPassword
      })
      
      if (response.success) {
        ElMessage.success('密码修改成功')
        return true
      } else {
        ElMessage.error(response.message || '密码修改失败')
        return false
      }
    } catch (error: any) {
      console.error('Change password error:', error)
      ElMessage.error(error.message || '密码修改失败')
      return false
    }
  }

  // 重置状态
  const resetState = (): void => {
    token.value = ''
    currentUser.value = null
    permissions.value = []
    isLoading.value = false
    removeToken()
  }

  return {
    // 状态
    token: readonly(token),
    currentUser: readonly(currentUser),
    permissions: readonly(permissions),
    isLoading: readonly(isLoading),
    
    // 计算属性
    isLoggedIn,
    userInfo,
    hasPermission,
    hasRole,
    isAdmin,
    
    // 方法
    login,
    logout,
    refreshToken,
    getCurrentUserInfo,
    updateUserInfo,
    checkLoginStatus,
    changePassword,
    resetState
  }
})
