package com.wtms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.wtms.common.exception.BusinessException;
import com.wtms.common.result.ResultCode;
import com.wtms.entity.TaskCategory;
import com.wtms.mapper.TaskCategoryMapper;
import com.wtms.service.TaskCategoryService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 任务分类服务实现
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class TaskCategoryServiceImpl implements TaskCategoryService {

    @Autowired
    private TaskCategoryMapper taskCategoryMapper;

    @Override
    public List<TaskCategory> getEnabledCategories() {
        return taskCategoryMapper.selectEnabledCategories();
    }

    @Override
    public TaskCategory getCategoryById(String categoryId) {
        TaskCategory category = taskCategoryMapper.selectById(categoryId);
        if (category == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "任务分类不存在");
        }
        return category;
    }

    @Override
    public TaskCategory getCategoryByCode(String code) {
        return taskCategoryMapper.selectByCode(code);
    }

    @Override
    public TaskCategory createCategory(TaskCategory category) {
        log.info("Creating task category: {}", category.getName());

        // 验证分类编码唯一性
        if (StrUtil.isNotBlank(category.getCode()) && existsByCode(category.getCode())) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS.getCode(), "分类编码已存在");
        }

        // 验证分类名称唯一性
        if (existsByName(category.getName())) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS.getCode(), "分类名称已存在");
        }

        // 设置默认值
        if (category.getIsEnabled() == null) {
            category.setIsEnabled(true);
        }

        if (category.getSortOrder() == null) {
            category.setSortOrder(taskCategoryMapper.getNextSortOrder());
        }

        if (StrUtil.isBlank(category.getColor())) {
            category.setColor("#409EFF");
        }

        if (StrUtil.isBlank(category.getIcon())) {
            category.setIcon("folder");
        }

        taskCategoryMapper.insert(category);

        log.info("Task category created successfully: {} - {}", category.getCode(), category.getName());

        return category;
    }

    @Override
    public TaskCategory updateCategory(String categoryId, TaskCategory category) {
        log.info("Updating task category: {}", categoryId);

        // 检查分类是否存在
        TaskCategory existingCategory = getCategoryById(categoryId);

        // 验证分类编码唯一性（如果编码有变更）
        if (StrUtil.isNotBlank(category.getCode()) && 
            !category.getCode().equals(existingCategory.getCode()) && 
            existsByCode(category.getCode())) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS.getCode(), "分类编码已存在");
        }

        // 验证分类名称唯一性（如果名称有变更）
        if (StrUtil.isNotBlank(category.getName()) && 
            !category.getName().equals(existingCategory.getName()) && 
            existsByName(category.getName())) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS.getCode(), "分类名称已存在");
        }

        // 更新分类信息
        category.setId(categoryId);
        taskCategoryMapper.updateById(category);

        log.info("Task category updated successfully: {}", categoryId);

        return getCategoryById(categoryId);
    }

    @Override
    public void deleteCategory(String categoryId) {
        log.info("Deleting task category: {}", categoryId);

        // 检查分类是否存在
        TaskCategory category = getCategoryById(categoryId);

        // TODO: 检查是否有任务使用此分类
        // 这里可以添加检查逻辑，如果有任务使用此分类则不允许删除

        taskCategoryMapper.deleteById(categoryId);

        log.info("Task category deleted successfully: {}", categoryId);
    }

    @Override
    public void enableCategory(String categoryId) {
        log.info("Enabling task category: {}", categoryId);

        // 检查分类是否存在
        getCategoryById(categoryId);

        TaskCategory category = new TaskCategory();
        category.setId(categoryId);
        category.setIsEnabled(true);
        taskCategoryMapper.updateById(category);

        log.info("Task category enabled successfully: {}", categoryId);
    }

    @Override
    public void disableCategory(String categoryId) {
        log.info("Disabling task category: {}", categoryId);

        // 检查分类是否存在
        getCategoryById(categoryId);

        TaskCategory category = new TaskCategory();
        category.setId(categoryId);
        category.setIsEnabled(false);
        taskCategoryMapper.updateById(category);

        log.info("Task category disabled successfully: {}", categoryId);
    }

    @Override
    public boolean existsByCode(String code) {
        return taskCategoryMapper.countByCode(code) > 0;
    }

    @Override
    public boolean existsByName(String name) {
        return taskCategoryMapper.countByName(name) > 0;
    }
}
