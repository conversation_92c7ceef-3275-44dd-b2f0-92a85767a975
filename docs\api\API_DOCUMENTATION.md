# WTMS API接口文档

## 文档概述

### 版本信息
- **API版本**: v1.0.0
- **文档版本**: 1.0.0
- **更新时间**: 2024年1月
- **基础URL**: `http://localhost:8080/api/v1`

### 认证方式
WTMS API使用JWT（JSON Web Token）进行身份认证。

#### 获取Token
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123456"
}
```

#### 使用Token
在请求头中添加Authorization字段：
```http
Authorization: Bearer <your-jwt-token>
```

### 响应格式
所有API响应都遵循统一的格式：

#### 成功响应
```json
{
  "code": 200,
  "message": "操作成功",
  "data": {
    // 具体数据内容
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

#### 错误响应
```json
{
  "code": 400,
  "message": "请求参数错误",
  "data": null,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### 状态码说明
- `200`: 请求成功
- `201`: 创建成功
- `400`: 请求参数错误
- `401`: 未授权访问
- `403`: 权限不足
- `404`: 资源不存在
- `500`: 服务器内部错误

## 用户认证接口

### 用户登录
**接口地址**: `POST /auth/login`

**请求参数**:
```json
{
  "username": "string",     // 用户名，必填
  "password": "string",     // 密码，必填
  "rememberMe": "boolean"   // 记住登录状态，可选，默认false
}
```

**响应示例**:
```json
{
  "code": 200,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "refreshToken": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "expiresIn": 86400,
    "user": {
      "id": "1",
      "username": "admin",
      "fullName": "系统管理员",
      "email": "<EMAIL>",
      "avatar": "http://localhost:8080/uploads/avatars/admin.jpg",
      "roles": ["ADMIN"],
      "permissions": ["user:view", "user:create", "user:edit", "user:delete"]
    }
  }
}
```

### 用户注册
**接口地址**: `POST /auth/register`

**请求参数**:
```json
{
  "username": "string",      // 用户名，必填，3-20字符
  "password": "string",      // 密码，必填，8-20字符
  "fullName": "string",      // 姓名，必填
  "email": "string",         // 邮箱，必填
  "phone": "string",         // 手机号，可选
  "departmentId": "string"   // 部门ID，可选
}
```

**响应示例**:
```json
{
  "code": 201,
  "message": "注册成功",
  "data": {
    "id": "123",
    "username": "newuser",
    "fullName": "新用户",
    "email": "<EMAIL>",
    "status": "ACTIVE",
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

### 刷新Token
**接口地址**: `POST /auth/refresh`

**请求参数**:
```json
{
  "refreshToken": "string"   // 刷新令牌，必填
}
```

### 用户登出
**接口地址**: `POST /auth/logout`

**请求头**: `Authorization: Bearer <token>`

**响应示例**:
```json
{
  "code": 200,
  "message": "登出成功",
  "data": null
}
```

## 用户管理接口

### 获取用户列表
**接口地址**: `GET /users`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页大小，默认10
- `keyword`: 搜索关键词，可选
- `departmentId`: 部门ID，可选
- `status`: 用户状态，可选

**请求示例**:
```http
GET /api/v1/users?page=1&size=10&keyword=张三&departmentId=1&status=ACTIVE
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": "1",
        "username": "zhangsan",
        "fullName": "张三",
        "email": "<EMAIL>",
        "phone": "13800138000",
        "avatar": "http://localhost:8080/uploads/avatars/zhangsan.jpg",
        "department": {
          "id": "1",
          "name": "技术部"
        },
        "roles": ["USER"],
        "status": "ACTIVE",
        "lastLoginAt": "2024-01-01T12:00:00Z",
        "createdAt": "2024-01-01T10:00:00Z"
      }
    ],
    "total": 100,
    "size": 10,
    "current": 1,
    "pages": 10
  }
}
```

### 获取用户详情
**接口地址**: `GET /users/{userId}`

**路径参数**:
- `userId`: 用户ID

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": "1",
    "username": "zhangsan",
    "fullName": "张三",
    "email": "<EMAIL>",
    "phone": "13800138000",
    "avatar": "http://localhost:8080/uploads/avatars/zhangsan.jpg",
    "department": {
      "id": "1",
      "name": "技术部",
      "parentId": null,
      "level": 1
    },
    "roles": [
      {
        "id": "2",
        "name": "普通用户",
        "code": "USER",
        "description": "系统普通用户"
      }
    ],
    "permissions": ["task:view", "task:create", "task:edit"],
    "status": "ACTIVE",
    "lastLoginAt": "2024-01-01T12:00:00Z",
    "createdAt": "2024-01-01T10:00:00Z",
    "updatedAt": "2024-01-01T11:00:00Z"
  }
}
```

### 创建用户
**接口地址**: `POST /users`

**请求参数**:
```json
{
  "username": "string",      // 用户名，必填
  "password": "string",      // 密码，必填
  "fullName": "string",      // 姓名，必填
  "email": "string",         // 邮箱，必填
  "phone": "string",         // 手机号，可选
  "departmentId": "string",  // 部门ID，可选
  "roleIds": ["string"],     // 角色ID列表，可选
  "status": "string"         // 状态，可选，默认ACTIVE
}
```

### 更新用户
**接口地址**: `PUT /users/{userId}`

**路径参数**:
- `userId`: 用户ID

**请求参数**: 同创建用户，但所有字段都是可选的

### 删除用户
**接口地址**: `DELETE /users/{userId}`

**路径参数**:
- `userId`: 用户ID

### 批量删除用户
**接口地址**: `DELETE /users/batch`

**请求参数**:
```json
{
  "userIds": ["1", "2", "3"]  // 用户ID列表
}
```

## 任务管理接口

### 获取任务列表
**接口地址**: `GET /tasks`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页大小，默认10
- `keyword`: 搜索关键词，可选
- `status`: 任务状态，可选
- `priority`: 优先级，可选
- `assigneeId`: 负责人ID，可选
- `creatorId`: 创建人ID，可选
- `startDate`: 开始日期，可选
- `endDate`: 结束日期，可选

**请求示例**:
```http
GET /api/v1/tasks?page=1&size=10&status=IN_PROGRESS&priority=HIGH
Authorization: Bearer <token>
```

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "records": [
      {
        "id": "1",
        "title": "开发用户管理模块",
        "description": "实现用户的增删改查功能",
        "status": "IN_PROGRESS",
        "priority": "HIGH",
        "type": "DEVELOPMENT",
        "creator": {
          "id": "1",
          "fullName": "张三"
        },
        "assignee": {
          "id": "2",
          "fullName": "李四"
        },
        "participants": [
          {
            "id": "3",
            "fullName": "王五"
          }
        ],
        "tags": ["后端", "用户管理"],
        "estimatedHours": 40,
        "actualHours": 20,
        "progress": 50,
        "startDate": "2024-01-01",
        "dueDate": "2024-01-15",
        "createdAt": "2024-01-01T10:00:00Z",
        "updatedAt": "2024-01-05T15:30:00Z"
      }
    ],
    "total": 50,
    "size": 10,
    "current": 1,
    "pages": 5
  }
}
```

### 获取任务详情
**接口地址**: `GET /tasks/{taskId}`

**路径参数**:
- `taskId`: 任务ID

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "id": "1",
    "title": "开发用户管理模块",
    "description": "实现用户的增删改查功能，包括用户列表、用户详情、用户创建、用户编辑、用户删除等功能",
    "status": "IN_PROGRESS",
    "priority": "HIGH",
    "type": "DEVELOPMENT",
    "creator": {
      "id": "1",
      "username": "zhangsan",
      "fullName": "张三",
      "avatar": "http://localhost:8080/uploads/avatars/zhangsan.jpg"
    },
    "assignee": {
      "id": "2",
      "username": "lisi",
      "fullName": "李四",
      "avatar": "http://localhost:8080/uploads/avatars/lisi.jpg"
    },
    "participants": [
      {
        "id": "3",
        "username": "wangwu",
        "fullName": "王五",
        "avatar": "http://localhost:8080/uploads/avatars/wangwu.jpg"
      }
    ],
    "tags": ["后端", "用户管理", "CRUD"],
    "estimatedHours": 40,
    "actualHours": 20,
    "progress": 50,
    "startDate": "2024-01-01",
    "dueDate": "2024-01-15",
    "attachments": [
      {
        "id": "1",
        "fileName": "需求文档.pdf",
        "fileSize": 1024000,
        "fileUrl": "http://localhost:8080/api/v1/files/download/1",
        "uploadedBy": "张三",
        "uploadedAt": "2024-01-01T10:00:00Z"
      }
    ],
    "comments": [
      {
        "id": "1",
        "content": "已完成数据库设计",
        "author": {
          "id": "2",
          "fullName": "李四"
        },
        "createdAt": "2024-01-03T14:30:00Z"
      }
    ],
    "createdAt": "2024-01-01T10:00:00Z",
    "updatedAt": "2024-01-05T15:30:00Z"
  }
}
```

### 创建任务
**接口地址**: `POST /tasks`

**请求参数**:
```json
{
  "title": "string",           // 任务标题，必填
  "description": "string",     // 任务描述，可选
  "priority": "string",        // 优先级：LOW/MEDIUM/HIGH/URGENT，默认MEDIUM
  "type": "string",           // 任务类型，可选
  "assigneeId": "string",     // 负责人ID，可选
  "participantIds": ["string"], // 参与者ID列表，可选
  "tags": ["string"],         // 标签列表，可选
  "estimatedHours": "number", // 预估工时，可选
  "startDate": "string",      // 开始日期，格式：YYYY-MM-DD，可选
  "dueDate": "string",        // 截止日期，格式：YYYY-MM-DD，可选
  "attachmentIds": ["string"] // 附件ID列表，可选
}
```

**响应示例**:
```json
{
  "code": 201,
  "message": "任务创建成功",
  "data": {
    "id": "123",
    "title": "新任务",
    "status": "TODO",
    "createdAt": "2024-01-01T12:00:00Z"
  }
}
```

### 更新任务
**接口地址**: `PUT /tasks/{taskId}`

**路径参数**:
- `taskId`: 任务ID

**请求参数**: 同创建任务，但所有字段都是可选的

### 更新任务状态
**接口地址**: `PUT /tasks/{taskId}/status`

**路径参数**:
- `taskId`: 任务ID

**请求参数**:
```json
{
  "status": "string",    // 新状态：TODO/IN_PROGRESS/REVIEW/DONE/CANCELLED
  "comment": "string"    // 状态变更说明，可选
}
```

### 分配任务
**接口地址**: `PUT /tasks/{taskId}/assign`

**路径参数**:
- `taskId`: 任务ID

**请求参数**:
```json
{
  "assigneeId": "string",      // 新负责人ID，必填
  "participantIds": ["string"], // 参与者ID列表，可选
  "comment": "string"          // 分配说明，可选
}
```

### 删除任务
**接口地址**: `DELETE /tasks/{taskId}`

**路径参数**:
- `taskId`: 任务ID

### 批量操作任务
**接口地址**: `POST /tasks/batch`

**请求参数**:
```json
{
  "taskIds": ["1", "2", "3"],  // 任务ID列表
  "operation": "string",       // 操作类型：DELETE/UPDATE_STATUS/ASSIGN
  "params": {                  // 操作参数，根据operation类型而定
    "status": "DONE",          // 更新状态时使用
    "assigneeId": "123"        // 分配任务时使用
  }
}
```

## 任务评论接口

### 获取任务评论
**接口地址**: `GET /tasks/{taskId}/comments`

**路径参数**:
- `taskId`: 任务ID

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页大小，默认20

### 添加任务评论
**接口地址**: `POST /tasks/{taskId}/comments`

**路径参数**:
- `taskId`: 任务ID

**请求参数**:
```json
{
  "content": "string",        // 评论内容，必填
  "parentId": "string",       // 父评论ID，回复评论时使用，可选
  "mentionUserIds": ["string"] // @提及的用户ID列表，可选
}
```

### 更新评论
**接口地址**: `PUT /comments/{commentId}`

**路径参数**:
- `commentId`: 评论ID

**请求参数**:
```json
{
  "content": "string"  // 新的评论内容
}
```

### 删除评论
**接口地址**: `DELETE /comments/{commentId}`

**路径参数**:
- `commentId`: 评论ID

## 文件管理接口

### 上传文件
**接口地址**: `POST /files/upload`

**请求类型**: `multipart/form-data`

**请求参数**:
- `file`: 文件，必填
- `category`: 文件分类，可选

**响应示例**:
```json
{
  "code": 200,
  "message": "上传成功",
  "data": {
    "id": "123",
    "fileName": "document.pdf",
    "originalName": "需求文档.pdf",
    "fileSize": 1024000,
    "mimeType": "application/pdf",
    "fileUrl": "http://localhost:8080/api/v1/files/download/123",
    "uploadedAt": "2024-01-01T12:00:00Z"
  }
}
```

### 下载文件
**接口地址**: `GET /files/download/{fileId}`

**路径参数**:
- `fileId`: 文件ID

### 删除文件
**接口地址**: `DELETE /files/{fileId}`

**路径参数**:
- `fileId`: 文件ID

### 批量上传文件
**接口地址**: `POST /files/batch-upload`

**请求类型**: `multipart/form-data`

**请求参数**:
- `files`: 文件列表，必填
- `category`: 文件分类，可选

## 工作流接口

### 获取工作流列表
**接口地址**: `GET /workflows`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页大小，默认10
- `keyword`: 搜索关键词，可选
- `status`: 状态，可选

### 获取工作流详情
**接口地址**: `GET /workflows/{workflowId}`

**路径参数**:
- `workflowId`: 工作流ID

### 创建工作流
**接口地址**: `POST /workflows`

**请求参数**:
```json
{
  "name": "string",           // 工作流名称，必填
  "description": "string",    // 描述，可选
  "category": "string",       // 分类，可选
  "definition": {             // 工作流定义，必填
    "nodes": [
      {
        "id": "start",
        "type": "start",
        "name": "开始",
        "x": 100,
        "y": 100
      },
      {
        "id": "task1",
        "type": "userTask",
        "name": "审核任务",
        "x": 300,
        "y": 100,
        "properties": {
          "assignee": "admin",
          "formKey": "review-form"
        }
      },
      {
        "id": "end",
        "type": "end",
        "name": "结束",
        "x": 500,
        "y": 100
      }
    ],
    "connections": [
      {
        "from": "start",
        "to": "task1"
      },
      {
        "from": "task1",
        "to": "end"
      }
    ]
  }
}
```

### 启动工作流实例
**接口地址**: `POST /workflows/{workflowId}/instances`

**路径参数**:
- `workflowId`: 工作流ID

**请求参数**:
```json
{
  "businessKey": "string",    // 业务键，可选
  "variables": {              // 流程变量，可选
    "applicant": "张三",
    "amount": 1000
  }
}
```

### 获取工作流实例列表
**接口地址**: `GET /workflow-instances`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页大小，默认10
- `workflowId`: 工作流ID，可选
- `status`: 状态，可选

### 获取待办任务
**接口地址**: `GET /tasks/pending`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页大小，默认10
- `assignee`: 处理人，可选

### 完成任务
**接口地址**: `POST /workflow-tasks/{taskId}/complete`

**路径参数**:
- `taskId`: 任务ID

**请求参数**:
```json
{
  "variables": {              // 任务变量，可选
    "approved": true,
    "comment": "审核通过"
  }
}
```

## 质量评价接口

### 获取评价列表
**接口地址**: `GET /evaluations`

**查询参数**:
- `page`: 页码，默认1
- `size`: 每页大小，默认10
- `taskId`: 任务ID，可选
- `evaluatorId`: 评价人ID，可选
- `evaluateeId`: 被评价人ID，可选
- `status`: 状态，可选

### 创建评价
**接口地址**: `POST /evaluations`

**请求参数**:
```json
{
  "taskId": "string",           // 任务ID，必填
  "evaluateeId": "string",      // 被评价人ID，必填
  "evaluationType": "string",   // 评价类型：SELF/PEER/SUPERVISOR，必填
  "overallScore": "number",     // 总体评分，必填，0-100
  "qualityScore": "number",     // 质量评分，可选，0-100
  "efficiencyScore": "number",  // 效率评分，可选，0-100
  "communicationScore": "number", // 沟通评分，可选，0-100
  "innovationScore": "number",  // 创新评分，可选，0-100
  "teamworkScore": "number",    // 团队协作评分，可选，0-100
  "content": "string",          // 评价内容，必填
  "strengths": "string",        // 优点，可选
  "improvements": "string",     // 改进建议，可选
  "tags": ["string"],          // 标签，可选
  "isAnonymous": "boolean",    // 是否匿名，可选，默认false
  "isPublic": "boolean"        // 是否公开，可选，默认true
}
```

### 提交评价
**接口地址**: `POST /evaluations/{evaluationId}/submit`

**路径参数**:
- `evaluationId`: 评价ID

### 获取评价统计
**接口地址**: `GET /evaluations/statistics`

**查询参数**:
- `targetType`: 统计对象类型：USER/TASK/DEPARTMENT，可选
- `targetId`: 统计对象ID，可选
- `startDate`: 开始日期，可选
- `endDate`: 结束日期，可选

**响应示例**:
```json
{
  "code": 200,
  "message": "查询成功",
  "data": {
    "totalEvaluations": 100,
    "averageScore": 85.5,
    "scoreDistribution": {
      "excellent": 30,    // 90-100分
      "good": 45,         // 80-89分
      "average": 20,      // 70-79分
      "poor": 5           // <70分
    },
    "dimensionScores": {
      "quality": 86.2,
      "efficiency": 84.8,
      "communication": 87.1,
      "innovation": 82.5,
      "teamwork": 88.3
    }
  }
}
```

## 错误码说明

### 通用错误码
- `400001`: 请求参数错误
- `400002`: 请求参数缺失
- `400003`: 请求参数格式错误
- `401001`: 未登录
- `401002`: Token已过期
- `401003`: Token无效
- `403001`: 权限不足
- `403002`: 账户被禁用
- `404001`: 资源不存在
- `409001`: 资源冲突
- `500001`: 服务器内部错误
- `500002`: 数据库操作失败

### 业务错误码
- `10001`: 用户名已存在
- `10002`: 邮箱已存在
- `10003`: 用户不存在
- `10004`: 密码错误
- `20001`: 任务不存在
- `20002`: 任务状态不允许此操作
- `20003`: 任务已被删除
- `30001`: 工作流不存在
- `30002`: 工作流实例不存在
- `30003`: 工作流节点配置错误
- `40001`: 评价不存在
- `40002`: 重复评价
- `40003`: 评价已提交

## 接口调用示例

### JavaScript/Axios示例
```javascript
// 登录
const login = async (username, password) => {
  try {
    const response = await axios.post('/api/v1/auth/login', {
      username,
      password
    });
    
    // 保存token
    localStorage.setItem('token', response.data.data.token);
    
    return response.data;
  } catch (error) {
    console.error('登录失败:', error.response.data);
    throw error;
  }
};

// 获取任务列表
const getTasks = async (params = {}) => {
  try {
    const response = await axios.get('/api/v1/tasks', {
      params,
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('获取任务列表失败:', error.response.data);
    throw error;
  }
};

// 创建任务
const createTask = async (taskData) => {
  try {
    const response = await axios.post('/api/v1/tasks', taskData, {
      headers: {
        'Authorization': `Bearer ${localStorage.getItem('token')}`,
        'Content-Type': 'application/json'
      }
    });
    
    return response.data;
  } catch (error) {
    console.error('创建任务失败:', error.response.data);
    throw error;
  }
};
```

### Java/Spring Boot示例
```java
// 使用RestTemplate调用API
@Service
public class WtmsApiClient {
    
    private final RestTemplate restTemplate;
    private final String baseUrl = "http://localhost:8080/api/v1";
    
    public WtmsApiClient(RestTemplate restTemplate) {
        this.restTemplate = restTemplate;
    }
    
    // 登录
    public LoginResponse login(String username, String password) {
        LoginRequest request = new LoginRequest(username, password);
        
        ResponseEntity<ApiResponse<LoginResponse>> response = restTemplate.postForEntity(
            baseUrl + "/auth/login",
            request,
            new ParameterizedTypeReference<ApiResponse<LoginResponse>>() {}
        );
        
        return response.getBody().getData();
    }
    
    // 获取任务列表
    public PageResult<Task> getTasks(int page, int size, String keyword) {
        HttpHeaders headers = new HttpHeaders();
        headers.setBearerAuth(getToken());
        
        HttpEntity<?> entity = new HttpEntity<>(headers);
        
        String url = String.format("%s/tasks?page=%d&size=%d&keyword=%s", 
                                 baseUrl, page, size, keyword);
        
        ResponseEntity<ApiResponse<PageResult<Task>>> response = restTemplate.exchange(
            url,
            HttpMethod.GET,
            entity,
            new ParameterizedTypeReference<ApiResponse<PageResult<Task>>>() {}
        );
        
        return response.getBody().getData();
    }
}
```

### Python/Requests示例
```python
import requests
import json

class WtmsApiClient:
    def __init__(self, base_url="http://localhost:8080/api/v1"):
        self.base_url = base_url
        self.token = None
    
    def login(self, username, password):
        """用户登录"""
        url = f"{self.base_url}/auth/login"
        data = {
            "username": username,
            "password": password
        }
        
        response = requests.post(url, json=data)
        response.raise_for_status()
        
        result = response.json()
        self.token = result['data']['token']
        return result
    
    def get_headers(self):
        """获取请求头"""
        return {
            "Authorization": f"Bearer {self.token}",
            "Content-Type": "application/json"
        }
    
    def get_tasks(self, page=1, size=10, **params):
        """获取任务列表"""
        url = f"{self.base_url}/tasks"
        params.update({"page": page, "size": size})
        
        response = requests.get(url, params=params, headers=self.get_headers())
        response.raise_for_status()
        
        return response.json()
    
    def create_task(self, task_data):
        """创建任务"""
        url = f"{self.base_url}/tasks"
        
        response = requests.post(url, json=task_data, headers=self.get_headers())
        response.raise_for_status()
        
        return response.json()

# 使用示例
client = WtmsApiClient()
client.login("admin", "admin123456")

# 获取任务列表
tasks = client.get_tasks(page=1, size=10, status="IN_PROGRESS")
print(f"获取到 {len(tasks['data']['records'])} 个任务")

# 创建任务
new_task = {
    "title": "测试任务",
    "description": "这是一个测试任务",
    "priority": "HIGH",
    "dueDate": "2024-01-31"
}
result = client.create_task(new_task)
print(f"任务创建成功，ID: {result['data']['id']}")
```

## Postman集合

为了方便测试，我们提供了Postman集合文件，包含了所有API接口的示例请求。

### 导入Postman集合
1. 下载 `WTMS_API.postman_collection.json` 文件
2. 在Postman中点击"Import"
3. 选择下载的JSON文件
4. 导入完成后即可使用

### 环境变量设置
在Postman中设置以下环境变量：
- `baseUrl`: http://localhost:8080/api/v1
- `token`: 登录后获取的JWT token

## 在线API文档

系统提供了基于Swagger UI的在线API文档，可以直接在浏览器中查看和测试API。

**访问地址**: http://localhost:8080/swagger-ui.html

### Swagger配置
在后端项目中已集成Swagger 3.0，配置如下：

```java
@Configuration
@EnableOpenApi
public class SwaggerConfig {

    @Bean
    public OpenAPI customOpenAPI() {
        return new OpenAPI()
                .info(new Info()
                        .title("WTMS API")
                        .version("1.0.0")
                        .description("工作任务管理系统API接口文档")
                        .contact(new Contact()
                                .name("WTMS团队")
                                .email("<EMAIL>")))
                .addSecurityItem(new SecurityRequirement().addList("Bearer Authentication"))
                .components(new Components()
                        .addSecuritySchemes("Bearer Authentication",
                                new SecurityScheme()
                                        .type(SecurityScheme.Type.HTTP)
                                        .scheme("bearer")
                                        .bearerFormat("JWT")));
    }
}
```

## 接口测试

### 单元测试
后端项目包含完整的API单元测试，使用MockMvc进行测试：

```java
@SpringBootTest
@AutoConfigureTestDatabase
@Transactional
class TaskControllerTest {

    @Autowired
    private MockMvc mockMvc;

    @Test
    void testCreateTask() throws Exception {
        String taskJson = """
            {
                "title": "测试任务",
                "description": "这是一个测试任务",
                "priority": "HIGH"
            }
            """;

        mockMvc.perform(post("/api/v1/tasks")
                .contentType(MediaType.APPLICATION_JSON)
                .content(taskJson)
                .header("Authorization", "Bearer " + getTestToken()))
                .andExpect(status().isCreated())
                .andExpect(jsonPath("$.code").value(201))
                .andExpect(jsonPath("$.data.title").value("测试任务"));
    }
}
```

### 集成测试
使用TestContainers进行集成测试，确保API在真实环境中的正确性：

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@Testcontainers
class ApiIntegrationTest {

    @Container
    static MySQLContainer<?> mysql = new MySQLContainer<>("mysql:8.0")
            .withDatabaseName("wtms_test")
            .withUsername("test")
            .withPassword("test");

    @Autowired
    private TestRestTemplate restTemplate;

    @Test
    void testTaskLifecycle() {
        // 测试任务的完整生命周期
        // 1. 创建任务
        // 2. 更新任务
        // 3. 分配任务
        // 4. 完成任务
        // 5. 删除任务
    }
}
```

## 版本管理

### API版本策略
- 使用URL路径版本控制：`/api/v1/`, `/api/v2/`
- 向后兼容原则：新版本不破坏现有功能
- 废弃通知：提前通知API废弃计划

### 版本更新日志
- **v1.0.0** (2024-01-01): 初始版本发布
- **v1.0.1** (计划中): 性能优化和bug修复
- **v1.1.0** (计划中): 新增批量操作接口

---

**文档维护**: WTMS开发团队
**最后更新**: 2024年1月
**版本**: v1.0.0
