package com.wtms.service;

import com.wtms.dto.request.AssignUserRoleRequest;
import com.wtms.entity.Role;
import com.wtms.entity.UserRole;

import java.util.List;

/**
 * 用户角色管理服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface UserRoleService {

    /**
     * 为用户分配角色
     *
     * @param request 分配请求
     * @return 分配结果
     */
    List<UserRole> assignRolesToUser(AssignUserRoleRequest request);

    /**
     * 移除用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    void removeRolesFromUser(String userId, List<String> roleIds);

    /**
     * 清空用户的所有角色
     *
     * @param userId 用户ID
     */
    void clearUserRoles(String userId);

    /**
     * 获取用户的角色列表
     *
     * @param userId 用户ID
     * @return 角色列表
     */
    List<Role> getUserRoles(String userId);

    /**
     * 获取用户的角色关联列表
     *
     * @param userId 用户ID
     * @return 角色关联列表
     */
    List<UserRole> getUserRoleAssignments(String userId);

    /**
     * 获取角色的用户关联列表
     *
     * @param roleId 角色ID
     * @return 用户关联列表
     */
    List<UserRole> getRoleUserAssignments(String roleId);

    /**
     * 检查用户是否有指定角色
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否有角色
     */
    boolean hasRole(String userId, String roleId);

    /**
     * 检查用户是否有指定角色编码
     *
     * @param userId 用户ID
     * @param roleCode 角色编码
     * @return 是否有角色
     */
    boolean hasRoleByCode(String userId, String roleCode);

    /**
     * 检查用户角色关联是否存在
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     * @return 是否存在
     */
    boolean existsUserRole(String userId, String roleId);

    /**
     * 启用用户角色关联
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    void enableUserRole(String userId, String roleId);

    /**
     * 禁用用户角色关联
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    void disableUserRole(String userId, String roleId);

    /**
     * 复制用户角色
     *
     * @param sourceUserId 源用户ID
     * @param targetUserId 目标用户ID
     */
    void copyUserRoles(String sourceUserId, String targetUserId);

    /**
     * 同步用户角色
     *
     * @param userId 用户ID
     * @param roleIds 角色ID列表
     */
    void syncUserRoles(String userId, List<String> roleIds);

    /**
     * 处理过期的角色关联
     */
    void handleExpiredRoles();

    /**
     * 获取用户角色统计
     *
     * @param userId 用户ID
     * @return 统计信息
     */
    Object getUserRoleStatistics(String userId);

    /**
     * 检查用户是否为超级管理员
     *
     * @param userId 用户ID
     * @return 是否为超级管理员
     */
    boolean isSuperAdmin(String userId);

    /**
     * 检查用户是否有管理员权限
     *
     * @param userId 用户ID
     * @return 是否有管理员权限
     */
    boolean isAdmin(String userId);
}
