package com.wtms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.wtms.common.exception.BusinessException;
import com.wtms.common.result.ResultCode;
import com.wtms.dto.request.CreatePermissionRequest;
import com.wtms.dto.request.UpdatePermissionRequest;
import com.wtms.dto.response.PermissionTreeResponse;
import com.wtms.dto.response.UserPermissionResponse;
import com.wtms.entity.Permission;
import com.wtms.entity.Role;
import com.wtms.entity.User;
import com.wtms.mapper.PermissionMapper;
import com.wtms.mapper.UserMapper;
import com.wtms.service.PermissionService;
import com.wtms.service.UserRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 权限管理服务实现
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class PermissionServiceImpl implements PermissionService {

    @Autowired
    private PermissionMapper permissionMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private com.wtms.service.DevelopmentPermissionService developmentPermissionService;

    @Override
    @CacheEvict(value = "permissions", allEntries = true)
    public Permission createPermission(CreatePermissionRequest request) {
        log.info("Creating permission: {}", request.getCode());

        // 检查权限编码是否已存在
        if (existsByCode(request.getCode())) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS.getCode(), "权限编码已存在");
        }

        // 检查权限名称是否已存在
        if (existsByName(request.getName())) {
            throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS.getCode(), "权限名称已存在");
        }

        Permission permission = new Permission();
        BeanUtils.copyProperties(request, permission);

        // 设置权限层级和路径
        if (StrUtil.isNotBlank(request.getParentId())) {
            Permission parent = getPermissionById(request.getParentId());
            permission.setLevel(parent.getLevel() + 1);
            permission.setPath(parent.getPath() + "/" + request.getCode());
        } else {
            permission.setLevel(1);
            permission.setPath("/" + request.getCode());
        }

        // 设置排序
        if (permission.getSortOrder() == null) {
            int maxSortOrder = permissionMapper.getMaxSortOrder(request.getParentId());
            permission.setSortOrder(maxSortOrder + 1);
        }

        permission.setCreatedAt(LocalDateTime.now());
        permission.setUpdatedAt(LocalDateTime.now());

        permissionMapper.insert(permission);

        log.info("Permission created successfully: {}", permission.getId());
        return permission;
    }

    @Override
    @CacheEvict(value = "permissions", allEntries = true)
    public Permission updatePermission(String permissionId, UpdatePermissionRequest request) {
        log.info("Updating permission: {}", permissionId);

        Permission permission = getPermissionById(permissionId);

        // 检查是否为系统权限
        if (permission.isSystem()) {
            throw new BusinessException(ResultCode.OPERATION_NOT_ALLOWED.getCode(), "系统权限不允许修改");
        }

        // 检查权限编码是否已存在（排除自己）
        if (StrUtil.isNotBlank(request.getCode()) && !request.getCode().equals(permission.getCode())) {
            if (existsByCode(request.getCode())) {
                throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS.getCode(), "权限编码已存在");
            }
        }

        // 检查权限名称是否已存在（排除自己）
        if (StrUtil.isNotBlank(request.getName()) && !request.getName().equals(permission.getName())) {
            if (existsByName(request.getName())) {
                throw new BusinessException(ResultCode.DATA_ALREADY_EXISTS.getCode(), "权限名称已存在");
            }
        }

        // 更新权限信息
        if (StrUtil.isNotBlank(request.getCode())) {
            permission.setCode(request.getCode());
        }
        if (StrUtil.isNotBlank(request.getName())) {
            permission.setName(request.getName());
        }
        if (StrUtil.isNotBlank(request.getDescription())) {
            permission.setDescription(request.getDescription());
        }
        if (StrUtil.isNotBlank(request.getType())) {
            permission.setType(request.getType());
        }
        if (StrUtil.isNotBlank(request.getGroupName())) {
            permission.setGroupName(request.getGroupName());
        }
        if (StrUtil.isNotBlank(request.getPath())) {
            permission.setPath(request.getPath());
        }
        if (request.getSortOrder() != null) {
            permission.setSortOrder(request.getSortOrder());
        }
        if (StrUtil.isNotBlank(request.getResource())) {
            permission.setResource(request.getResource());
        }
        if (StrUtil.isNotBlank(request.getAction())) {
            permission.setAction(request.getAction());
        }
        if (StrUtil.isNotBlank(request.getExpression())) {
            permission.setExpression(request.getExpression());
        }
        if (request.getIsEnabled() != null) {
            permission.setIsEnabled(request.getIsEnabled());
        }

        permission.setUpdatedAt(LocalDateTime.now());
        permissionMapper.updateById(permission);

        log.info("Permission updated successfully: {}", permissionId);
        return permission;
    }

    @Override
    @CacheEvict(value = "permissions", allEntries = true)
    public void deletePermission(String permissionId) {
        log.info("Deleting permission: {}", permissionId);

        Permission permission = getPermissionById(permissionId);

        // 检查是否为系统权限
        if (permission.isSystem()) {
            throw new BusinessException(ResultCode.OPERATION_NOT_ALLOWED.getCode(), "系统权限不允许删除");
        }

        // 检查是否有子权限
        int childrenCount = permissionMapper.countChildren(permissionId);
        if (childrenCount > 0) {
            throw new BusinessException(ResultCode.OPERATION_NOT_ALLOWED.getCode(), "存在子权限，不允许删除");
        }

        // 软删除
        permission.setDeletedAt(LocalDateTime.now());
        permissionMapper.updateById(permission);

        log.info("Permission deleted successfully: {}", permissionId);
    }

    @Override
    @Cacheable(value = "permissions", key = "#permissionId")
    public Permission getPermissionById(String permissionId) {
        Permission permission = permissionMapper.selectById(permissionId);
        if (permission == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "权限不存在");
        }
        return permission;
    }

    @Override
    @Cacheable(value = "permissions", key = "'code:' + #code")
    public Permission getPermissionByCode(String code) {
        Permission permission = permissionMapper.selectByCode(code);
        if (permission == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "权限不存在");
        }
        return permission;
    }

    @Override
    @Cacheable(value = "permissions", key = "'tree'")
    public List<PermissionTreeResponse> getPermissionTree() {
        List<Permission> permissions = permissionMapper.selectPermissionTree();
        return PermissionTreeResponse.buildTree(permissions);
    }

    @Override
    public List<Permission> getPermissionsByType(String type) {
        return permissionMapper.selectByType(type);
    }

    @Override
    public List<Permission> getPermissionsByGroup(String groupName) {
        return permissionMapper.selectByGroup(groupName);
    }

    @Override
    public List<Permission> getEnabledPermissions() {
        return permissionMapper.selectEnabledPermissions();
    }

    @Override
    public List<Permission> getSystemPermissions() {
        return permissionMapper.selectSystemPermissions();
    }

    @Override
    @CacheEvict(value = "permissions", allEntries = true)
    public void enablePermission(String permissionId) {
        Permission permission = getPermissionById(permissionId);
        permission.setIsEnabled(true);
        permission.setUpdatedAt(LocalDateTime.now());
        permissionMapper.updateById(permission);
    }

    @Override
    @CacheEvict(value = "permissions", allEntries = true)
    public void disablePermission(String permissionId) {
        Permission permission = getPermissionById(permissionId);
        permission.setIsEnabled(false);
        permission.setUpdatedAt(LocalDateTime.now());
        permissionMapper.updateById(permission);
    }

    @Override
    @CacheEvict(value = "permissions", allEntries = true)
    public void batchEnablePermissions(List<String> permissionIds) {
        permissionMapper.batchUpdateStatus(permissionIds, true);
    }

    @Override
    @CacheEvict(value = "permissions", allEntries = true)
    public void batchDisablePermissions(List<String> permissionIds) {
        permissionMapper.batchUpdateStatus(permissionIds, false);
    }

    @Override
    @Cacheable(value = "userPermissions", key = "#userId")
    public UserPermissionResponse getUserPermissions(String userId) {
        log.info("Getting user permissions: {}", userId);

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 开发环境权限覆盖：设置为超级管理员
        boolean isSuperAdmin = userRoleService.isSuperAdmin(userId) ||
                              developmentPermissionService.shouldTreatAsSuperAdmin(userId);

        UserPermissionResponse.UserPermissionResponseBuilder builder = UserPermissionResponse.builder()
                .userId(userId)
                .username(user.getUsername())
                .isSuperAdmin(isSuperAdmin);

        if (isSuperAdmin) {
            // 超级管理员拥有所有权限
            List<Permission> allPermissions = getEnabledPermissions();
            Set<String> allPermissionCodes = allPermissions.stream()
                    .map(Permission::getCode)
                    .collect(Collectors.toSet());

            builder.permissions(allPermissionCodes)
                   .permissionTree(getPermissionTree())
                   .buttons(allPermissionCodes)
                   .apis(allPermissionCodes)
                   .dataPermissions(allPermissionCodes);
        } else {
            // 普通用户权限
            Set<String> permissionCodes = getUserPermissionCodes(userId);
            List<Permission> menuPermissions = permissionMapper.selectMenuPermissionsByUserId(userId);
            Set<String> buttonPermissions = permissionMapper.selectButtonPermissionsByUserId(userId);
            Set<String> apiPermissions = permissionMapper.selectApiPermissionsByUserId(userId);
            Set<String> dataPermissions = permissionMapper.selectDataPermissionsByUserId(userId);

            builder.permissions(permissionCodes)
                   .buttons(buttonPermissions)
                   .apis(apiPermissions)
                   .dataPermissions(dataPermissions);
        }

        return builder.build();
    }

    @Override
    public Set<String> getUserPermissionCodes(String userId) {
        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            return getEnabledPermissions().stream()
                    .map(Permission::getCode)
                    .collect(Collectors.toSet());
        }
        return permissionMapper.selectPermissionCodesByUserId(userId);
    }

    @Override
    public List<Permission> getRolePermissions(String roleId) {
        return permissionMapper.selectPermissionsByRoleId(roleId);
    }

    @Override
    public Set<String> getRolePermissionCodes(String roleId) {
        return permissionMapper.selectPermissionCodesByRoleId(roleId);
    }

    @Override
    public boolean hasPermission(String userId, String permissionCode) {
        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            developmentPermissionService.logPermissionOverride("hasPermission", permissionCode, userId);
            return true;
        }
        Set<String> userPermissions = getUserPermissionCodes(userId);
        return userPermissions.contains(permissionCode);
    }

    @Override
    public boolean hasAnyPermission(String userId, String... permissionCodes) {
        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            developmentPermissionService.logPermissionOverride("hasAnyPermission",
                String.join(",", permissionCodes), userId);
            return true;
        }
        Set<String> userPermissions = getUserPermissionCodes(userId);
        return Arrays.stream(permissionCodes).anyMatch(userPermissions::contains);
    }

    @Override
    public boolean hasAllPermissions(String userId, String... permissionCodes) {
        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            developmentPermissionService.logPermissionOverride("hasAllPermissions",
                String.join(",", permissionCodes), userId);
            return true;
        }
        Set<String> userPermissions = getUserPermissionCodes(userId);
        return Arrays.stream(permissionCodes).allMatch(userPermissions::contains);
    }

    @Override
    public boolean existsByCode(String code) {
        return permissionMapper.countByCode(code) > 0;
    }

    @Override
    public boolean existsByName(String name) {
        return permissionMapper.countByName(name) > 0;
    }

    @Override
    public void initSystemPermissions() {
        log.info("Initializing system permissions...");
        // TODO: 实现系统权限初始化逻辑
    }

    @Override
    @CacheEvict(value = {"permissions", "userPermissions"}, allEntries = true)
    public void refreshPermissionCache() {
        log.info("Permission cache refreshed");
    }

    @Override
    public Object getPermissionStatistics() {
        // TODO: 实现权限统计逻辑
        return new HashMap<>();
    }


}
