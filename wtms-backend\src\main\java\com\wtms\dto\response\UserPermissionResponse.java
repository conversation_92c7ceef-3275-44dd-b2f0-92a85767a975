package com.wtms.dto.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Set;

/**
 * 用户权限响应DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "用户权限响应")
public class UserPermissionResponse {

    @Schema(description = "用户ID")
    private String userId;

    @Schema(description = "用户名")
    private String username;

    @Schema(description = "用户角色列表")
    private List<RoleInfo> roles;

    @Schema(description = "权限编码列表")
    private Set<String> permissions;

    @Schema(description = "权限树")
    private List<PermissionTreeResponse> permissionTree;

    @Schema(description = "菜单权限")
    private List<MenuPermission> menus;

    @Schema(description = "按钮权限")
    private Set<String> buttons;

    @Schema(description = "API权限")
    private Set<String> apis;

    @Schema(description = "数据权限")
    private Set<String> dataPermissions;

    @Schema(description = "是否超级管理员")
    private Boolean isSuperAdmin;

    /**
     * 角色信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "角色信息")
    public static class RoleInfo {
        @Schema(description = "角色ID")
        private String id;

        @Schema(description = "角色编码")
        private String code;

        @Schema(description = "角色名称")
        private String name;

        @Schema(description = "角色描述")
        private String description;

        @Schema(description = "是否启用")
        private Boolean isEnabled;
    }

    /**
     * 菜单权限
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "菜单权限")
    public static class MenuPermission {
        @Schema(description = "权限ID")
        private String id;

        @Schema(description = "权限编码")
        private String code;

        @Schema(description = "权限名称")
        private String name;

        @Schema(description = "权限路径")
        private String path;

        @Schema(description = "父权限ID")
        private String parentId;

        @Schema(description = "权限层级")
        private Integer level;

        @Schema(description = "排序")
        private Integer sortOrder;

        @Schema(description = "子菜单")
        private List<MenuPermission> children;
    }
}
