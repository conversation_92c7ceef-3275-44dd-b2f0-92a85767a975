package com.wtms.security;

import com.wtms.entity.User;
import com.wtms.mapper.UserMapper;
import com.wtms.service.PermissionService;
import com.wtms.service.UserRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.PermissionEvaluator;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * 自定义权限评估器
 * 用于Spring Security的@PreAuthorize注解中的权限检查
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Component
public class CustomPermissionEvaluator implements PermissionEvaluator {

    @Autowired
    private PermissionService permissionService;

    @Autowired
    private UserRoleService userRoleService;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private com.wtms.service.DevelopmentPermissionService developmentPermissionService;

    @Override
    public boolean hasPermission(Authentication authentication, Object targetDomainObject, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String userId = getUserId(authentication);
        if (userId == null) {
            return false;
        }

        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            log.debug("Development mode: granting permission {} to user {}", permission, userId);
            return true;
        }

        // 超级管理员拥有所有权限
        if (userRoleService.isSuperAdmin(userId)) {
            log.debug("Super admin: granting permission {} to user {}", permission, userId);
            return true;
        }

        String permissionCode = permission.toString();
        boolean hasPermission = permissionService.hasPermission(userId, permissionCode);
        
        log.debug("Permission check: user={}, permission={}, result={}", userId, permissionCode, hasPermission);
        return hasPermission;
    }

    @Override
    public boolean hasPermission(Authentication authentication, Serializable targetId, String targetType, Object permission) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String userId = getUserId(authentication);
        if (userId == null) {
            return false;
        }

        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            log.debug("Development mode: granting permission {} on {} {} to user {}",
                     permission, targetType, targetId, userId);
            return true;
        }

        // 超级管理员拥有所有权限
        if (userRoleService.isSuperAdmin(userId)) {
            log.debug("Super admin: granting permission {} on {} {} to user {}", 
                     permission, targetType, targetId, userId);
            return true;
        }

        // 构建权限表达式
        String permissionCode = targetType + ":" + permission;
        boolean hasPermission = permissionService.hasPermission(userId, permissionCode);

        // 如果没有通用权限，检查特定资源权限
        if (!hasPermission && targetId != null) {
            String specificPermissionCode = targetType + ":" + permission + ":" + targetId;
            hasPermission = permissionService.hasPermission(userId, specificPermissionCode);
        }

        log.debug("Permission check: user={}, permission={}, targetType={}, targetId={}, result={}", 
                 userId, permission, targetType, targetId, hasPermission);
        return hasPermission;
    }

    /**
     * 检查用户是否有任意一个权限
     */
    public boolean hasAnyPermission(Authentication authentication, String... permissions) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String userId = getUserId(authentication);
        if (userId == null) {
            return false;
        }

        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            return true;
        }

        // 超级管理员拥有所有权限
        if (userRoleService.isSuperAdmin(userId)) {
            return true;
        }

        return permissionService.hasAnyPermission(userId, permissions);
    }

    /**
     * 检查用户是否有所有权限
     */
    public boolean hasAllPermissions(Authentication authentication, String... permissions) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String userId = getUserId(authentication);
        if (userId == null) {
            return false;
        }

        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            return true;
        }

        // 超级管理员拥有所有权限
        if (userRoleService.isSuperAdmin(userId)) {
            return true;
        }

        return permissionService.hasAllPermissions(userId, permissions);
    }

    /**
     * 检查用户是否有指定角色
     */
    public boolean hasRole(Authentication authentication, String roleCode) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String userId = getUserId(authentication);
        if (userId == null) {
            return false;
        }

        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            return true;
        }

        return userRoleService.hasRoleByCode(userId, roleCode);
    }

    /**
     * 检查用户是否有任意一个角色
     */
    public boolean hasAnyRole(Authentication authentication, String... roleCodes) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String userId = getUserId(authentication);
        if (userId == null) {
            return false;
        }

        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            return true;
        }

        for (String roleCode : roleCodes) {
            if (userRoleService.hasRoleByCode(userId, roleCode)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查是否为资源所有者
     */
    public boolean isOwner(Authentication authentication, Object targetDomainObject) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String userId = getUserId(authentication);
        if (userId == null) {
            return false;
        }

        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            return true;
        }

        // 超级管理员拥有所有权限
        if (userRoleService.isSuperAdmin(userId)) {
            return true;
        }

        // TODO: 实现具体的所有者检查逻辑
        // 这里需要根据具体的业务对象来判断用户是否为所有者
        return false;
    }

    /**
     * 检查是否为同一用户
     */
    public boolean isSameUser(Authentication authentication, String targetUserId) {
        if (authentication == null || !authentication.isAuthenticated()) {
            return false;
        }

        String userId = getUserId(authentication);
        return userId != null && userId.equals(targetUserId);
    }

    /**
     * 从认证信息中获取用户ID
     */
    private String getUserId(Authentication authentication) {
        Object principal = authentication.getPrincipal();
        if (principal instanceof UserDetails) {
            String username = ((UserDetails) principal).getUsername();
            User user = userMapper.findByUsernameWithRole(username);
            return user != null ? user.getId() : null;
        }
        return null;
    }


}
