import { describe, it, expect, vi, beforeEach } from 'vitest'
import { mount } from '@vue/test-utils'
import { ElCard, ElTag, ElButton, ElDropdown } from 'element-plus'
import TaskCard from '../TaskCard.vue'
import type { Task } from '@/types/task'

// Mock Element Plus components
vi.mock('element-plus', () => ({
  ElCard: { name: 'ElCard', template: '<div class="el-card"><slot /></div>' },
  ElTag: { name: 'ElTag', template: '<span class="el-tag"><slot /></span>' },
  ElButton: { name: 'ElButton', template: '<button class="el-button"><slot /></button>' },
  ElDropdown: { name: 'ElDropdown', template: '<div class="el-dropdown"><slot /></div>' },
  ElDropdownMenu: { name: 'ElDropdownMenu', template: '<div class="el-dropdown-menu"><slot /></div>' },
  ElDropdownItem: { name: 'ElDropdownItem', template: '<div class="el-dropdown-item"><slot /></div>' }
}))

describe('TaskCard', () => {
  const mockTask: Task = {
    id: 'task-001',
    title: '测试任务',
    description: '这是一个测试任务的描述',
    status: 'IN_PROGRESS',
    priority: 'HIGH',
    type: 'DEVELOPMENT',
    creator: {
      id: 'user-001',
      fullName: '张三',
      avatar: '/avatars/zhangsan.jpg'
    },
    assignee: {
      id: 'user-002',
      fullName: '李四',
      avatar: '/avatars/lisi.jpg'
    },
    estimatedHours: 8,
    actualHours: 4,
    progress: 50,
    startDate: '2024-01-01',
    dueDate: '2024-01-15',
    tags: ['前端', '开发'],
    createdAt: '2024-01-01T10:00:00Z',
    updatedAt: '2024-01-05T15:30:00Z'
  }

  let wrapper: any

  beforeEach(() => {
    wrapper = mount(TaskCard, {
      props: {
        task: mockTask
      },
      global: {
        components: {
          ElCard,
          ElTag,
          ElButton,
          ElDropdown
        }
      }
    })
  })

  it('应该正确渲染任务标题', () => {
    expect(wrapper.find('.task-title').text()).toBe('测试任务')
  })

  it('应该正确渲染任务描述', () => {
    expect(wrapper.find('.task-description').text()).toBe('这是一个测试任务的描述')
  })

  it('应该根据任务状态显示正确的标签颜色', () => {
    const statusTag = wrapper.find('.task-status')
    expect(statusTag.exists()).toBe(true)
    
    // 测试不同状态的标签颜色
    const statusColors = {
      'TODO': 'info',
      'IN_PROGRESS': 'warning',
      'REVIEW': 'primary',
      'DONE': 'success',
      'CANCELLED': 'danger'
    }
    
    expect(statusTag.attributes('type')).toBe(statusColors[mockTask.status])
  })

  it('应该根据优先级显示正确的标签颜色', () => {
    const priorityTag = wrapper.find('.task-priority')
    expect(priorityTag.exists()).toBe(true)
    
    const priorityColors = {
      'LOW': 'info',
      'MEDIUM': 'warning',
      'HIGH': 'danger',
      'URGENT': 'danger'
    }
    
    expect(priorityTag.attributes('type')).toBe(priorityColors[mockTask.priority])
  })

  it('应该正确显示任务进度', () => {
    const progressElement = wrapper.find('.task-progress')
    expect(progressElement.exists()).toBe(true)
    expect(progressElement.text()).toContain('50%')
  })

  it('应该正确显示负责人信息', () => {
    const assigneeElement = wrapper.find('.task-assignee')
    expect(assigneeElement.exists()).toBe(true)
    expect(assigneeElement.text()).toContain('李四')
  })

  it('应该正确显示截止日期', () => {
    const dueDateElement = wrapper.find('.task-due-date')
    expect(dueDateElement.exists()).toBe(true)
    expect(dueDateElement.text()).toContain('2024-01-15')
  })

  it('应该正确渲染任务标签', () => {
    const tagElements = wrapper.findAll('.task-tag')
    expect(tagElements).toHaveLength(2)
    expect(tagElements[0].text()).toBe('前端')
    expect(tagElements[1].text()).toBe('开发')
  })

  it('应该在点击编辑按钮时触发编辑事件', async () => {
    const editButton = wrapper.find('.edit-button')
    await editButton.trigger('click')
    
    expect(wrapper.emitted('edit')).toBeTruthy()
    expect(wrapper.emitted('edit')[0]).toEqual([mockTask])
  })

  it('应该在点击删除按钮时触发删除事件', async () => {
    const deleteButton = wrapper.find('.delete-button')
    await deleteButton.trigger('click')
    
    expect(wrapper.emitted('delete')).toBeTruthy()
    expect(wrapper.emitted('delete')[0]).toEqual([mockTask.id])
  })

  it('应该在点击状态更新时触发状态更新事件', async () => {
    const statusButton = wrapper.find('.status-update-button')
    await statusButton.trigger('click')
    
    expect(wrapper.emitted('status-update')).toBeTruthy()
    expect(wrapper.emitted('status-update')[0]).toEqual([mockTask.id, 'DONE'])
  })

  it('应该正确计算任务是否逾期', () => {
    // 测试逾期任务
    const overdueTask = {
      ...mockTask,
      dueDate: '2023-12-31', // 过去的日期
      status: 'IN_PROGRESS'
    }
    
    const overdueWrapper = mount(TaskCard, {
      props: { task: overdueTask },
      global: {
        components: { ElCard, ElTag, ElButton, ElDropdown }
      }
    })
    
    expect(overdueWrapper.find('.task-overdue').exists()).toBe(true)
    expect(overdueWrapper.find('.task-overdue').classes()).toContain('overdue')
  })

  it('应该正确显示工时信息', () => {
    const hoursElement = wrapper.find('.task-hours')
    expect(hoursElement.exists()).toBe(true)
    expect(hoursElement.text()).toContain('4/8')
  })

  it('应该在没有负责人时显示未分配状态', () => {
    const unassignedTask = {
      ...mockTask,
      assignee: null
    }
    
    const unassignedWrapper = mount(TaskCard, {
      props: { task: unassignedTask },
      global: {
        components: { ElCard, ElTag, ElButton, ElDropdown }
      }
    })
    
    const assigneeElement = unassignedWrapper.find('.task-assignee')
    expect(assigneeElement.text()).toContain('未分配')
  })

  it('应该根据任务类型显示对应的图标', () => {
    const typeIcon = wrapper.find('.task-type-icon')
    expect(typeIcon.exists()).toBe(true)
    
    // 测试不同类型的图标
    const typeIcons = {
      'DEVELOPMENT': 'code',
      'TESTING': 'bug',
      'DESIGN': 'palette',
      'DOCUMENTATION': 'document'
    }
    
    expect(typeIcon.classes()).toContain(`icon-${typeIcons[mockTask.type] || 'default'}`)
  })

  it('应该在悬停时显示完整的任务描述', async () => {
    const descriptionElement = wrapper.find('.task-description')
    await descriptionElement.trigger('mouseenter')
    
    // 检查是否显示了tooltip或者完整描述
    expect(wrapper.find('.task-description-tooltip').exists()).toBe(true)
  })

  it('应该正确处理空的标签数组', () => {
    const noTagsTask = {
      ...mockTask,
      tags: []
    }
    
    const noTagsWrapper = mount(TaskCard, {
      props: { task: noTagsTask },
      global: {
        components: { ElCard, ElTag, ElButton, ElDropdown }
      }
    })
    
    const tagElements = noTagsWrapper.findAll('.task-tag')
    expect(tagElements).toHaveLength(0)
  })

  it('应该正确格式化日期显示', () => {
    const dateElement = wrapper.find('.task-created-date')
    expect(dateElement.exists()).toBe(true)
    
    // 检查日期格式是否正确
    const dateText = dateElement.text()
    expect(dateText).toMatch(/\d{4}-\d{2}-\d{2}/)
  })

  it('应该在任务完成时显示完成标识', () => {
    const completedTask = {
      ...mockTask,
      status: 'DONE',
      progress: 100
    }
    
    const completedWrapper = mount(TaskCard, {
      props: { task: completedTask },
      global: {
        components: { ElCard, ElTag, ElButton, ElDropdown }
      }
    })
    
    expect(completedWrapper.find('.task-completed').exists()).toBe(true)
    expect(completedWrapper.find('.task-completed-icon').exists()).toBe(true)
  })
})
