package com.wtms.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 创建评价请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "创建评价请求")
public class CreateEvaluationRequest {

    @Schema(description = "任务ID", required = true)
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    @Schema(description = "被评价者ID", required = true)
    @NotBlank(message = "被评价者ID不能为空")
    private String evaluateeId;

    @Schema(description = "评价类型", required = true)
    @NotBlank(message = "评价类型不能为空")
    private String evaluationType;

    @Schema(description = "评价阶段")
    private String evaluationStage;

    @Schema(description = "总体评分", required = true)
    @NotNull(message = "总体评分不能为空")
    @DecimalMin(value = "0", message = "总体评分不能小于0")
    @DecimalMax(value = "100", message = "总体评分不能大于100")
    private BigDecimal overallScore;

    @Schema(description = "质量评分")
    @DecimalMin(value = "0", message = "质量评分不能小于0")
    @DecimalMax(value = "100", message = "质量评分不能大于100")
    private BigDecimal qualityScore;

    @Schema(description = "效率评分")
    @DecimalMin(value = "0", message = "效率评分不能小于0")
    @DecimalMax(value = "100", message = "效率评分不能大于100")
    private BigDecimal efficiencyScore;

    @Schema(description = "沟通评分")
    @DecimalMin(value = "0", message = "沟通评分不能小于0")
    @DecimalMax(value = "100", message = "沟通评分不能大于100")
    private BigDecimal communicationScore;

    @Schema(description = "创新评分")
    @DecimalMin(value = "0", message = "创新评分不能小于0")
    @DecimalMax(value = "100", message = "创新评分不能大于100")
    private BigDecimal innovationScore;

    @Schema(description = "团队协作评分")
    @DecimalMin(value = "0", message = "团队协作评分不能小于0")
    @DecimalMax(value = "100", message = "团队协作评分不能大于100")
    private BigDecimal teamworkScore;

    @Schema(description = "评价内容", required = true)
    @NotBlank(message = "评价内容不能为空")
    @Size(max = 2000, message = "评价内容长度不能超过2000个字符")
    private String evaluationContent;

    @Schema(description = "优点描述")
    @Size(max = 1000, message = "优点描述长度不能超过1000个字符")
    private String strengths;

    @Schema(description = "改进建议")
    @Size(max = 1000, message = "改进建议长度不能超过1000个字符")
    private String improvements;

    @Schema(description = "评价标签")
    private String evaluationTags;

    @Schema(description = "是否匿名")
    private Boolean isAnonymous;

    @Schema(description = "是否公开")
    private Boolean isPublic;

    @Schema(description = "权重")
    @DecimalMin(value = "0", message = "权重不能小于0")
    @DecimalMax(value = "10", message = "权重不能大于10")
    private BigDecimal weight;

    @Schema(description = "评价截止时间")
    private LocalDateTime deadline;

    @Schema(description = "评价维度列表")
    @Valid
    private List<EvaluationDimensionRequest> dimensions;

    @Schema(description = "附件ID列表")
    private List<String> attachmentIds;

    /**
     * 评价维度请求
     */
    @Data
    @Schema(description = "评价维度请求")
    public static class EvaluationDimensionRequest {

        @Schema(description = "维度名称", required = true)
        @NotBlank(message = "维度名称不能为空")
        private String dimensionName;

        @Schema(description = "维度编码")
        private String dimensionCode;

        @Schema(description = "维度描述")
        private String description;

        @Schema(description = "评分", required = true)
        @NotNull(message = "评分不能为空")
        @DecimalMin(value = "0", message = "评分不能小于0")
        @DecimalMax(value = "100", message = "评分不能大于100")
        private BigDecimal score;

        @Schema(description = "最大分值")
        @DecimalMin(value = "0", message = "最大分值不能小于0")
        private BigDecimal maxScore;

        @Schema(description = "最小分值")
        @DecimalMin(value = "0", message = "最小分值不能小于0")
        private BigDecimal minScore;

        @Schema(description = "权重")
        @DecimalMin(value = "0", message = "权重不能小于0")
        @DecimalMax(value = "10", message = "权重不能大于10")
        private BigDecimal weight;

        @Schema(description = "评价内容")
        @Size(max = 1000, message = "评价内容长度不能超过1000个字符")
        private String evaluationContent;

        @Schema(description = "评价标准")
        @Size(max = 500, message = "评价标准长度不能超过500个字符")
        private String evaluationCriteria;

        @Schema(description = "是否必填")
        private Boolean isRequired;

        @Schema(description = "排序号")
        private Integer sortOrder;
    }

    /**
     * 设置默认值
     */
    public void setDefaults() {
        if (evaluationStage == null) {
            evaluationStage = "final";
        }
        if (isAnonymous == null) {
            isAnonymous = false;
        }
        if (isPublic == null) {
            isPublic = true;
        }
        if (weight == null) {
            weight = new BigDecimal("1.0");
        }
        
        // 设置维度默认值
        if (dimensions != null) {
            for (EvaluationDimensionRequest dimension : dimensions) {
                if (dimension.getMaxScore() == null) {
                    dimension.setMaxScore(new BigDecimal("100"));
                }
                if (dimension.getMinScore() == null) {
                    dimension.setMinScore(BigDecimal.ZERO);
                }
                if (dimension.getWeight() == null) {
                    dimension.setWeight(new BigDecimal("1.0"));
                }
                if (dimension.getIsRequired() == null) {
                    dimension.setIsRequired(false);
                }
                if (dimension.getSortOrder() == null) {
                    dimension.setSortOrder(0);
                }
            }
        }
    }

    /**
     * 验证评分一致性
     */
    public boolean isScoreConsistent() {
        // 检查各维度评分是否与总体评分一致
        if (qualityScore == null || efficiencyScore == null) {
            return true; // 如果没有维度评分，则不进行一致性检查
        }
        
        // 简单的一致性检查：各维度平均分与总体评分的差异不超过10分
        BigDecimal avgDimensionScore = qualityScore.add(efficiencyScore);
        if (communicationScore != null) {
            avgDimensionScore = avgDimensionScore.add(communicationScore);
        }
        if (innovationScore != null) {
            avgDimensionScore = avgDimensionScore.add(innovationScore);
        }
        if (teamworkScore != null) {
            avgDimensionScore = avgDimensionScore.add(teamworkScore);
        }
        
        int dimensionCount = 2; // 质量和效率是必须的
        if (communicationScore != null) dimensionCount++;
        if (innovationScore != null) dimensionCount++;
        if (teamworkScore != null) dimensionCount++;
        
        avgDimensionScore = avgDimensionScore.divide(new BigDecimal(dimensionCount), 2, BigDecimal.ROUND_HALF_UP);
        
        BigDecimal difference = overallScore.subtract(avgDimensionScore).abs();
        return difference.compareTo(new BigDecimal("10")) <= 0;
    }
}
