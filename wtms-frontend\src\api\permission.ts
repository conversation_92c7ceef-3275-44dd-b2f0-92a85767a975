import request from '@/utils/request'

/**
 * 权限相关接口
 */

// 权限类型定义
export interface Permission {
  id: string
  code: string
  name: string
  description?: string
  type: 'menu' | 'button' | 'api' | 'data'
  groupName?: string
  parentId?: string
  path?: string
  level?: number
  sortOrder?: number
  resource?: string
  action?: string
  expression?: string
  isEnabled: boolean
  isSystem: boolean
  createdAt: string
  updatedAt: string
  children?: Permission[]
}

// 权限树响应
export interface PermissionTreeResponse {
  id: string
  code: string
  name: string
  description?: string
  type: string
  typeText: string
  groupName?: string
  parentId?: string
  path?: string
  level?: number
  sortOrder?: number
  resource?: string
  action?: string
  expression?: string
  fullExpression?: string
  isEnabled: boolean
  isSystem: boolean
  isLeaf: boolean
  createdAt: string
  updatedAt: string
  children?: PermissionTreeResponse[]
}

// 用户权限响应
export interface UserPermissionResponse {
  userId: string
  username: string
  roles: Array<{
    id: string
    code: string
    name: string
    description?: string
    isEnabled: boolean
  }>
  permissions: string[]
  permissionTree: PermissionTreeResponse[]
  menus: Array<{
    id: string
    code: string
    name: string
    path?: string
    parentId?: string
    level?: number
    sortOrder?: number
    children?: any[]
  }>
  buttons: string[]
  apis: string[]
  dataPermissions: string[]
  isSuperAdmin: boolean
}

// 创建权限请求
export interface CreatePermissionRequest {
  code: string
  name: string
  description?: string
  type: 'menu' | 'button' | 'api' | 'data'
  groupName?: string
  parentId?: string
  path?: string
  sortOrder?: number
  resource?: string
  action?: string
  expression?: string
  isEnabled?: boolean
  isSystem?: boolean
}

// 更新权限请求
export interface UpdatePermissionRequest {
  code?: string
  name?: string
  description?: string
  type?: 'menu' | 'button' | 'api' | 'data'
  groupName?: string
  parentId?: string
  path?: string
  sortOrder?: number
  resource?: string
  action?: string
  expression?: string
  isEnabled?: boolean
  isSystem?: boolean
}

// 角色权限分配请求
export interface AssignRolePermissionRequest {
  roleId: string
  permissionIds: string[]
  grantType?: 'direct' | 'inherit' | 'temporary'
  isEnabled?: boolean
  expiresAt?: string
  comment?: string
}

// 用户角色分配请求
export interface AssignUserRoleRequest {
  userId: string
  roleIds: string[]
  assignType?: 'direct' | 'inherit' | 'temporary' | 'default'
  isEnabled?: boolean
  expiresAt?: string
  comment?: string
}

// API 接口函数

/**
 * 权限管理
 */
export function createPermission(data: CreatePermissionRequest) {
  return request.post<Permission>('/api/v1/permissions', data)
}

export function updatePermission(permissionId: string, data: UpdatePermissionRequest) {
  return request.put<Permission>(`/api/v1/permissions/${permissionId}`, data)
}

export function deletePermission(permissionId: string) {
  return request.delete(`/api/v1/permissions/${permissionId}`)
}

export function getPermission(permissionId: string) {
  return request.get<Permission>(`/api/v1/permissions/${permissionId}`)
}

export function getPermissionByCode(code: string) {
  return request.get<Permission>(`/api/v1/permissions/code/${code}`)
}

export function getPermissionTree() {
  return request.get<PermissionTreeResponse[]>('/api/v1/permissions/tree')
}

export function getPermissionsByType(type: string) {
  return request.get<Permission[]>(`/api/v1/permissions/type/${type}`)
}

export function getPermissionsByGroup(groupName: string) {
  return request.get<Permission[]>(`/api/v1/permissions/group/${groupName}`)
}

export function getEnabledPermissions() {
  return request.get<Permission[]>('/api/v1/permissions/enabled')
}

export function getSystemPermissions() {
  return request.get<Permission[]>('/api/v1/permissions/system')
}

export function enablePermission(permissionId: string) {
  return request.put(`/api/v1/permissions/${permissionId}/enable`)
}

export function disablePermission(permissionId: string) {
  return request.put(`/api/v1/permissions/${permissionId}/disable`)
}

export function batchEnablePermissions(permissionIds: string[]) {
  return request.put('/api/v1/permissions/batch/enable', permissionIds)
}

export function batchDisablePermissions(permissionIds: string[]) {
  return request.put('/api/v1/permissions/batch/disable', permissionIds)
}

export function getUserPermissions(userId: string) {
  return request.get<UserPermissionResponse>(`/api/v1/permissions/users/${userId}`)
}

export function getUserPermissionCodes(userId: string) {
  return request.get<string[]>(`/api/v1/permissions/users/${userId}/codes`)
}

export function getRolePermissions(roleId: string) {
  return request.get<Permission[]>(`/api/v1/permissions/roles/${roleId}`)
}

export function getRolePermissionCodes(roleId: string) {
  return request.get<string[]>(`/api/v1/permissions/roles/${roleId}/codes`)
}

export function initSystemPermissions() {
  return request.post('/api/v1/permissions/init')
}

export function refreshPermissionCache() {
  return request.post('/api/v1/permissions/refresh-cache')
}

export function getPermissionStatistics() {
  return request.get('/api/v1/permissions/statistics')
}

/**
 * 角色权限管理
 */
export function assignPermissionsToRole(data: AssignRolePermissionRequest) {
  return request.post('/api/v1/role-permissions/assign', data)
}

export function removePermissionsFromRole(roleId: string, permissionIds: string[]) {
  return request.delete('/api/v1/role-permissions/remove', {
    params: { roleId },
    data: permissionIds
  })
}

export function clearRolePermissions(roleId: string) {
  return request.delete(`/api/v1/role-permissions/roles/${roleId}/clear`)
}

export function getRolePermissionAssignments(roleId: string) {
  return request.get(`/api/v1/role-permissions/roles/${roleId}/assignments`)
}

export function getPermissionRoleAssignments(permissionId: string) {
  return request.get(`/api/v1/role-permissions/permissions/${permissionId}/assignments`)
}

export function checkRolePermission(roleId: string, permissionId: string) {
  return request.get<boolean>('/api/v1/role-permissions/check', {
    params: { roleId, permissionId }
  })
}

export function enableRolePermission(roleId: string, permissionId: string) {
  return request.put('/api/v1/role-permissions/enable', null, {
    params: { roleId, permissionId }
  })
}

export function disableRolePermission(roleId: string, permissionId: string) {
  return request.put('/api/v1/role-permissions/disable', null, {
    params: { roleId, permissionId }
  })
}

export function copyRolePermissions(sourceRoleId: string, targetRoleId: string) {
  return request.post('/api/v1/role-permissions/copy', null, {
    params: { sourceRoleId, targetRoleId }
  })
}

export function syncRolePermissions(roleId: string, permissionIds: string[]) {
  return request.post('/api/v1/role-permissions/sync', permissionIds, {
    params: { roleId }
  })
}

/**
 * 用户角色管理
 */
export function assignRolesToUser(data: AssignUserRoleRequest) {
  return request.post('/api/v1/user-roles/assign', data)
}

export function removeRolesFromUser(userId: string, roleIds: string[]) {
  return request.delete('/api/v1/user-roles/remove', {
    params: { userId },
    data: roleIds
  })
}

export function clearUserRoles(userId: string) {
  return request.delete(`/api/v1/user-roles/users/${userId}/clear`)
}

export function getUserRoles(userId: string) {
  return request.get(`/api/v1/user-roles/users/${userId}/roles`)
}

export function getUserRoleAssignments(userId: string) {
  return request.get(`/api/v1/user-roles/users/${userId}/assignments`)
}

export function getRoleUserAssignments(roleId: string) {
  return request.get(`/api/v1/user-roles/roles/${roleId}/assignments`)
}

export function checkUserRole(userId: string, roleId: string) {
  return request.get<boolean>('/api/v1/user-roles/check', {
    params: { userId, roleId }
  })
}

export function checkUserRoleByCode(userId: string, roleCode: string) {
  return request.get<boolean>('/api/v1/user-roles/check-code', {
    params: { userId, roleCode }
  })
}

export function enableUserRole(userId: string, roleId: string) {
  return request.put('/api/v1/user-roles/enable', null, {
    params: { userId, roleId }
  })
}

export function disableUserRole(userId: string, roleId: string) {
  return request.put('/api/v1/user-roles/disable', null, {
    params: { userId, roleId }
  })
}

export function copyUserRoles(sourceUserId: string, targetUserId: string) {
  return request.post('/api/v1/user-roles/copy', null, {
    params: { sourceUserId, targetUserId }
  })
}

export function syncUserRoles(userId: string, roleIds: string[]) {
  return request.post('/api/v1/user-roles/sync', roleIds, {
    params: { userId }
  })
}

export function isSuperAdmin(userId: string) {
  return request.get<boolean>(`/api/v1/user-roles/users/${userId}/is-super-admin`)
}

export function isAdmin(userId: string) {
  return request.get<boolean>(`/api/v1/user-roles/users/${userId}/is-admin`)
}
