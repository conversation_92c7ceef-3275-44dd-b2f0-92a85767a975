package com.wtms;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * WTMS 快速启动应用
 * 排除数据库依赖，提供基本API服务
 * 
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication
public class WtmsQuickStartApp {

    public static void main(String[] args) {
        // 设置端口
        System.setProperty("server.port", "55557");
        
        SpringApplication app = new SpringApplication(WtmsQuickStartApp.class);
        app.run(args);
        
        System.out.println("=================================");
        System.out.println("WTMS Java后端服务启动成功！");
        System.out.println("访问地址: http://localhost:55557/api/v1");
        System.out.println("健康检查: http://localhost:55557/api/v1/health");
        System.out.println("登录接口: http://localhost:55557/api/v1/auth/login");
        System.out.println("=================================");
    }












}
