{"info": {"name": "WTMS API Tests", "description": "WTMS工作任务管理系统API接口测试集合", "version": "1.0.0", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:55557/api/v1", "type": "string"}, {"key": "token", "value": "", "type": "string"}, {"key": "userId", "value": "", "type": "string"}, {"key": "taskId", "value": "", "type": "string"}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{token}}", "type": "string"}]}, "item": [{"name": "认证接口", "item": [{"name": "用户登录", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"登录成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.token).to.be.a('string');", "    ", "    // 保存token到环境变量", "    pm.environment.set('token', response.data.token);", "    pm.environment.set('userId', response.data.user.id);", "});", "", "pm.test(\"响应时间小于2秒\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(2000);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"admin123456\",\n  \"rememberMe\": false\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "用户登录 - 错误密码", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"登录失败\", function () {", "    pm.response.to.have.status(401);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(401);", "    pm.expect(response.message).to.include('密码');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"admin\",\n  \"password\": \"wrongpassword\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/login", "host": ["{{baseUrl}}"], "path": ["auth", "login"]}}}, {"name": "用户注册", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"注册成功\", function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(201);", "    pm.expect(response.data.username).to.eql('testuser');", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"testuser\",\n  \"password\": \"testuser123456\",\n  \"fullName\": \"测试用户\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"13800138999\"\n}"}, "url": {"raw": "{{baseUrl}}/auth/register", "host": ["{{baseUrl}}"], "path": ["auth", "register"]}}}, {"name": "用户登出", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"登出成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/auth/logout", "host": ["{{baseUrl}}"], "path": ["auth", "logout"]}}}]}, {"name": "用户管理", "item": [{"name": "获取用户列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取用户列表成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.records).to.be.an('array');", "    pm.expect(response.data.total).to.be.a('number');", "});", "", "pm.test(\"分页参数正确\", function () {", "    const response = pm.response.json();", "    pm.expect(response.data.current).to.eql(1);", "    pm.expect(response.data.size).to.eql(10);", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users?page=1&size=10", "host": ["{{baseUrl}}"], "path": ["users"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}]}}}, {"name": "获取用户详情", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取用户详情成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.id).to.be.a('string');", "    pm.expect(response.data.username).to.be.a('string');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/users/{{userId}}", "host": ["{{baseUrl}}"], "path": ["users", "{{userId}}"]}}}, {"name": "创建用户", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"创建用户成功\", function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(201);", "    pm.expect(response.data.username).to.eql('newuser');", "    ", "    // 保存新用户ID", "    pm.environment.set('newUserId', response.data.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"newuser\",\n  \"password\": \"newuser123456\",\n  \"fullName\": \"新用户\",\n  \"email\": \"<EMAIL>\",\n  \"phone\": \"13800138888\",\n  \"departmentId\": \"1\",\n  \"status\": \"ACTIVE\"\n}"}, "url": {"raw": "{{baseUrl}}/users", "host": ["{{baseUrl}}"], "path": ["users"]}}}]}, {"name": "任务管理", "item": [{"name": "获取任务列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取任务列表成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.records).to.be.an('array');", "    ", "    if (response.data.records.length > 0) {", "        pm.environment.set('taskId', response.data.records[0].id);", "    }", "});", "", "pm.test(\"任务数据结构正确\", function () {", "    const response = pm.response.json();", "    if (response.data.records.length > 0) {", "        const task = response.data.records[0];", "        pm.expect(task).to.have.property('id');", "        pm.expect(task).to.have.property('title');", "        pm.expect(task).to.have.property('status');", "        pm.expect(task).to.have.property('priority');", "    }", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/tasks?page=1&size=10&status=IN_PROGRESS", "host": ["{{baseUrl}}"], "path": ["tasks"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}, {"key": "status", "value": "IN_PROGRESS"}]}}}, {"name": "创建任务", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"创建任务成功\", function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(201);", "    pm.expect(response.data.title).to.eql('API测试任务');", "    pm.expect(response.data.status).to.eql('TODO');", "    ", "    // 保存新任务ID", "    pm.environment.set('newTaskId', response.data.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"title\": \"API测试任务\",\n  \"description\": \"这是一个通过API创建的测试任务\",\n  \"priority\": \"MEDIUM\",\n  \"type\": \"TESTING\",\n  \"estimatedHours\": 8,\n  \"dueDate\": \"2024-02-15\",\n  \"tags\": [\"API\", \"测试\", \"自动化\"]\n}"}, "url": {"raw": "{{baseUrl}}/tasks", "host": ["{{baseUrl}}"], "path": ["tasks"]}}}, {"name": "获取任务详情", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取任务详情成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.id).to.be.a('string');", "});", "", "pm.test(\"任务详情包含完整信息\", function () {", "    const response = pm.response.json();", "    const task = response.data;", "    pm.expect(task).to.have.property('title');", "    pm.expect(task).to.have.property('description');", "    pm.expect(task).to.have.property('creator');", "    pm.expect(task).to.have.property('createdAt');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/tasks/{{taskId}}", "host": ["{{baseUrl}}"], "path": ["tasks", "{{taskId}}"]}}}, {"name": "更新任务状态", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"更新任务状态成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "});"], "type": "text/javascript"}}], "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"status\": \"IN_PROGRESS\",\n  \"comment\": \"开始执行任务\"\n}"}, "url": {"raw": "{{baseUrl}}/tasks/{{newTaskId}}/status", "host": ["{{baseUrl}}"], "path": ["tasks", "{{newTaskId}}", "status"]}}}]}, {"name": "质量评价", "item": [{"name": "获取评价列表", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取评价列表成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data.records).to.be.an('array');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/evaluations?page=1&size=10", "host": ["{{baseUrl}}"], "path": ["evaluations"], "query": [{"key": "page", "value": "1"}, {"key": "size", "value": "10"}]}}}, {"name": "创建评价", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"创建评价成功\", function () {", "    pm.response.to.have.status(201);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(201);", "    pm.expect(response.data.overallScore).to.eql(85);", "    ", "    // 保存评价ID", "    pm.environment.set('evaluationId', response.data.id);", "});"], "type": "text/javascript"}}], "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"taskId\": \"{{taskId}}\",\n  \"evaluateeId\": \"2\",\n  \"evaluationType\": \"PEER\",\n  \"overallScore\": 85,\n  \"qualityScore\": 88,\n  \"efficiencyScore\": 82,\n  \"communicationScore\": 87,\n  \"innovationScore\": 80,\n  \"teamworkScore\": 85,\n  \"content\": \"通过API创建的测试评价，整体表现良好\",\n  \"strengths\": \"技术能力强，执行力好\",\n  \"improvements\": \"可以在创新方面进一步提升\",\n  \"tags\": [\"API测试\", \"同事评价\"],\n  \"isAnonymous\": false,\n  \"isPublic\": true\n}"}, "url": {"raw": "{{baseUrl}}/evaluations", "host": ["{{baseUrl}}"], "path": ["evaluations"]}}}, {"name": "获取评价统计", "event": [{"listen": "test", "script": {"exec": ["pm.test(\"获取评价统计成功\", function () {", "    pm.response.to.have.status(200);", "    const response = pm.response.json();", "    pm.expect(response.code).to.eql(200);", "    pm.expect(response.data).to.have.property('totalEvaluations');", "    pm.expect(response.data).to.have.property('averageScore');", "});"], "type": "text/javascript"}}], "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{token}}"}], "url": {"raw": "{{baseUrl}}/evaluations/statistics?targetType=USER&targetId={{userId}}", "host": ["{{baseUrl}}"], "path": ["evaluations", "statistics"], "query": [{"key": "targetType", "value": "USER"}, {"key": "targetId", "value": "{{userId}}"}]}}}]}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// 全局前置脚本", "console.log('执行API测试: ' + pm.info.requestName);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// 全局测试脚本", "pm.test(\"响应格式正确\", function () {", "    const response = pm.response.json();", "    pm.expect(response).to.have.property('code');", "    pm.expect(response).to.have.property('message');", "    pm.expect(response).to.have.property('timestamp');", "});", "", "pm.test(\"响应时间合理\", function () {", "    pm.expect(pm.response.responseTime).to.be.below(5000);", "});"]}}]}