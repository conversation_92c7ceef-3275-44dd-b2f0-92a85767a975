package com.wtms.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.*;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 更新任务请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "更新任务请求")
public class UpdateTaskRequest {

    @Schema(description = "任务标题", example = "开发用户管理模块")
    @Size(max = 200, message = "任务标题长度不能超过200个字符")
    private String title;

    @Schema(description = "任务描述", example = "实现用户的增删改查功能，包括用户列表、用户详情等页面")
    @Size(max = 2000, message = "任务描述长度不能超过2000个字符")
    private String description;

    @Schema(description = "任务分类ID", example = "uuid-string")
    private String categoryId;

    @Schema(description = "优先级", example = "3", allowableValues = {"1", "2", "3", "4", "5"})
    @Min(value = 1, message = "优先级最小值为1")
    @Max(value = 5, message = "优先级最大值为5")
    private Integer priority;

    @Schema(description = "难度等级", example = "3", allowableValues = {"1", "2", "3", "4", "5"})
    @Min(value = 1, message = "难度等级最小值为1")
    @Max(value = 5, message = "难度等级最大值为5")
    private Integer difficultyLevel;

    @Schema(description = "预估工时（小时）", example = "16.5")
    @DecimalMin(value = "0.1", message = "预估工时必须大于0")
    @DecimalMax(value = "999.9", message = "预估工时不能超过999.9小时")
    private BigDecimal estimatedHours;

    @Schema(description = "实际工时（小时）", example = "18.0")
    @DecimalMin(value = "0", message = "实际工时不能为负数")
    @DecimalMax(value = "999.9", message = "实际工时不能超过999.9小时")
    private BigDecimal actualHours;

    @Schema(description = "进度百分比", example = "75.5")
    @DecimalMin(value = "0", message = "进度不能为负数")
    @DecimalMax(value = "100", message = "进度不能超过100%")
    private BigDecimal progress;

    @Schema(description = "负责人ID", example = "uuid-string")
    private String assigneeId;

    @Schema(description = "审核人ID", example = "uuid-string")
    private String reviewerId;

    @Schema(description = "父任务ID", example = "uuid-string")
    private String parentId;

    @Schema(description = "项目ID", example = "uuid-string")
    private String projectId;

    @Schema(description = "计划开始时间", example = "2024-01-15 09:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedStartDate;

    @Schema(description = "计划结束时间", example = "2024-01-20 18:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedEndDate;

    @Schema(description = "实际开始时间", example = "2024-01-15 10:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualStartDate;

    @Schema(description = "实际结束时间", example = "2024-01-21 16:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualEndDate;

    @Schema(description = "标签列表", example = "[\"前端\", \"Vue.js\", \"紧急\"]")
    private List<String> tags;

    @Schema(description = "自定义字段", example = "{\"客户\":\"ABC公司\",\"版本\":\"v1.0\"}")
    private String customFields;

    /**
     * 验证计划时间
     */
    @AssertTrue(message = "计划结束时间必须晚于计划开始时间")
    public boolean isValidPlannedDates() {
        if (plannedStartDate != null && plannedEndDate != null) {
            return plannedEndDate.isAfter(plannedStartDate);
        }
        return true;
    }

    /**
     * 验证实际时间
     */
    @AssertTrue(message = "实际结束时间必须晚于实际开始时间")
    public boolean isValidActualDates() {
        if (actualStartDate != null && actualEndDate != null) {
            return actualEndDate.isAfter(actualStartDate);
        }
        return true;
    }
}
