import { request } from '@/utils/request'
import type { 
  LoginCredentials, 
  LoginResponse, 
  RefreshTokenRequest, 
  TokenResponse, 
  UserInfoResponse,
  ChangePasswordRequest
} from '@/types/user'

/**
 * 认证相关API
 */
export const authApi = {
  /**
   * 用户登录
   */
  login(credentials: LoginCredentials) {
    return request.post<LoginResponse>('/auth/login', credentials)
  },

  /**
   * 用户登出
   */
  logout() {
    return request.post('/auth/logout')
  },

  /**
   * 刷新Token
   */
  refreshToken(data: RefreshTokenRequest) {
    return request.post<TokenResponse>('/auth/refresh', data)
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return request.get<UserInfoResponse>('/user/profile')
  },

  /**
   * 修改密码
   */
  changePassword(data: ChangePasswordRequest) {
    return request.put('/auth/password', data)
  },

  /**
   * 忘记密码
   */
  forgotPassword(email: string) {
    return request.post('/auth/forgot-password', { email })
  },

  /**
   * 重置密码
   */
  resetPassword(token: string, newPassword: string) {
    return request.post('/auth/reset-password', {
      token,
      newPassword
    })
  },

  /**
   * 获取验证码
   */
  getCaptcha() {
    return request.get('/auth/captcha')
  },

  /**
   * 验证验证码
   */
  verifyCaptcha(captchaId: string, captcha: string) {
    return request.post('/auth/verify-captcha', {
      captchaId,
      captcha
    })
  }
}
