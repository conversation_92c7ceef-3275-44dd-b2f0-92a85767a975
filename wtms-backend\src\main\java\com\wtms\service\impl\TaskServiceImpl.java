package com.wtms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.common.exception.BusinessException;
import com.wtms.common.result.PageResult;
import com.wtms.common.result.ResultCode;
import com.wtms.dto.request.CreateTaskRequest;
import com.wtms.dto.request.TaskQueryRequest;
import com.wtms.dto.request.UpdateTaskRequest;
import com.wtms.dto.response.TaskDetailResponse;
import com.wtms.dto.response.TaskListResponse;
import com.wtms.entity.Task;
import com.wtms.entity.TaskCategory;
import com.wtms.entity.User;
import com.wtms.mapper.TaskCategoryMapper;
import com.wtms.mapper.TaskMapper;
import com.wtms.mapper.UserMapper;
import com.wtms.service.TaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务服务实现
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class TaskServiceImpl implements TaskService {

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private TaskCategoryMapper taskCategoryMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    public TaskDetailResponse createTask(CreateTaskRequest request) {
        log.info("Creating task: {}", request.getTitle());

        // 获取当前用户
        String currentUserId = getCurrentUserId();
        User currentUser = userMapper.selectById(currentUserId);
        if (currentUser == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 验证任务分类
        TaskCategory category = taskCategoryMapper.selectById(request.getCategoryId());
        if (category == null || !category.isEnabled()) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "任务分类不存在或已禁用");
        }

        // 验证负责人
        if (StrUtil.isNotBlank(request.getAssigneeId())) {
            User assignee = userMapper.selectById(request.getAssigneeId());
            if (assignee == null || !assignee.isActive()) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND.getCode(), "指定的负责人不存在或已禁用");
            }
        }

        // 验证审核人
        if (StrUtil.isNotBlank(request.getReviewerId())) {
            User reviewer = userMapper.selectById(request.getReviewerId());
            if (reviewer == null || !reviewer.isActive()) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND.getCode(), "指定的审核人不存在或已禁用");
            }
        }

        // 验证父任务
        if (StrUtil.isNotBlank(request.getParentId())) {
            Task parentTask = taskMapper.selectById(request.getParentId());
            if (parentTask == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "父任务不存在");
            }
        }

        // 创建任务实体
        Task task = new Task();
        BeanUtils.copyProperties(request, task);
        
        // 设置基本信息
        task.setCreatorId(currentUserId);
        task.setStatus(Task.Status.DRAFT.getCode());
        task.setProgress(BigDecimal.ZERO);
        task.setIsArchived(false);

        // 生成任务编号
        task.setTaskCode(generateTaskCode(category));

        // 处理标签
        if (request.getTags() != null && !request.getTags().isEmpty()) {
            task.setTags(String.join(",", request.getTags()));
        }

        // 保存任务
        taskMapper.insert(task);

        log.info("Task created successfully: {} - {}", task.getTaskCode(), task.getTitle());

        return getTaskById(task.getId());
    }

    @Override
    public TaskDetailResponse getTaskById(String taskId) {
        Task task = taskMapper.selectTaskDetailById(taskId);
        if (task == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "任务不存在");
        }

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!hasTaskPermission(taskId, currentUserId, "read")) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        return TaskDetailResponse.from(task);
    }

    @Override
    public TaskDetailResponse updateTask(String taskId, UpdateTaskRequest request) {
        log.info("Updating task: {}", taskId);

        // 检查任务是否存在
        Task existingTask = taskMapper.selectById(taskId);
        if (existingTask == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "任务不存在");
        }

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!hasTaskPermission(taskId, currentUserId, "update")) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        // 检查任务是否可编辑
        if (!existingTask.isEditable()) {
            throw new BusinessException(ResultCode.OPERATION_NOT_ALLOWED.getCode(), "任务当前状态不允许编辑");
        }

        // 验证分类
        if (StrUtil.isNotBlank(request.getCategoryId()) && !request.getCategoryId().equals(existingTask.getCategoryId())) {
            TaskCategory category = taskCategoryMapper.selectById(request.getCategoryId());
            if (category == null || !category.isEnabled()) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "任务分类不存在或已禁用");
            }
        }

        // 验证负责人
        if (StrUtil.isNotBlank(request.getAssigneeId()) && !request.getAssigneeId().equals(existingTask.getAssigneeId())) {
            User assignee = userMapper.selectById(request.getAssigneeId());
            if (assignee == null || !assignee.isActive()) {
                throw new BusinessException(ResultCode.USER_NOT_FOUND.getCode(), "指定的负责人不存在或已禁用");
            }
        }

        // 更新任务信息
        Task updateTask = new Task();
        updateTask.setId(taskId);
        
        if (StrUtil.isNotBlank(request.getTitle())) {
            updateTask.setTitle(request.getTitle());
        }
        if (StrUtil.isNotBlank(request.getDescription())) {
            updateTask.setDescription(request.getDescription());
        }
        if (StrUtil.isNotBlank(request.getCategoryId())) {
            updateTask.setCategoryId(request.getCategoryId());
        }
        if (request.getPriority() != null) {
            updateTask.setPriority(request.getPriority());
        }
        if (request.getDifficultyLevel() != null) {
            updateTask.setDifficultyLevel(request.getDifficultyLevel());
        }
        if (request.getEstimatedHours() != null) {
            updateTask.setEstimatedHours(request.getEstimatedHours());
        }
        if (request.getActualHours() != null) {
            updateTask.setActualHours(request.getActualHours());
        }
        if (request.getProgress() != null) {
            updateTask.setProgress(request.getProgress());
        }
        if (StrUtil.isNotBlank(request.getAssigneeId())) {
            updateTask.setAssigneeId(request.getAssigneeId());
        }
        if (StrUtil.isNotBlank(request.getReviewerId())) {
            updateTask.setReviewerId(request.getReviewerId());
        }
        if (request.getPlannedStartDate() != null) {
            updateTask.setPlannedStartDate(request.getPlannedStartDate());
        }
        if (request.getPlannedEndDate() != null) {
            updateTask.setPlannedEndDate(request.getPlannedEndDate());
        }
        if (request.getActualStartDate() != null) {
            updateTask.setActualStartDate(request.getActualStartDate());
        }
        if (request.getActualEndDate() != null) {
            updateTask.setActualEndDate(request.getActualEndDate());
        }
        if (StrUtil.isNotBlank(request.getCustomFields())) {
            updateTask.setCustomFields(request.getCustomFields());
        }

        // 处理标签
        if (request.getTags() != null) {
            if (request.getTags().isEmpty()) {
                updateTask.setTags("");
            } else {
                updateTask.setTags(String.join(",", request.getTags()));
            }
        }

        taskMapper.updateById(updateTask);

        log.info("Task updated successfully: {}", taskId);

        return getTaskById(taskId);
    }

    @Override
    public void deleteTask(String taskId) {
        log.info("Deleting task: {}", taskId);

        // 检查任务是否存在
        Task task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "任务不存在");
        }

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!hasTaskPermission(taskId, currentUserId, "delete")) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        // 检查是否有子任务
        List<Task> subTasks = taskMapper.selectSubTasksByParentId(taskId);
        if (!subTasks.isEmpty()) {
            throw new BusinessException(ResultCode.OPERATION_NOT_ALLOWED.getCode(), "存在子任务，无法删除");
        }

        // 软删除任务
        taskMapper.deleteById(taskId);

        log.info("Task deleted successfully: {}", taskId);
    }

    @Override
    public PageResult<TaskListResponse> getTaskList(TaskQueryRequest request, Integer page, Integer size) {
        String currentUserId = getCurrentUserId();
        
        Page<Task> pageParam = new Page<>(page, size);
        IPage<Task> taskPage = taskMapper.selectTaskPageWithRelations(pageParam, request, currentUserId);

        List<TaskListResponse> taskList = taskPage.getRecords().stream()
                .map(TaskListResponse::from)
                .collect(Collectors.toList());

        return PageResult.of(taskPage, taskList);
    }

    @Override
    public TaskDetailResponse updateTaskStatus(String taskId, String status) {
        log.info("Updating task status: {} -> {}", taskId, status);

        // 验证状态
        try {
            Task.Status.fromCode(status);
        } catch (Exception e) {
            throw new BusinessException(ResultCode.BAD_REQUEST.getCode(), "无效的任务状态");
        }

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!hasTaskPermission(taskId, currentUserId, "update_status")) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        // 更新状态
        taskMapper.updateTaskStatus(taskId, status);

        // 如果是开始任务，更新实际开始时间
        if (Task.Status.IN_PROGRESS.getCode().equals(status)) {
            taskMapper.updateTaskActualStartDate(taskId, LocalDateTime.now());
        }

        // 如果是完成任务，更新实际结束时间和进度
        if (Task.Status.COMPLETED.getCode().equals(status)) {
            taskMapper.updateTaskActualEndDate(taskId, LocalDateTime.now());
            taskMapper.updateTaskProgress(taskId, new BigDecimal("100"));
        }

        log.info("Task status updated successfully: {} -> {}", taskId, status);

        return getTaskById(taskId);
    }

    @Override
    public TaskDetailResponse updateTaskProgress(String taskId, BigDecimal progress) {
        log.info("Updating task progress: {} -> {}%", taskId, progress);

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!hasTaskPermission(taskId, currentUserId, "update")) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        taskMapper.updateTaskProgress(taskId, progress);

        log.info("Task progress updated successfully: {} -> {}%", taskId, progress);

        return getTaskById(taskId);
    }

    @Override
    public TaskDetailResponse assignTask(String taskId, String assigneeId) {
        log.info("Assigning task: {} -> {}", taskId, assigneeId);

        // 验证负责人
        User assignee = userMapper.selectById(assigneeId);
        if (assignee == null || !assignee.isActive()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND.getCode(), "指定的负责人不存在或已禁用");
        }

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!hasTaskPermission(taskId, currentUserId, "assign")) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        taskMapper.updateTaskAssignee(taskId, assigneeId);

        log.info("Task assigned successfully: {} -> {}", taskId, assigneeId);

        return getTaskById(taskId);
    }

    @Override
    public TaskDetailResponse startTask(String taskId) {
        return updateTaskStatus(taskId, Task.Status.IN_PROGRESS.getCode());
    }

    @Override
    public TaskDetailResponse completeTask(String taskId) {
        return updateTaskStatus(taskId, Task.Status.COMPLETED.getCode());
    }

    @Override
    public TaskDetailResponse pauseTask(String taskId) {
        return updateTaskStatus(taskId, Task.Status.PAUSED.getCode());
    }

    @Override
    public TaskDetailResponse cancelTask(String taskId) {
        return updateTaskStatus(taskId, Task.Status.CANCELLED.getCode());
    }

    @Override
    public void archiveTask(String taskId) {
        log.info("Archiving task: {}", taskId);

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!hasTaskPermission(taskId, currentUserId, "archive")) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        taskMapper.updateTaskArchiveStatus(taskId, true);
        log.info("Task archived successfully: {}", taskId);
    }

    @Override
    public void unarchiveTask(String taskId) {
        log.info("Unarchiving task: {}", taskId);

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!hasTaskPermission(taskId, currentUserId, "archive")) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        taskMapper.updateTaskArchiveStatus(taskId, false);
        log.info("Task unarchived successfully: {}", taskId);
    }

    @Override
    public TaskDetailResponse copyTask(String taskId) {
        log.info("Copying task: {}", taskId);

        // 获取原任务
        Task originalTask = taskMapper.selectTaskDetailById(taskId);
        if (originalTask == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "任务不存在");
        }

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!hasTaskPermission(taskId, currentUserId, "read")) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED);
        }

        // 创建新任务
        Task newTask = new Task();
        BeanUtils.copyProperties(originalTask, newTask);

        // 重置关键字段
        newTask.setId(null);
        newTask.setTaskCode(null); // 将重新生成
        newTask.setTitle(originalTask.getTitle() + " (副本)");
        newTask.setCreatorId(currentUserId);
        newTask.setStatus(Task.Status.DRAFT.getCode());
        newTask.setProgress(BigDecimal.ZERO);
        newTask.setActualHours(null);
        newTask.setActualStartDate(null);
        newTask.setActualEndDate(null);
        newTask.setIsArchived(false);
        newTask.setCreatedAt(null);
        newTask.setUpdatedAt(null);

        // 生成新的任务编号
        TaskCategory category = taskCategoryMapper.selectById(originalTask.getCategoryId());
        newTask.setTaskCode(generateTaskCode(category));

        taskMapper.insert(newTask);

        log.info("Task copied successfully: {} -> {}", taskId, newTask.getId());

        return getTaskById(newTask.getId());
    }

    @Override
    public List<TaskListResponse> getSubTasks(String parentId) {
        List<Task> subTasks = taskMapper.selectSubTasksByParentId(parentId);
        return subTasks.stream()
                .map(TaskListResponse::from)
                .collect(Collectors.toList());
    }

    @Override
    public PageResult<TaskListResponse> getMyTasks(String status, Integer page, Integer size) {
        String currentUserId = getCurrentUserId();

        TaskQueryRequest request = new TaskQueryRequest();
        request.setAssigneeId(currentUserId);
        request.setStatus(status);
        request.setOnlyMyTasks(true);

        return getTaskList(request, page, size);
    }

    @Override
    public PageResult<TaskListResponse> getMyCreatedTasks(String status, Integer page, Integer size) {
        String currentUserId = getCurrentUserId();

        TaskQueryRequest request = new TaskQueryRequest();
        request.setCreatorId(currentUserId);
        request.setStatus(status);
        request.setOnlyMyCreated(true);

        return getTaskList(request, page, size);
    }

    @Override
    public Object getTaskStatistics() {
        String currentUserId = getCurrentUserId();
        return taskMapper.getTaskStatistics(currentUserId, currentUserId);
    }

    @Override
    public void batchUpdateTaskStatus(List<String> taskIds, String status) {
        log.info("Batch updating task status: {} tasks -> {}", taskIds.size(), status);

        String currentUserId = getCurrentUserId();
        for (String taskId : taskIds) {
            if (hasTaskPermission(taskId, currentUserId, "update_status")) {
                taskMapper.updateTaskStatus(taskId, status);
            }
        }

        log.info("Batch task status update completed");
    }

    @Override
    public void batchAssignTasks(List<String> taskIds, String assigneeId) {
        log.info("Batch assigning tasks: {} tasks -> {}", taskIds.size(), assigneeId);

        // 验证负责人
        User assignee = userMapper.selectById(assigneeId);
        if (assignee == null || !assignee.isActive()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND.getCode(), "指定的负责人不存在或已禁用");
        }

        String currentUserId = getCurrentUserId();
        for (String taskId : taskIds) {
            if (hasTaskPermission(taskId, currentUserId, "assign")) {
                taskMapper.updateTaskAssignee(taskId, assigneeId);
            }
        }

        log.info("Batch task assignment completed");
    }

    @Override
    public void batchDeleteTasks(List<String> taskIds) {
        log.info("Batch deleting tasks: {} tasks", taskIds.size());

        String currentUserId = getCurrentUserId();
        for (String taskId : taskIds) {
            if (hasTaskPermission(taskId, currentUserId, "delete")) {
                // 检查是否有子任务
                List<Task> subTasks = taskMapper.selectSubTasksByParentId(taskId);
                if (subTasks.isEmpty()) {
                    taskMapper.deleteById(taskId);
                }
            }
        }

        log.info("Batch task deletion completed");
    }

    @Override
    public boolean existsById(String taskId) {
        return taskMapper.selectById(taskId) != null;
    }

    @Override
    public boolean hasTaskPermission(String taskId, String userId, String permission) {
        Task task = taskMapper.selectById(taskId);
        if (task == null) {
            return false;
        }

        User user = userMapper.selectById(userId);
        if (user == null) {
            return false;
        }

        // 系统管理员拥有所有权限
        if (user.getRole() != null && "ADMIN".equals(user.getRole().getCode())) {
            return true;
        }

        // 任务创建者拥有所有权限
        if (task.getCreatorId().equals(userId)) {
            return true;
        }

        // 任务负责人拥有读取和更新权限
        if (task.getAssigneeId() != null && task.getAssigneeId().equals(userId)) {
            return Arrays.asList("read", "update", "update_status").contains(permission);
        }

        // 审核人拥有读取权限
        if (task.getReviewerId() != null && task.getReviewerId().equals(userId)) {
            return "read".equals(permission);
        }

        // 项目经理拥有分配和状态更新权限
        if (user.getRole() != null && "PM".equals(user.getRole().getCode())) {
            return Arrays.asList("read", "assign", "update_status").contains(permission);
        }

        // 默认只有读取权限
        return "read".equals(permission);
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof UserDetails) {
            String username = ((UserDetails) principal).getUsername();
            User user = userMapper.findByUsernameWithRole(username);
            return user != null ? user.getId() : null;
        }
        return null;
    }

    /**
     * 生成任务编号
     */
    private String generateTaskCode(TaskCategory category) {
        String template = category.getNamingTemplate();
        if (StrUtil.isBlank(template)) {
            template = "{CODE}-{YYYY}{MM}{DD}-{###}";
        }

        LocalDateTime now = LocalDateTime.now();
        String code = template
                .replace("{CODE}", category.getCode())
                .replace("{YYYY}", now.format(DateTimeFormatter.ofPattern("yyyy")))
                .replace("{MM}", now.format(DateTimeFormatter.ofPattern("MM")))
                .replace("{DD}", now.format(DateTimeFormatter.ofPattern("dd")));

        // 生成序号
        if (code.contains("{###}")) {
            String prefix = code.substring(0, code.indexOf("{###}"));
            int count = taskMapper.countByTaskCode(prefix + "%") + 1;
            code = code.replace("{###}", String.format("%03d", count));
        }

        return code;
    }
}
