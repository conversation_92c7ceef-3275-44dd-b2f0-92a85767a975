#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
开发用户权限提升脚本
为开发用户分配超级管理员权限
"""

import pymysql
import sys
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3308,
    'user': 'root',
    'password': 'ankaixin.docker.mysql',
    'database': 'wtms',
    'charset': 'utf8mb4'
}

def upgrade_dev_users():
    """为开发用户分配超级管理员权限"""
    print("🚀 开始为开发用户分配超级管理员权限...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 1. 检查开发用户当前状态
        print("\n📋 检查开发用户当前状态...")
        cursor.execute("""
            SELECT 
                u.username,
                u.full_name,
                u.status,
                r.name as current_role
            FROM users u
            LEFT JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = 1
            LEFT JOIN roles r ON ur.role_id = r.id
            WHERE u.username IN ('dev001', 'dev002')
        """)
        
        current_status = cursor.fetchall()
        for username, full_name, status, role in current_status:
            print(f"  {username} ({full_name}): {status} - 当前角色: {role or '无'}")
        
        # 2. 删除开发用户现有的角色分配
        print("\n🗑️ 删除开发用户现有角色分配...")
        cursor.execute("""
            DELETE FROM user_roles 
            WHERE user_id IN (
                SELECT id FROM users WHERE username IN ('dev001', 'dev002')
            )
        """)
        deleted_count = cursor.rowcount
        print(f"  删除了 {deleted_count} 个现有角色分配")
        
        # 3. 为开发用户分配超级管理员角色
        print("\n👑 为开发用户分配超级管理员角色...")
        cursor.execute("""
            INSERT INTO user_roles (id, user_id, role_id, assigned_by, is_active) 
            SELECT 
                CONCAT('ur-dev-', u.username) as id,
                u.id as user_id,
                r.id as role_id,
                'user-001' as assigned_by,
                1 as is_active
            FROM users u
            CROSS JOIN roles r
            WHERE u.username IN ('dev001', 'dev002')
            AND r.code = 'SUPER_ADMIN'
        """)
        assigned_count = cursor.rowcount
        print(f"  成功分配 {assigned_count} 个超级管理员角色")
        
        # 4. 验证权限分配结果
        print("\n🔍 验证权限分配结果...")
        cursor.execute("""
            SELECT 
                u.username as '用户名',
                u.full_name as '姓名',
                r.name as '角色名称',
                COUNT(DISTINCT p.id) as '权限数量'
            FROM users u
            JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = 1
            JOIN roles r ON ur.role_id = r.id
            LEFT JOIN role_permissions rp ON r.id = rp.role_id
            LEFT JOIN permissions p ON rp.permission_id = p.id AND p.is_enabled = 1
            WHERE u.username IN ('dev001', 'dev002')
            GROUP BY u.id, u.username, u.full_name, r.name
            ORDER BY u.username
        """)
        
        verification_results = cursor.fetchall()
        for username, full_name, role_name, perm_count in verification_results:
            status = "✅ 权限充足" if perm_count >= 45 else "❌ 权限不足"
            print(f"  {username} ({full_name}): {role_name} - {perm_count} 个权限 {status}")
        
        # 5. 显示所有超级管理员用户
        print("\n👥 当前超级管理员用户列表:")
        cursor.execute("""
            SELECT 
                u.username as '用户名',
                u.full_name as '姓名',
                u.email as '邮箱',
                u.status as '状态'
            FROM users u
            JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = 1
            JOIN roles r ON ur.role_id = r.id
            WHERE r.code = 'SUPER_ADMIN'
            ORDER BY u.username
        """)
        
        super_admins = cursor.fetchall()
        for username, full_name, email, status in super_admins:
            print(f"  {username} ({full_name}) - {email} [{status}]")
        
        # 6. 添加开发测试数据
        print("\n📝 添加开发测试数据...")
        
        # 获取开发用户ID
        cursor.execute("SELECT id FROM users WHERE username = 'dev001'")
        dev001_id = cursor.fetchone()[0] if cursor.rowcount > 0 else None
        
        cursor.execute("SELECT id FROM users WHERE username = 'dev002'")
        dev002_id = cursor.fetchone()[0] if cursor.rowcount > 0 else None
        
        if dev001_id and dev002_id:
            # 添加测试任务
            test_tasks = [
                ('task-dev-001', 'DEV-001', '权限系统测试', '测试权限系统的各项功能', 'cat-102', 'in_progress', 4, 3, 8.00, dev001_id, dev001_id),
                ('task-dev-002', 'DEV-002', '前端权限组件开发', '开发前端权限控制组件', 'cat-101', 'pending', 3, 2, 12.00, dev002_id, dev001_id),
                ('task-dev-003', 'DEV-003', 'API权限验证', '验证所有API接口的权限控制', 'cat-102', 'pending', 5, 4, 16.00, dev001_id, dev002_id)
            ]
            
            for task_data in test_tasks:
                try:
                    cursor.execute("""
                        INSERT INTO tasks (id, task_code, title, description, category_id, status, priority, difficulty_level, estimated_hours, creator_id, assignee_id, planned_start_date, planned_end_date)
                        VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s, NOW(), DATE_ADD(NOW(), INTERVAL 3 DAY))
                    """, task_data)
                    print(f"  ✅ 添加测试任务: {task_data[2]}")
                except Exception as e:
                    if 'duplicate' not in str(e).lower():
                        print(f"  ⚠️ 任务添加警告: {str(e)[:50]}...")
            
            # 添加测试评论
            test_comments = [
                ('comment-dev-001', 'task-dev-001', dev001_id, '开始权限系统功能测试，重点验证角色权限分配'),
                ('comment-dev-002', 'task-dev-002', dev001_id, '前端权限组件需要支持动态权限控制'),
                ('comment-dev-003', 'task-dev-003', dev002_id, 'API权限验证需要覆盖所有Controller接口')
            ]
            
            for comment_data in test_comments:
                try:
                    cursor.execute("""
                        INSERT INTO task_comments (id, task_id, user_id, content, comment_type)
                        VALUES (%s, %s, %s, %s, 'comment')
                    """, comment_data)
                    print(f"  ✅ 添加测试评论")
                except Exception as e:
                    if 'duplicate' not in str(e).lower():
                        print(f"  ⚠️ 评论添加警告: {str(e)[:50]}...")
        
        connection.commit()
        connection.close()
        
        print("\n" + "="*60)
        print("🎉 开发用户权限提升完成！")
        print("="*60)
        print("📋 开发用户登录信息:")
        print("   用户名: dev001 / dev002")
        print("   密码: admin123")
        print("   角色: 超级管理员")
        print("   权限: 拥有所有系统权限")
        print("   用途: 开发调试和功能测试")
        print("="*60)
        
        return True
        
    except Exception as e:
        print(f"❌ 开发用户权限提升失败: {str(e)}")
        return False

def main():
    """主函数"""
    print("="*60)
    print("🔧 WTMS 开发用户权限提升脚本")
    print("="*60)
    print(f"⏰ 开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    if not upgrade_dev_users():
        sys.exit(1)
    
    print(f"⏰ 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    try:
        import pymysql
    except ImportError:
        print("❌ 缺少pymysql模块，请安装: pip install pymysql")
        sys.exit(1)
    
    main()
