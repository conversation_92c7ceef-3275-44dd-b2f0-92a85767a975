package com.wtms.dto.response;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.wtms.entity.Task;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务详情响应DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "任务详情响应")
public class TaskDetailResponse {

    @Schema(description = "任务ID")
    private String id;

    @Schema(description = "任务编号")
    private String taskCode;

    @Schema(description = "任务标题")
    private String title;

    @Schema(description = "任务描述")
    private String description;

    @Schema(description = "任务状态")
    private String status;

    @Schema(description = "任务状态描述")
    private String statusText;

    @Schema(description = "优先级")
    private Integer priority;

    @Schema(description = "优先级描述")
    private String priorityText;

    @Schema(description = "难度等级")
    private Integer difficultyLevel;

    @Schema(description = "预估工时")
    private BigDecimal estimatedHours;

    @Schema(description = "实际工时")
    private BigDecimal actualHours;

    @Schema(description = "进度百分比")
    private BigDecimal progress;

    @Schema(description = "任务分类")
    private CategoryInfo category;

    @Schema(description = "创建者")
    private UserInfo creator;

    @Schema(description = "负责人")
    private UserInfo assignee;

    @Schema(description = "审核人")
    private UserInfo reviewer;

    @Schema(description = "父任务")
    private ParentTaskInfo parent;

    @Schema(description = "子任务列表")
    private List<SubTaskInfo> children;

    @Schema(description = "项目ID")
    private String projectId;

    @Schema(description = "计划开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedStartDate;

    @Schema(description = "计划结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedEndDate;

    @Schema(description = "实际开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualStartDate;

    @Schema(description = "实际结束时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime actualEndDate;

    @Schema(description = "标签列表")
    private List<String> tags;

    @Schema(description = "附件信息")
    private String attachments;

    @Schema(description = "自定义字段")
    private String customFields;

    @Schema(description = "是否已归档")
    private Boolean isArchived;

    @Schema(description = "是否可编辑")
    private Boolean isEditable;

    @Schema(description = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    /**
     * 分类信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "分类信息")
    public static class CategoryInfo {
        @Schema(description = "分类ID")
        private String id;

        @Schema(description = "分类名称")
        private String name;

        @Schema(description = "分类编码")
        private String code;

        @Schema(description = "分类颜色")
        private String color;

        @Schema(description = "分类图标")
        private String icon;

        @Schema(description = "命名模板")
        private String namingTemplate;
    }

    /**
     * 用户信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "用户信息")
    public static class UserInfo {
        @Schema(description = "用户ID")
        private String id;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "姓名")
        private String fullName;

        @Schema(description = "头像")
        private String avatar;

        @Schema(description = "邮箱")
        private String email;

        @Schema(description = "部门")
        private String department;
    }

    /**
     * 父任务信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "父任务信息")
    public static class ParentTaskInfo {
        @Schema(description = "任务ID")
        private String id;

        @Schema(description = "任务编号")
        private String taskCode;

        @Schema(description = "任务标题")
        private String title;

        @Schema(description = "任务状态")
        private String status;

        @Schema(description = "任务状态描述")
        private String statusText;
    }

    /**
     * 子任务信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "子任务信息")
    public static class SubTaskInfo {
        @Schema(description = "任务ID")
        private String id;

        @Schema(description = "任务编号")
        private String taskCode;

        @Schema(description = "任务标题")
        private String title;

        @Schema(description = "任务状态")
        private String status;

        @Schema(description = "任务状态描述")
        private String statusText;

        @Schema(description = "负责人")
        private String assigneeName;

        @Schema(description = "进度百分比")
        private BigDecimal progress;
    }

    /**
     * 从Task实体转换
     */
    public static TaskDetailResponse from(Task task) {
        TaskDetailResponseBuilder builder = TaskDetailResponse.builder()
                .id(task.getId())
                .taskCode(task.getTaskCode())
                .title(task.getTitle())
                .description(task.getDescription())
                .status(task.getStatus())
                .statusText(Task.Status.fromCode(task.getStatus()).getDescription())
                .priority(task.getPriority())
                .priorityText(Task.Priority.fromLevel(task.getPriority()).getDescription())
                .difficultyLevel(task.getDifficultyLevel())
                .estimatedHours(task.getEstimatedHours())
                .actualHours(task.getActualHours())
                .progress(task.getProgress())
                .projectId(task.getProjectId())
                .plannedStartDate(task.getPlannedStartDate())
                .plannedEndDate(task.getPlannedEndDate())
                .actualStartDate(task.getActualStartDate())
                .actualEndDate(task.getActualEndDate())
                .attachments(task.getAttachments())
                .customFields(task.getCustomFields())
                .isArchived(task.getIsArchived())
                .isEditable(task.isEditable())
                .createdAt(task.getCreatedAt())
                .updatedAt(task.getUpdatedAt());

        // 设置分类信息
        if (task.getCategory() != null) {
            builder.category(CategoryInfo.builder()
                    .id(task.getCategory().getId())
                    .name(task.getCategory().getName())
                    .code(task.getCategory().getCode())
                    .color(task.getCategory().getColor())
                    .icon(task.getCategory().getIcon())
                    .namingTemplate(task.getCategory().getNamingTemplate())
                    .build());
        }

        // 设置创建者信息
        if (task.getCreator() != null) {
            builder.creator(UserInfo.builder()
                    .id(task.getCreator().getId())
                    .username(task.getCreator().getUsername())
                    .fullName(task.getCreator().getFullName())
                    .avatar(task.getCreator().getAvatarUrl())
                    .email(task.getCreator().getEmail())
                    .department(task.getCreator().getDepartment() != null ? 
                               task.getCreator().getDepartment().getName() : null)
                    .build());
        }

        // 设置负责人信息
        if (task.getAssignee() != null) {
            builder.assignee(UserInfo.builder()
                    .id(task.getAssignee().getId())
                    .username(task.getAssignee().getUsername())
                    .fullName(task.getAssignee().getFullName())
                    .avatar(task.getAssignee().getAvatarUrl())
                    .email(task.getAssignee().getEmail())
                    .department(task.getAssignee().getDepartment() != null ? 
                               task.getAssignee().getDepartment().getName() : null)
                    .build());
        }

        // 设置审核人信息
        if (task.getReviewer() != null) {
            builder.reviewer(UserInfo.builder()
                    .id(task.getReviewer().getId())
                    .username(task.getReviewer().getUsername())
                    .fullName(task.getReviewer().getFullName())
                    .avatar(task.getReviewer().getAvatarUrl())
                    .email(task.getReviewer().getEmail())
                    .department(task.getReviewer().getDepartment() != null ? 
                               task.getReviewer().getDepartment().getName() : null)
                    .build());
        }

        // 设置父任务信息
        if (task.getParent() != null) {
            builder.parent(ParentTaskInfo.builder()
                    .id(task.getParent().getId())
                    .taskCode(task.getParent().getTaskCode())
                    .title(task.getParent().getTitle())
                    .status(task.getParent().getStatus())
                    .statusText(Task.Status.fromCode(task.getParent().getStatus()).getDescription())
                    .build());
        }

        // 设置子任务列表
        if (task.getChildren() != null && !task.getChildren().isEmpty()) {
            List<SubTaskInfo> subTasks = task.getChildren().stream()
                    .map(child -> SubTaskInfo.builder()
                            .id(child.getId())
                            .taskCode(child.getTaskCode())
                            .title(child.getTitle())
                            .status(child.getStatus())
                            .statusText(Task.Status.fromCode(child.getStatus()).getDescription())
                            .assigneeName(child.getAssignee() != null ? child.getAssignee().getFullName() : null)
                            .progress(child.getProgress())
                            .build())
                    .collect(Collectors.toList());
            builder.children(subTasks);
        }

        // 设置标签列表
        if (task.getTags() != null && !task.getTags().isEmpty()) {
            List<String> tagList = Arrays.stream(task.getTags().split(","))
                    .map(String::trim)
                    .filter(tag -> !tag.isEmpty())
                    .collect(Collectors.toList());
            builder.tags(tagList);
        }

        return builder.build();
    }
}
