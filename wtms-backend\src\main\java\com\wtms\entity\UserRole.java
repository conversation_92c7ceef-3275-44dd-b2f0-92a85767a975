package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 用户角色关联实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("user_roles")
@Schema(description = "用户角色关联实体")
public class UserRole implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "关联ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "用户ID")
    @TableField("user_id")
    private String userId;

    @Schema(description = "角色ID")
    @TableField("role_id")
    private String roleId;

    @Schema(description = "分配类型")
    @TableField("assign_type")
    private String assignType;

    @Schema(description = "是否启用")
    @TableField("is_enabled")
    private Boolean isEnabled;

    @Schema(description = "分配时间")
    @TableField("assigned_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime assignedAt;

    @Schema(description = "分配人ID")
    @TableField("assigned_by")
    private String assignedBy;

    @Schema(description = "过期时间")
    @TableField("expires_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime expiresAt;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    // 非数据库字段
    @Schema(description = "用户信息")
    @TableField(exist = false)
    private User user;

    @Schema(description = "角色信息")
    @TableField(exist = false)
    private Role role;

    @Schema(description = "分配人信息")
    @TableField(exist = false)
    private User assignedByUser;

    /**
     * 分配类型枚举
     */
    public enum AssignType {
        DIRECT("direct", "直接分配"),
        INHERIT("inherit", "继承分配"),
        TEMPORARY("temporary", "临时分配"),
        DEFAULT("default", "默认分配");

        private final String code;
        private final String description;

        AssignType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static AssignType fromCode(String code) {
            for (AssignType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return DIRECT;
        }
    }

    /**
     * 检查角色是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.isEnabled);
    }

    /**
     * 检查角色是否已过期
     */
    public boolean isExpired() {
        return expiresAt != null && expiresAt.isBefore(LocalDateTime.now());
    }

    /**
     * 检查角色是否有效
     */
    public boolean isValid() {
        return isEnabled() && !isExpired();
    }
}
