package com.wtms.service.impl;

import com.wtms.common.exception.BusinessException;
import com.wtms.common.result.ResultCode;
import com.wtms.dto.request.AssignUserRoleRequest;
import com.wtms.entity.Role;
import com.wtms.entity.User;
import com.wtms.entity.UserRole;
import com.wtms.mapper.RoleMapper;
import com.wtms.mapper.UserMapper;
import com.wtms.mapper.UserRoleMapper;
import com.wtms.service.UserRoleService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 用户角色管理服务实现
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class UserRoleServiceImpl implements UserRoleService {

    @Autowired
    private UserRoleMapper userRoleMapper;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private RoleMapper roleMapper;

    @Autowired
    private com.wtms.service.DevelopmentPermissionService developmentPermissionService;

    @Override
    @CacheEvict(value = {"userRoles", "userPermissions"}, allEntries = true)
    public List<UserRole> assignRolesToUser(AssignUserRoleRequest request) {
        log.info("Assigning roles to user: {} -> {}", request.getUserId(), request.getRoleIds());

        // 验证用户是否存在
        User user = userMapper.selectById(request.getUserId());
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 验证角色是否存在
        for (String roleId : request.getRoleIds()) {
            Role role = roleMapper.selectById(roleId);
            if (role == null) {
                throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "角色不存在: " + roleId);
            }
        }

        // 获取当前用户ID
        String currentUserId = getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();

        List<UserRole> userRoles = new ArrayList<>();
        for (String roleId : request.getRoleIds()) {
            // 检查是否已存在关联
            if (!existsUserRole(request.getUserId(), roleId)) {
                UserRole userRole = new UserRole();
                userRole.setUserId(request.getUserId());
                userRole.setRoleId(roleId);
                userRole.setAssignType(request.getAssignType());
                userRole.setIsEnabled(request.getIsEnabled());
                userRole.setAssignedAt(now);
                userRole.setAssignedBy(currentUserId);
                userRole.setExpiresAt(request.getExpiresAt());
                userRole.setCreatedAt(now);
                userRole.setUpdatedAt(now);

                userRoleMapper.insert(userRole);
                userRoles.add(userRole);
            }
        }

        log.info("Roles assigned successfully: {} assignments created", userRoles.size());
        return userRoles;
    }

    @Override
    @CacheEvict(value = {"userRoles", "userPermissions"}, allEntries = true)
    public void removeRolesFromUser(String userId, List<String> roleIds) {
        log.info("Removing roles from user: {} -> {}", userId, roleIds);

        userRoleMapper.batchDeleteByUserIdAndRoleIds(userId, roleIds);

        log.info("Roles removed successfully from user: {}", userId);
    }

    @Override
    @CacheEvict(value = {"userRoles", "userPermissions"}, allEntries = true)
    public void clearUserRoles(String userId) {
        log.info("Clearing all roles for user: {}", userId);

        userRoleMapper.deleteByUserId(userId);

        log.info("All roles cleared for user: {}", userId);
    }

    @Override
    @Cacheable(value = "userRoles", key = "'user:' + #userId")
    public List<Role> getUserRoles(String userId) {
        List<UserRole> userRoles = userRoleMapper.selectValidByUserId(userId);
        return userRoles.stream()
                .map(ur -> roleMapper.selectById(ur.getRoleId()))
                .filter(role -> role != null)
                .collect(Collectors.toList());
    }

    @Override
    public List<UserRole> getUserRoleAssignments(String userId) {
        return userRoleMapper.selectByUserId(userId);
    }

    @Override
    public List<UserRole> getRoleUserAssignments(String roleId) {
        return userRoleMapper.selectByRoleId(roleId);
    }

    @Override
    public boolean hasRole(String userId, String roleId) {
        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            developmentPermissionService.logRoleOverride(roleId, userId);
            return true;
        }
        return userRoleMapper.countByUserIdAndRoleId(userId, roleId) > 0;
    }

    @Override
    public boolean hasRoleByCode(String userId, String roleCode) {
        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            developmentPermissionService.logRoleOverride(roleCode, userId);
            return true;
        }
        List<Role> userRoles = getUserRoles(userId);
        return userRoles.stream().anyMatch(role -> roleCode.equals(role.getCode()));
    }

    @Override
    public boolean existsUserRole(String userId, String roleId) {
        return userRoleMapper.countByUserIdAndRoleId(userId, roleId) > 0;
    }

    @Override
    @CacheEvict(value = {"userRoles", "userPermissions"}, allEntries = true)
    public void enableUserRole(String userId, String roleId) {
        UserRole userRole = userRoleMapper.selectByUserIdAndRoleId(userId, roleId);
        if (userRole != null) {
            userRole.setIsEnabled(true);
            userRole.setUpdatedAt(LocalDateTime.now());
            userRoleMapper.updateById(userRole);
        }
    }

    @Override
    @CacheEvict(value = {"userRoles", "userPermissions"}, allEntries = true)
    public void disableUserRole(String userId, String roleId) {
        UserRole userRole = userRoleMapper.selectByUserIdAndRoleId(userId, roleId);
        if (userRole != null) {
            userRole.setIsEnabled(false);
            userRole.setUpdatedAt(LocalDateTime.now());
            userRoleMapper.updateById(userRole);
        }
    }

    @Override
    @CacheEvict(value = {"userRoles", "userPermissions"}, allEntries = true)
    public void copyUserRoles(String sourceUserId, String targetUserId) {
        log.info("Copying user roles: {} -> {}", sourceUserId, targetUserId);

        List<UserRole> sourceRoles = userRoleMapper.selectValidByUserId(sourceUserId);
        String currentUserId = getCurrentUserId();
        LocalDateTime now = LocalDateTime.now();

        List<UserRole> targetRoles = new ArrayList<>();
        for (UserRole sourceRole : sourceRoles) {
            if (!existsUserRole(targetUserId, sourceRole.getRoleId())) {
                UserRole targetRole = new UserRole();
                targetRole.setUserId(targetUserId);
                targetRole.setRoleId(sourceRole.getRoleId());
                targetRole.setAssignType(sourceRole.getAssignType());
                targetRole.setIsEnabled(sourceRole.getIsEnabled());
                targetRole.setAssignedAt(now);
                targetRole.setAssignedBy(currentUserId);
                targetRole.setExpiresAt(sourceRole.getExpiresAt());
                targetRole.setCreatedAt(now);
                targetRole.setUpdatedAt(now);

                targetRoles.add(targetRole);
            }
        }

        if (!targetRoles.isEmpty()) {
            userRoleMapper.batchInsert(targetRoles);
        }

        log.info("User roles copied successfully: {} roles", targetRoles.size());
    }

    @Override
    @CacheEvict(value = {"userRoles", "userPermissions"}, allEntries = true)
    public void syncUserRoles(String userId, List<String> roleIds) {
        log.info("Syncing user roles: {} -> {}", userId, roleIds);

        // 删除现有角色
        clearUserRoles(userId);

        // 添加新角色
        if (roleIds != null && !roleIds.isEmpty()) {
            AssignUserRoleRequest request = new AssignUserRoleRequest();
            request.setUserId(userId);
            request.setRoleIds(roleIds);
            request.setAssignType("direct");
            request.setIsEnabled(true);

            assignRolesToUser(request);
        }

        log.info("User roles synced successfully");
    }

    @Override
    public void handleExpiredRoles() {
        log.info("Handling expired user roles...");

        List<UserRole> expiredRoles = userRoleMapper.selectExpiredUserRoles();
        if (!expiredRoles.isEmpty()) {
            userRoleMapper.updateExpiredUserRoles();
            log.info("Updated {} expired user roles", expiredRoles.size());
        }
    }

    @Override
    public Object getUserRoleStatistics(String userId) {
        // TODO: 实现用户角色统计逻辑
        return new HashMap<>();
    }

    @Override
    public boolean isSuperAdmin(String userId) {
        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            return true;
        }
        return hasRoleByCode(userId, Role.SystemRole.ADMIN);
    }

    @Override
    public boolean isAdmin(String userId) {
        // 开发环境权限覆盖
        if (developmentPermissionService.shouldTreatAsSuperAdmin(userId)) {
            return true;
        }
        return hasRoleByCode(userId, Role.SystemRole.ADMIN) ||
               hasRoleByCode(userId, Role.SystemRole.PM);
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof UserDetails) {
            String username = ((UserDetails) principal).getUsername();
            User user = userMapper.findByUsernameWithRole(username);
            return user != null ? user.getId() : null;
        }
        return null;
    }


}
