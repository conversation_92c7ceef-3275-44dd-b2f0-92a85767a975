package com.wtms.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

/**
 * 更新权限请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "更新权限请求")
public class UpdatePermissionRequest {

    @Schema(description = "权限编码", example = "user:create")
    private String code;

    @Schema(description = "权限名称", example = "创建用户")
    private String name;

    @Schema(description = "权限描述", example = "允许创建新用户")
    private String description;

    @Schema(description = "权限类型", example = "api", allowableValues = {"menu", "button", "api", "data"})
    private String type;

    @Schema(description = "权限分组", example = "user")
    private String groupName;

    @Schema(description = "父权限ID")
    private String parentId;

    @Schema(description = "权限路径", example = "/system/user")
    private String path;

    @Schema(description = "排序", example = "1")
    private Integer sortOrder;

    @Schema(description = "资源标识", example = "user")
    private String resource;

    @Schema(description = "操作标识", example = "create")
    private String action;

    @Schema(description = "权限表达式", example = "hasRole('ADMIN') or hasPermission('user:create')")
    private String expression;

    @Schema(description = "是否启用", example = "true")
    private Boolean isEnabled;

    @Schema(description = "是否系统权限", example = "false")
    private Boolean isSystem;
}
