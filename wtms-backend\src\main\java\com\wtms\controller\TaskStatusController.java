package com.wtms.controller;

import com.wtms.common.result.Result;
import com.wtms.service.TaskStatusService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.NotBlank;
import java.util.List;
import java.util.Map;

/**
 * 任务状态流转控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/task-status")
@Tag(name = "任务状态管理", description = "任务状态流转相关接口")
public class TaskStatusController {

    @Autowired
    private TaskStatusService taskStatusService;

    @GetMapping("/all")
    @Operation(summary = "获取所有任务状态", description = "获取系统中所有可用的任务状态")
    public Result<Map<String, String>> getAllTaskStatuses() {
        Map<String, String> statuses = taskStatusService.getAllTaskStatuses();
        return Result.success(statuses);
    }

    @GetMapping("/transition-rules")
    @Operation(summary = "获取状态流转规则", description = "获取任务状态流转规则")
    public Result<Map<String, List<String>>> getStatusTransitionRules() {
        Map<String, List<String>> rules = taskStatusService.getStatusTransitionRules();
        return Result.success(rules);
    }

    @GetMapping("/{taskId}/available-next")
    @Operation(summary = "获取可用的下一状态", description = "获取指定任务当前用户可以流转到的状态列表")
    public Result<List<String>> getAvailableNextStatuses(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        String currentUserId = getCurrentUserId();
        List<String> availableStatuses = taskStatusService.getAvailableNextStatuses(taskId, currentUserId);
        return Result.success(availableStatuses);
    }

    @PostMapping("/{taskId}/transition")
    @Operation(summary = "执行状态流转", description = "执行任务状态流转")
    public Result<String> executeStatusTransition(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "目标状态", required = true)
            @RequestParam @NotBlank(message = "目标状态不能为空") String targetStatus,
            @Parameter(description = "流转备注")
            @RequestParam(required = false) String comment) {
        
        String currentUserId = getCurrentUserId();
        
        log.info("Executing status transition: task={}, targetStatus={}, user={}", 
                taskId, targetStatus, currentUserId);
        
        taskStatusService.executeStatusTransition(taskId, targetStatus, currentUserId, comment);
        return Result.success("状态流转成功");
    }

    @GetMapping("/{taskId}/history")
    @Operation(summary = "获取状态流转历史", description = "获取指定任务的状态流转历史记录")
    public Result<List<TaskStatusService.TaskStatusHistory>> getStatusHistory(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId) {
        
        List<TaskStatusService.TaskStatusHistory> history = taskStatusService.getStatusHistory(taskId);
        return Result.success(history);
    }

    @GetMapping("/validate-transition")
    @Operation(summary = "验证状态流转", description = "验证从当前状态到目标状态的流转是否有效")
    public Result<Boolean> validateStatusTransition(
            @Parameter(description = "当前状态", required = true)
            @RequestParam @NotBlank(message = "当前状态不能为空") String currentStatus,
            @Parameter(description = "目标状态", required = true)
            @RequestParam @NotBlank(message = "目标状态不能为空") String targetStatus) {
        
        boolean isValid = taskStatusService.isValidStatusTransition(currentStatus, targetStatus);
        return Result.success(isValid);
    }

    @GetMapping("/{taskId}/permission")
    @Operation(summary = "检查流转权限", description = "检查当前用户是否有权限执行指定的状态流转")
    public Result<Boolean> checkTransitionPermission(
            @Parameter(description = "任务ID", required = true)
            @PathVariable @NotBlank(message = "任务ID不能为空") String taskId,
            @Parameter(description = "目标状态", required = true)
            @RequestParam @NotBlank(message = "目标状态不能为空") String targetStatus) {
        
        String currentUserId = getCurrentUserId();
        boolean hasPermission = taskStatusService.hasStatusTransitionPermission(taskId, currentUserId, targetStatus);
        return Result.success(hasPermission);
    }

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof UserDetails) {
            // 这里需要根据实际的UserDetails实现来获取用户ID
            // 假设UserDetails中包含用户ID信息
            return "current-user-id"; // 临时返回，实际需要从UserDetails中获取
        }
        return null;
    }
}
