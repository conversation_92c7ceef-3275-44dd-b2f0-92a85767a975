<template>
  <div class="dashboard">
    <div class="dashboard-header">
      <h1>工作台</h1>
      <p>欢迎回来，{{ userStore.userInfo?.fullName }}！</p>
    </div>

    <!-- 统计卡片 -->
    <el-row :gutter="16" class="stats-cards">
      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon primary">
              <el-icon><List /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.totalTasks }}</div>
              <div class="stat-label">总任务数</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon warning">
              <el-icon><Clock /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.pendingTasks }}</div>
              <div class="stat-label">待处理</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon info">
              <el-icon><Loading /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.inProgressTasks }}</div>
              <div class="stat-label">进行中</div>
            </div>
          </div>
        </el-card>
      </el-col>

      <el-col :xs="12" :sm="6" :md="6" :lg="6" :xl="6">
        <el-card class="stat-card">
          <div class="stat-content">
            <div class="stat-icon success">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-info">
              <div class="stat-value">{{ stats.completedTasks }}</div>
              <div class="stat-label">已完成</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 主要内容区域 -->
    <el-row :gutter="16" class="main-content">
      <!-- 我的任务 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>我的任务</span>
              <el-button type="text" @click="goToTasks">查看全部</el-button>
            </div>
          </template>
          
          <div class="task-list">
            <div
              v-for="task in myTasks"
              :key="task.id"
              class="task-item"
              @click="goToTaskDetail(task.id)"
            >
              <div class="task-info">
                <div class="task-title">{{ task.title }}</div>
                <div class="task-meta">
                  <el-tag :type="getTaskStatusType(task.status)" size="small">
                    {{ getTaskStatusText(task.status) }}
                  </el-tag>
                  <span class="task-date">{{ formatDate(task.dueDate) }}</span>
                </div>
              </div>
              <div class="task-priority">
                <el-tag
                  :type="getPriorityType(task.priority)"
                  size="small"
                  effect="plain"
                >
                  {{ getPriorityText(task.priority) }}
                </el-tag>
              </div>
            </div>
            
            <el-empty v-if="myTasks.length === 0" description="暂无任务" />
          </div>
        </el-card>
      </el-col>

      <!-- 待办事项 -->
      <el-col :xs="24" :sm="24" :md="12" :lg="8" :xl="8">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>待办事项</span>
              <el-button type="text" @click="addTodo">添加</el-button>
            </div>
          </template>
          
          <div class="todo-list">
            <div
              v-for="todo in todoList"
              :key="todo.id"
              class="todo-item"
            >
              <el-checkbox
                v-model="todo.completed"
                @change="toggleTodo(todo)"
              />
              <span
                :class="{ 'completed': todo.completed }"
                class="todo-text"
              >
                {{ todo.text }}
              </span>
            </div>
            
            <el-empty v-if="todoList.length === 0" description="暂无待办事项" />
          </div>
        </el-card>
      </el-col>

      <!-- 最近活动 -->
      <el-col :xs="24" :sm="24" :md="24" :lg="8" :xl="8">
        <el-card class="content-card">
          <template #header>
            <div class="card-header">
              <span>最近活动</span>
              <el-button type="text">查看更多</el-button>
            </div>
          </template>
          
          <el-timeline class="activity-timeline">
            <el-timeline-item
              v-for="activity in recentActivities"
              :key="activity.id"
              :timestamp="formatDateTime(activity.createdAt)"
              placement="top"
            >
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-description">{{ activity.description }}</div>
              </div>
            </el-timeline-item>
          </el-timeline>
          
          <el-empty v-if="recentActivities.length === 0" description="暂无活动记录" />
        </el-card>
      </el-col>
    </el-row>

    <!-- 快速操作 -->
    <el-row :gutter="16" class="quick-actions">
      <el-col :span="24">
        <el-card>
          <template #header>
            <span>快速操作</span>
          </template>
          
          <div class="action-buttons">
            <el-button type="primary" @click="createTask">
              <el-icon><Plus /></el-icon>
              新建任务
            </el-button>
            <el-button @click="goToWorkflow">
              <el-icon><Share /></el-icon>
              工作流
            </el-button>
            <el-button @click="goToEvaluation">
              <el-icon><Star /></el-icon>
              质量评价
            </el-button>
            <el-button @click="goToAnalytics">
              <el-icon><TrendCharts /></el-icon>
              数据分析
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import {
  List,
  Clock,
  Loading,
  Check,
  User,
  Calendar,
  TrendCharts,
  Document,
  Bell,
  Setting,
  Plus,
  Edit,
  Delete,
  View,
  ArrowRight
} from '@element-plus/icons-vue'
import { useUserStore } from '@/stores/user'
import dayjs from 'dayjs'

const router = useRouter()
const userStore = useUserStore()

// 统计数据
const stats = reactive({
  totalTasks: 0,
  pendingTasks: 0,
  inProgressTasks: 0,
  completedTasks: 0
})

// 我的任务
const myTasks = ref([
  {
    id: '1',
    title: '完成用户认证模块开发',
    status: 'in_progress',
    priority: 4,
    dueDate: '2024-01-15'
  },
  {
    id: '2',
    title: '设计数据库表结构',
    status: 'pending',
    priority: 3,
    dueDate: '2024-01-20'
  }
])

// 待办事项
const todoList = ref([
  { id: 1, text: '完成项目文档编写', completed: false },
  { id: 2, text: '参加团队会议', completed: true },
  { id: 3, text: '代码审查', completed: false }
])

// 最近活动
const recentActivities = ref([
  {
    id: 1,
    title: '任务状态更新',
    description: '将"用户认证模块"状态更新为进行中',
    createdAt: '2024-01-10 14:30:00'
  },
  {
    id: 2,
    title: '新建任务',
    description: '创建了新任务"数据库设计"',
    createdAt: '2024-01-10 10:15:00'
  }
])

// 方法
const loadDashboardData = async () => {
  try {
    // 这里应该调用API获取真实数据
    stats.totalTasks = 25
    stats.pendingTasks = 8
    stats.inProgressTasks = 12
    stats.completedTasks = 5
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
  }
}

const getTaskStatusType = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: 'warning',
    in_progress: 'primary',
    completed: 'success',
    paused: 'info',
    cancelled: 'danger'
  }
  return statusMap[status] || 'info'
}

const getTaskStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    pending: '待开始',
    in_progress: '进行中',
    completed: '已完成',
    paused: '已暂停',
    cancelled: '已取消'
  }
  return statusMap[status] || status
}

const getPriorityType = (priority: number) => {
  if (priority >= 4) return 'danger'
  if (priority >= 3) return 'warning'
  return 'info'
}

const getPriorityText = (priority: number) => {
  const priorityMap: Record<number, string> = {
    1: '最低',
    2: '低',
    3: '中',
    4: '高',
    5: '最高'
  }
  return priorityMap[priority] || '中'
}

const formatDate = (date: string) => {
  return dayjs(date).format('MM-DD')
}

const formatDateTime = (datetime: string) => {
  return dayjs(datetime).format('MM-DD HH:mm')
}

const goToTasks = () => {
  router.push('/tasks')
}

const goToTaskDetail = (taskId: string) => {
  router.push(`/tasks/${taskId}`)
}

const goToWorkflow = () => {
  router.push('/workflow')
}

const goToEvaluation = () => {
  router.push('/evaluation')
}

const goToAnalytics = () => {
  router.push('/analytics')
}

const createTask = () => {
  ElMessage.info('新建任务功能开发中...')
}

const addTodo = () => {
  ElMessage.info('添加待办事项功能开发中...')
}

const toggleTodo = (todo: any) => {
  ElMessage.success(`待办事项已${todo.completed ? '完成' : '取消完成'}`)
}

// 生命周期
onMounted(() => {
  loadDashboardData()
})
</script>

<style lang="scss" scoped>
.dashboard {
  .dashboard-header {
    margin-bottom: 24px;
    
    h1 {
      margin: 0 0 8px;
      font-size: 24px;
      font-weight: 600;
      color: #333;
    }
    
    p {
      margin: 0;
      color: #666;
      font-size: 14px;
    }
  }
  
  .stats-cards {
    margin-bottom: 24px;
    
    .stat-card {
      .stat-content {
        display: flex;
        align-items: center;
        gap: 16px;
        
        .stat-icon {
          width: 48px;
          height: 48px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          
          &.primary {
            background: #e6f7ff;
            color: #1890ff;
          }
          
          &.success {
            background: #f6ffed;
            color: #52c41a;
          }
          
          &.warning {
            background: #fff7e6;
            color: #faad14;
          }
          
          &.info {
            background: #f0f0f0;
            color: #666;
          }
        }
        
        .stat-info {
          .stat-value {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            line-height: 1;
          }
          
          .stat-label {
            font-size: 12px;
            color: #666;
            margin-top: 4px;
          }
        }
      }
    }
  }
  
  .main-content {
    margin-bottom: 24px;
    
    .content-card {
      height: 400px;
      
      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      
      :deep(.el-card__body) {
        height: calc(100% - 60px);
        overflow-y: auto;
      }
    }
  }
  
  .task-list {
    .task-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 0;
      border-bottom: 1px solid #f0f0f0;
      cursor: pointer;
      
      &:hover {
        background: #fafafa;
        margin: 0 -16px;
        padding: 12px 16px;
      }
      
      &:last-child {
        border-bottom: none;
      }
      
      .task-info {
        flex: 1;
        
        .task-title {
          font-size: 14px;
          color: #333;
          margin-bottom: 4px;
        }
        
        .task-meta {
          display: flex;
          align-items: center;
          gap: 8px;
          
          .task-date {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
  
  .todo-list {
    .todo-item {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 8px 0;
      
      .todo-text {
        font-size: 14px;
        color: #333;
        
        &.completed {
          text-decoration: line-through;
          color: #999;
        }
      }
    }
  }
  
  .activity-timeline {
    .activity-content {
      .activity-title {
        font-size: 14px;
        color: #333;
        margin-bottom: 4px;
      }
      
      .activity-description {
        font-size: 12px;
        color: #666;
      }
    }
  }
  
  .quick-actions {
    .action-buttons {
      display: flex;
      gap: 16px;
      flex-wrap: wrap;
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dashboard {
    .stats-cards {
      .stat-card {
        margin-bottom: 16px;
      }
    }
    
    .main-content {
      .content-card {
        height: auto;
        margin-bottom: 16px;
      }
    }
    
    .action-buttons {
      justify-content: center;
    }
  }
}
</style>
