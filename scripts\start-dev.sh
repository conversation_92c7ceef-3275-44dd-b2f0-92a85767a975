#!/bin/bash

# WTMS开发环境启动脚本

set -e

echo "🚀 启动WTMS开发环境..."

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker"
    exit 1
fi

if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose未安装，请先安装Docker Compose"
    exit 1
fi

# 检查端口是否被占用
check_port() {
    local port=$1
    local service=$2
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo "⚠️  端口 $port 已被占用，请检查 $service 服务"
        return 1
    fi
    return 0
}

echo "📋 检查端口占用情况..."
check_port 3308 "MySQL" || exit 1
check_port 6379 "Redis" || exit 1
check_port 8081 "phpMyAdmin" || exit 1
check_port 8082 "Redis Commander" || exit 1

# 启动基础服务
echo "🐳 启动基础服务 (MySQL + Redis)..."
docker-compose -f docker-compose.dev.yml up -d mysql-dev redis-dev

# 等待MySQL启动
echo "⏳ 等待MySQL启动..."
sleep 10

# 检查MySQL是否就绪
until docker-compose -f docker-compose.dev.yml exec mysql-dev mysqladmin ping -h"localhost" --silent; do
    echo "⏳ 等待MySQL就绪..."
    sleep 2
done

echo "✅ MySQL已就绪"

# 检查Redis是否就绪
echo "⏳ 等待Redis启动..."
until docker-compose -f docker-compose.dev.yml exec redis-dev redis-cli ping | grep -q PONG; do
    echo "⏳ 等待Redis就绪..."
    sleep 2
done

echo "✅ Redis已就绪"

# 启动管理工具（可选）
echo "🔧 启动管理工具..."
docker-compose -f docker-compose.dev.yml up -d phpmyadmin redis-commander

echo ""
echo "🎉 开发环境基础服务启动完成！"
echo ""
echo "📊 服务访问地址："
echo "   MySQL:          localhost:3308"
echo "   Redis:          localhost:6379"
echo "   phpMyAdmin:     http://localhost:8081"
echo "   Redis Commander: http://localhost:8082"
echo ""
echo "🔑 MySQL连接信息："
echo "   Host: localhost"
echo "   Port: 3308"
echo "   Database: wtms_db"
echo "   Username: root"
echo "   Password: ankaixin.docker.mysql"
echo ""
echo "📝 接下来请手动启动前后端服务："
echo "   后端: cd wtms-backend && mvn spring-boot:run"
echo "   前端: cd wtms-frontend && npm run dev"
echo ""
echo "🛑 停止服务: ./scripts/stop-dev.sh"
