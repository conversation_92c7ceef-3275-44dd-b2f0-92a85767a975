<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 
         https://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>2.7.18</version>
        <relativePath/>
    </parent>
    
    <groupId>com.wtms</groupId>
    <artifactId>wtms-backend</artifactId>
    <version>1.0.0</version>
    <name>wtms-backend</name>
    <description>WTMS工作任务管理平台后端服务</description>
    
    <properties>
        <java.version>8</java.version>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        
        <!-- 依赖版本 -->
        <mybatis-plus.version>3.5.4</mybatis-plus.version>
        <mysql.version>8.0.33</mysql.version>
        <druid.version>1.2.20</druid.version>
        <jwt.version>0.11.5</jwt.version>
        <springdoc.version>1.7.0</springdoc.version>
        <hutool.version>5.8.22</hutool.version>
        <fastjson2.version>2.0.43</fastjson2.version>
    </properties>
    
    <dependencies>
        <!-- Spring Boot Starters -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-security</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-data-redis</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-actuator</artifactId>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-aop</artifactId>
        </dependency>
        
        <!-- MyBatis Plus -->
        <dependency>
            <groupId>com.baomidou</groupId>
            <artifactId>mybatis-plus-boot-starter</artifactId>
            <version>${mybatis-plus.version}</version>
        </dependency>
        
        <!-- MySQL Driver -->
        <dependency>
            <groupId>mysql</groupId>
            <artifactId>mysql-connector-java</artifactId>
            <version>${mysql.version}</version>
        </dependency>
        
        <!-- Druid Connection Pool -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid-spring-boot-starter</artifactId>
            <version>${druid.version}</version>
        </dependency>
        
        <!-- JWT -->
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>${jwt.version}</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>${jwt.version}</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>${jwt.version}</version>
            <scope>runtime</scope>
        </dependency>
        
        <!-- SpringDoc OpenAPI (Swagger) -->
        <dependency>
            <groupId>org.springdoc</groupId>
            <artifactId>springdoc-openapi-ui</artifactId>
            <version>${springdoc.version}</version>
        </dependency>
        
        <!-- Hutool工具类 -->
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>${hutool.version}</version>
        </dependency>
        
        <!-- FastJSON2 -->
        <dependency>
            <groupId>com.alibaba.fastjson2</groupId>
            <artifactId>fastjson2</artifactId>
            <version>${fastjson2.version}</version>
        </dependency>
        
        <!-- Lombok -->
        <dependency>
            <groupId>org.projectlombok</groupId>
            <artifactId>lombok</artifactId>
            <optional>true</optional>
        </dependency>
        
        <!-- Spring Boot Test -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>
        
        <dependency>
            <groupId>org.springframework.security</groupId>
            <artifactId>spring-security-test</artifactId>
            <scope>test</scope>
        </dependency>
    </dependencies>
    
    <build>
        <plugins>
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <configuration>
                    <mainClass>com.wtms.WtmsQuickStartApp</mainClass>
                    <excludes>
                        <exclude>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                        </exclude>
                    </excludes>
                </configuration>
            </plugin>
            
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
                <configuration>
                    <source>8</source>
                    <target>8</target>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>

            <!-- Checkstyle 代码风格检查 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-checkstyle-plugin</artifactId>
                <version>3.3.0</version>
                <configuration>
                    <configLocation>checkstyle.xml</configLocation>
                    <suppressionsLocation>suppressions.xml</suppressionsLocation>
                    <encoding>UTF-8</encoding>
                    <consoleOutput>true</consoleOutput>
                    <failsOnError>false</failsOnError>
                    <linkXRef>false</linkXRef>
                </configuration>
                <executions>
                    <execution>
                        <id>validate</id>
                        <phase>validate</phase>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
                <dependencies>
                    <dependency>
                        <groupId>com.puppycrawl.tools</groupId>
                        <artifactId>checkstyle</artifactId>
                        <version>10.12.4</version>
                    </dependency>
                </dependencies>
            </plugin>

            <!-- SpotBugs 静态代码分析 -->
            <plugin>
                <groupId>com.github.spotbugs</groupId>
                <artifactId>spotbugs-maven-plugin</artifactId>
                <version>4.7.3.6</version>
                <configuration>
                    <effort>Max</effort>
                    <threshold>Low</threshold>
                    <xmlOutput>true</xmlOutput>
                    <failOnError>false</failOnError>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- PMD 代码质量检查 -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.21.0</version>
                <configuration>
                    <sourceEncoding>UTF-8</sourceEncoding>
                    <minimumTokens>100</minimumTokens>
                    <targetJdk>8</targetJdk>
                    <failOnViolation>false</failOnViolation>
                    <printFailingErrors>true</printFailingErrors>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                            <goal>cpd-check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Google Java Format 代码格式化 -->
            <plugin>
                <groupId>com.coveo</groupId>
                <artifactId>fmt-maven-plugin</artifactId>
                <version>2.13</version>
                <configuration>
                    <sourceDirectory>src/main/java</sourceDirectory>
                    <testSourceDirectory>src/test/java</testSourceDirectory>
                    <verbose>true</verbose>
                    <filesNamePattern>.*\.java</filesNamePattern>
                    <skip>false</skip>
                    <skipSortingImports>false</skipSortingImports>
                    <style>google</style>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>format</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- OWASP 依赖安全扫描 -->
            <plugin>
                <groupId>org.owasp</groupId>
                <artifactId>dependency-check-maven</artifactId>
                <version>8.4.2</version>
                <configuration>
                    <!-- 报告格式 -->
                    <formats>
                        <format>HTML</format>
                        <format>JSON</format>
                        <format>XML</format>
                    </formats>
                    <!-- 输出目录 -->
                    <outputDirectory>${project.build.directory}/dependency-check</outputDirectory>
                    <!-- 扫描范围 -->
                    <scanSet>
                        <fileSet>
                            <directory>${project.build.directory}</directory>
                            <includes>
                                <include>**/*.jar</include>
                            </includes>
                        </fileSet>
                    </scanSet>
                    <!-- 数据库配置 -->
                    <dataDirectory>${project.build.directory}/dependency-check-data</dataDirectory>
                    <databaseDriverName>org.h2.Driver</databaseDriverName>
                    <connectionString>jdbc:h2:file:${project.build.directory}/dependency-check-data/odc</connectionString>
                    <!-- 漏洞数据源 -->
                    <nvdApiKey>${env.NVD_API_KEY}</nvdApiKey>
                    <nvdMaxRetryCount>10</nvdMaxRetryCount>
                    <nvdApiDelay>4000</nvdApiDelay>
                    <!-- 失败阈值 -->
                    <failBuildOnCVSS>7.0</failBuildOnCVSS>
                    <failOnError>false</failOnError>
                    <!-- 抑制误报 -->
                    <suppressionFiles>
                        <suppressionFile>owasp-suppressions.xml</suppressionFile>
                    </suppressionFiles>
                    <!-- 分析器配置 -->
                    <archiveAnalyzerEnabled>true</archiveAnalyzerEnabled>
                    <jarAnalyzerEnabled>true</jarAnalyzerEnabled>
                    <centralAnalyzerEnabled>true</centralAnalyzerEnabled>
                    <nexusAnalyzerEnabled>false</nexusAnalyzerEnabled>
                    <pyDistributionAnalyzerEnabled>false</pyDistributionAnalyzerEnabled>
                    <pyPackageAnalyzerEnabled>false</pyPackageAnalyzerEnabled>
                    <rubygemsAnalyzerEnabled>false</rubygemsAnalyzerEnabled>
                    <opensslAnalyzerEnabled>false</opensslAnalyzerEnabled>
                    <cmakeAnalyzerEnabled>false</cmakeAnalyzerEnabled>
                    <autoconfAnalyzerEnabled>false</autoconfAnalyzerEnabled>
                    <composerAnalyzerEnabled>false</composerAnalyzerEnabled>
                    <nodeAnalyzerEnabled>false</nodeAnalyzerEnabled>
                    <nuspecAnalyzerEnabled>false</nuspecAnalyzerEnabled>
                    <assemblyAnalyzerEnabled>false</assemblyAnalyzerEnabled>
                </configuration>
                <executions>
                    <execution>
                        <goals>
                            <goal>check</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>

            <!-- Snyk 漏洞扫描 (可选) -->
            <plugin>
                <groupId>io.snyk</groupId>
                <artifactId>snyk-maven-plugin</artifactId>
                <version>2.2.0</version>
                <configuration>
                    <apiToken>${env.SNYK_TOKEN}</apiToken>
                    <failOnIssues>false</failOnIssues>
                    <org>${env.SNYK_ORG}</org>
                </configuration>
                <executions>
                    <execution>
                        <id>snyk-test</id>
                        <goals>
                            <goal>test</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>snyk-monitor</id>
                        <goals>
                            <goal>monitor</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>
