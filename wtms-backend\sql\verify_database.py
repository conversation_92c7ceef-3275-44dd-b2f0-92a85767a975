#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WTMS数据库验证脚本
验证数据库结构和数据的完整性
"""

import pymysql
import sys
from datetime import datetime

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'port': 3308,
    'user': 'root',
    'password': 'ankaixin.docker.mysql',
    'database': 'wtms',
    'charset': 'utf8mb4'
}

def verify_database_structure():
    """验证数据库结构"""
    print("🔍 验证数据库结构...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 检查必要的表是否存在
        required_tables = [
            'users', 'departments', 'roles', 'permissions',
            'user_roles', 'role_permissions', 'task_categories',
            'tasks', 'task_comments'
        ]
        
        cursor.execute("SHOW TABLES")
        existing_tables = [table[0] for table in cursor.fetchall()]
        
        missing_tables = []
        for table in required_tables:
            if table not in existing_tables:
                missing_tables.append(table)
        
        if missing_tables:
            print(f"❌ 缺少表: {', '.join(missing_tables)}")
            return False
        else:
            print(f"✅ 所有必要的表都存在 ({len(required_tables)}个)")
        
        # 检查外键约束
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.table_constraints 
            WHERE constraint_schema = 'wtms' 
            AND constraint_type = 'FOREIGN KEY'
        """)
        fk_count = cursor.fetchone()[0]
        print(f"✅ 外键约束数量: {fk_count}")
        
        # 检查索引
        cursor.execute("""
            SELECT COUNT(*) 
            FROM information_schema.statistics 
            WHERE table_schema = 'wtms'
        """)
        index_count = cursor.fetchone()[0]
        print(f"✅ 索引数量: {index_count}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据库结构验证失败: {str(e)}")
        return False

def verify_data_integrity():
    """验证数据完整性"""
    print("\n🔍 验证数据完整性...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 验证用户数据
        cursor.execute("SELECT COUNT(*) FROM users WHERE status = 'active'")
        active_users = cursor.fetchone()[0]
        print(f"✅ 活跃用户数量: {active_users}")
        
        # 验证角色数据
        cursor.execute("SELECT COUNT(*) FROM roles WHERE is_system = 1")
        system_roles = cursor.fetchone()[0]
        print(f"✅ 系统角色数量: {system_roles}")
        
        # 验证权限数据
        cursor.execute("SELECT COUNT(*) FROM permissions WHERE is_enabled = 1")
        enabled_permissions = cursor.fetchone()[0]
        print(f"✅ 启用权限数量: {enabled_permissions}")
        
        # 验证用户角色关联
        cursor.execute("SELECT COUNT(*) FROM user_roles WHERE is_active = 1")
        active_user_roles = cursor.fetchone()[0]
        print(f"✅ 活跃用户角色关联: {active_user_roles}")
        
        # 验证角色权限关联
        cursor.execute("SELECT COUNT(*) FROM role_permissions")
        role_permissions = cursor.fetchone()[0]
        print(f"✅ 角色权限关联: {role_permissions}")
        
        # 验证任务数据
        cursor.execute("SELECT COUNT(*) FROM tasks")
        task_count = cursor.fetchone()[0]
        print(f"✅ 任务数量: {task_count}")
        
        # 验证任务分类
        cursor.execute("SELECT COUNT(*) FROM task_categories WHERE is_enabled = 1")
        enabled_categories = cursor.fetchone()[0]
        print(f"✅ 启用任务分类: {enabled_categories}")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 数据完整性验证失败: {str(e)}")
        return False

def verify_user_permissions():
    """验证用户权限配置"""
    print("\n🔍 验证用户权限配置...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 验证超级管理员权限
        cursor.execute("""
            SELECT COUNT(DISTINCT p.id) as permission_count
            FROM users u
            JOIN user_roles ur ON u.id = ur.user_id AND ur.is_active = 1
            JOIN roles r ON ur.role_id = r.id
            JOIN role_permissions rp ON r.id = rp.role_id
            JOIN permissions p ON rp.permission_id = p.id
            WHERE u.username = 'admin' AND p.is_enabled = 1
        """)
        admin_permissions = cursor.fetchone()[0]
        print(f"✅ 超级管理员权限数量: {admin_permissions}")
        
        # 验证各角色的权限分配
        cursor.execute("""
            SELECT r.name, COUNT(DISTINCT p.id) as permission_count
            FROM roles r
            LEFT JOIN role_permissions rp ON r.id = rp.role_id
            LEFT JOIN permissions p ON rp.permission_id = p.id AND p.is_enabled = 1
            WHERE r.is_system = 1
            GROUP BY r.id, r.name
            ORDER BY permission_count DESC
        """)
        
        role_permissions = cursor.fetchall()
        print("📊 各角色权限统计:")
        for role_name, perm_count in role_permissions:
            print(f"   {role_name}: {perm_count} 个权限")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ 用户权限验证失败: {str(e)}")
        return False

def verify_mybaits_compatibility():
    """验证MyBatis Plus兼容性"""
    print("\n🔍 验证MyBatis Plus兼容性...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 检查UUID字段格式
        cursor.execute("SELECT id FROM users LIMIT 1")
        result = cursor.fetchone()
        if result:
            user_id = result[0]
            # 检查是否为有效的UUID格式（可能是标准UUID或自定义格式）
            if user_id and len(user_id) > 0:
                print(f"✅ UUID格式正确 (示例: {user_id})")
            else:
                print("❌ UUID字段为空")
                return False
        else:
            print("❌ 没有用户数据")
            return False
        
        # 检查时间戳字段
        cursor.execute("SELECT created_at, updated_at FROM users WHERE created_at IS NOT NULL LIMIT 1")
        result = cursor.fetchone()
        if result and result[0] and result[1]:
            print("✅ 时间戳字段正常")
        else:
            print("❌ 时间戳字段异常")
            return False
        
        # 检查软删除字段
        cursor.execute("DESCRIBE users")
        columns = [col[0] for col in cursor.fetchall()]
        if 'deleted_at' in columns:
            print("✅ 软删除字段存在")
        else:
            print("❌ 软删除字段缺失")
            return False
        
        # 检查JSON字段
        cursor.execute("SELECT tags FROM tasks WHERE tags IS NOT NULL LIMIT 1")
        result = cursor.fetchone()
        if result is None:
            print("⚠️  JSON字段暂无数据，无法验证")
        else:
            print("✅ JSON字段支持正常")
        
        connection.close()
        return True
        
    except Exception as e:
        print(f"❌ MyBatis Plus兼容性验证失败: {str(e)}")
        return False

def generate_test_report():
    """生成测试报告"""
    print("\n📋 生成数据库验证报告...")
    
    try:
        connection = pymysql.connect(**DB_CONFIG)
        cursor = connection.cursor()
        
        # 数据库基本信息
        cursor.execute("SELECT VERSION()")
        mysql_version = cursor.fetchone()[0]
        
        cursor.execute("SELECT @@character_set_database, @@collation_database")
        charset_info = cursor.fetchone()
        
        # 表统计信息
        cursor.execute("""
            SELECT 
                table_name,
                table_rows,
                ROUND(((data_length + index_length) / 1024 / 1024), 2) as size_mb
            FROM information_schema.tables 
            WHERE table_schema = 'wtms' 
            ORDER BY table_name
        """)
        table_stats = cursor.fetchall()
        
        print("\n" + "="*60)
        print("📊 WTMS数据库验证报告")
        print("="*60)
        print(f"📅 生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🗄️  MySQL版本: {mysql_version}")
        print(f"🔤 字符集: {charset_info[0]}")
        print(f"📝 排序规则: {charset_info[1]}")
        
        print("\n📈 数据表统计:")
        total_rows = 0
        total_size = 0
        for table_name, rows, size in table_stats:
            rows = rows or 0
            size = size or 0
            total_rows += rows
            total_size += size
            print(f"   {table_name:<20} {rows:>8} 行  {size:>8.2f} MB")
        
        print(f"\n📊 总计: {len(table_stats)} 个表, {total_rows} 行数据, {total_size:.2f} MB")
        
        connection.close()
        
    except Exception as e:
        print(f"❌ 生成报告失败: {str(e)}")

def main():
    """主函数"""
    print("="*60)
    print("🔍 WTMS 数据库验证脚本")
    print("="*60)
    
    success = True
    
    # 1. 验证数据库结构
    if not verify_database_structure():
        success = False
    
    # 2. 验证数据完整性
    if not verify_data_integrity():
        success = False
    
    # 3. 验证用户权限配置
    if not verify_user_permissions():
        success = False
    
    # 4. 验证MyBatis Plus兼容性
    if not verify_mybaits_compatibility():
        success = False
    
    # 5. 生成测试报告
    generate_test_report()
    
    print("\n" + "="*60)
    if success:
        print("🎉 数据库验证通过！系统可以正常使用。")
        print("📋 默认登录信息:")
        print("   用户名: admin")
        print("   密码: admin123")
        print("   API地址: http://localhost:55557/api/v1")
    else:
        print("❌ 数据库验证失败！请检查上述错误信息。")
        sys.exit(1)
    print("="*60)

if __name__ == "__main__":
    try:
        import pymysql
    except ImportError:
        print("❌ 缺少pymysql模块，请安装: pip install pymysql")
        sys.exit(1)
    
    main()
