<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WTMS权限系统测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .permission-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 12px;
            margin: 4px 0;
            border-radius: 4px;
            background-color: #f8f9fa;
        }
        .permission-granted {
            background-color: #d4edda;
            border: 1px solid #c3e6cb;
            color: #155724;
        }
        .permission-denied {
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
        }
        .user-info {
            background-color: #e3f2fd;
            border: 1px solid #90caf9;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }
        .role-badge {
            display: inline-block;
            background-color: #2196f3;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin: 2px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #2563eb;
        }
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        .status-granted { background-color: #10b981; }
        .status-denied { background-color: #ef4444; }
    </style>
</head>
<body>
    <h1>🔐 WTMS权限系统测试</h1>
    
    <div class="test-container">
        <h2>👤 用户信息</h2>
        <div id="user-info" class="user-info">
            <p>请先登录以查看用户信息和权限</p>
        </div>
        <button onclick="loginAndGetUserInfo()">登录并获取用户信息</button>
        <button onclick="logout()">退出登录</button>
    </div>

    <div class="test-container">
        <h2>🔑 权限检查</h2>
        <div id="permissions-list">
            <p>请先登录以查看权限列表</p>
        </div>
    </div>

    <div class="test-container">
        <h2>🎛️ 功能权限测试</h2>
        <div id="feature-permissions">
            <p>请先登录以测试功能权限</p>
        </div>
    </div>

    <script>
        const API_BASE = 'http://localhost:55557/api/v1';
        let authToken = null;
        let currentUser = null;

        // 系统权限列表
        const systemPermissions = [
            'user:create', 'user:read', 'user:update', 'user:delete',
            'task:create', 'task:read', 'task:update', 'task:delete',
            'role:create', 'role:read', 'role:update', 'role:delete',
            'permission:create', 'permission:read', 'permission:update', 'permission:delete',
            'department:create', 'department:read', 'department:update', 'department:delete'
        ];

        // 功能权限映射
        const featurePermissions = {
            '创建用户': 'user:create',
            '查看用户列表': 'user:read',
            '编辑用户信息': 'user:update',
            '删除用户': 'user:delete',
            '创建任务': 'task:create',
            '查看任务': 'task:read',
            '编辑任务': 'task:update',
            '删除任务': 'task:delete',
            '管理角色': 'role:create',
            '查看角色': 'role:read',
            '管理权限': 'permission:create',
            '查看权限': 'permission:read',
            '管理部门': 'department:create'
        };

        // API调用函数
        async function apiCall(endpoint, options = {}) {
            const url = `${API_BASE}${endpoint}`;
            const config = {
                headers: {
                    'Content-Type': 'application/json',
                    'Origin': 'http://localhost:33335',
                    ...options.headers
                },
                ...options
            };

            if (authToken && !config.headers.Authorization) {
                config.headers.Authorization = `Bearer ${authToken}`;
            }

            try {
                const response = await fetch(url, config);
                const data = await response.json();
                return { success: response.ok, status: response.status, data };
            } catch (error) {
                return { success: false, error: error.message };
            }
        }

        // 登录并获取用户信息
        async function loginAndGetUserInfo() {
            try {
                // 1. 登录
                const loginResult = await apiCall('/auth/login', {
                    method: 'POST',
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123456'
                    })
                });

                if (!loginResult.success || !loginResult.data.success) {
                    alert('登录失败: ' + (loginResult.data?.message || loginResult.error));
                    return;
                }

                authToken = loginResult.data.data.token;
                currentUser = loginResult.data.data.user;

                // 2. 获取用户详细信息
                const userResult = await apiCall('/user/profile');
                
                if (userResult.success && userResult.data.success) {
                    currentUser = userResult.data.data;
                }

                // 3. 更新界面
                updateUserInfo();
                updatePermissionsList();
                updateFeaturePermissions();

            } catch (error) {
                console.error('Login error:', error);
                alert('登录过程中发生错误: ' + error.message);
            }
        }

        // 退出登录
        function logout() {
            authToken = null;
            currentUser = null;
            
            document.getElementById('user-info').innerHTML = '<p>请先登录以查看用户信息和权限</p>';
            document.getElementById('permissions-list').innerHTML = '<p>请先登录以查看权限列表</p>';
            document.getElementById('feature-permissions').innerHTML = '<p>请先登录以测试功能权限</p>';
        }

        // 更新用户信息显示
        function updateUserInfo() {
            if (!currentUser) return;

            const userInfoHtml = `
                <h3>基本信息</h3>
                <p><strong>用户ID:</strong> ${currentUser.id}</p>
                <p><strong>用户名:</strong> ${currentUser.username}</p>
                <p><strong>姓名:</strong> ${currentUser.fullName}</p>
                <p><strong>邮箱:</strong> ${currentUser.email}</p>
                <p><strong>电话:</strong> ${currentUser.phone || '未设置'}</p>
                <p><strong>状态:</strong> ${currentUser.status}</p>
                
                <h3>角色信息</h3>
                <div>
                    ${currentUser.roles ? currentUser.roles.map(role => 
                        `<span class="role-badge">${role}</span>`
                    ).join('') : '无角色'}
                </div>
                
                <h3>权限统计</h3>
                <p><strong>权限数量:</strong> ${currentUser.permissions ? currentUser.permissions.length : 0}</p>
                <p><strong>最后登录:</strong> ${currentUser.lastLoginAt || '未知'}</p>
            `;

            document.getElementById('user-info').innerHTML = userInfoHtml;
        }

        // 更新权限列表显示
        function updatePermissionsList() {
            if (!currentUser || !currentUser.permissions) return;

            const userPermissions = currentUser.permissions;
            let permissionsHtml = '<h3>权限详情</h3>';

            systemPermissions.forEach(permission => {
                const hasPermission = userPermissions.includes(permission);
                const statusClass = hasPermission ? 'permission-granted' : 'permission-denied';
                const statusIcon = hasPermission ? 'status-granted' : 'status-denied';
                const statusText = hasPermission ? '✓ 已授权' : '✗ 未授权';

                permissionsHtml += `
                    <div class="permission-item ${statusClass}">
                        <span>
                            <span class="status-indicator ${statusIcon}"></span>
                            ${permission}
                        </span>
                        <span>${statusText}</span>
                    </div>
                `;
            });

            // 显示用户拥有但不在系统权限列表中的权限
            const extraPermissions = userPermissions.filter(p => !systemPermissions.includes(p));
            if (extraPermissions.length > 0) {
                permissionsHtml += '<h4>额外权限</h4>';
                extraPermissions.forEach(permission => {
                    permissionsHtml += `
                        <div class="permission-item permission-granted">
                            <span>
                                <span class="status-indicator status-granted"></span>
                                ${permission}
                            </span>
                            <span>✓ 已授权</span>
                        </div>
                    `;
                });
            }

            document.getElementById('permissions-list').innerHTML = permissionsHtml;
        }

        // 更新功能权限测试
        function updateFeaturePermissions() {
            if (!currentUser || !currentUser.permissions) return;

            const userPermissions = currentUser.permissions;
            let featuresHtml = '<h3>功能权限测试</h3>';

            Object.entries(featurePermissions).forEach(([featureName, permission]) => {
                const hasPermission = userPermissions.includes(permission);
                const statusClass = hasPermission ? 'permission-granted' : 'permission-denied';
                const statusIcon = hasPermission ? 'status-granted' : 'status-denied';
                const statusText = hasPermission ? '可访问' : '禁止访问';

                featuresHtml += `
                    <div class="permission-item ${statusClass}">
                        <span>
                            <span class="status-indicator ${statusIcon}"></span>
                            ${featureName} (${permission})
                        </span>
                        <span>${statusText}</span>
                    </div>
                `;
            });

            document.getElementById('feature-permissions').innerHTML = featuresHtml;
        }

        // 检查特定权限
        function hasPermission(permission) {
            if (!currentUser || !currentUser.permissions) return false;
            return currentUser.permissions.includes(permission) || currentUser.permissions.includes('*');
        }

        // 页面加载时的初始化
        window.onload = function() {
            console.log('WTMS权限系统测试页面已加载');
        };
    </script>
</body>
</html>
