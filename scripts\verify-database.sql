-- WTMS 数据库验证脚本
-- 用于验证数据库连接和表结构是否正确

-- 检查数据库连接
SELECT 'Database connection successful' AS status;

-- 检查数据库版本
SELECT VERSION() AS mysql_version;

-- 检查当前数据库
SELECT DATABASE() AS current_database;

-- 检查字符集
SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'wtms';

-- 验证所有表是否存在
SELECT 
    TABLE_NAME,
    TABLE_TYPE,
    ENGINE,
    TABLE_ROWS,
    CREATE_TIME
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'wtms'
ORDER BY TABLE_NAME;

-- 验证核心表的字段结构
DESCRIBE users;
DESCRIBE tasks;
DESCRIBE workflow_definitions;
DESCRIBE task_evaluations;

-- 检查外键约束
SELECT 
    CONSTRAINT_NAME,
    TABLE_NAME,
    COLUMN_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM information_schema.KEY_COLUMN_USAGE 
WHERE TABLE_SCHEMA = 'wtms' 
AND REFERENCED_TABLE_NAME IS NOT NULL
ORDER BY TABLE_NAME, CONSTRAINT_NAME;

-- 检查索引
SELECT 
    TABLE_NAME,
    INDEX_NAME,
    COLUMN_NAME,
    NON_UNIQUE,
    INDEX_TYPE
FROM information_schema.STATISTICS 
WHERE TABLE_SCHEMA = 'wtms'
ORDER BY TABLE_NAME, INDEX_NAME, SEQ_IN_INDEX;

-- 验证测试数据
SELECT 'Checking test data...' AS status;

-- 检查用户数据
SELECT COUNT(*) AS user_count FROM users;
SELECT username, full_name, status FROM users LIMIT 5;

-- 检查任务数据
SELECT COUNT(*) AS task_count FROM tasks;
SELECT title, status, priority FROM tasks LIMIT 5;

-- 检查部门数据
SELECT COUNT(*) AS department_count FROM departments;
SELECT name, level FROM departments LIMIT 5;

-- 检查角色权限数据
SELECT COUNT(*) AS role_count FROM roles;
SELECT COUNT(*) AS permission_count FROM permissions;
SELECT COUNT(*) AS role_permission_count FROM role_permissions;

-- 检查工作流数据
SELECT COUNT(*) AS workflow_definition_count FROM workflow_definitions;
SELECT COUNT(*) AS workflow_instance_count FROM workflow_instances;

-- 检查评价数据
SELECT COUNT(*) AS evaluation_count FROM task_evaluations;

-- 验证数据完整性
SELECT 'Checking data integrity...' AS status;

-- 检查用户-角色关联
SELECT 
    u.username,
    r.name AS role_name
FROM users u
JOIN user_roles ur ON u.id = ur.user_id
JOIN roles r ON ur.role_id = r.id
LIMIT 10;

-- 检查任务-用户关联
SELECT 
    t.title,
    creator.full_name AS creator_name,
    assignee.full_name AS assignee_name
FROM tasks t
LEFT JOIN users creator ON t.creator_id = creator.id
LEFT JOIN users assignee ON t.assignee_id = assignee.id
LIMIT 10;

-- 检查任务评价关联
SELECT 
    t.title,
    evaluator.full_name AS evaluator_name,
    evaluatee.full_name AS evaluatee_name,
    te.overall_score
FROM task_evaluations te
JOIN tasks t ON te.task_id = t.id
JOIN users evaluator ON te.evaluator_id = evaluator.id
JOIN users evaluatee ON te.evaluatee_id = evaluatee.id
LIMIT 10;

-- 性能检查
SELECT 'Performance check...' AS status;

-- 检查表大小
SELECT 
    TABLE_NAME,
    ROUND(((DATA_LENGTH + INDEX_LENGTH) / 1024 / 1024), 2) AS 'Size (MB)',
    TABLE_ROWS
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'wtms'
ORDER BY (DATA_LENGTH + INDEX_LENGTH) DESC;

-- 检查慢查询设置
SHOW VARIABLES LIKE 'slow_query_log%';
SHOW VARIABLES LIKE 'long_query_time';

-- 最终验证结果
SELECT 
    CASE 
        WHEN (SELECT COUNT(*) FROM information_schema.TABLES WHERE TABLE_SCHEMA = 'wtms') >= 15
        AND (SELECT COUNT(*) FROM users) > 0
        AND (SELECT COUNT(*) FROM tasks) > 0
        AND (SELECT COUNT(*) FROM roles) > 0
        AND (SELECT COUNT(*) FROM permissions) > 0
        THEN 'PASS: Database initialization successful'
        ELSE 'FAIL: Database initialization incomplete'
    END AS verification_result;

SELECT 'Database verification completed' AS status;
