version: '3.8'

services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: wtms-mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ankaixin.docker.mysql
      MYSQL_DATABASE: wtms
      MYSQL_USER: wtms
      MYSQL_PASSWORD: ankaixin.docker.mysql
      TZ: Asia/Shanghai
    ports:
      - "3308:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./database/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
      - ./database/test-data.sql:/docker-entrypoint-initdb.d/02-test-data.sql:ro
      - ./mysql/conf:/etc/mysql/conf.d:ro
    command: --default-authentication-plugin=mysql_native_password
    networks:
      - wtms-network

  # Redis缓存
  redis:
    image: redis:7.0-alpine
    container_name: wtms-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
      - ./redis/redis.conf:/usr/local/etc/redis/redis.conf:ro
    command: redis-server /usr/local/etc/redis/redis.conf
    networks:
      - wtms-network

  # 后端服务
  backend:
    build:
      context: ./wtms-backend
      dockerfile: Dockerfile
    container_name: wtms-backend
    restart: unless-stopped
    environment:
      SPRING_PROFILES_ACTIVE: docker
      DB_HOST: mysql
      DB_PORT: 3306
      DB_NAME: wtms
      DB_USERNAME: root
      DB_PASSWORD: ankaixin.docker.mysql
      REDIS_HOST: redis
      REDIS_PORT: 6379
      JWT_SECRET: wtms-secret-key-2024-very-long-secret-key-for-security-docker
    ports:
      - "55557:8080"
    volumes:
      - backend_logs:/app/logs
      - backend_uploads:/app/uploads
    depends_on:
      - mysql
      - redis
    networks:
      - wtms-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # 前端服务
  frontend:
    build:
      context: ./wtms-frontend
      dockerfile: Dockerfile
    container_name: wtms-frontend
    restart: unless-stopped
    environment:
      VITE_API_BASE_URL: http://localhost:55557/api/v1
    ports:
      - "33335:80"
    depends_on:
      - backend
    networks:
      - wtms-network

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: wtms-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - nginx_logs:/var/log/nginx
    depends_on:
      - frontend
      - backend
    networks:
      - wtms-network

volumes:
  mysql_data:
    driver: local
  redis_data:
    driver: local
  backend_logs:
    driver: local
  backend_uploads:
    driver: local
  nginx_logs:
    driver: local

networks:
  wtms-network:
    driver: bridge
