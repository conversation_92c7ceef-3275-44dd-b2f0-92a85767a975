import request from '@/utils/request'

/**
 * 任务状态历史
 */
export interface TaskStatusHistory {
  id: string
  taskId: string
  fromStatus: string
  toStatus: string
  operatorId: string
  operatorName: string
  comment?: string
  createdAt: string
}

// API 接口函数

/**
 * 获取所有任务状态
 */
export function getAllTaskStatuses() {
  return request.get<Record<string, string>>('/api/v1/task-status/all')
}

/**
 * 获取状态流转规则
 */
export function getStatusTransitionRules() {
  return request.get<Record<string, string[]>>('/api/v1/task-status/transition-rules')
}

/**
 * 获取可用的下一状态
 */
export function getAvailableNextStatuses(taskId: string) {
  return request.get<string[]>(`/api/v1/task-status/${taskId}/available-next`)
}

/**
 * 执行状态流转
 */
export function executeStatusTransition(taskId: string, targetStatus: string, comment?: string) {
  return request.post(`/api/v1/task-status/${taskId}/transition`, null, {
    params: { targetStatus, comment }
  })
}

/**
 * 获取状态流转历史
 */
export function getStatusHistory(taskId: string) {
  return request.get<TaskStatusHistory[]>(`/api/v1/task-status/${taskId}/history`)
}

/**
 * 验证状态流转
 */
export function validateStatusTransition(currentStatus: string, targetStatus: string) {
  return request.get<boolean>('/api/v1/task-status/validate-transition', {
    params: { currentStatus, targetStatus }
  })
}

/**
 * 检查流转权限
 */
export function checkTransitionPermission(taskId: string, targetStatus: string) {
  return request.get<boolean>(`/api/v1/task-status/${taskId}/permission`, {
    params: { targetStatus }
  })
}
