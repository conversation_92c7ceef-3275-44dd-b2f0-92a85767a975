package com.wtms.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 创建评论请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "创建评论请求")
public class CreateCommentRequest {

    @Schema(description = "任务ID", example = "task-uuid")
    @NotBlank(message = "任务ID不能为空")
    private String taskId;

    @Schema(description = "父评论ID", example = "comment-uuid")
    private String parentId;

    @Schema(description = "评论内容", example = "这是一条评论")
    @NotBlank(message = "评论内容不能为空")
    @Size(max = 2000, message = "评论内容不能超过2000个字符")
    private String content;

    @Schema(description = "评论类型", example = "normal", allowableValues = {"normal", "system", "status_change", "assignment", "progress", "reminder"})
    private String commentType = "normal";

    @Schema(description = "是否置顶", example = "false")
    private Boolean isPinned = false;

    @Schema(description = "是否私有", example = "false")
    private Boolean isPrivate = false;

    @Schema(description = "附件ID列表", example = "[\"attachment-uuid-1\", \"attachment-uuid-2\"]")
    private List<String> attachmentIds;

    @Schema(description = "提及的用户ID列表", example = "[\"user-uuid-1\", \"user-uuid-2\"]")
    private List<String> mentionedUserIds;
}
