#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WTMS后端Result<Void>类型批量修复脚本
将所有返回Result.success(String)的方法从Result<Void>改为Result<String>
"""

import os
import re
import glob

def fix_result_void_types():
    """批量修复Result<Void>类型问题"""
    
    # 需要修复的控制器文件
    controller_files = [
        "src/main/java/com/wtms/controller/TaskCommentController.java",
        "src/main/java/com/wtms/controller/TaskAttachmentController.java", 
        "src/main/java/com/wtms/controller/RolePermissionController.java",
        "src/main/java/com/wtms/controller/PermissionController.java",
        "src/main/java/com/wtms/controller/WorkflowDefinitionController.java",
        "src/main/java/com/wtms/controller/TaskAssignmentController.java",
        "src/main/java/com/wtms/controller/TaskEvaluationController.java"
    ]
    
    total_fixed = 0
    
    for file_path in controller_files:
        if os.path.exists(file_path):
            print(f"正在处理: {file_path}")
            
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 修复Result<Void>为Result<String>
            # 匹配模式：public Result<Void> methodName(
            pattern = r'public Result<Void>'
            replacement = 'public Result<String>'
            
            content = re.sub(pattern, replacement, content)
            
            # 统计修复数量
            fixes_count = len(re.findall(pattern, original_content))
            total_fixed += fixes_count
            
            # 写回文件
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✓ 修复了 {fixes_count} 个方法")
            else:
                print(f"  - 无需修复")
        else:
            print(f"  ✗ 文件不存在: {file_path}")
    
    print(f"\n批量修复完成！总共修复了 {total_fixed} 个方法。")
    return total_fixed

def fix_conditional_expressions():
    """修复条件表达式中的类型错误"""
    
    files_to_fix = [
        "src/main/java/com/wtms/controller/WorkflowDefinitionController.java",
        "src/main/java/com/wtms/controller/TaskEvaluationController.java"
    ]
    
    for file_path in files_to_fix:
        if os.path.exists(file_path):
            print(f"修复条件表达式: {file_path}")
            
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            original_content = content
            
            # 修复条件表达式中的Result类型不匹配
            # 模式：return success ? Result.success("message") : Result.error("message");
            # 需要确保两个分支返回相同类型
            
            # 查找并修复这种模式
            pattern = r'return\s+(\w+)\s*\?\s*Result\.success\("([^"]+)"\)\s*:\s*Result\.error\("([^"]+)"\);'
            
            def replace_conditional(match):
                condition = match.group(1)
                success_msg = match.group(2)
                error_msg = match.group(3)
                return f'return {condition} ? Result.success("{success_msg}") : Result.<String>error("{error_msg}");'
            
            content = re.sub(pattern, replace_conditional, content)
            
            if content != original_content:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"  ✓ 修复了条件表达式类型问题")
            else:
                print(f"  - 无需修复条件表达式")

def main():
    """主函数"""
    print("🚀 开始批量修复WTMS后端Result<Void>类型问题...")
    print("=" * 60)
    
    # 切换到正确的目录
    os.chdir("C:/Users/<USER>/Desktop/WTMS/wtms-backend")
    
    # 修复Result<Void>类型
    total_fixed = fix_result_void_types()
    
    # 修复条件表达式
    fix_conditional_expressions()
    
    print("\n" + "=" * 60)
    print(f"✅ 批量修复完成！总共修复了 {total_fixed} 个方法。")
    print("现在尝试编译项目...")
    
    # 尝试编译
    import subprocess
    try:
        result = subprocess.run(['mvn', 'compile', '-q'], 
                              capture_output=True, text=True, timeout=120)
        if result.returncode == 0:
            print("🎉 编译成功！")
        else:
            print("⚠️ 编译仍有错误，需要进一步检查")
            print("错误信息:")
            print(result.stderr)
    except Exception as e:
        print(f"编译测试失败: {e}")

if __name__ == "__main__":
    main()
