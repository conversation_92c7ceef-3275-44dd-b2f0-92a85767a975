# WTMS 服务启动指南

## 🚀 服务启动步骤

根据您的环境配置，WTMS系统需要以下服务：
- MySQL数据库 (端口: 3308)
- Redis缓存 (端口: 6379)
- 后端服务 (端口: 55557)
- 前端服务 (端口: 33335)

## 方案一：Docker启动（推荐）

### 1. 安装Docker Desktop
如果还没有安装Docker，请先安装：
1. 下载Docker Desktop for Windows: https://www.docker.com/products/docker-desktop
2. 安装并启动Docker Desktop
3. 确保Docker服务正在运行

### 2. 启动所有服务
```bash
# 在项目根目录执行
docker-compose up -d

# 查看服务状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 3. 验证服务
```bash
# 检查前端
curl http://localhost:33335

# 检查后端API
curl http://localhost:55557/api/v1/health

# 检查数据库连接
docker exec -it wtms-mysql mysql -uroot -pankaixin.docker.mysql -e "SHOW DATABASES;"
```

## 方案二：手动启动服务

### 1. 启动MySQL数据库

#### 选项A：使用现有MySQL服务
如果系统已安装MySQL：
```bash
# 启动MySQL服务
net start mysql

# 或者使用服务管理器启动MySQL服务
```

#### 选项B：使用Docker单独启动MySQL
```bash
docker run -d \
  --name wtms-mysql \
  -p 3308:3306 \
  -e MYSQL_ROOT_PASSWORD=ankaixin.docker.mysql \
  -e MYSQL_DATABASE=wtms \
  -v mysql_data:/var/lib/mysql \
  mysql:8.0
```

#### 选项C：下载并安装MySQL
1. 下载MySQL 8.0: https://dev.mysql.com/downloads/mysql/
2. 安装并配置端口为3308
3. 设置root密码为: ankaixin.docker.mysql

### 2. 初始化数据库
```bash
# 连接数据库
mysql -h localhost -P 3308 -u root -pankaixin.docker.mysql

# 创建数据库
CREATE DATABASE IF NOT EXISTS wtms CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

# 使用数据库
USE wtms;

# 导入表结构
source database/schema.sql;

# 导入测试数据
source database/test-data.sql;
```

### 3. 启动Redis

#### 选项A：使用Docker启动Redis
```bash
docker run -d \
  --name wtms-redis \
  -p 6379:6379 \
  redis:7.0-alpine
```

#### 选项B：下载并安装Redis
1. 下载Redis for Windows: https://github.com/microsoftarchive/redis/releases
2. 解压并启动redis-server.exe
3. 确保监听端口6379

### 4. 启动后端服务

#### 前提条件
- Java 17或更高版本
- Maven 3.6+

#### 启动步骤
```bash
# 进入后端目录
cd wtms-backend

# 编译项目
mvn clean compile

# 启动服务
mvn spring-boot:run

# 或者打包后启动
mvn clean package -DskipTests
java -jar target/wtms-backend-1.0.0.jar
```

#### 验证后端服务
```bash
# 检查健康状态
curl http://localhost:55557/api/v1/actuator/health

# 测试登录接口
curl -X POST http://localhost:55557/api/v1/auth/login \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin123456"}'
```

### 5. 启动前端服务

#### 前提条件
- Node.js 18或更高版本
- npm或yarn

#### 启动步骤
```bash
# 进入前端目录
cd wtms-frontend

# 安装依赖
npm install

# 启动开发服务器
npm run dev

# 或者构建生产版本
npm run build
npm run preview
```

#### 验证前端服务
访问: http://localhost:33335

## 🔍 服务验证

### 1. 端口检查
```bash
# Windows
netstat -an | findstr "3308"  # MySQL
netstat -an | findstr "6379"  # Redis
netstat -an | findstr "55557" # 后端
netstat -an | findstr "33335" # 前端

# 或使用PowerShell
Get-NetTCPConnection -LocalPort 3308,6379,55557,33335
```

### 2. 服务状态检查
```bash
# 检查MySQL
mysql -h localhost -P 3308 -u root -pankaixin.docker.mysql -e "SELECT 1;"

# 检查Redis
redis-cli -h localhost -p 6379 ping

# 检查后端API
curl http://localhost:55557/api/v1/health

# 检查前端
curl http://localhost:33335
```

### 3. 功能验证
1. 访问前端: http://localhost:33335
2. 使用默认账户登录: admin / admin123456
3. 查看任务列表
4. 创建新任务
5. 检查API文档: http://localhost:55557/swagger-ui.html

## 🐛 常见问题解决

### 1. 端口被占用
```bash
# 查找占用端口的进程
netstat -ano | findstr "端口号"

# 结束进程
taskkill /PID 进程ID /F
```

### 2. 数据库连接失败
- 检查MySQL服务是否启动
- 验证端口3308是否正确
- 确认用户名密码: root / ankaixin.docker.mysql
- 检查防火墙设置

### 3. 后端服务启动失败
- 检查Java版本 (需要17+)
- 验证数据库连接配置
- 查看application.yml配置
- 检查日志文件

### 4. 前端访问失败
- 检查Node.js版本 (需要18+)
- 验证.env文件中的API地址
- 确认后端服务已启动
- 检查网络连接

## 📝 启动检查清单

- [ ] MySQL服务运行在端口3308
- [ ] Redis服务运行在端口6379
- [ ] 数据库wtms已创建并初始化
- [ ] 后端服务运行在端口55557
- [ ] 前端服务运行在端口33335
- [ ] 所有服务健康检查通过
- [ ] 可以正常登录系统
- [ ] API接口响应正常

## 🎯 快速启动命令

### Docker方式（推荐）
```bash
# 一键启动
docker-compose up -d

# 查看状态
docker-compose ps

# 查看日志
docker-compose logs -f
```

### 手动方式
```bash
# 1. 启动MySQL (如果未启动)
net start mysql

# 2. 启动Redis (如果使用Docker)
docker run -d --name wtms-redis -p 6379:6379 redis:7.0-alpine

# 3. 启动后端
cd wtms-backend && mvn spring-boot:run

# 4. 启动前端 (新终端)
cd wtms-frontend && npm run dev
```

## 📞 技术支持

如果遇到问题，请检查：
1. 系统日志文件
2. 服务启动日志
3. 网络连接状态
4. 防火墙设置
5. 端口占用情况

---

**启动成功后访问地址：**
- 前端应用: http://localhost:33335
- 后端API: http://localhost:55557/api/v1
- 默认账户: admin / admin123456
