package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.dto.request.TaskQueryRequest;
import com.wtms.entity.Task;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface TaskMapper extends BaseMapper<Task> {

    /**
     * 根据ID查询任务详情（包含关联信息）
     */
    Task selectTaskDetailById(@Param("taskId") String taskId);

    /**
     * 分页查询任务列表（包含关联信息）
     */
    IPage<Task> selectTaskPageWithRelations(Page<Task> page, @Param("query") TaskQueryRequest query, @Param("currentUserId") String currentUserId);

    /**
     * 根据父任务ID查询子任务列表
     */
    @Select("SELECT * FROM tasks WHERE parent_id = #{parentId} AND deleted_at IS NULL ORDER BY created_at ASC")
    List<Task> selectSubTasksByParentId(@Param("parentId") String parentId);

    /**
     * 根据负责人ID查询任务列表
     */
    @Select("SELECT * FROM tasks WHERE assignee_id = #{assigneeId} AND deleted_at IS NULL")
    List<Task> selectTasksByAssigneeId(@Param("assigneeId") String assigneeId);

    /**
     * 根据创建者ID查询任务列表
     */
    @Select("SELECT * FROM tasks WHERE creator_id = #{creatorId} AND deleted_at IS NULL")
    List<Task> selectTasksByCreatorId(@Param("creatorId") String creatorId);

    /**
     * 根据项目ID查询任务列表
     */
    @Select("SELECT * FROM tasks WHERE project_id = #{projectId} AND deleted_at IS NULL")
    List<Task> selectTasksByProjectId(@Param("projectId") String projectId);

    /**
     * 根据分类ID查询任务列表
     */
    @Select("SELECT * FROM tasks WHERE category_id = #{categoryId} AND deleted_at IS NULL")
    List<Task> selectTasksByCategoryId(@Param("categoryId") String categoryId);

    /**
     * 更新任务状态
     */
    @Update("UPDATE tasks SET status = #{status}, updated_at = NOW() WHERE id = #{taskId}")
    int updateTaskStatus(@Param("taskId") String taskId, @Param("status") String status);

    /**
     * 更新任务进度
     */
    @Update("UPDATE tasks SET progress = #{progress}, updated_at = NOW() WHERE id = #{taskId}")
    int updateTaskProgress(@Param("taskId") String taskId, @Param("progress") BigDecimal progress);

    /**
     * 更新任务负责人
     */
    @Update("UPDATE tasks SET assignee_id = #{assigneeId}, updated_at = NOW() WHERE id = #{taskId}")
    int updateTaskAssignee(@Param("taskId") String taskId, @Param("assigneeId") String assigneeId);

    /**
     * 更新任务实际开始时间
     */
    @Update("UPDATE tasks SET actual_start_date = #{startDate}, updated_at = NOW() WHERE id = #{taskId}")
    int updateTaskActualStartDate(@Param("taskId") String taskId, @Param("startDate") LocalDateTime startDate);

    /**
     * 更新任务实际结束时间
     */
    @Update("UPDATE tasks SET actual_end_date = #{endDate}, updated_at = NOW() WHERE id = #{taskId}")
    int updateTaskActualEndDate(@Param("taskId") String taskId, @Param("endDate") LocalDateTime endDate);

    /**
     * 归档任务
     */
    @Update("UPDATE tasks SET is_archived = #{archived}, updated_at = NOW() WHERE id = #{taskId}")
    int updateTaskArchiveStatus(@Param("taskId") String taskId, @Param("archived") Boolean archived);

    /**
     * 检查任务编号是否存在
     */
    @Select("SELECT COUNT(*) FROM tasks WHERE task_code = #{taskCode} AND deleted_at IS NULL")
    int countByTaskCode(@Param("taskCode") String taskCode);

    /**
     * 检查是否存在循环依赖
     */
    boolean checkCircularDependency(@Param("taskId") String taskId, @Param("parentId") String parentId);

    /**
     * 获取任务统计信息
     */
    TaskStatistics getTaskStatistics(@Param("creatorId") String creatorId, @Param("assigneeId") String assigneeId);

    /**
     * 获取用户任务统计
     */
    UserTaskStatistics getUserTaskStatistics(@Param("userId") String userId);

    /**
     * 获取分类任务统计
     */
    List<CategoryTaskStatistics> getCategoryTaskStatistics();

    /**
     * 任务统计信息内部类
     */
    class TaskStatistics {
        private Long totalTasks;
        private Long pendingTasks;
        private Long inProgressTasks;
        private Long completedTasks;
        private Long overdueTasks;
        private BigDecimal totalEstimatedHours;
        private BigDecimal totalActualHours;

        // getters and setters
        public Long getTotalTasks() { return totalTasks; }
        public void setTotalTasks(Long totalTasks) { this.totalTasks = totalTasks; }
        public Long getPendingTasks() { return pendingTasks; }
        public void setPendingTasks(Long pendingTasks) { this.pendingTasks = pendingTasks; }
        public Long getInProgressTasks() { return inProgressTasks; }
        public void setInProgressTasks(Long inProgressTasks) { this.inProgressTasks = inProgressTasks; }
        public Long getCompletedTasks() { return completedTasks; }
        public void setCompletedTasks(Long completedTasks) { this.completedTasks = completedTasks; }
        public Long getOverdueTasks() { return overdueTasks; }
        public void setOverdueTasks(Long overdueTasks) { this.overdueTasks = overdueTasks; }
        public BigDecimal getTotalEstimatedHours() { return totalEstimatedHours; }
        public void setTotalEstimatedHours(BigDecimal totalEstimatedHours) { this.totalEstimatedHours = totalEstimatedHours; }
        public BigDecimal getTotalActualHours() { return totalActualHours; }
        public void setTotalActualHours(BigDecimal totalActualHours) { this.totalActualHours = totalActualHours; }
    }

    /**
     * 用户任务统计内部类
     */
    class UserTaskStatistics {
        private String userId;
        private String userName;
        private Long assignedTasks;
        private Long completedTasks;
        private Long overdueTasks;
        private BigDecimal completionRate;

        // getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public Long getAssignedTasks() { return assignedTasks; }
        public void setAssignedTasks(Long assignedTasks) { this.assignedTasks = assignedTasks; }
        public Long getCompletedTasks() { return completedTasks; }
        public void setCompletedTasks(Long completedTasks) { this.completedTasks = completedTasks; }
        public Long getOverdueTasks() { return overdueTasks; }
        public void setOverdueTasks(Long overdueTasks) { this.overdueTasks = overdueTasks; }
        public BigDecimal getCompletionRate() { return completionRate; }
        public void setCompletionRate(BigDecimal completionRate) { this.completionRate = completionRate; }
    }

    /**
     * 分类任务统计内部类
     */
    class CategoryTaskStatistics {
        private String categoryId;
        private String categoryName;
        private Long taskCount;
        private Long completedCount;
        private BigDecimal completionRate;

        // getters and setters
        public String getCategoryId() { return categoryId; }
        public void setCategoryId(String categoryId) { this.categoryId = categoryId; }
        public String getCategoryName() { return categoryName; }
        public void setCategoryName(String categoryName) { this.categoryName = categoryName; }
        public Long getTaskCount() { return taskCount; }
        public void setTaskCount(Long taskCount) { this.taskCount = taskCount; }
        public Long getCompletedCount() { return completedCount; }
        public void setCompletedCount(Long completedCount) { this.completedCount = completedCount; }
        public BigDecimal getCompletionRate() { return completionRate; }
        public void setCompletionRate(BigDecimal completionRate) { this.completionRate = completionRate; }
    }
}
