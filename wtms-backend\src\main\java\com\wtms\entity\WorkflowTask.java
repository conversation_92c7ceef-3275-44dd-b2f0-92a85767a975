package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 工作流任务实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("workflow_tasks")
@Schema(description = "工作流任务实体")
public class WorkflowTask implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "任务ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "工作流实例ID")
    @TableField("workflow_instance_id")
    private String workflowInstanceId;

    @Schema(description = "工作流节点ID")
    @TableField("workflow_node_id")
    private String workflowNodeId;

    @Schema(description = "任务名称")
    @TableField("task_name")
    private String taskName;

    @Schema(description = "任务类型")
    @TableField("task_type")
    private String taskType;

    @Schema(description = "任务状态")
    @TableField("status")
    private String status;

    @Schema(description = "分配者ID")
    @TableField("assignee_id")
    private String assigneeId;

    @Schema(description = "候选用户IDs")
    @TableField("candidate_users")
    private String candidateUsers;

    @Schema(description = "候选组IDs")
    @TableField("candidate_groups")
    private String candidateGroups;

    @Schema(description = "委托者ID")
    @TableField("delegator_id")
    private String delegatorId;

    @Schema(description = "所有者ID")
    @TableField("owner_id")
    private String ownerId;

    @Schema(description = "任务描述")
    @TableField("description")
    private String description;

    @Schema(description = "任务表单Key")
    @TableField("form_key")
    private String formKey;

    @Schema(description = "任务变量JSON")
    @TableField("variables_json")
    private String variablesJson;

    @Schema(description = "优先级")
    @TableField("priority")
    private Integer priority;

    @Schema(description = "到期时间")
    @TableField("due_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime dueDate;

    @Schema(description = "跟进时间")
    @TableField("follow_up_date")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime followUpDate;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "认领时间")
    @TableField("claimed_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime claimedAt;

    @Schema(description = "开始时间")
    @TableField("started_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime startedAt;

    @Schema(description = "完成时间")
    @TableField("completed_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "工作流实例信息")
    @TableField(exist = false)
    private WorkflowInstance workflowInstance;

    @Schema(description = "工作流节点信息")
    @TableField(exist = false)
    private WorkflowNode workflowNode;

    @Schema(description = "分配者信息")
    @TableField(exist = false)
    private User assignee;

    @Schema(description = "委托者信息")
    @TableField(exist = false)
    private User delegator;

    @Schema(description = "所有者信息")
    @TableField(exist = false)
    private User owner;

    /**
     * 任务状态枚举
     */
    public enum Status {
        CREATED("created", "已创建"),
        READY("ready", "就绪"),
        RESERVED("reserved", "已预留"),
        IN_PROGRESS("in_progress", "进行中"),
        SUSPENDED("suspended", "已暂停"),
        COMPLETED("completed", "已完成"),
        FAILED("failed", "失败"),
        ERROR("error", "错误"),
        EXITED("exited", "已退出"),
        OBSOLETE("obsolete", "已废弃");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return CREATED;
        }
    }

    /**
     * 任务类型枚举
     */
    public enum TaskType {
        USER_TASK("user_task", "用户任务"),
        SERVICE_TASK("service_task", "服务任务"),
        SCRIPT_TASK("script_task", "脚本任务"),
        MAIL_TASK("mail_task", "邮件任务"),
        TIMER_TASK("timer_task", "定时任务"),
        MANUAL_TASK("manual_task", "手工任务"),
        RECEIVE_TASK("receive_task", "接收任务"),
        BUSINESS_RULE_TASK("business_rule_task", "业务规则任务");

        private final String code;
        private final String description;

        TaskType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static TaskType fromCode(String code) {
            for (TaskType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return USER_TASK;
        }
    }

    /**
     * 检查任务是否活跃
     */
    public boolean isActive() {
        return Status.READY.getCode().equals(this.status) || 
               Status.RESERVED.getCode().equals(this.status) || 
               Status.IN_PROGRESS.getCode().equals(this.status);
    }

    /**
     * 检查任务是否已完成
     */
    public boolean isCompleted() {
        return Status.COMPLETED.getCode().equals(this.status);
    }

    /**
     * 检查任务是否已暂停
     */
    public boolean isSuspended() {
        return Status.SUSPENDED.getCode().equals(this.status);
    }

    /**
     * 检查任务是否有分配者
     */
    public boolean hasAssignee() {
        return assigneeId != null && !assigneeId.trim().isEmpty();
    }

    /**
     * 检查任务是否已认领
     */
    public boolean isClaimed() {
        return claimedAt != null;
    }

    /**
     * 检查任务是否已开始
     */
    public boolean isStarted() {
        return startedAt != null;
    }

    /**
     * 检查任务是否超期
     */
    public boolean isOverdue() {
        return dueDate != null && LocalDateTime.now().isAfter(dueDate) && isActive();
    }

    /**
     * 获取状态描述
     */
    public String getStatusText() {
        return Status.fromCode(this.status).getDescription();
    }

    /**
     * 获取任务类型描述
     */
    public String getTaskTypeText() {
        return TaskType.fromCode(this.taskType).getDescription();
    }

    /**
     * 计算任务执行时长（毫秒）
     */
    public Long getDuration() {
        if (startedAt == null) {
            return null;
        }
        LocalDateTime endTime = completedAt != null ? completedAt : LocalDateTime.now();
        return java.time.Duration.between(startedAt, endTime).toMillis();
    }

    /**
     * 计算任务等待时长（毫秒）
     */
    public Long getWaitingDuration() {
        if (createdAt == null) {
            return null;
        }
        LocalDateTime startTime = startedAt != null ? startedAt : LocalDateTime.now();
        return java.time.Duration.between(createdAt, startTime).toMillis();
    }
}
