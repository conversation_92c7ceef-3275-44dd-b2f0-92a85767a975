package com.wtms.service;

import com.wtms.entity.Task;

import java.util.List;
import java.util.Map;

/**
 * 任务状态流转服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface TaskStatusService {

    /**
     * 获取任务可用的下一状态
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @return 可用状态列表
     */
    List<String> getAvailableNextStatuses(String taskId, String userId);

    /**
     * 检查状态流转是否有效
     *
     * @param currentStatus 当前状态
     * @param targetStatus 目标状态
     * @return 是否有效
     */
    boolean isValidStatusTransition(String currentStatus, String targetStatus);

    /**
     * 检查用户是否有权限进行状态流转
     *
     * @param taskId 任务ID
     * @param userId 用户ID
     * @param targetStatus 目标状态
     * @return 是否有权限
     */
    boolean hasStatusTransitionPermission(String taskId, String userId, String targetStatus);

    /**
     * 执行状态流转
     *
     * @param taskId 任务ID
     * @param targetStatus 目标状态
     * @param userId 操作用户ID
     * @param comment 流转备注
     * @return 更新后的任务
     */
    Task executeStatusTransition(String taskId, String targetStatus, String userId, String comment);

    /**
     * 获取状态流转历史
     *
     * @param taskId 任务ID
     * @return 流转历史
     */
    List<TaskStatusHistory> getStatusHistory(String taskId);

    /**
     * 获取所有任务状态及其描述
     *
     * @return 状态映射
     */
    Map<String, String> getAllTaskStatuses();

    /**
     * 获取状态流转规则
     *
     * @return 流转规则映射
     */
    Map<String, List<String>> getStatusTransitionRules();

    /**
     * 任务状态历史记录
     */
    class TaskStatusHistory {
        private String id;
        private String taskId;
        private String fromStatus;
        private String toStatus;
        private String operatorId;
        private String operatorName;
        private String comment;
        private java.time.LocalDateTime createdAt;

        // getters and setters
        public String getId() { return id; }
        public void setId(String id) { this.id = id; }
        public String getTaskId() { return taskId; }
        public void setTaskId(String taskId) { this.taskId = taskId; }
        public String getFromStatus() { return fromStatus; }
        public void setFromStatus(String fromStatus) { this.fromStatus = fromStatus; }
        public String getToStatus() { return toStatus; }
        public void setToStatus(String toStatus) { this.toStatus = toStatus; }
        public String getOperatorId() { return operatorId; }
        public void setOperatorId(String operatorId) { this.operatorId = operatorId; }
        public String getOperatorName() { return operatorName; }
        public void setOperatorName(String operatorName) { this.operatorName = operatorName; }
        public String getComment() { return comment; }
        public void setComment(String comment) { this.comment = comment; }
        public java.time.LocalDateTime getCreatedAt() { return createdAt; }
        public void setCreatedAt(java.time.LocalDateTime createdAt) { this.createdAt = createdAt; }
    }
}
