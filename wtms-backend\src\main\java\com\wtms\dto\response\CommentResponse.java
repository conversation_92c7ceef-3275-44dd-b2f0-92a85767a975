package com.wtms.dto.response;

import com.wtms.entity.TaskAttachment;
import com.wtms.entity.TaskComment;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 评论响应DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Schema(description = "评论响应")
public class CommentResponse {

    @Schema(description = "评论ID")
    private String id;

    @Schema(description = "任务ID")
    private String taskId;

    @Schema(description = "父评论ID")
    private String parentId;

    @Schema(description = "评论内容")
    private String content;

    @Schema(description = "评论类型")
    private String commentType;

    @Schema(description = "评论类型描述")
    private String commentTypeText;

    @Schema(description = "评论层级")
    private Integer level;

    @Schema(description = "评论路径")
    private String path;

    @Schema(description = "是否置顶")
    private Boolean isPinned;

    @Schema(description = "是否私有")
    private Boolean isPrivate;

    @Schema(description = "点赞数")
    private Integer likeCount;

    @Schema(description = "回复数")
    private Integer replyCount;

    @Schema(description = "状态")
    private String status;

    @Schema(description = "状态描述")
    private String statusText;

    @Schema(description = "创建时间")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    private LocalDateTime updatedAt;

    @Schema(description = "评论者信息")
    private CommenterInfo commenter;

    @Schema(description = "附件列表")
    private List<AttachmentInfo> attachments;

    @Schema(description = "子评论列表")
    private List<CommentResponse> children;

    @Schema(description = "是否可编辑")
    private Boolean canEdit;

    @Schema(description = "是否可删除")
    private Boolean canDelete;

    @Schema(description = "是否已点赞")
    private Boolean isLiked;

    /**
     * 评论者信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "评论者信息")
    public static class CommenterInfo {
        @Schema(description = "用户ID")
        private String id;

        @Schema(description = "用户名")
        private String username;

        @Schema(description = "全名")
        private String fullName;

        @Schema(description = "头像")
        private String avatar;

        @Schema(description = "部门")
        private String department;

        @Schema(description = "职位")
        private String position;
    }

    /**
     * 附件信息
     */
    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    @Schema(description = "附件信息")
    public static class AttachmentInfo {
        @Schema(description = "附件ID")
        private String id;

        @Schema(description = "文件名")
        private String fileName;

        @Schema(description = "原始文件名")
        private String originalName;

        @Schema(description = "文件大小")
        private Long fileSize;

        @Schema(description = "可读文件大小")
        private String readableFileSize;

        @Schema(description = "文件类型")
        private String fileType;

        @Schema(description = "文件扩展名")
        private String fileExtension;

        @Schema(description = "文件URL")
        private String fileUrl;

        @Schema(description = "缩略图URL")
        private String thumbnailUrl;

        @Schema(description = "下载次数")
        private Integer downloadCount;

        @Schema(description = "是否为图片")
        private Boolean isImage;

        @Schema(description = "是否为文档")
        private Boolean isDocument;

        @Schema(description = "创建时间")
        private LocalDateTime createdAt;
    }

    /**
     * 从TaskComment实体转换
     */
    public static CommentResponse from(TaskComment comment) {
        CommentResponseBuilder builder = CommentResponse.builder()
                .id(comment.getId())
                .taskId(comment.getTaskId())
                .parentId(comment.getParentId())
                .content(comment.getContent())
                .commentType(comment.getCommentType())
                .commentTypeText(TaskComment.CommentType.fromCode(comment.getCommentType()).getDescription())
                .level(comment.getLevel())
                .path(comment.getPath())
                .isPinned(comment.getIsPinned())
                .isPrivate(comment.getIsPrivate())
                .likeCount(comment.getLikeCount())
                .replyCount(comment.getReplyCount())
                .status(comment.getStatus())
                .statusText(TaskComment.Status.fromCode(comment.getStatus()).getDescription())
                .createdAt(comment.getCreatedAt())
                .updatedAt(comment.getUpdatedAt());

        // 转换评论者信息
        if (comment.getCommenter() != null) {
            CommenterInfo commenterInfo = CommenterInfo.builder()
                    .id(comment.getCommenter().getId())
                    .username(comment.getCommenter().getUsername())
                    .fullName(comment.getCommenter().getFullName())
                    .avatar(comment.getCommenter().getAvatarUrl())
                    .department(comment.getCommenter().getDepartment() != null ? comment.getCommenter().getDepartment().getName() : null)
                    .position(comment.getCommenter().getFullName()) // 使用fullName作为position的临时替代
                    .build();
            builder.commenter(commenterInfo);
        }

        // 转换附件信息
        if (comment.getAttachments() != null && !comment.getAttachments().isEmpty()) {
            List<AttachmentInfo> attachmentInfos = comment.getAttachments().stream()
                    .map(attachment -> AttachmentInfo.builder()
                            .id(attachment.getId())
                            .fileName(attachment.getFileName())
                            .originalName(attachment.getOriginalName())
                            .fileSize(attachment.getFileSize())
                            .readableFileSize(attachment.getReadableFileSize())
                            .fileType(attachment.getFileType())
                            .fileExtension(attachment.getFileExtension())
                            .fileUrl(attachment.getFileUrl())
                            .thumbnailUrl(attachment.getThumbnailUrl())
                            .downloadCount(attachment.getDownloadCount())
                            .isImage(attachment.isImage())
                            .isDocument(attachment.isDocument())
                            .createdAt(attachment.getCreatedAt())
                            .build())
                    .collect(Collectors.toList());
            builder.attachments(attachmentInfos);
        }

        // 转换子评论
        if (comment.getChildren() != null && !comment.getChildren().isEmpty()) {
            List<CommentResponse> childrenResponse = comment.getChildren().stream()
                    .map(CommentResponse::from)
                    .collect(Collectors.toList());
            builder.children(childrenResponse);
        }

        return builder.build();
    }

    /**
     * 从TaskComment列表转换为树形结构
     */
    public static List<CommentResponse> buildTree(List<TaskComment> comments) {
        return comments.stream()
                .filter(TaskComment::isRoot)
                .map(CommentResponse::from)
                .collect(Collectors.toList());
    }
}
