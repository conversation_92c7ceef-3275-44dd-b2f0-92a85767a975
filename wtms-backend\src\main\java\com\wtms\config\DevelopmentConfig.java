package com.wtms.config;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Configuration;

/**
 * 开发环境配置
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Configuration
@ConfigurationProperties(prefix = "wtms.development")
public class DevelopmentConfig {

    /**
     * 是否启用开发模式
     */
    private boolean enabled = true;

    /**
     * 权限覆盖配置
     */
    private PermissionOverride permissionOverride = new PermissionOverride();

    /**
     * 超级管理员配置
     */
    private SuperAdmin superAdmin = new SuperAdmin();

    /**
     * 权限覆盖配置
     */
    @Data
    public static class PermissionOverride {
        /**
         * 是否启用权限覆盖
         */
        private boolean enabled = true;

        /**
         * 覆盖所有权限检查
         */
        private boolean overrideAll = true;

        /**
         * 覆盖角色检查
         */
        private boolean overrideRoles = true;

        /**
         * 覆盖权限检查
         */
        private boolean overridePermissions = true;

        /**
         * 白名单用户（这些用户在开发环境下拥有所有权限）
         */
        private String[] whitelistUsers = {"admin", "developer", "test", "dev001", "dev002"};

        /**
         * 黑名单用户（这些用户即使在开发环境下也不会获得权限覆盖）
         */
        private String[] blacklistUsers = {};
    }

    /**
     * 超级管理员配置
     */
    @Data
    public static class SuperAdmin {
        /**
         * 默认超级管理员用户名
         */
        private String defaultUsername = "admin";

        /**
         * 默认超级管理员密码
         */
        private String defaultPassword = "123456";

        /**
         * 是否自动创建超级管理员
         */
        private boolean autoCreate = true;

        /**
         * 超级管理员角色编码
         */
        private String roleCode = "ADMIN";

        /**
         * 超级管理员角色名称
         */
        private String roleName = "超级管理员";
    }

    /**
     * 检查是否启用开发模式
     */
    public boolean isDevelopmentMode() {
        return enabled;
    }

    /**
     * 检查是否启用权限覆盖
     */
    public boolean isPermissionOverrideEnabled() {
        return enabled && permissionOverride.enabled;
    }

    /**
     * 检查用户是否在白名单中
     */
    public boolean isUserInWhitelist(String username) {
        if (permissionOverride.whitelistUsers == null || permissionOverride.whitelistUsers.length == 0) {
            return true; // 如果没有配置白名单，则所有用户都在白名单中
        }
        
        for (String whitelistUser : permissionOverride.whitelistUsers) {
            if (whitelistUser.equals(username)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查用户是否在黑名单中
     */
    public boolean isUserInBlacklist(String username) {
        if (permissionOverride.blacklistUsers == null || permissionOverride.blacklistUsers.length == 0) {
            return false; // 如果没有配置黑名单，则没有用户在黑名单中
        }
        
        for (String blacklistUser : permissionOverride.blacklistUsers) {
            if (blacklistUser.equals(username)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查用户是否应该获得权限覆盖
     */
    public boolean shouldOverridePermissions(String username) {
        if (!isPermissionOverrideEnabled()) {
            return false;
        }
        
        // 黑名单用户不会获得权限覆盖
        if (isUserInBlacklist(username)) {
            return false;
        }
        
        // 白名单用户会获得权限覆盖
        return isUserInWhitelist(username);
    }
}
