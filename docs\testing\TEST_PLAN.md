# WTMS 测试计划文档

## 测试概述

### 测试目标
确保WTMS工作任务管理系统的功能完整性、性能稳定性和用户体验质量，验证系统是否满足业务需求和技术规范。

### 测试范围
- **功能测试**: 验证所有功能模块的正确性
- **接口测试**: 验证API接口的正确性和稳定性
- **性能测试**: 验证系统在不同负载下的性能表现
- **安全测试**: 验证系统的安全防护能力
- **兼容性测试**: 验证系统在不同环境下的兼容性
- **用户体验测试**: 验证用户界面和交互的易用性

### 测试环境
- **开发环境**: 用于开发阶段的功能验证
- **测试环境**: 用于系统测试和集成测试
- **预生产环境**: 用于用户验收测试
- **生产环境**: 用于生产发布后的监控测试

## 测试策略

### 测试层级
```
┌─────────────────────────────────────┐
│           用户验收测试 (UAT)           │
├─────────────────────────────────────┤
│           系统测试 (ST)              │
├─────────────────────────────────────┤
│           集成测试 (IT)              │
├─────────────────────────────────────┤
│           单元测试 (UT)              │
└─────────────────────────────────────┘
```

### 测试类型分布
- **单元测试**: 70% - 验证代码单元的正确性
- **集成测试**: 20% - 验证模块间的集成
- **系统测试**: 8% - 验证系统整体功能
- **用户验收测试**: 2% - 验证业务需求满足度

### 测试工具
- **单元测试**: JUnit 5, Mockito, Vue Test Utils
- **集成测试**: Spring Boot Test, TestContainers
- **接口测试**: Postman, REST Assured
- **性能测试**: JMeter, Artillery
- **自动化测试**: Selenium WebDriver, Cypress
- **代码覆盖率**: JaCoCo, Istanbul

## 功能测试用例

### 用户认证模块

#### TC001: 用户登录功能
**测试目的**: 验证用户登录功能的正确性

**前置条件**: 
- 系统已部署并运行
- 存在有效的用户账户

**测试步骤**:
1. 打开登录页面
2. 输入正确的用户名和密码
3. 点击登录按钮
4. 验证登录结果

**测试数据**:
```json
{
  "validUser": {
    "username": "admin",
    "password": "admin123456"
  },
  "invalidUser": {
    "username": "invalid",
    "password": "wrongpassword"
  }
}
```

**预期结果**:
- 正确凭据：登录成功，跳转到工作台页面
- 错误凭据：显示错误提示信息

**测试用例**:
| 用例ID | 用户名 | 密码 | 预期结果 |
|--------|--------|------|----------|
| TC001-01 | admin | admin123456 | 登录成功 |
| TC001-02 | admin | wrongpassword | 密码错误 |
| TC001-03 | invalid | admin123456 | 用户不存在 |
| TC001-04 | "" | admin123456 | 用户名不能为空 |
| TC001-05 | admin | "" | 密码不能为空 |

#### TC002: 用户注册功能
**测试目的**: 验证用户注册功能的正确性

**测试步骤**:
1. 打开注册页面
2. 填写注册信息
3. 点击注册按钮
4. 验证注册结果

**测试数据**:
```json
{
  "validRegistration": {
    "username": "newuser",
    "password": "newuser123456",
    "fullName": "新用户",
    "email": "<EMAIL>",
    "phone": "13800138000"
  }
}
```

### 任务管理模块

#### TC003: 任务创建功能
**测试目的**: 验证任务创建功能的正确性

**前置条件**:
- 用户已登录
- 用户有创建任务的权限

**测试步骤**:
1. 进入任务管理页面
2. 点击"新建任务"按钮
3. 填写任务信息
4. 点击保存按钮
5. 验证任务创建结果

**测试数据**:
```json
{
  "validTask": {
    "title": "测试任务",
    "description": "这是一个测试任务",
    "priority": "HIGH",
    "type": "DEVELOPMENT",
    "estimatedHours": 8,
    "dueDate": "2024-02-01"
  }
}
```

#### TC004: 任务状态流转
**测试目的**: 验证任务状态流转的正确性

**测试步骤**:
1. 创建一个新任务（状态：TODO）
2. 将任务状态更改为"进行中"
3. 将任务状态更改为"待审核"
4. 将任务状态更改为"已完成"
5. 验证每次状态变更的结果

**状态流转图**:
```
TODO → IN_PROGRESS → REVIEW → DONE
  ↓         ↓          ↓       ↓
CANCELLED ← ← ← ← ← ← ← ← ← ← ← ←
```

### 工作流引擎模块

#### TC005: 工作流设计功能
**测试目的**: 验证工作流设计器的功能

**测试步骤**:
1. 进入工作流设计页面
2. 拖拽节点到画布
3. 连接节点
4. 配置节点属性
5. 保存工作流
6. 验证保存结果

#### TC006: 工作流执行功能
**测试目的**: 验证工作流执行的正确性

**测试步骤**:
1. 选择已设计的工作流
2. 启动工作流实例
3. 处理工作流任务
4. 验证流程执行结果

### 质量评价模块

#### TC007: 质量评价创建
**测试目的**: 验证质量评价创建功能

**测试步骤**:
1. 选择要评价的任务
2. 填写评价信息
3. 设置各维度评分
4. 提交评价
5. 验证评价结果

**测试数据**:
```json
{
  "evaluation": {
    "taskId": "task-001",
    "evaluateeId": "user-002",
    "evaluationType": "PEER",
    "overallScore": 85,
    "qualityScore": 88,
    "efficiencyScore": 82,
    "communicationScore": 87,
    "content": "工作质量很好，效率有待提升"
  }
}
```

## 接口测试用例

### 认证接口测试

#### API001: 登录接口测试
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "admin123456"
}
```

**测试用例**:
| 用例ID | 请求体 | 预期状态码 | 预期响应 |
|--------|--------|------------|----------|
| API001-01 | 正确凭据 | 200 | 返回token |
| API001-02 | 错误密码 | 401 | 认证失败 |
| API001-03 | 缺少参数 | 400 | 参数错误 |

#### API002: 任务列表接口测试
```http
GET /api/v1/tasks?page=1&size=10&status=IN_PROGRESS
Authorization: Bearer <token>
```

**测试用例**:
| 用例ID | 查询参数 | 预期状态码 | 预期响应 |
|--------|----------|------------|----------|
| API002-01 | 正常参数 | 200 | 返回任务列表 |
| API002-02 | 无权限 | 403 | 权限不足 |
| API002-03 | 无效token | 401 | 未授权 |

## 性能测试用例

### 负载测试

#### PERF001: 用户登录并发测试
**测试目标**: 验证系统在高并发登录时的性能表现

**测试配置**:
- 并发用户数: 100, 500, 1000
- 测试时长: 10分钟
- 响应时间要求: < 2秒
- 成功率要求: > 99%

**JMeter测试脚本**:
```xml
<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="WTMS登录性能测试">
      <elementProp name="TestPlan.arguments" elementType="Arguments" guiclass="ArgumentsPanel">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <stringProp name="TestPlan.user_define_classpath"></stringProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
    </TestPlan>
    <hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="登录用户组">
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController">
          <boolProp name="LoopController.continue_forever">false</boolProp>
          <stringProp name="LoopController.loops">10</stringProp>
        </elementProp>
        <stringProp name="ThreadGroup.num_threads">100</stringProp>
        <stringProp name="ThreadGroup.ramp_time">60</stringProp>
      </ThreadGroup>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
```

#### PERF002: 任务查询性能测试
**测试目标**: 验证任务查询接口的性能

**测试配置**:
- 并发用户数: 200
- 查询条件: 不同的筛选条件组合
- 响应时间要求: < 1秒
- 吞吐量要求: > 500 TPS

### 压力测试

#### PERF003: 系统极限压力测试
**测试目标**: 找出系统的性能瓶颈和极限

**测试配置**:
- 逐步增加并发用户数: 100 → 500 → 1000 → 2000
- 监控系统资源使用情况
- 记录系统崩溃点

## 安全测试用例

### 认证安全测试

#### SEC001: JWT Token安全测试
**测试目标**: 验证JWT Token的安全性

**测试用例**:
1. Token过期验证
2. Token篡改检测
3. Token重放攻击防护
4. 无效Token处理

#### SEC002: SQL注入测试
**测试目标**: 验证系统对SQL注入攻击的防护

**测试用例**:
```sql
-- 测试用例1: 登录SQL注入
username: admin' OR '1'='1' --
password: anything

-- 测试用例2: 搜索SQL注入
keyword: '; DROP TABLE tasks; --

-- 测试用例3: 参数SQL注入
taskId: 1' UNION SELECT * FROM users --
```

#### SEC003: XSS攻击测试
**测试目标**: 验证系统对XSS攻击的防护

**测试用例**:
```html
<!-- 测试用例1: 存储型XSS -->
<script>alert('XSS')</script>

<!-- 测试用例2: 反射型XSS -->
<img src="x" onerror="alert('XSS')">

<!-- 测试用例3: DOM型XSS -->
javascript:alert('XSS')
```

## 兼容性测试用例

### 浏览器兼容性测试

#### COMP001: 主流浏览器兼容性
**测试目标**: 验证系统在不同浏览器中的兼容性

**测试环境**:
| 浏览器 | 版本 | 操作系统 | 测试结果 |
|--------|------|----------|----------|
| Chrome | 120+ | Windows 10 | ✓ |
| Firefox | 115+ | Windows 10 | ✓ |
| Safari | 16+ | macOS | ✓ |
| Edge | 120+ | Windows 10 | ✓ |

### 移动端兼容性测试

#### COMP002: 移动设备兼容性
**测试目标**: 验证系统在移动设备上的表现

**测试设备**:
- iPhone 12/13/14 (iOS 15+)
- Samsung Galaxy S21/S22 (Android 11+)
- iPad Air/Pro (iPadOS 15+)

## 用户体验测试用例

### 易用性测试

#### UX001: 新用户上手测试
**测试目标**: 验证新用户能否快速上手使用系统

**测试场景**:
1. 新用户首次登录
2. 创建第一个任务
3. 查看任务列表
4. 完成任务操作

**评估指标**:
- 完成时间: < 5分钟
- 错误次数: < 3次
- 用户满意度: > 4分（5分制）

#### UX002: 界面响应性测试
**测试目标**: 验证界面操作的响应性

**测试用例**:
- 页面加载时间: < 3秒
- 按钮点击响应: < 0.5秒
- 表单提交响应: < 2秒
- 数据刷新时间: < 1秒

## 自动化测试

### 前端自动化测试

#### Cypress测试脚本示例
```javascript
// cypress/integration/login.spec.js
describe('用户登录测试', () => {
  beforeEach(() => {
    cy.visit('/login')
  })

  it('应该能够成功登录', () => {
    cy.get('[data-cy=username]').type('admin')
    cy.get('[data-cy=password]').type('admin123456')
    cy.get('[data-cy=login-btn]').click()
    
    cy.url().should('include', '/dashboard')
    cy.get('[data-cy=user-menu]').should('contain', 'admin')
  })

  it('应该显示错误信息当密码错误时', () => {
    cy.get('[data-cy=username]').type('admin')
    cy.get('[data-cy=password]').type('wrongpassword')
    cy.get('[data-cy=login-btn]').click()
    
    cy.get('[data-cy=error-message]').should('contain', '密码错误')
  })
})
```

### 后端自动化测试

#### Spring Boot测试示例
```java
@SpringBootTest
@AutoConfigureTestDatabase
@Transactional
class TaskServiceTest {
    
    @Autowired
    private TaskService taskService;
    
    @Test
    @DisplayName("应该能够创建任务")
    void shouldCreateTask() {
        // Given
        CreateTaskRequest request = CreateTaskRequest.builder()
            .title("测试任务")
            .description("这是一个测试任务")
            .priority(TaskPriority.HIGH)
            .build();
        
        // When
        Task task = taskService.createTask(request);
        
        // Then
        assertThat(task).isNotNull();
        assertThat(task.getTitle()).isEqualTo("测试任务");
        assertThat(task.getStatus()).isEqualTo(TaskStatus.TODO);
    }
    
    @Test
    @DisplayName("应该能够更新任务状态")
    void shouldUpdateTaskStatus() {
        // Given
        Task task = createTestTask();
        
        // When
        taskService.updateTaskStatus(task.getId(), TaskStatus.IN_PROGRESS);
        
        // Then
        Task updatedTask = taskService.getTaskById(task.getId());
        assertThat(updatedTask.getStatus()).isEqualTo(TaskStatus.IN_PROGRESS);
    }
}
```

## 测试数据管理

### 测试数据分类
1. **基础数据**: 用户、部门、角色等主数据
2. **业务数据**: 任务、工作流、评价等业务数据
3. **配置数据**: 系统参数、字典数据等配置
4. **测试专用数据**: 专门用于测试的模拟数据

### 测试数据准备脚本
```sql
-- 测试用户数据
INSERT INTO users (id, username, password, full_name, email, status) VALUES
('1', 'admin', '$2a$10$...', '系统管理员', '<EMAIL>', 'ACTIVE'),
('2', 'user1', '$2a$10$...', '张三', '<EMAIL>', 'ACTIVE'),
('3', 'user2', '$2a$10$...', '李四', '<EMAIL>', 'ACTIVE');

-- 测试任务数据
INSERT INTO tasks (id, title, description, status, priority, creator_id, assignee_id) VALUES
('1', '开发用户管理模块', '实现用户的增删改查功能', 'IN_PROGRESS', 'HIGH', '1', '2'),
('2', '设计数据库表结构', '设计系统所需的数据库表', 'DONE', 'MEDIUM', '1', '3'),
('3', '编写API文档', '编写系统API接口文档', 'TODO', 'LOW', '2', '3');

-- 测试评价数据
INSERT INTO task_evaluations (id, task_id, evaluator_id, evaluatee_id, overall_score, content) VALUES
('1', '2', '1', '3', 85, '任务完成质量很好，按时交付'),
('2', '1', '2', '2', 78, '功能实现正确，但代码质量有待提升');
```

## 测试执行计划

### 测试阶段安排
```
阶段1: 单元测试 (1-2周)
├── 后端单元测试
├── 前端单元测试
└── 代码覆盖率检查

阶段2: 集成测试 (1周)
├── 模块间集成测试
├── API接口测试
└── 数据库集成测试

阶段3: 系统测试 (2周)
├── 功能测试
├── 性能测试
├── 安全测试
└── 兼容性测试

阶段4: 用户验收测试 (1周)
├── 业务场景测试
├── 用户体验测试
└── 验收标准确认
```

### 测试通过标准
- **单元测试覆盖率**: ≥ 80%
- **集成测试通过率**: ≥ 95%
- **功能测试通过率**: ≥ 98%
- **性能测试达标率**: ≥ 90%
- **安全测试通过率**: 100%
- **用户验收满意度**: ≥ 4.0/5.0

## 缺陷管理

### 缺陷分级
- **P0 - 阻塞**: 系统无法使用，影响核心功能
- **P1 - 严重**: 影响主要功能，有替代方案
- **P2 - 一般**: 影响次要功能，不影响主流程
- **P3 - 轻微**: 界面问题，不影响功能使用

### 缺陷处理流程
```
发现缺陷 → 记录缺陷 → 分配处理 → 修复验证 → 关闭缺陷
    ↓         ↓         ↓         ↓         ↓
  测试人员   测试人员   开发人员   测试人员   测试人员
```

## 测试报告模板

### 测试执行报告
```
测试项目: WTMS工作任务管理系统
测试版本: v1.0.0
测试时间: 2024-01-01 ~ 2024-01-15
测试人员: 测试团队

测试概况:
- 计划测试用例: 150个
- 执行测试用例: 148个
- 通过测试用例: 145个
- 失败测试用例: 3个
- 阻塞测试用例: 0个

测试覆盖率:
- 功能覆盖率: 98%
- 代码覆盖率: 85%
- 需求覆盖率: 100%

缺陷统计:
- P0缺陷: 0个
- P1缺陷: 1个
- P2缺陷: 3个
- P3缺陷: 5个

测试结论: 系统基本满足上线要求，建议修复P1和P2缺陷后发布。
```

### 性能测试报告
```
测试场景: 用户登录并发测试
并发用户数: 500
测试时长: 10分钟
总请求数: 15000
成功请求数: 14985
失败请求数: 15
成功率: 99.9%
平均响应时间: 1.2秒
90%响应时间: 1.8秒
95%响应时间: 2.1秒
最大响应时间: 3.5秒

结论: 系统在500并发用户下表现良好，满足性能要求。
```

## 持续集成测试

### Jenkins Pipeline配置
```groovy
pipeline {
    agent any

    stages {
        stage('代码检出') {
            steps {
                git 'https://github.com/your-org/wtms.git'
            }
        }

        stage('后端测试') {
            steps {
                dir('wtms-backend') {
                    sh './mvnw clean test'
                    publishTestResults testResultsPattern: 'target/surefire-reports/*.xml'
                    publishCoverage adapters: [jacocoAdapter('target/site/jacoco/jacoco.xml')]
                }
            }
        }

        stage('前端测试') {
            steps {
                dir('wtms-frontend') {
                    sh 'npm install'
                    sh 'npm run test:unit'
                    sh 'npm run test:e2e'
                    publishTestResults testResultsPattern: 'test-results/*.xml'
                }
            }
        }

        stage('构建镜像') {
            steps {
                sh 'docker-compose build'
            }
        }

        stage('部署测试环境') {
            steps {
                sh 'docker-compose -f docker-compose.test.yml up -d'
            }
        }

        stage('接口测试') {
            steps {
                sh 'newman run postman/WTMS_API_Tests.json'
            }
        }
    }

    post {
        always {
            sh 'docker-compose -f docker-compose.test.yml down'
        }
        success {
            emailext (
                subject: "WTMS测试通过 - Build ${env.BUILD_NUMBER}",
                body: "所有测试已通过，可以进行部署。",
                to: "<EMAIL>"
            )
        }
        failure {
            emailext (
                subject: "WTMS测试失败 - Build ${env.BUILD_NUMBER}",
                body: "测试失败，请检查构建日志。",
                to: "<EMAIL>"
            )
        }
    }
}
```

### GitHub Actions配置
```yaml
name: WTMS CI/CD

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test123456
          MYSQL_DATABASE: wtms_test
        options: >-
          --health-cmd="mysqladmin ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

      redis:
        image: redis:7.0
        options: >-
          --health-cmd="redis-cli ping"
          --health-interval=10s
          --health-timeout=5s
          --health-retries=3

    steps:
    - uses: actions/checkout@v3

    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        java-version: '17'
        distribution: 'temurin'

    - name: Set up Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '18'

    - name: Cache Maven dependencies
      uses: actions/cache@v3
      with:
        path: ~/.m2
        key: ${{ runner.os }}-m2-${{ hashFiles('**/pom.xml') }}

    - name: Run backend tests
      run: |
        cd wtms-backend
        ./mvnw clean test

    - name: Run frontend tests
      run: |
        cd wtms-frontend
        npm install
        npm run test:unit

    - name: Upload coverage reports
      uses: codecov/codecov-action@v3
      with:
        files: ./wtms-backend/target/site/jacoco/jacoco.xml,./wtms-frontend/coverage/lcov.info
```

---

**文档维护**: WTMS测试团队
**版本**: v1.0.0
**更新时间**: 2024年1月
