package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 用户实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("users")
@Schema(description = "用户实体")
public class User implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "用户ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "用户名")
    @TableField("username")
    private String username;

    @Schema(description = "邮箱")
    @TableField("email")
    private String email;

    @Schema(description = "手机号")
    @TableField("phone")
    private String phone;

    @Schema(description = "密码哈希")
    @TableField("password_hash")
    @JsonIgnore
    private String passwordHash;

    @Schema(description = "密码盐值")
    @TableField("salt")
    @JsonIgnore
    private String salt;

    @Schema(description = "姓名")
    @TableField("full_name")
    private String fullName;

    @Schema(description = "头像URL")
    @TableField("avatar_url")
    private String avatarUrl;

    @Schema(description = "部门ID")
    @TableField("department_id")
    private String departmentId;

    @Schema(description = "角色ID")
    @TableField("role_id")
    private String roleId;

    @Schema(description = "工号")
    @TableField("employee_id")
    private String employeeId;

    @Schema(description = "入职日期")
    @TableField("hire_date")
    private LocalDate hireDate;

    @Schema(description = "状态")
    @TableField("status")
    private String status;

    @Schema(description = "最后登录时间")
    @TableField("last_login_at")
    private LocalDateTime lastLoginAt;

    @Schema(description = "登录次数")
    @TableField("login_count")
    private Integer loginCount;

    @Schema(description = "用户设置")
    @TableField("settings")
    private String settings;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "部门信息")
    @TableField(exist = false)
    private Department department;

    @Schema(description = "角色信息")
    @TableField(exist = false)
    private Role role;

    /**
     * 用户状态枚举
     */
    public enum Status {
        ACTIVE("active", "活跃"),
        INACTIVE("inactive", "停用"),
        LOCKED("locked", "锁定");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return ACTIVE;
        }
    }

    /**
     * 检查用户是否活跃
     */
    public boolean isActive() {
        return Status.ACTIVE.getCode().equals(this.status);
    }

    /**
     * 检查用户是否被锁定
     */
    public boolean isLocked() {
        return Status.LOCKED.getCode().equals(this.status);
    }

    /**
     * 检查用户是否被停用
     */
    public boolean isInactive() {
        return Status.INACTIVE.getCode().equals(this.status);
    }
}
