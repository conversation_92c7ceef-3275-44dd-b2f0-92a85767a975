package com.wtms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wtms.entity.QualityStatistics;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Map;

/**
 * 质量统计服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface QualityStatisticsService {

    /**
     * 创建质量统计
     *
     * @param statistics 质量统计
     * @return 质量统计
     */
    QualityStatistics createStatistics(QualityStatistics statistics);

    /**
     * 更新质量统计
     *
     * @param statisticsId 统计ID
     * @param statistics 质量统计
     * @return 质量统计
     */
    QualityStatistics updateStatistics(String statisticsId, QualityStatistics statistics);

    /**
     * 删除质量统计
     *
     * @param statisticsId 统计ID
     */
    void deleteStatistics(String statisticsId);

    /**
     * 根据ID获取质量统计
     *
     * @param statisticsId 统计ID
     * @return 质量统计
     */
    QualityStatistics getStatisticsById(String statisticsId);

    /**
     * 根据统计对象ID获取统计列表
     *
     * @param targetId 统计对象ID
     * @return 统计列表
     */
    List<QualityStatistics> getStatisticsByTargetId(String targetId);

    /**
     * 根据统计对象类型获取统计列表
     *
     * @param targetType 统计对象类型
     * @return 统计列表
     */
    List<QualityStatistics> getStatisticsByTargetType(String targetType);

    /**
     * 根据统计周期获取统计列表
     *
     * @param periodType 统计周期
     * @return 统计列表
     */
    List<QualityStatistics> getStatisticsByPeriodType(String periodType);

    /**
     * 根据统计对象和周期获取统计列表
     *
     * @param targetId 统计对象ID
     * @param periodType 统计周期
     * @return 统计列表
     */
    List<QualityStatistics> getStatisticsByTargetAndPeriod(String targetId, String periodType);

    /**
     * 根据时间范围获取统计列表
     *
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计列表
     */
    List<QualityStatistics> getStatisticsByTimeRange(LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 根据统计对象和时间范围获取统计列表
     *
     * @param targetId 统计对象ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计列表
     */
    List<QualityStatistics> getStatisticsByTargetAndTimeRange(String targetId, LocalDateTime startTime, LocalDateTime endTime);

    /**
     * 获取最新统计
     *
     * @param targetId 统计对象ID
     * @param targetType 统计对象类型
     * @return 最新统计
     */
    QualityStatistics getLatestStatistics(String targetId, String targetType);

    /**
     * 获取指定周期的统计
     *
     * @param targetId 统计对象ID
     * @param periodType 统计周期
     * @param periodStart 周期开始时间
     * @return 质量统计
     */
    QualityStatistics getStatisticsByTargetAndPeriodStart(String targetId, String periodType, LocalDateTime periodStart);

    /**
     * 搜索质量统计
     *
     * @param targetId 统计对象ID
     * @param targetType 统计对象类型
     * @param periodType 统计周期
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    IPage<QualityStatistics> searchStatistics(String targetId, String targetType, String periodType, 
                                              LocalDateTime startTime, LocalDateTime endTime, 
                                              Integer page, Integer size);

    /**
     * 生成用户质量统计
     *
     * @param userId 用户ID
     * @param periodType 统计周期
     * @param periodStart 周期开始时间
     * @param periodEnd 周期结束时间
     * @return 是否成功
     */
    boolean generateUserStatistics(String userId, String periodType, LocalDateTime periodStart, LocalDateTime periodEnd);

    /**
     * 生成任务质量统计
     *
     * @param taskId 任务ID
     * @param periodType 统计周期
     * @param periodStart 周期开始时间
     * @param periodEnd 周期结束时间
     * @return 是否成功
     */
    boolean generateTaskStatistics(String taskId, String periodType, LocalDateTime periodStart, LocalDateTime periodEnd);

    /**
     * 生成部门质量统计
     *
     * @param departmentId 部门ID
     * @param periodType 统计周期
     * @param periodStart 周期开始时间
     * @param periodEnd 周期结束时间
     * @return 是否成功
     */
    boolean generateDepartmentStatistics(String departmentId, String periodType, LocalDateTime periodStart, LocalDateTime periodEnd);

    /**
     * 生成组织质量统计
     *
     * @param organizationId 组织ID
     * @param periodType 统计周期
     * @param periodStart 周期开始时间
     * @param periodEnd 周期结束时间
     * @return 是否成功
     */
    boolean generateOrganizationStatistics(String organizationId, String periodType, LocalDateTime periodStart, LocalDateTime periodEnd);

    /**
     * 批量生成统计
     *
     * @param targetType 统计对象类型
     * @param periodType 统计周期
     * @param periodStart 周期开始时间
     * @param periodEnd 周期结束时间
     * @return 生成数量
     */
    int batchGenerateStatistics(String targetType, String periodType, LocalDateTime periodStart, LocalDateTime periodEnd);

    /**
     * 更新统计状态
     *
     * @param statisticsId 统计ID
     * @param status 状态
     * @return 是否成功
     */
    boolean updateStatisticsStatus(String statisticsId, String status);

    /**
     * 批量删除统计
     *
     * @param statisticsIds 统计ID列表
     */
    void batchDeleteStatistics(List<String> statisticsIds);

    /**
     * 批量更新统计状态
     *
     * @param statisticsIds 统计ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<String> statisticsIds, String status);

    /**
     * 获取质量趋势
     *
     * @param targetId 统计对象ID
     * @param targetType 统计对象类型
     * @param periodType 统计周期
     * @param periods 周期数量
     * @return 质量趋势
     */
    List<QualityStatistics> getQualityTrend(String targetId, String targetType, String periodType, Integer periods);

    /**
     * 获取质量排名
     *
     * @param targetType 统计对象类型
     * @param periodType 统计周期
     * @param periodStart 周期开始时间
     * @param periodEnd 周期结束时间
     * @param limit 数量限制
     * @return 质量排名
     */
    List<QualityStatistics> getQualityRanking(String targetType, String periodType, LocalDateTime periodStart, 
                                              LocalDateTime periodEnd, Integer limit);

    /**
     * 获取质量对比
     *
     * @param targetIds 统计对象ID列表
     * @param targetType 统计对象类型
     * @param periodType 统计周期
     * @param periodStart 周期开始时间
     * @param periodEnd 周期结束时间
     * @return 质量对比
     */
    List<QualityStatistics> getQualityComparison(List<String> targetIds, String targetType, String periodType, 
                                                 LocalDateTime periodStart, LocalDateTime periodEnd);

    /**
     * 获取质量分布
     *
     * @param targetType 统计对象类型
     * @param periodType 统计周期
     * @param periodStart 周期开始时间
     * @param periodEnd 周期结束时间
     * @return 质量分布
     */
    List<Map<String, Object>> getQualityDistribution(String targetType, String periodType, 
                                                     LocalDateTime periodStart, LocalDateTime periodEnd);

    /**
     * 获取质量改进统计
     *
     * @param targetType 统计对象类型
     * @param periods 周期数量
     * @return 质量改进统计
     */
    List<Map<String, Object>> getQualityImprovementStatistics(String targetType, Integer periods);

    /**
     * 获取质量预警统计
     *
     * @param targetType 统计对象类型
     * @param threshold 预警阈值
     * @return 质量预警统计
     */
    List<QualityStatistics> getQualityWarningStatistics(String targetType, Double threshold);

    /**
     * 获取优秀质量统计
     *
     * @param minScore 最低分数
     * @param limit 数量限制
     * @return 优秀质量统计
     */
    List<QualityStatistics> getExcellentQualityStatistics(Double minScore, Integer limit);

    /**
     * 获取需要改进的质量统计
     *
     * @param maxScore 最高分数
     * @param limit 数量限制
     * @return 需要改进的质量统计
     */
    List<QualityStatistics> getImprovementNeededStatistics(Double maxScore, Integer limit);

    /**
     * 获取最新统计记录
     *
     * @param limit 数量限制
     * @return 最新统计记录
     */
    List<QualityStatistics> getRecentStatistics(Integer limit);

    /**
     * 清理过期统计
     *
     * @param days 保留天数
     * @return 清理数量
     */
    int cleanupExpiredStatistics(Integer days);

    /**
     * 检查统计是否存在
     *
     * @param targetId 统计对象ID
     * @param periodType 统计周期
     * @param periodStart 周期开始时间
     * @return 是否存在
     */
    boolean existsByTargetAndPeriod(String targetId, String periodType, LocalDateTime periodStart);

    /**
     * 统计记录数量
     *
     * @return 总数量
     */
    int countAllStatistics();

    /**
     * 根据统计对象类型统计数量
     *
     * @param targetType 统计对象类型
     * @return 数量
     */
    int countStatisticsByTargetType(String targetType);

    /**
     * 根据统计周期统计数量
     *
     * @param periodType 统计周期
     * @return 数量
     */
    int countStatisticsByPeriodType(String periodType);

    /**
     * 根据统计对象统计数量
     *
     * @param targetId 统计对象ID
     * @return 数量
     */
    int countStatisticsByTargetId(String targetId);

    /**
     * 获取统计周期列表
     *
     * @return 统计周期列表
     */
    List<String> getDistinctPeriodTypes();

    /**
     * 获取统计对象类型列表
     *
     * @return 统计对象类型列表
     */
    List<String> getDistinctTargetTypes();

    /**
     * 获取统计时间范围
     *
     * @param targetId 统计对象ID
     * @param targetType 统计对象类型
     * @return 统计时间范围
     */
    Map<String, Object> getStatisticsTimeRange(String targetId, String targetType);

    /**
     * 获取质量波动统计
     *
     * @param targetId 统计对象ID
     * @param periods 周期数量
     * @return 质量波动统计
     */
    List<Map<String, Object>> getQualityVolatilityStatistics(String targetId, Integer periods);

    /**
     * 获取质量稳定性统计
     *
     * @param targetId 统计对象ID
     * @param periods 周期数量
     * @return 质量稳定性统计
     */
    Map<String, Object> getQualityStabilityStatistics(String targetId, Integer periods);

    /**
     * 获取统计概览
     *
     * @return 统计概览
     */
    Map<String, Object> getStatisticsOverview();

    /**
     * 获取统计汇总
     *
     * @param targetType 统计对象类型
     * @param periodType 统计周期
     * @param periodStart 周期开始时间
     * @param periodEnd 周期结束时间
     * @return 统计汇总
     */
    Map<String, Object> getStatisticsSummary(String targetType, String periodType, 
                                            LocalDateTime periodStart, LocalDateTime periodEnd);

    /**
     * 自动生成定期统计
     *
     * @param periodType 统计周期
     * @return 生成数量
     */
    int autoGeneratePeriodicStatistics(String periodType);

    /**
     * 刷新统计数据
     *
     * @param targetId 统计对象ID
     * @param targetType 统计对象类型
     * @return 是否成功
     */
    boolean refreshStatistics(String targetId, String targetType);

    /**
     * 重新计算统计
     *
     * @param statisticsId 统计ID
     * @return 是否成功
     */
    boolean recalculateStatistics(String statisticsId);
}
