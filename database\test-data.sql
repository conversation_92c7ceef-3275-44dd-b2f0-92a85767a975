-- WTMS 测试数据脚本
-- 用于系统测试和演示的基础数据

-- 清理现有数据（测试环境使用）
SET FOREIGN_KEY_CHECKS = 0;
TRUNCATE TABLE task_evaluation_dimensions;
TRUNCATE TABLE task_evaluations;
TRUNCATE TABLE workflow_instances;
TRUNCATE TABLE workflow_tasks;
TRUNCATE TABLE workflow_definitions;
TRUNCATE TABLE task_comments;
TRUNCATE TABLE task_attachments;
TRUNCATE TABLE task_participants;
TRUNCATE TABLE tasks;
TRUNCATE TABLE user_roles;
TRUNCATE TABLE role_permissions;
TRUNCATE TABLE permissions;
TRUNCATE TABLE roles;
TRUNCATE TABLE users;
TRUNCATE TABLE departments;
SET FOREIGN_KEY_CHECKS = 1;

-- 插入部门数据
INSERT INTO departments (id, name, parent_id, level, description, created_at, updated_at) VALUES
('1', '技术部', NULL, 1, '负责系统开发和技术支持', NOW(), NOW()),
('2', '产品部', NULL, 1, '负责产品规划和需求管理', NOW(), NOW()),
('3', '测试部', NULL, 1, '负责质量保证和测试工作', NOW(), NOW()),
('4', '运营部', NULL, 1, '负责系统运营和用户支持', NOW(), NOW()),
('5', '前端开发组', '1', 2, '负责前端界面开发', NOW(), NOW()),
('6', '后端开发组', '1', 2, '负责后端服务开发', NOW(), NOW()),
('7', '产品设计组', '2', 2, '负责产品设计和用户体验', NOW(), NOW()),
('8', '功能测试组', '3', 2, '负责功能测试和用户验收', NOW(), NOW());

-- 插入权限数据
INSERT INTO permissions (id, name, code, description, resource, action, created_at, updated_at) VALUES
('1', '查看用户', 'user:view', '查看用户信息', 'user', 'view', NOW(), NOW()),
('2', '创建用户', 'user:create', '创建新用户', 'user', 'create', NOW(), NOW()),
('3', '编辑用户', 'user:edit', '编辑用户信息', 'user', 'edit', NOW(), NOW()),
('4', '删除用户', 'user:delete', '删除用户', 'user', 'delete', NOW(), NOW()),
('5', '查看任务', 'task:view', '查看任务信息', 'task', 'view', NOW(), NOW()),
('6', '创建任务', 'task:create', '创建新任务', 'task', 'create', NOW(), NOW()),
('7', '编辑任务', 'task:edit', '编辑任务信息', 'task', 'edit', NOW(), NOW()),
('8', '删除任务', 'task:delete', '删除任务', 'task', 'delete', NOW(), NOW()),
('9', '分配任务', 'task:assign', '分配任务给他人', 'task', 'assign', NOW(), NOW()),
('10', '查看工作流', 'workflow:view', '查看工作流', 'workflow', 'view', NOW(), NOW()),
('11', '设计工作流', 'workflow:design', '设计工作流', 'workflow', 'design', NOW(), NOW()),
('12', '执行工作流', 'workflow:execute', '执行工作流', 'workflow', 'execute', NOW(), NOW()),
('13', '查看评价', 'evaluation:view', '查看质量评价', 'evaluation', 'view', NOW(), NOW()),
('14', '创建评价', 'evaluation:create', '创建质量评价', 'evaluation', 'create', NOW(), NOW()),
('15', '系统管理', 'system:admin', '系统管理权限', 'system', 'admin', NOW(), NOW());

-- 插入角色数据
INSERT INTO roles (id, name, code, description, created_at, updated_at) VALUES
('1', '超级管理员', 'SUPER_ADMIN', '拥有系统所有权限', NOW(), NOW()),
('2', '部门经理', 'DEPT_MANAGER', '部门管理权限', NOW(), NOW()),
('3', '项目经理', 'PROJECT_MANAGER', '项目管理权限', NOW(), NOW()),
('4', '开发人员', 'DEVELOPER', '开发人员权限', NOW(), NOW()),
('5', '测试人员', 'TESTER', '测试人员权限', NOW(), NOW()),
('6', '普通用户', 'USER', '基础用户权限', NOW(), NOW());

-- 插入角色权限关联数据
INSERT INTO role_permissions (role_id, permission_id) VALUES
-- 超级管理员拥有所有权限
('1', '1'), ('1', '2'), ('1', '3'), ('1', '4'), ('1', '5'), ('1', '6'), ('1', '7'), ('1', '8'), ('1', '9'), ('1', '10'), ('1', '11'), ('1', '12'), ('1', '13'), ('1', '14'), ('1', '15'),
-- 部门经理权限
('2', '1'), ('2', '2'), ('2', '3'), ('2', '5'), ('2', '6'), ('2', '7'), ('2', '8'), ('2', '9'), ('2', '10'), ('2', '12'), ('2', '13'), ('2', '14'),
-- 项目经理权限
('3', '1'), ('3', '5'), ('3', '6'), ('3', '7'), ('3', '8'), ('3', '9'), ('3', '10'), ('3', '11'), ('3', '12'), ('3', '13'), ('3', '14'),
-- 开发人员权限
('4', '1'), ('4', '5'), ('4', '6'), ('4', '7'), ('4', '10'), ('4', '12'), ('4', '13'), ('4', '14'),
-- 测试人员权限
('5', '1'), ('5', '5'), ('5', '6'), ('5', '7'), ('5', '13'), ('5', '14'),
-- 普通用户权限
('6', '1'), ('6', '5'), ('6', '6'), ('6', '13'), ('6', '14');

-- 插入用户数据（密码都是对应用户名+123456，如admin123456）
INSERT INTO users (id, username, password, full_name, email, phone, avatar, department_id, status, last_login_at, created_at, updated_at) VALUES
('1', 'admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/fG', '系统管理员', '<EMAIL>', '13800000001', '/uploads/avatars/admin.jpg', '1', 'ACTIVE', NOW(), NOW(), NOW()),
('2', 'zhangsan', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/fG', '张三', '<EMAIL>', '13800000002', '/uploads/avatars/zhangsan.jpg', '6', 'ACTIVE', NOW(), NOW(), NOW()),
('3', 'lisi', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/fG', '李四', '<EMAIL>', '13800000003', '/uploads/avatars/lisi.jpg', '5', 'ACTIVE', NOW(), NOW(), NOW()),
('4', 'wangwu', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/fG', '王五', '<EMAIL>', '13800000004', '/uploads/avatars/wangwu.jpg', '8', 'ACTIVE', NOW(), NOW(), NOW()),
('5', 'zhaoliu', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/fG', '赵六', '<EMAIL>', '13800000005', '/uploads/avatars/zhaoliu.jpg', '7', 'ACTIVE', NOW(), NOW(), NOW()),
('6', 'sunqi', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/fG', '孙七', '<EMAIL>', '13800000006', '/uploads/avatars/sunqi.jpg', '2', 'ACTIVE', NOW(), NOW(), NOW()),
('7', 'zhouba', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/fG', '周八', '<EMAIL>', '13800000007', '/uploads/avatars/zhouba.jpg', '4', 'ACTIVE', NOW(), NOW(), NOW()),
('8', 'wujiu', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9tYjKUiFjFO2/fG', '吴九', '<EMAIL>', '13800000008', '/uploads/avatars/wujiu.jpg', '3', 'ACTIVE', NOW(), NOW(), NOW());

-- 插入用户角色关联数据
INSERT INTO user_roles (user_id, role_id) VALUES
('1', '1'), -- admin: 超级管理员
('2', '4'), -- zhangsan: 开发人员
('3', '4'), -- lisi: 开发人员
('4', '5'), -- wangwu: 测试人员
('5', '3'), -- zhaoliu: 项目经理
('6', '2'), -- sunqi: 部门经理
('7', '6'), -- zhouba: 普通用户
('8', '5'); -- wujiu: 测试人员

-- 插入任务数据
INSERT INTO tasks (id, title, description, status, priority, type, creator_id, assignee_id, estimated_hours, actual_hours, progress, start_date, due_date, tags, created_at, updated_at) VALUES
('1', '开发用户管理模块', '实现用户的增删改查功能，包括用户列表、用户详情、用户创建、用户编辑、用户删除等功能', 'IN_PROGRESS', 'HIGH', 'DEVELOPMENT', '1', '2', 40, 20, 50, '2024-01-01', '2024-01-15', '["后端", "用户管理", "CRUD"]', NOW(), NOW()),
('2', '设计数据库表结构', '设计系统所需的数据库表结构，包括用户表、任务表、工作流表等', 'DONE', 'HIGH', 'DESIGN', '1', '3', 16, 18, 100, '2023-12-20', '2024-01-05', '["数据库", "设计", "架构"]', NOW(), NOW()),
('3', '前端界面设计', '设计系统的前端界面原型，包括任务管理、工作流设计等页面', 'DONE', 'MEDIUM', 'DESIGN', '6', '5', 24, 26, 100, '2023-12-25', '2024-01-10', '["前端", "UI设计", "原型"]', NOW(), NOW()),
('4', '工作流引擎开发', '开发可视化工作流引擎，支持拖拽设计和流程执行', 'IN_PROGRESS', 'HIGH', 'DEVELOPMENT', '5', '2', 60, 35, 60, '2024-01-05', '2024-01-25', '["工作流", "引擎", "可视化"]', NOW(), NOW()),
('5', '任务评论功能', '实现任务评论和附件上传功能', 'REVIEW', 'MEDIUM', 'DEVELOPMENT', '2', '3', 20, 22, 90, '2024-01-08', '2024-01-18', '["评论", "附件", "协作"]', NOW(), NOW()),
('6', '质量评价系统', '开发任务质量评价系统，支持多维度评价', 'TODO', 'MEDIUM', 'DEVELOPMENT', '5', '2', 32, 0, 0, '2024-01-15', '2024-02-01', '["评价", "质量", "统计"]', NOW(), NOW()),
('7', '系统测试', '对整个系统进行全面测试，包括功能测试、性能测试等', 'TODO', 'HIGH', 'TESTING', '8', '4', 40, 0, 0, '2024-01-20', '2024-02-05', '["测试", "质量保证"]', NOW(), NOW()),
('8', '用户手册编写', '编写系统用户使用手册和管理员手册', 'TODO', 'LOW', 'DOCUMENTATION', '7', '7', 16, 0, 0, '2024-01-25', '2024-02-10', '["文档", "手册", "说明"]', NOW(), NOW());

-- 插入任务参与者数据
INSERT INTO task_participants (task_id, user_id) VALUES
('1', '3'), ('1', '4'), -- 用户管理模块：李四、王五参与
('4', '3'), ('4', '5'), -- 工作流引擎：李四、赵六参与
('5', '2'), ('5', '4'), -- 任务评论功能：张三、王五参与
('7', '4'), ('7', '8'); -- 系统测试：王五、吴九参与

-- 插入任务评论数据
INSERT INTO task_comments (id, task_id, user_id, content, parent_id, created_at, updated_at) VALUES
('1', '1', '2', '已完成用户列表页面的开发，正在进行用户详情页面的开发', NULL, NOW(), NOW()),
('2', '1', '3', '数据库表结构已经设计完成，可以开始接口开发了', NULL, NOW(), NOW()),
('3', '1', '1', '进度不错，注意代码质量和单元测试', NULL, NOW(), NOW()),
('4', '1', '2', '好的，会注意代码质量', '3', NOW(), NOW()),
('5', '2', '3', '数据库表结构设计已完成，已通过评审', NULL, NOW(), NOW()),
('6', '4', '2', '工作流设计器的基础框架已搭建完成', NULL, NOW(), NOW()),
('7', '4', '5', '需要确认一下节点类型的定义', NULL, NOW(), NOW()),
('8', '5', '3', '评论功能开发完成，正在进行测试', NULL, NOW(), NOW());

-- 插入工作流定义数据
INSERT INTO workflow_definitions (id, name, description, category, version, definition_json, status, creator_id, created_at, updated_at) VALUES
('1', '任务审批流程', '用于任务创建和变更的审批流程', 'APPROVAL', '1.0', '{"nodes":[{"id":"start","type":"start","name":"开始","x":100,"y":100},{"id":"apply","type":"userTask","name":"申请","x":250,"y":100,"properties":{"assignee":"${applicant}"}},{"id":"review","type":"userTask","name":"审核","x":400,"y":100,"properties":{"assignee":"${manager}"}},{"id":"approve","type":"userTask","name":"批准","x":550,"y":100,"properties":{"assignee":"${director}"}},{"id":"end","type":"end","name":"结束","x":700,"y":100}],"connections":[{"from":"start","to":"apply"},{"from":"apply","to":"review"},{"from":"review","to":"approve","condition":"${approved}"},{"from":"approve","to":"end"},{"from":"review","to":"end","condition":"${!approved}"}]}', 'ACTIVE', '1', NOW(), NOW()),
('2', '请假审批流程', '员工请假申请审批流程', 'APPROVAL', '1.0', '{"nodes":[{"id":"start","type":"start","name":"开始","x":100,"y":100},{"id":"apply","type":"userTask","name":"请假申请","x":250,"y":100},{"id":"manager_review","type":"userTask","name":"直属经理审核","x":400,"y":100},{"id":"hr_review","type":"userTask","name":"HR审核","x":550,"y":100},{"id":"end","type":"end","name":"结束","x":700,"y":100}],"connections":[{"from":"start","to":"apply"},{"from":"apply","to":"manager_review"},{"from":"manager_review","to":"hr_review","condition":"${days > 3}"},{"from":"manager_review","to":"end","condition":"${days <= 3 && approved}"},{"from":"hr_review","to":"end"}]}', 'ACTIVE', '5', NOW(), NOW());

-- 插入工作流实例数据
INSERT INTO workflow_instances (id, workflow_id, business_key, status, variables_json, creator_id, created_at, updated_at) VALUES
('1', '1', 'TASK_001', 'RUNNING', '{"applicant":"zhangsan","manager":"sunqi","director":"admin","taskId":"1"}', '2', NOW(), NOW()),
('2', '2', 'LEAVE_001', 'COMPLETED', '{"applicant":"lisi","manager":"sunqi","days":2,"approved":true}', '3', NOW(), NOW());

-- 插入工作流任务数据
INSERT INTO workflow_tasks (id, instance_id, node_id, name, assignee_id, status, variables_json, created_at, updated_at, completed_at) VALUES
('1', '1', 'apply', '申请', '2', 'COMPLETED', '{"comment":"申请创建用户管理模块任务"}', NOW(), NOW(), NOW()),
('2', '1', 'review', '审核', '6', 'ACTIVE', '{}', NOW(), NOW(), NULL),
('3', '2', 'apply', '请假申请', '3', 'COMPLETED', '{"days":2,"reason":"个人事务"}', NOW(), NOW(), NOW()),
('4', '2', 'manager_review', '直属经理审核', '6', 'COMPLETED', '{"approved":true,"comment":"同意请假"}', NOW(), NOW(), NOW());

-- 插入任务评价数据
INSERT INTO task_evaluations (id, task_id, evaluator_id, evaluatee_id, evaluation_type, evaluation_stage, overall_score, quality_score, efficiency_score, communication_score, innovation_score, teamwork_score, evaluation_content, strengths, improvements, evaluation_tags, weight, is_anonymous, is_public, status, deadline, created_at, updated_at) VALUES
('1', '2', '1', '3', 'SUPERVISOR', 'COMPLETION', 88, 90, 85, 88, 82, 90, '数据库设计工作完成得很好，表结构设计合理，考虑了扩展性和性能优化。按时完成任务，质量较高。', '设计思路清晰，考虑周全，文档完整', '可以在性能优化方面进一步提升', '数据库,设计,优秀', 1.0, false, true, 'PUBLISHED', NULL, NOW(), NOW()),
('2', '3', '6', '5', 'SUPERVISOR', 'COMPLETION', 92, 95, 88, 90, 85, 92, '前端界面设计非常出色，用户体验良好，设计风格统一。创新性强，提出了很多好的设计理念。', '设计能力强，用户体验意识好，创新思维活跃', '可以在设计效率方面进一步提升', '前端,设计,创新', 1.0, false, true, 'PUBLISHED', NULL, NOW(), NOW()),
('3', '1', '2', '2', 'SELF', 'EXECUTION', 75, 78, 72, 80, 70, 75, '用户管理模块开发进展顺利，已完成大部分功能。代码质量还需要进一步提升，测试覆盖率有待加强。', '功能实现正确，进度控制良好', '代码质量和测试覆盖率需要提升', '开发,自评,改进', 0.3, false, true, 'SUBMITTED', NULL, NOW(), NOW()),
('4', '5', '4', '3', 'PEER', 'COMPLETION', 85, 88, 82, 85, 80, 88, '任务评论功能开发得很好，界面友好，功能完整。协作过程中沟通及时，配合度高。', '技术能力强，沟通配合好', '可以在代码优化方面继续努力', '评论,协作,同事评价', 0.5, false, true, 'PUBLISHED', NULL, NOW(), NOW());

-- 插入评价维度数据
INSERT INTO task_evaluation_dimensions (id, evaluation_id, dimension_name, dimension_description, score, max_score, weight, evaluation_content, evaluation_criteria) VALUES
('1', '1', '需求理解', '对需求的理解和把握程度', 90, 100, 0.2, '对数据库设计需求理解准确，把握到位', '能够准确理解需求，设计方案符合要求'),
('2', '1', '技术实现', '技术实现的质量和水平', 88, 100, 0.3, '技术实现水平较高，代码质量良好', '技术方案合理，实现质量高'),
('3', '1', '文档质量', '文档编写的完整性和质量', 85, 100, 0.2, '文档编写完整，说明清晰', '文档完整，格式规范，说明清楚'),
('4', '1', '时间管理', '任务完成的及时性', 90, 100, 0.3, '按时完成任务，时间管理良好', '能够按时完成任务，时间安排合理'),
('5', '2', '设计创新', '设计的创新性和独特性', 95, 100, 0.3, '设计具有创新性，用户体验优秀', '设计新颖，有创新点，用户体验好'),
('6', '2', '视觉效果', '界面视觉效果的质量', 92, 100, 0.25, '界面美观，视觉效果良好', '界面美观，色彩搭配合理'),
('7', '2', '交互设计', '用户交互设计的合理性', 88, 100, 0.25, '交互设计合理，操作流畅', '交互逻辑清晰，操作便捷'),
('8', '2', '规范遵循', '对设计规范的遵循程度', 90, 100, 0.2, '严格遵循设计规范', '遵循公司设计规范和行业标准');

-- 更新序列值（如果使用自增ID）
-- ALTER TABLE departments AUTO_INCREMENT = 9;
-- ALTER TABLE users AUTO_INCREMENT = 9;
-- ALTER TABLE tasks AUTO_INCREMENT = 9;
-- ALTER TABLE task_comments AUTO_INCREMENT = 9;
-- ALTER TABLE task_evaluations AUTO_INCREMENT = 5;
-- ALTER TABLE task_evaluation_dimensions AUTO_INCREMENT = 9;

-- 提交事务
COMMIT;
