package com.wtms.service.impl;

import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.SecureUtil;
import com.wtms.common.exception.BusinessException;
import com.wtms.common.result.ResultCode;
import com.wtms.dto.request.LoginRequest;
import com.wtms.dto.response.LoginResponse;
import com.wtms.dto.response.UserInfoResponse;
import com.wtms.entity.User;
import com.wtms.mapper.UserMapper;
import com.wtms.security.JwtTokenProvider;
import com.wtms.service.AuthService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 认证服务实现
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class AuthServiceImpl implements AuthService {

    @Autowired
    private AuthenticationManager authenticationManager;

    @Autowired
    private UserMapper userMapper;

    @Autowired
    private PasswordEncoder passwordEncoder;

    @Autowired
    private JwtTokenProvider tokenProvider;

    @Autowired
    private RedisTemplate<String, Object> redisTemplate;

    private static final String TOKEN_BLACKLIST_PREFIX = "blacklist:token:";
    private static final String RESET_TOKEN_PREFIX = "reset:token:";

    @Override
    public LoginResponse login(LoginRequest loginRequest) {
        log.info("User login attempt: {}", loginRequest.getUsername());

        // 验证用户名和密码
        Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                        loginRequest.getUsername(),
                        loginRequest.getPassword()
                )
        );

        // 设置安全上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 获取用户信息
        User user = userMapper.findByUsernameWithRole(loginRequest.getUsername());
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 检查用户状态
        if (!user.isActive()) {
            if (user.isLocked()) {
                throw new BusinessException(ResultCode.USER_LOCKED);
            } else {
                throw new BusinessException(ResultCode.USER_DISABLED);
            }
        }

        // 生成JWT令牌
        String accessToken = tokenProvider.generateToken(authentication);
        String refreshToken = tokenProvider.generateRefreshToken(user.getUsername());

        // 更新用户最后登录时间
        userMapper.updateLastLoginTime(user.getId(), LocalDateTime.now());

        // 缓存刷新令牌
        redisTemplate.opsForValue().set(
                "refresh:token:" + user.getUsername(),
                refreshToken,
                7, TimeUnit.DAYS
        );

        log.info("User login successful: {}", loginRequest.getUsername());

        return LoginResponse.create(
                accessToken,
                refreshToken,
                tokenProvider.getTokenRemainingTime(accessToken) / 1000,
                user
        );
    }

    @Override
    public void logout(String token) {
        try {
            // 获取用户名
            String username = tokenProvider.getUsernameFromToken(token);
            
            // 将令牌加入黑名单
            long remainingTime = tokenProvider.getTokenRemainingTime(token);
            if (remainingTime > 0) {
                redisTemplate.opsForValue().set(
                        TOKEN_BLACKLIST_PREFIX + token,
                        username,
                        remainingTime,
                        TimeUnit.MILLISECONDS
                );
            }

            // 删除刷新令牌
            redisTemplate.delete("refresh:token:" + username);

            log.info("User logout successful: {}", username);
        } catch (Exception e) {
            log.error("Logout error", e);
        }
    }

    @Override
    public String refreshToken(String refreshToken) {
        if (!tokenProvider.validateToken(refreshToken) || !tokenProvider.isRefreshToken(refreshToken)) {
            throw new BusinessException(ResultCode.REFRESH_TOKEN_INVALID);
        }

        String username = tokenProvider.getUsernameFromToken(refreshToken);
        
        // 验证刷新令牌是否存在于缓存中
        String cachedToken = (String) redisTemplate.opsForValue().get("refresh:token:" + username);
        if (!refreshToken.equals(cachedToken)) {
            throw new BusinessException(ResultCode.REFRESH_TOKEN_INVALID);
        }

        // 获取用户信息
        User user = userMapper.findByUsernameWithRole(username);
        if (user == null || !user.isActive()) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 生成新的访问令牌
        String authorities = user.getRole() != null ? 
                String.join(",", user.getRole().getPermissionList()) : "";
        
        return tokenProvider.generateToken(username, authorities);
    }

    @Override
    public UserInfoResponse getCurrentUserInfo() {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null || !authentication.isAuthenticated()) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        User user = userMapper.findByUsernameWithRole(userDetails.getUsername());
        
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        return UserInfoResponse.from(user);
    }

    @Override
    public void changePassword(String oldPassword, String newPassword) {
        Authentication authentication = SecurityContextHolder.getContext().getAuthentication();
        if (authentication == null) {
            throw new BusinessException(ResultCode.UNAUTHORIZED);
        }

        UserDetails userDetails = (UserDetails) authentication.getPrincipal();
        User user = userMapper.findByUsernameWithRole(userDetails.getUsername());
        
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 验证旧密码
        if (!passwordEncoder.matches(oldPassword + user.getSalt(), user.getPasswordHash())) {
            throw new BusinessException(ResultCode.INVALID_CREDENTIALS.getCode(), "原密码错误");
        }

        // 生成新密码
        String salt = SecureUtil.md5(user.getUsername() + System.currentTimeMillis());
        String hashedPassword = passwordEncoder.encode(newPassword + salt);

        // 更新密码
        userMapper.updatePassword(user.getId(), hashedPassword, salt);

        log.info("Password changed for user: {}", user.getUsername());
    }

    @Override
    public void forgotPassword(String email) {
        User user = userMapper.findByEmail(email);
        if (user == null) {
            // 为了安全，即使用户不存在也返回成功
            log.warn("Forgot password request for non-existent email: {}", email);
            return;
        }

        // 生成重置令牌
        String resetToken = SecureUtil.md5(user.getEmail() + System.currentTimeMillis());
        
        // 缓存重置令牌（30分钟有效）
        redisTemplate.opsForValue().set(
                RESET_TOKEN_PREFIX + resetToken,
                user.getId(),
                30,
                TimeUnit.MINUTES
        );

        // TODO: 发送重置密码邮件
        log.info("Password reset token generated for user: {}", user.getUsername());
    }

    @Override
    public void resetPassword(String token, String newPassword) {
        String userId = (String) redisTemplate.opsForValue().get(RESET_TOKEN_PREFIX + token);
        if (StrUtil.isBlank(userId)) {
            throw new BusinessException(ResultCode.TOKEN_INVALID.getCode(), "重置令牌无效或已过期");
        }

        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 生成新密码
        String salt = SecureUtil.md5(user.getUsername() + System.currentTimeMillis());
        String hashedPassword = passwordEncoder.encode(newPassword + salt);

        // 更新密码
        userMapper.updatePassword(user.getId(), hashedPassword, salt);

        // 删除重置令牌
        redisTemplate.delete(RESET_TOKEN_PREFIX + token);

        log.info("Password reset successful for user: {}", user.getUsername());
    }
}
