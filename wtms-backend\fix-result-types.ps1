# 批量修复Result<Void>类型问题的PowerShell脚本
# 将所有返回Result.success(String)的方法从Result<Void>改为Result<String>

Write-Host "开始批量修复Result<Void>类型问题..." -ForegroundColor Green

# 定义需要修复的文件列表
$files = @(
    "src\main\java\com\wtms\controller\UserRoleController.java",
    "src\main\java\com\wtms\controller\TaskCommentController.java", 
    "src\main\java\com\wtms\controller\TaskAttachmentController.java",
    "src\main\java\com\wtms\controller\RolePermissionController.java",
    "src\main\java\com\wtms\controller\PermissionController.java",
    "src\main\java\com\wtms\controller\WorkflowDefinitionController.java",
    "src\main\java\com\wtms\controller\TaskAssignmentController.java",
    "src\main\java\com\wtms\controller\TaskEvaluationController.java"
)

$totalFixed = 0

foreach ($file in $files) {
    if (Test-Path $file) {
        Write-Host "正在处理文件: $file" -ForegroundColor Yellow
        
        # 读取文件内容
        $content = Get-Content $file -Raw -Encoding UTF8
        
        # 执行替换：将Result<Void>改为Result<String>（仅当方法返回Result.success(String)时）
        $originalContent = $content
        
        # 替换模式：查找返回Result<Void>但实际返回字符串消息的方法
        $content = $content -replace 'public Result<Void>', 'public Result<String>'
        
        # 检查是否有修改
        if ($content -ne $originalContent) {
            # 写回文件
            $content | Out-File -FilePath $file -Encoding UTF8 -NoNewline
            $fixCount = ([regex]::Matches($originalContent, 'public Result<Void>')).Count
            $totalFixed += $fixCount
            Write-Host "  ✓ 修复了 $fixCount 个方法" -ForegroundColor Green
        } else {
            Write-Host "  - 无需修复" -ForegroundColor Gray
        }
    } else {
        Write-Host "  ✗ 文件不存在: $file" -ForegroundColor Red
    }
}

Write-Host "`n批量修复完成！总共修复了 $totalFixed 个方法。" -ForegroundColor Green
Write-Host "现在尝试编译项目..." -ForegroundColor Yellow

# 尝试编译
try {
    $compileResult = & mvn compile -q 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✓ 编译成功！" -ForegroundColor Green
    } else {
        Write-Host "✗ 编译仍有错误，需要进一步修复" -ForegroundColor Red
        Write-Host $compileResult
    }
} catch {
    Write-Host "编译过程中出现异常: $_" -ForegroundColor Red
}
