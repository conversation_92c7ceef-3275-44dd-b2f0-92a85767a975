package com.wtms.workflow.model;

import com.wtms.entity.*;
import lombok.Data;
import lombok.Builder;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;
import java.util.Map;
import java.util.HashMap;
import java.util.List;

/**
 * 工作流上下文
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WorkflowContext {

    /**
     * 工作流定义
     */
    private WorkflowDefinition definition;

    /**
     * 工作流实例
     */
    private WorkflowInstance instance;

    /**
     * 当前节点
     */
    private WorkflowNode currentNode;

    /**
     * 目标节点
     */
    private WorkflowNode targetNode;

    /**
     * 当前任务
     */
    private WorkflowTask currentTask;

    /**
     * 当前用户
     */
    private User currentUser;

    /**
     * 工作流变量
     */
    @Builder.Default
    private Map<String, Object> variables = new HashMap<>();

    /**
     * 任务变量
     */
    @Builder.Default
    private Map<String, Object> taskVariables = new HashMap<>();

    /**
     * 执行参数
     */
    @Builder.Default
    private Map<String, Object> parameters = new HashMap<>();

    /**
     * 执行时间
     */
    @Builder.Default
    private LocalDateTime executionTime = LocalDateTime.now();

    /**
     * 执行类型
     */
    private ExecutionType executionType;

    /**
     * 操作类型
     */
    private ActionType actionType;

    /**
     * 执行结果
     */
    private WorkflowExecutionResult result;

    /**
     * 错误信息
     */
    private String errorMessage;

    /**
     * 是否异步执行
     */
    @Builder.Default
    private boolean async = false;

    /**
     * 执行超时时间（毫秒）
     */
    @Builder.Default
    private long timeout = 30000L;

    /**
     * 重试次数
     */
    @Builder.Default
    private int retryCount = 0;

    /**
     * 最大重试次数
     */
    @Builder.Default
    private int maxRetries = 3;

    /**
     * 执行类型枚举
     */
    public enum ExecutionType {
        START_PROCESS("start_process", "启动流程"),
        COMPLETE_TASK("complete_task", "完成任务"),
        CLAIM_TASK("claim_task", "认领任务"),
        DELEGATE_TASK("delegate_task", "委托任务"),
        JUMP_NODE("jump_node", "跳转节点"),
        ROLLBACK_NODE("rollback_node", "回退节点"),
        SUSPEND_INSTANCE("suspend_instance", "暂停实例"),
        RESUME_INSTANCE("resume_instance", "恢复实例"),
        TERMINATE_INSTANCE("terminate_instance", "终止实例"),
        CANCEL_INSTANCE("cancel_instance", "取消实例"),
        EXECUTE_NODE("execute_node", "执行节点"),
        EVALUATE_CONDITION("evaluate_condition", "计算条件");

        private final String code;
        private final String description;

        ExecutionType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 操作类型枚举
     */
    public enum ActionType {
        CREATE("create", "创建"),
        UPDATE("update", "更新"),
        DELETE("delete", "删除"),
        EXECUTE("execute", "执行"),
        VALIDATE("validate", "验证"),
        NOTIFY("notify", "通知"),
        CALLBACK("callback", "回调");

        private final String code;
        private final String description;

        ActionType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }
    }

    /**
     * 获取变量值
     */
    public Object getVariable(String key) {
        return variables.get(key);
    }

    /**
     * 获取变量值（带默认值）
     */
    public Object getVariable(String key, Object defaultValue) {
        return variables.getOrDefault(key, defaultValue);
    }

    /**
     * 设置变量值
     */
    public void setVariable(String key, Object value) {
        if (variables == null) {
            variables = new HashMap<>();
        }
        variables.put(key, value);
    }

    /**
     * 移除变量
     */
    public void removeVariable(String key) {
        if (variables != null) {
            variables.remove(key);
        }
    }

    /**
     * 获取任务变量值
     */
    public Object getTaskVariable(String key) {
        return taskVariables.get(key);
    }

    /**
     * 获取任务变量值（带默认值）
     */
    public Object getTaskVariable(String key, Object defaultValue) {
        return taskVariables.getOrDefault(key, defaultValue);
    }

    /**
     * 设置任务变量值
     */
    public void setTaskVariable(String key, Object value) {
        if (taskVariables == null) {
            taskVariables = new HashMap<>();
        }
        taskVariables.put(key, value);
    }

    /**
     * 获取参数值
     */
    public Object getParameter(String key) {
        return parameters.get(key);
    }

    /**
     * 获取参数值（带默认值）
     */
    public Object getParameter(String key, Object defaultValue) {
        return parameters.getOrDefault(key, defaultValue);
    }

    /**
     * 设置参数值
     */
    public void setParameter(String key, Object value) {
        if (parameters == null) {
            parameters = new HashMap<>();
        }
        parameters.put(key, value);
    }

    /**
     * 检查是否有错误
     */
    public boolean hasError() {
        return errorMessage != null && !errorMessage.trim().isEmpty();
    }

    /**
     * 检查是否成功
     */
    public boolean isSuccess() {
        return result != null && result.isSuccess();
    }

    /**
     * 检查是否需要重试
     */
    public boolean needRetry() {
        return hasError() && retryCount < maxRetries;
    }

    /**
     * 增加重试次数
     */
    public void incrementRetryCount() {
        this.retryCount++;
    }

    /**
     * 重置重试次数
     */
    public void resetRetryCount() {
        this.retryCount = 0;
    }

    /**
     * 获取实例ID
     */
    public String getInstanceId() {
        return instance != null ? instance.getId() : null;
    }

    /**
     * 获取定义ID
     */
    public String getDefinitionId() {
        return definition != null ? definition.getId() : null;
    }

    /**
     * 获取当前节点ID
     */
    public String getCurrentNodeId() {
        return currentNode != null ? currentNode.getId() : null;
    }

    /**
     * 获取目标节点ID
     */
    public String getTargetNodeId() {
        return targetNode != null ? targetNode.getId() : null;
    }

    /**
     * 获取当前任务ID
     */
    public String getCurrentTaskId() {
        return currentTask != null ? currentTask.getId() : null;
    }

    /**
     * 获取当前用户ID
     */
    public String getCurrentUserId() {
        return currentUser != null ? currentUser.getId() : null;
    }

    /**
     * 创建子上下文
     */
    public WorkflowContext createChildContext() {
        return WorkflowContext.builder()
                .definition(this.definition)
                .instance(this.instance)
                .currentUser(this.currentUser)
                .variables(new HashMap<>(this.variables))
                .taskVariables(new HashMap<>(this.taskVariables))
                .parameters(new HashMap<>(this.parameters))
                .executionTime(LocalDateTime.now())
                .async(this.async)
                .timeout(this.timeout)
                .maxRetries(this.maxRetries)
                .build();
    }

    /**
     * 复制上下文
     */
    public WorkflowContext copy() {
        return WorkflowContext.builder()
                .definition(this.definition)
                .instance(this.instance)
                .currentNode(this.currentNode)
                .targetNode(this.targetNode)
                .currentTask(this.currentTask)
                .currentUser(this.currentUser)
                .variables(new HashMap<>(this.variables))
                .taskVariables(new HashMap<>(this.taskVariables))
                .parameters(new HashMap<>(this.parameters))
                .executionTime(this.executionTime)
                .executionType(this.executionType)
                .actionType(this.actionType)
                .result(this.result)
                .errorMessage(this.errorMessage)
                .async(this.async)
                .timeout(this.timeout)
                .retryCount(this.retryCount)
                .maxRetries(this.maxRetries)
                .build();
    }
}
