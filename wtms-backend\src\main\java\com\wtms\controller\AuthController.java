package com.wtms.controller;

import com.wtms.common.result.Result;
import com.wtms.dto.request.LoginRequest;
import com.wtms.dto.response.LoginResponse;
import com.wtms.dto.response.UserInfoResponse;
import com.wtms.service.AuthService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import javax.validation.constraints.Email;
import javax.validation.constraints.NotBlank;

/**
 * 认证控制器
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@RestController
@RequestMapping("/api/v1/auth")
@Tag(name = "认证管理", description = "用户认证相关接口")
public class AuthController {

    @Autowired
    private AuthService authService;

    @PostMapping("/login")
    @Operation(summary = "用户登录", description = "用户名密码登录")
    public Result<LoginResponse> login(@Valid @RequestBody LoginRequest loginRequest) {
        log.info("Login request from user: {}", loginRequest.getUsername());
        
        LoginResponse response = authService.login(loginRequest);
        return Result.success("登录成功", response);
    }

    @PostMapping("/logout")
    @Operation(summary = "用户登出", description = "退出登录")
    public Result<String> logout(HttpServletRequest request) {
        String token = getTokenFromRequest(request);
        if (StringUtils.hasText(token)) {
            authService.logout(token);
        }
        return Result.success("登出成功");
    }

    @PostMapping("/refresh")
    @Operation(summary = "刷新Token", description = "使用刷新令牌获取新的访问令牌")
    public Result<String> refreshToken(
            @Parameter(description = "刷新令牌", required = true)
            @RequestBody @NotBlank(message = "刷新令牌不能为空") String refreshToken) {
        
        String newToken = authService.refreshToken(refreshToken);
        return Result.success("Token刷新成功", newToken);
    }

    @GetMapping("/me")
    @Operation(summary = "获取当前用户信息", description = "获取当前登录用户的详细信息")
    @PreAuthorize("isAuthenticated()")
    public Result<UserInfoResponse> getCurrentUser() {
        UserInfoResponse userInfo = authService.getCurrentUserInfo();
        return Result.success(userInfo);
    }

    @PutMapping("/password")
    @Operation(summary = "修改密码", description = "修改当前用户密码")
    @PreAuthorize("isAuthenticated()")
    public Result<String> changePassword(
            @Parameter(description = "旧密码", required = true)
            @RequestParam @NotBlank(message = "旧密码不能为空") String oldPassword,
            @Parameter(description = "新密码", required = true)
            @RequestParam @NotBlank(message = "新密码不能为空") String newPassword) {
        
        authService.changePassword(oldPassword, newPassword);
        return Result.success("密码修改成功");
    }

    @PostMapping("/forgot-password")
    @Operation(summary = "忘记密码", description = "发送密码重置邮件")
    public Result<String> forgotPassword(
            @Parameter(description = "邮箱地址", required = true)
            @RequestParam @Email(message = "邮箱格式不正确") String email) {
        
        authService.forgotPassword(email);
        return Result.success("密码重置邮件已发送，请查收邮箱");
    }

    @PostMapping("/reset-password")
    @Operation(summary = "重置密码", description = "使用重置令牌重置密码")
    public Result<String> resetPassword(
            @Parameter(description = "重置令牌", required = true)
            @RequestParam @NotBlank(message = "重置令牌不能为空") String token,
            @Parameter(description = "新密码", required = true)
            @RequestParam @NotBlank(message = "新密码不能为空") String newPassword) {
        
        authService.resetPassword(token, newPassword);
        return Result.success("密码重置成功");
    }

    @GetMapping("/captcha")
    @Operation(summary = "获取验证码", description = "获取图形验证码")
    public Result<String> getCaptcha() {
        // TODO: 实现验证码生成逻辑
        return Result.success("验证码功能开发中");
    }

    @PostMapping("/verify-captcha")
    @Operation(summary = "验证验证码", description = "验证图形验证码")
    public Result<String> verifyCaptcha(
            @Parameter(description = "验证码ID", required = true)
            @RequestParam @NotBlank(message = "验证码ID不能为空") String captchaId,
            @Parameter(description = "验证码", required = true)
            @RequestParam @NotBlank(message = "验证码不能为空") String captcha) {
        
        // TODO: 实现验证码验证逻辑
        return Result.success("验证码验证成功");
    }

    /**
     * 从请求中获取JWT Token
     */
    private String getTokenFromRequest(HttpServletRequest request) {
        String bearerToken = request.getHeader("Authorization");
        if (StringUtils.hasText(bearerToken) && bearerToken.startsWith("Bearer ")) {
            return bearerToken.substring(7);
        }
        return null;
    }
}
