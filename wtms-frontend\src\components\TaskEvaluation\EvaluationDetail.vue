<template>
  <div v-loading="loading" class="evaluation-detail">
    <div v-if="evaluation" class="detail-content">
      <!-- 基本信息 -->
      <el-card class="info-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Document /></el-icon>
            <span>基本信息</span>
            <div class="header-actions">
              <el-tag :type="getStatusTagType(evaluation.status)" size="large">
                {{ getStatusText(evaluation.status) }}
              </el-tag>
            </div>
          </div>
        </template>

        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务">
            <div class="task-info">
              <div class="task-title">{{ evaluation.task?.title }}</div>
              <div class="task-desc">{{ evaluation.task?.description }}</div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="被评价者">
            <div class="user-info">
              <el-avatar :size="32" :src="evaluation.evaluatee?.avatar">
                {{ evaluation.evaluatee?.fullName?.charAt(0) }}
              </el-avatar>
              <div class="user-details">
                <div class="user-name">{{ evaluation.evaluatee?.fullName }}</div>
                <div class="user-role">{{ evaluation.evaluatee?.roleName }}</div>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="评价者">
            <div class="user-info">
              <el-avatar :size="32" :src="evaluation.evaluator?.avatar">
                {{ evaluation.evaluator?.fullName?.charAt(0) }}
              </el-avatar>
              <div class="user-details">
                <div class="user-name">{{ evaluation.evaluator?.fullName }}</div>
                <div class="user-role">{{ evaluation.evaluator?.roleName }}</div>
              </div>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="评价类型">
            <el-tag :type="getEvaluationTypeTagType(evaluation.evaluationType)">
              {{ getEvaluationTypeText(evaluation.evaluationType) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="评价阶段">
            {{ getEvaluationStageText(evaluation.evaluationStage) }}
          </el-descriptions-item>
          <el-descriptions-item label="权重">
            {{ evaluation.weight }}
          </el-descriptions-item>
          <el-descriptions-item label="匿名评价">
            <el-tag :type="evaluation.isAnonymous ? 'warning' : 'success'" size="small">
              {{ evaluation.isAnonymous ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="公开评价">
            <el-tag :type="evaluation.isPublic ? 'success' : 'info'" size="small">
              {{ evaluation.isPublic ? '是' : '否' }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(evaluation.createdAt) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ formatDateTime(evaluation.updatedAt) }}
          </el-descriptions-item>
          <el-descriptions-item v-if="evaluation.deadline" label="截止时间">
            <span :class="{ 'overdue': isOverdue(evaluation.deadline) }">
              {{ formatDateTime(evaluation.deadline) }}
            </span>
          </el-descriptions-item>
        </el-descriptions>
      </el-card>

      <!-- 评分详情 -->
      <el-card class="score-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Star /></el-icon>
            <span>评分详情</span>
          </div>
        </template>

        <div class="score-overview">
          <div class="overall-score">
            <div class="score-circle">
              <el-progress
                type="circle"
                :percentage="evaluation.overallScore"
                :width="120"
                :stroke-width="8"
                :color="getScoreColor(evaluation.overallScore)"
              >
                <template #default="{ percentage }">
                  <div class="score-content">
                    <div class="score-number">{{ percentage }}</div>
                    <div class="score-grade">{{ getScoreGrade(percentage) }}</div>
                  </div>
                </template>
              </el-progress>
            </div>
            <div class="score-label">总体评分</div>
          </div>

          <div class="dimension-scores">
            <div class="dimension-item">
              <div class="dimension-label">质量评分</div>
              <div class="dimension-progress">
                <el-progress
                  :percentage="evaluation.qualityScore || 0"
                  :color="getScoreColor(evaluation.qualityScore || 0)"
                  :stroke-width="12"
                />
              </div>
              <div class="dimension-value">{{ evaluation.qualityScore || 0 }}</div>
            </div>
            <div class="dimension-item">
              <div class="dimension-label">效率评分</div>
              <div class="dimension-progress">
                <el-progress
                  :percentage="evaluation.efficiencyScore || 0"
                  :color="getScoreColor(evaluation.efficiencyScore || 0)"
                  :stroke-width="12"
                />
              </div>
              <div class="dimension-value">{{ evaluation.efficiencyScore || 0 }}</div>
            </div>
            <div class="dimension-item">
              <div class="dimension-label">沟通评分</div>
              <div class="dimension-progress">
                <el-progress
                  :percentage="evaluation.communicationScore || 0"
                  :color="getScoreColor(evaluation.communicationScore || 0)"
                  :stroke-width="12"
                />
              </div>
              <div class="dimension-value">{{ evaluation.communicationScore || 0 }}</div>
            </div>
            <div class="dimension-item">
              <div class="dimension-label">创新评分</div>
              <div class="dimension-progress">
                <el-progress
                  :percentage="evaluation.innovationScore || 0"
                  :color="getScoreColor(evaluation.innovationScore || 0)"
                  :stroke-width="12"
                />
              </div>
              <div class="dimension-value">{{ evaluation.innovationScore || 0 }}</div>
            </div>
            <div class="dimension-item">
              <div class="dimension-label">团队协作</div>
              <div class="dimension-progress">
                <el-progress
                  :percentage="evaluation.teamworkScore || 0"
                  :color="getScoreColor(evaluation.teamworkScore || 0)"
                  :stroke-width="12"
                />
              </div>
              <div class="dimension-value">{{ evaluation.teamworkScore || 0 }}</div>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 评价内容 -->
      <el-card class="content-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><EditPen /></el-icon>
            <span>评价内容</span>
          </div>
        </template>

        <div class="content-item">
          <h4>评价内容</h4>
          <div class="content-text">{{ evaluation.evaluationContent }}</div>
        </div>

        <el-row v-if="evaluation.strengths || evaluation.improvements" :gutter="20">
          <el-col v-if="evaluation.strengths" :span="12">
            <div class="content-item">
              <h4>优点描述</h4>
              <div class="content-text strengths">{{ evaluation.strengths }}</div>
            </div>
          </el-col>
          <el-col v-if="evaluation.improvements" :span="12">
            <div class="content-item">
              <h4>改进建议</h4>
              <div class="content-text improvements">{{ evaluation.improvements }}</div>
            </div>
          </el-col>
        </el-row>

        <div v-if="evaluationTags.length" class="content-item">
          <h4>评价标签</h4>
          <div class="tags-container">
            <el-tag
              v-for="tag in evaluationTags"
              :key="tag"
              style="margin-right: 8px; margin-bottom: 8px;"
            >
              {{ tag }}
            </el-tag>
          </div>
        </div>
      </el-card>

      <!-- 自定义维度 -->
      <el-card v-if="evaluation.dimensions?.length" class="dimensions-section" shadow="never">
        <template #header>
          <div class="section-header">
            <el-icon><Grid /></el-icon>
            <span>自定义维度</span>
          </div>
        </template>

        <div class="custom-dimensions">
          <div
            v-for="dimension in evaluation.dimensions"
            :key="dimension.id"
            class="dimension-card"
          >
            <div class="dimension-header">
              <div class="dimension-name">{{ dimension.dimensionName }}</div>
              <div class="dimension-score">
                <span class="score">{{ dimension.score }}</span>
                <span class="max-score">/ {{ dimension.maxScore }}</span>
              </div>
            </div>
            <div v-if="dimension.description" class="dimension-desc">
              {{ dimension.description }}
            </div>
            <div class="dimension-progress">
              <el-progress
                :percentage="(dimension.score / dimension.maxScore) * 100"
                :color="getScoreColor((dimension.score / dimension.maxScore) * 100)"
                :stroke-width="8"
              />
            </div>
            <div v-if="dimension.evaluationContent" class="dimension-content">
              <strong>评价内容：</strong>{{ dimension.evaluationContent }}
            </div>
            <div v-if="dimension.evaluationCriteria" class="dimension-criteria">
              <strong>评价标准：</strong>{{ dimension.evaluationCriteria }}
            </div>
          </div>
        </div>
      </el-card>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <el-button v-if="canEdit" type="primary" @click="handleEdit">
          <el-icon><Edit /></el-icon>
          编辑评价
        </el-button>
        <el-button v-if="evaluation.status === 'draft'" type="success" @click="handleSubmit">
          <el-icon><Check /></el-icon>
          提交评价
        </el-button>
        <el-button v-if="evaluation.status === 'submitted'" type="warning" @click="handleReview">
          <el-icon><View /></el-icon>
          审核评价
        </el-button>
        <el-button v-if="evaluation.status === 'reviewed'" type="primary" @click="handlePublish">
          <el-icon><Upload /></el-icon>
          发布评价
        </el-button>
        <el-button @click="handlePrint">
          <el-icon><Printer /></el-icon>
          打印
        </el-button>
        <el-button @click="handleExport">
          <el-icon><Download /></el-icon>
          导出
        </el-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  Document, Star, EditPen, Grid, Edit, Check, View, Upload, Printer, Download 
} from '@element-plus/icons-vue'
import { 
  getTaskEvaluation, 
  submitTaskEvaluation,
  reviewTaskEvaluation,
  publishTaskEvaluation,
  type TaskEvaluation 
} from '@/api/evaluation'
import { useUserStore } from '@/stores/user'

// Props
interface Props {
  evaluationId: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'edit': [evaluationId: string]
}>()

// 响应式数据
const userStore = useUserStore()
const loading = ref(false)
const evaluation = ref<TaskEvaluation>()

// 计算属性
const evaluationTags = computed(() => {
  return evaluation.value?.evaluationTags 
    ? evaluation.value.evaluationTags.split(',').filter(tag => tag.trim()) 
    : []
})

const canEdit = computed(() => {
  return evaluation.value?.status === 'draft' && 
         evaluation.value?.evaluatorId === userStore.userInfo?.id
})

// 方法
const loadEvaluation = async () => {
  loading.value = true
  try {
    const response = await getTaskEvaluation(props.evaluationId)
    evaluation.value = response.data
  } catch (error) {
    console.error('Failed to load evaluation:', error)
    ElMessage.error('加载评价详情失败')
  } finally {
    loading.value = false
  }
}

const handleEdit = () => {
  emit('edit', props.evaluationId)
}

const handleSubmit = async () => {
  try {
    await submitTaskEvaluation(props.evaluationId)
    ElMessage.success('评价提交成功')
    loadEvaluation()
  } catch (error) {
    console.error('Failed to submit evaluation:', error)
    ElMessage.error('评价提交失败')
  }
}

const handleReview = async () => {
  try {
    await reviewTaskEvaluation(props.evaluationId)
    ElMessage.success('评价审核成功')
    loadEvaluation()
  } catch (error) {
    console.error('Failed to review evaluation:', error)
    ElMessage.error('评价审核失败')
  }
}

const handlePublish = async () => {
  try {
    await publishTaskEvaluation(props.evaluationId)
    ElMessage.success('评价发布成功')
    loadEvaluation()
  } catch (error) {
    console.error('Failed to publish evaluation:', error)
    ElMessage.error('评价发布失败')
  }
}

const handlePrint = () => {
  window.print()
}

const handleExport = () => {
  // 实现导出功能
  ElMessage.info('导出功能开发中...')
}

// 工具方法
const getEvaluationTypeText = (type: string) => {
  const typeMap: Record<string, string> = {
    self: '自评',
    peer: '同事评价',
    supervisor: '上级评价',
    subordinate: '下级评价',
    customer: '客户评价'
  }
  return typeMap[type] || type
}

const getEvaluationTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    self: 'info',
    peer: '',
    supervisor: 'success',
    subordinate: 'warning',
    customer: 'danger'
  }
  return typeMap[type] || ''
}

const getEvaluationStageText = (stage: string) => {
  const stageMap: Record<string, string> = {
    planning: '计划阶段',
    execution: '执行阶段',
    completion: '完成阶段',
    review: '回顾阶段',
    final: '最终评价'
  }
  return stageMap[stage] || stage
}

const getStatusText = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: '草稿',
    submitted: '已提交',
    reviewed: '已审核',
    published: '已发布',
    archived: '已归档',
    rejected: '已拒绝'
  }
  return statusMap[status] || status
}

const getStatusTagType = (status: string) => {
  const statusMap: Record<string, string> = {
    draft: 'info',
    submitted: 'warning',
    reviewed: 'primary',
    published: 'success',
    archived: '',
    rejected: 'danger'
  }
  return statusMap[status] || ''
}

const getScoreGrade = (score: number) => {
  if (score >= 90) return '优秀'
  if (score >= 80) return '良好'
  if (score >= 70) return '中等'
  if (score >= 60) return '及格'
  return '不及格'
}

const getScoreColor = (score: number) => {
  if (score >= 90) return '#67c23a'
  if (score >= 80) return '#409eff'
  if (score >= 70) return '#e6a23c'
  if (score >= 60) return '#f56c6c'
  return '#909399'
}

const formatDateTime = (dateTime: string) => {
  return new Date(dateTime).toLocaleString('zh-CN')
}

const isOverdue = (deadline: string) => {
  return new Date(deadline) < new Date()
}

// 生命周期
onMounted(() => {
  loadEvaluation()
})
</script>

<style scoped>
.evaluation-detail {
  padding: 20px;
}

.info-section,
.score-section,
.content-section,
.dimensions-section {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.header-actions {
  margin-left: auto;
}

.task-info {
  padding: 4px 0;
}

.task-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 4px;
}

.task-desc {
  font-size: 12px;
  color: #909399;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-weight: 500;
  color: #303133;
  margin-bottom: 2px;
}

.user-role {
  font-size: 12px;
  color: #909399;
}

.overdue {
  color: #f56c6c;
  font-weight: 500;
}

.score-overview {
  display: flex;
  gap: 40px;
  align-items: flex-start;
}

.overall-score {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.score-content {
  text-align: center;
}

.score-number {
  font-size: 24px;
  font-weight: 600;
  color: #303133;
}

.score-grade {
  font-size: 14px;
  color: #909399;
}

.score-label {
  font-size: 16px;
  font-weight: 500;
  color: #303133;
}

.dimension-scores {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.dimension-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dimension-label {
  width: 80px;
  font-size: 14px;
  color: #606266;
}

.dimension-progress {
  flex: 1;
}

.dimension-value {
  width: 40px;
  text-align: right;
  font-weight: 500;
  color: #303133;
}

.content-item {
  margin-bottom: 24px;
}

.content-item h4 {
  margin: 0 0 12px 0;
  font-size: 16px;
  color: #303133;
}

.content-text {
  padding: 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  line-height: 1.6;
  color: #606266;
}

.content-text.strengths {
  border-left: 4px solid #67c23a;
}

.content-text.improvements {
  border-left: 4px solid #e6a23c;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.custom-dimensions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.dimension-card {
  border: 1px solid #ebeef5;
  border-radius: 8px;
  padding: 16px;
  background-color: #fafafa;
}

.dimension-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.dimension-name {
  font-weight: 500;
  color: #303133;
}

.dimension-score .score {
  font-size: 18px;
  font-weight: 600;
  color: #409eff;
}

.dimension-score .max-score {
  color: #909399;
}

.dimension-desc {
  font-size: 12px;
  color: #909399;
  margin-bottom: 12px;
}

.dimension-progress {
  margin-bottom: 12px;
}

.dimension-content,
.dimension-criteria {
  font-size: 14px;
  color: #606266;
  margin-bottom: 8px;
  line-height: 1.5;
}

.action-buttons {
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

@media print {
  .action-buttons {
    display: none;
  }
}
</style>
