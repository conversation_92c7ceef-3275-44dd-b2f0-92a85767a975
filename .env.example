# WTMS 工作任务管理系统环境变量配置文件
# 复制此文件为 .env 并根据实际环境修改配置

# ================================
# 应用基础配置
# ================================
APP_NAME=WTMS工作任务管理系统
APP_VERSION=1.0.0
APP_ENV=production

# ================================
# 数据库配置
# ================================
# MySQL数据库配置
MYSQL_ROOT_PASSWORD=wtms123456
MYSQL_DATABASE=wtms_db
MYSQL_USER=wtms
MYSQL_PASSWORD=wtms123456
MYSQL_PORT=3306
MYSQL_HOST=localhost

# 数据库连接池配置
DB_POOL_INITIAL_SIZE=5
DB_POOL_MAX_ACTIVE=20
DB_POOL_MAX_IDLE=10
DB_POOL_MIN_IDLE=5

# ================================
# Redis配置
# ================================
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DATABASE=0
REDIS_TIMEOUT=3000
REDIS_POOL_MAX_ACTIVE=20
REDIS_POOL_MAX_IDLE=10
REDIS_POOL_MIN_IDLE=5

# ================================
# 后端服务配置
# ================================
# Spring Boot配置
SPRING_PROFILES_ACTIVE=prod
BACKEND_PORT=55557
SERVER_SERVLET_CONTEXT_PATH=/api/v1

# JWT配置
# JWT密钥 - 生产环境必须使用强随机密钥，至少32位字符
# 生成方法: openssl rand -base64 32
JWT_SECRET=your-super-secret-jwt-key-change-this-in-production-at-least-32-chars
JWT_EXPIRATION=86400000
JWT_REFRESH_EXPIRATION=604800000
JWT_ALGORITHM=HS256
JWT_ISSUER=WTMS-System
JWT_AUDIENCE=WTMS-Users

# 文件上传配置
UPLOAD_PATH=/app/uploads
MAX_FILE_SIZE=10MB
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,pdf,doc,docx,xls,xlsx,ppt,pptx,txt,zip,rar

# 日志配置
LOG_LEVEL=INFO
LOG_PATH=/app/logs
LOG_MAX_FILE_SIZE=100MB
LOG_MAX_HISTORY=30

# ================================
# 前端服务配置
# ================================
FRONTEND_PORT=33335
VITE_API_BASE_URL=http://localhost:55557/api/v1
VITE_APP_TITLE=WTMS工作任务管理系统
VITE_APP_VERSION=1.0.0
VITE_APP_DESCRIPTION=企业级工作任务管理系统

# 文件上传前端配置
VITE_UPLOAD_URL=http://localhost:55557/api/v1/files/upload
VITE_MAX_FILE_SIZE=10485760
VITE_CHUNK_SIZE=1048576

# ================================
# Nginx配置
# ================================
NGINX_PORT=80
NGINX_SSL_PORT=443
NGINX_CLIENT_MAX_BODY_SIZE=10m

# ================================
# 邮件服务配置（可选）
# ================================
MAIL_ENABLED=false
MAIL_HOST=smtp.gmail.com
MAIL_PORT=587
MAIL_USERNAME=<EMAIL>
MAIL_PASSWORD=your-app-password
MAIL_FROM=<EMAIL>
MAIL_FROM_NAME=WTMS系统

# ================================
# 短信服务配置（可选）
# ================================
SMS_ENABLED=false
SMS_ACCESS_KEY=your-access-key
SMS_SECRET_KEY=your-secret-key
SMS_SIGN_NAME=WTMS
SMS_TEMPLATE_CODE=SMS_123456789

# ================================
# 对象存储配置（可选）
# ================================
OSS_ENABLED=false
OSS_ENDPOINT=https://oss-cn-hangzhou.aliyuncs.com
OSS_ACCESS_KEY=your-access-key
OSS_SECRET_KEY=your-secret-key
OSS_BUCKET_NAME=wtms-files
OSS_DOMAIN=https://files.wtms.com

# ================================
# 监控配置
# ================================
# Actuator配置
MANAGEMENT_ENDPOINTS_ENABLED=true
MANAGEMENT_ENDPOINT_HEALTH_SHOW_DETAILS=always
MANAGEMENT_METRICS_ENABLED=true

# ================================
# 安全配置
# ================================
# CORS配置
CORS_ALLOWED_ORIGINS=http://localhost:33335,http://localhost:80
CORS_ALLOWED_METHODS=GET,POST,PUT,DELETE,OPTIONS
CORS_ALLOWED_HEADERS=*
CORS_ALLOW_CREDENTIALS=true

# 密码策略配置
PASSWORD_MIN_LENGTH=8
PASSWORD_REQUIRE_UPPERCASE=true
PASSWORD_REQUIRE_LOWERCASE=true
PASSWORD_REQUIRE_NUMBERS=true
PASSWORD_REQUIRE_SPECIAL_CHARS=false

# 登录安全配置
LOGIN_MAX_ATTEMPTS=5
LOGIN_LOCK_DURATION=300
SESSION_TIMEOUT=3600

# ================================
# 业务配置
# ================================
# 任务配置
TASK_AUTO_ASSIGN=true
TASK_REMINDER_ENABLED=true
TASK_REMINDER_ADVANCE_HOURS=24

# 工作流配置
WORKFLOW_MAX_NODES=50
WORKFLOW_MAX_CONNECTIONS=100
WORKFLOW_EXECUTION_TIMEOUT=3600

# 评价配置
EVALUATION_ANONYMOUS_ALLOWED=true
EVALUATION_SELF_EVALUATION_ENABLED=true
EVALUATION_DEADLINE_REMINDER=true

# 通知配置
NOTIFICATION_EMAIL_ENABLED=true
NOTIFICATION_SMS_ENABLED=false
NOTIFICATION_SYSTEM_ENABLED=true

# ================================
# 缓存配置
# ================================
CACHE_TTL_USER_INFO=3600
CACHE_TTL_PERMISSIONS=1800
CACHE_TTL_TASKS=300
CACHE_TTL_WORKFLOWS=600

# ================================
# 定时任务配置
# ================================
SCHEDULER_ENABLED=true
SCHEDULER_POOL_SIZE=10

# 数据清理任务
CLEANUP_TASK_ENABLED=true
CLEANUP_TASK_CRON=0 0 2 * * ?
CLEANUP_DAYS_KEEP_LOGS=30
CLEANUP_DAYS_KEEP_TEMP_FILES=7

# 统计任务
STATISTICS_TASK_ENABLED=true
STATISTICS_TASK_CRON=0 0 1 * * ?

# 提醒任务
REMINDER_TASK_ENABLED=true
REMINDER_TASK_CRON=0 0 9 * * ?

# ================================
# 开发环境配置
# ================================
# 调试配置
DEBUG_ENABLED=false
SQL_SHOW=false
SQL_FORMAT=false

# 热重载配置
HOT_RELOAD_ENABLED=false

# Mock数据配置
MOCK_DATA_ENABLED=false

# ================================
# 生产环境配置
# ================================
# 性能配置
TOMCAT_MAX_THREADS=200
TOMCAT_MIN_SPARE_THREADS=10
TOMCAT_MAX_CONNECTIONS=8192

# JVM配置
JVM_XMS=512m
JVM_XMX=1024m
JVM_XMN=256m

# ================================
# 备份配置
# ================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 0 3 * * ?
BACKUP_RETENTION_DAYS=30
BACKUP_PATH=/app/backups

# ================================
# 集群配置（可选）
# ================================
CLUSTER_ENABLED=false
CLUSTER_NODE_ID=node1
CLUSTER_NODES=node1:8080,node2:8080

# ================================
# 第三方集成配置
# ================================
# 钉钉集成
DINGTALK_ENABLED=false
DINGTALK_APP_KEY=your-app-key
DINGTALK_APP_SECRET=your-app-secret

# 企业微信集成
WECHAT_WORK_ENABLED=false
WECHAT_WORK_CORP_ID=your-corp-id
WECHAT_WORK_CORP_SECRET=your-corp-secret

# 飞书集成
FEISHU_ENABLED=false
FEISHU_APP_ID=your-app-id
FEISHU_APP_SECRET=your-app-secret

# ================================
# 安全扫描配置
# ================================
# NVD API Key (用于OWASP Dependency Check)
# 申请地址: https://nvd.nist.gov/developers/request-an-api-key
NVD_API_KEY=your-nvd-api-key-here

# Snyk Token (用于Snyk安全扫描)
# 申请地址: https://app.snyk.io/account
SNYK_TOKEN=your-snyk-token-here
SNYK_ORG=your-snyk-org-id
SNYK_MONITOR=false

# 安全扫描配置
SECURITY_SCAN_ENABLED=true
SECURITY_SCAN_SCHEDULE=weekly
SECURITY_REPORT_EMAIL=<EMAIL>
