[mysqld]
# 基本设置
default-storage-engine=INNODB
character-set-server=utf8mb4
collation-server=utf8mb4_unicode_ci
init_connect='SET NAMES utf8mb4'

# 连接设置
max_connections=200
max_connect_errors=10
table_open_cache=2000
max_allowed_packet=16M
binlog_cache_size=1M
max_heap_table_size=8M
tmp_table_size=16M

# 查询缓存
query_cache_size=8M
query_cache_type=1

# InnoDB设置
innodb_buffer_pool_size=256M
innodb_flush_log_at_trx_commit=1
innodb_lock_wait_timeout=50
innodb_log_buffer_size=8M
innodb_log_file_size=32M
innodb_log_files_in_group=3
innodb_max_dirty_pages_pct=90
innodb_file_per_table=1

# 日志设置
log_error=/var/log/mysql/error.log
slow_query_log=1
slow_query_log_file=/var/log/mysql/slow.log
long_query_time=3

# 时区设置
default-time-zone='+8:00'

[mysql]
default-character-set=utf8mb4

[client]
default-character-set=utf8mb4
