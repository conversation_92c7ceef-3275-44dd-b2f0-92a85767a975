package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务评论实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("task_comments")
@Schema(description = "任务评论实体")
public class TaskComment implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "评论ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "任务ID")
    @TableField("task_id")
    private String taskId;

    @Schema(description = "父评论ID")
    @TableField("parent_id")
    private String parentId;

    @Schema(description = "评论者ID")
    @TableField("commenter_id")
    private String commenterId;

    @Schema(description = "评论内容")
    @TableField("content")
    private String content;

    @Schema(description = "评论类型")
    @TableField("comment_type")
    private String commentType;

    @Schema(description = "评论层级")
    @TableField("level")
    private Integer level;

    @Schema(description = "评论路径")
    @TableField("path")
    private String path;

    @Schema(description = "是否置顶")
    @TableField("is_pinned")
    private Boolean isPinned;

    @Schema(description = "是否私有")
    @TableField("is_private")
    private Boolean isPrivate;

    @Schema(description = "点赞数")
    @TableField("like_count")
    private Integer likeCount;

    @Schema(description = "回复数")
    @TableField("reply_count")
    private Integer replyCount;

    @Schema(description = "状态")
    @TableField("status")
    private String status;

    @Schema(description = "IP地址")
    @TableField("ip_address")
    private String ipAddress;

    @Schema(description = "用户代理")
    @TableField("user_agent")
    private String userAgent;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "任务信息")
    @TableField(exist = false)
    private Task task;

    @Schema(description = "父评论信息")
    @TableField(exist = false)
    private TaskComment parent;

    @Schema(description = "评论者信息")
    @TableField(exist = false)
    private User commenter;

    @Schema(description = "子评论列表")
    @TableField(exist = false)
    private List<TaskComment> children;

    @Schema(description = "附件列表")
    @TableField(exist = false)
    private List<TaskAttachment> attachments;

    /**
     * 评论类型枚举
     */
    public enum CommentType {
        NORMAL("normal", "普通评论"),
        SYSTEM("system", "系统评论"),
        STATUS_CHANGE("status_change", "状态变更"),
        ASSIGNMENT("assignment", "任务分配"),
        PROGRESS("progress", "进度更新"),
        REMINDER("reminder", "提醒通知");

        private final String code;
        private final String description;

        CommentType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static CommentType fromCode(String code) {
            for (CommentType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return NORMAL;
        }
    }

    /**
     * 评论状态枚举
     */
    public enum Status {
        ACTIVE("active", "正常"),
        HIDDEN("hidden", "隐藏"),
        DELETED("deleted", "已删除"),
        PENDING("pending", "待审核"),
        REJECTED("rejected", "已拒绝");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return ACTIVE;
        }
    }

    /**
     * 检查是否为置顶评论
     */
    public boolean isPinned() {
        return Boolean.TRUE.equals(this.isPinned);
    }

    /**
     * 检查是否为私有评论
     */
    public boolean isPrivate() {
        return Boolean.TRUE.equals(this.isPrivate);
    }

    /**
     * 检查是否为根评论
     */
    public boolean isRoot() {
        return parentId == null || parentId.isEmpty();
    }

    /**
     * 检查是否为叶子评论
     */
    public boolean isLeaf() {
        return children == null || children.isEmpty();
    }

    /**
     * 检查是否为系统评论
     */
    public boolean isSystemComment() {
        return CommentType.SYSTEM.getCode().equals(this.commentType);
    }

    /**
     * 检查评论是否可见
     */
    public boolean isVisible() {
        return Status.ACTIVE.getCode().equals(this.status);
    }

    /**
     * 获取评论层级路径
     */
    public String getFullPath() {
        if (path != null) {
            return path;
        }
        if (isRoot()) {
            return "/" + id;
        }
        return "/" + parentId + "/" + id;
    }
}
