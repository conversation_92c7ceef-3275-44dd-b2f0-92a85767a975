package com.wtms.dto.request;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 角色权限分配请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "角色权限分配请求")
public class AssignRolePermissionRequest {

    @Schema(description = "角色ID", example = "role-uuid")
    @NotBlank(message = "角色ID不能为空")
    private String roleId;

    @Schema(description = "权限ID列表", example = "[\"perm-uuid-1\", \"perm-uuid-2\"]")
    @NotEmpty(message = "权限列表不能为空")
    private List<String> permissionIds;

    @Schema(description = "授权类型", example = "direct", allowableValues = {"direct", "inherit", "temporary"})
    private String grantType = "direct";

    @Schema(description = "是否启用", example = "true")
    private Boolean isEnabled = true;

    @Schema(description = "过期时间", example = "2024-12-31 23:59:59")
    private LocalDateTime expiresAt;

    @Schema(description = "备注")
    private String comment;
}
