import request from '@/utils/request'

/**
 * 评论相关接口
 */

// 评论类型定义
export interface TaskComment {
  id: string
  taskId: string
  parentId?: string
  commenterId: string
  content: string
  commentType: 'normal' | 'system' | 'status_change' | 'assignment' | 'progress' | 'reminder'
  level?: number
  path?: string
  isPinned: boolean
  isPrivate: boolean
  likeCount: number
  replyCount: number
  status: 'active' | 'hidden' | 'deleted' | 'pending' | 'rejected'
  ipAddress?: string
  userAgent?: string
  createdAt: string
  updatedAt: string
  deletedAt?: string
  commenter?: {
    id: string
    username: string
    fullName: string
    avatar?: string
    department?: string
    position?: string
  }
  attachments?: TaskAttachment[]
  children?: TaskComment[]
}

// 附件类型定义
export interface TaskAttachment {
  id: string
  taskId: string
  commentId?: string
  uploaderId: string
  fileName: string
  originalName: string
  filePath: string
  fileUrl: string
  fileSize: number
  fileType: 'image' | 'document' | 'video' | 'audio' | 'archive' | 'other'
  mimeType: string
  fileExtension: string
  fileMd5: string
  thumbnailPath?: string
  thumbnailUrl?: string
  downloadCount: number
  isPublic: boolean
  status: 'active' | 'processing' | 'failed' | 'expired' | 'deleted'
  storageType: 'local' | 'oss' | 'cos' | 's3' | 'minio'
  storageConfig?: string
  expiresAt?: string
  createdAt: string
  updatedAt: string
  uploader?: {
    id: string
    username: string
    fullName: string
    avatar?: string
  }
}

// 评论响应类型
export interface CommentResponse {
  id: string
  taskId: string
  parentId?: string
  content: string
  commentType: string
  commentTypeText: string
  level?: number
  path?: string
  isPinned: boolean
  isPrivate: boolean
  likeCount: number
  replyCount: number
  status: string
  statusText: string
  createdAt: string
  updatedAt: string
  commenter: {
    id: string
    username: string
    fullName: string
    avatar?: string
    department?: string
    position?: string
  }
  attachments?: Array<{
    id: string
    fileName: string
    originalName: string
    fileSize: number
    readableFileSize: string
    fileType: string
    fileExtension: string
    fileUrl: string
    thumbnailUrl?: string
    downloadCount: number
    isImage: boolean
    isDocument: boolean
    createdAt: string
  }>
  children?: CommentResponse[]
  canEdit: boolean
  canDelete: boolean
  isLiked: boolean
}

// 创建评论请求
export interface CreateCommentRequest {
  taskId: string
  parentId?: string
  content: string
  commentType?: 'normal' | 'system' | 'status_change' | 'assignment' | 'progress' | 'reminder'
  isPinned?: boolean
  isPrivate?: boolean
  attachmentIds?: string[]
  mentionedUserIds?: string[]
}

// 更新评论请求
export interface UpdateCommentRequest {
  content?: string
  isPinned?: boolean
  isPrivate?: boolean
  attachmentIds?: string[]
  mentionedUserIds?: string[]
}

// API 接口函数

/**
 * 评论管理
 */
export function createComment(data: CreateCommentRequest) {
  return request.post<TaskComment>('/api/v1/task-comments', data)
}

export function updateComment(commentId: string, data: UpdateCommentRequest) {
  return request.put<TaskComment>(`/api/v1/task-comments/${commentId}`, data)
}

export function deleteComment(commentId: string) {
  return request.delete(`/api/v1/task-comments/${commentId}`)
}

export function getComment(commentId: string) {
  return request.get<TaskComment>(`/api/v1/task-comments/${commentId}`)
}

export function getTaskCommentTree(taskId: string) {
  return request.get<CommentResponse[]>(`/api/v1/task-comments/tasks/${taskId}/tree`)
}

export function getTaskComments(taskId: string, params?: {
  commentType?: string
  status?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: CommentResponse[]
    total: number
    size: number
    current: number
    pages: number
  }>(`/api/v1/task-comments/tasks/${taskId}`, { params })
}

export function getUserComments(userId: string, params?: {
  status?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: CommentResponse[]
    total: number
    size: number
    current: number
    pages: number
  }>(`/api/v1/task-comments/users/${userId}`, { params })
}

export function getChildComments(parentId: string) {
  return request.get<CommentResponse[]>(`/api/v1/task-comments/${parentId}/children`)
}

export function getRootComments(taskId: string) {
  return request.get<CommentResponse[]>(`/api/v1/task-comments/tasks/${taskId}/root`)
}

export function getPinnedComments(taskId: string) {
  return request.get<CommentResponse[]>(`/api/v1/task-comments/tasks/${taskId}/pinned`)
}

export function getLatestComments(taskId: string, limit = 5) {
  return request.get<CommentResponse[]>(`/api/v1/task-comments/tasks/${taskId}/latest`, {
    params: { limit }
  })
}

export function getPopularComments(taskId: string, limit = 5) {
  return request.get<CommentResponse[]>(`/api/v1/task-comments/tasks/${taskId}/popular`, {
    params: { limit }
  })
}

export function searchComments(params: {
  taskId?: string
  keyword?: string
  commenterId?: string
  commentType?: string
  status?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: CommentResponse[]
    total: number
    size: number
    current: number
    pages: number
  }>('/api/v1/task-comments/search', { params })
}

export function likeComment(commentId: string) {
  return request.post(`/api/v1/task-comments/${commentId}/like`)
}

export function unlikeComment(commentId: string) {
  return request.delete(`/api/v1/task-comments/${commentId}/like`)
}

export function pinComment(commentId: string) {
  return request.put(`/api/v1/task-comments/${commentId}/pin`)
}

export function unpinComment(commentId: string) {
  return request.delete(`/api/v1/task-comments/${commentId}/pin`)
}

export function hideComment(commentId: string) {
  return request.put(`/api/v1/task-comments/${commentId}/hide`)
}

export function showComment(commentId: string) {
  return request.put(`/api/v1/task-comments/${commentId}/show`)
}

export function batchDeleteComments(commentIds: string[]) {
  return request.delete('/api/v1/task-comments/batch', { data: commentIds })
}

export function batchUpdateCommentStatus(commentIds: string[], status: string) {
  return request.put('/api/v1/task-comments/batch/status', null, {
    params: { commentIds, status }
  })
}

export function getCommentStatistics(taskId: string) {
  return request.get(`/api/v1/task-comments/tasks/${taskId}/statistics`)
}

export function getCommentActivityStats(taskId: string, days = 7) {
  return request.get(`/api/v1/task-comments/tasks/${taskId}/activity`, {
    params: { days }
  })
}

/**
 * 附件管理
 */
export function uploadAttachment(taskId: string, commentId: string | undefined, file: File, isPublic = true) {
  const formData = new FormData()
  formData.append('taskId', taskId)
  if (commentId) {
    formData.append('commentId', commentId)
  }
  formData.append('file', file)
  formData.append('isPublic', String(isPublic))

  return request.post<TaskAttachment>('/api/v1/task-attachments/upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export function batchUploadAttachments(taskId: string, commentId: string | undefined, files: File[], isPublic = true) {
  const formData = new FormData()
  formData.append('taskId', taskId)
  if (commentId) {
    formData.append('commentId', commentId)
  }
  files.forEach(file => {
    formData.append('files', file)
  })
  formData.append('isPublic', String(isPublic))

  return request.post<TaskAttachment[]>('/api/v1/task-attachments/batch-upload', formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export function downloadAttachment(attachmentId: string) {
  return request.get(`/api/v1/task-attachments/${attachmentId}/download`, {
    responseType: 'blob'
  })
}

export function previewAttachment(attachmentId: string) {
  return request.get(`/api/v1/task-attachments/${attachmentId}/preview`, {
    responseType: 'blob'
  })
}

export function deleteAttachment(attachmentId: string) {
  return request.delete(`/api/v1/task-attachments/${attachmentId}`)
}

export function getAttachment(attachmentId: string) {
  return request.get<TaskAttachment>(`/api/v1/task-attachments/${attachmentId}`)
}

export function getTaskAttachments(taskId: string) {
  return request.get<TaskAttachment[]>(`/api/v1/task-attachments/tasks/${taskId}`)
}

export function getCommentAttachments(commentId: string) {
  return request.get<TaskAttachment[]>(`/api/v1/task-attachments/comments/${commentId}`)
}

export function getAttachmentsByFileType(taskId: string, fileType: string) {
  return request.get<TaskAttachment[]>(`/api/v1/task-attachments/tasks/${taskId}/type/${fileType}`)
}

export function getUserAttachments(userId: string, params?: {
  fileType?: string
  status?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: TaskAttachment[]
    total: number
    size: number
    current: number
    pages: number
  }>(`/api/v1/task-attachments/users/${userId}`, { params })
}

export function getPublicAttachments(taskId: string) {
  return request.get<TaskAttachment[]>(`/api/v1/task-attachments/tasks/${taskId}/public`)
}

export function getPrivateAttachments(taskId: string) {
  return request.get<TaskAttachment[]>(`/api/v1/task-attachments/tasks/${taskId}/private`)
}

export function searchAttachments(params: {
  taskId?: string
  keyword?: string
  fileType?: string
  uploaderId?: string
  status?: string
  page?: number
  size?: number
}) {
  return request.get<{
    records: TaskAttachment[]
    total: number
    size: number
    current: number
    pages: number
  }>('/api/v1/task-attachments/search', { params })
}

export function batchDeleteAttachments(attachmentIds: string[]) {
  return request.delete('/api/v1/task-attachments/batch', { data: attachmentIds })
}

export function batchUpdateAttachmentStatus(attachmentIds: string[], status: string) {
  return request.put('/api/v1/task-attachments/batch/status', null, {
    params: { attachmentIds, status }
  })
}

export function getAttachmentStatistics(taskId: string) {
  return request.get(`/api/v1/task-attachments/tasks/${taskId}/statistics`)
}

export function getFileTypeStatistics(taskId: string) {
  return request.get(`/api/v1/task-attachments/tasks/${taskId}/file-types`)
}

export function getUploaderStatistics(taskId: string) {
  return request.get(`/api/v1/task-attachments/tasks/${taskId}/uploaders`)
}

export function cleanupTempFiles() {
  return request.post('/api/v1/task-attachments/cleanup')
}

export function handleExpiredAttachments() {
  return request.post('/api/v1/task-attachments/handle-expired')
}
