package com.wtms;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.transaction.annotation.EnableTransactionManagement;

/**
 * WTMS工作任务管理平台启动类
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@SpringBootApplication
@EnableTransactionManagement
@MapperScan("com.wtms.mapper")
public class WtmsApplication {

    public static void main(String[] args) {
        SpringApplication.run(WtmsApplication.class, args);
        System.out.println("=================================");
        System.out.println("WTMS工作任务管理平台启动成功！");
        System.out.println("API文档地址: http://localhost:8080/swagger-ui.html");
        System.out.println("=================================");
    }
}
