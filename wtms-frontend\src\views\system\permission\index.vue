<template>
  <div class="permission-management">
    <el-card>
      <template #header>
        <div class="card-header">
          <span>权限管理</span>
          <div class="header-actions">
            <el-button type="primary" @click="handleCreate">
              <el-icon><Plus /></el-icon>
              新增权限
            </el-button>
            <el-button @click="handleRefresh">
              <el-icon><Refresh /></el-icon>
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :model="searchForm" inline>
          <el-form-item label="权限名称">
            <el-input
              v-model="searchForm.name"
              placeholder="请输入权限名称"
              clearable
              @keyup.enter="handleSearch"
            />
          </el-form-item>
          <el-form-item label="权限类型">
            <el-select v-model="searchForm.type" placeholder="请选择权限类型" clearable>
              <el-option label="菜单权限" value="menu" />
              <el-option label="按钮权限" value="button" />
              <el-option label="接口权限" value="api" />
              <el-option label="数据权限" value="data" />
            </el-select>
          </el-form-item>
          <el-form-item label="权限分组">
            <el-select v-model="searchForm.groupName" placeholder="请选择权限分组" clearable>
              <el-option label="系统管理" value="system" />
              <el-option label="用户管理" value="user" />
              <el-option label="任务管理" value="task" />
              <el-option label="项目管理" value="project" />
              <el-option label="报表管理" value="report" />
              <el-option label="系统设置" value="setting" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">
              <el-icon><Search /></el-icon>
              搜索
            </el-button>
            <el-button @click="handleReset">
              <el-icon><RefreshLeft /></el-icon>
              重置
            </el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 权限树表格 -->
      <el-table
        v-loading="loading"
        :data="permissionTree"
        row-key="id"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        :default-expand-all="false"
        stripe
      >
        <el-table-column prop="name" label="权限名称" min-width="200">
          <template #default="{ row }">
            <div class="permission-name">
              <el-tag v-if="row.isSystem" type="warning" size="small">系统</el-tag>
              <span>{{ row.name }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="code" label="权限编码" min-width="150" />
        <el-table-column prop="typeText" label="权限类型" width="100">
          <template #default="{ row }">
            <el-tag :type="getTypeTagType(row.type)" size="small">
              {{ row.typeText }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="groupName" label="权限分组" width="120" />
        <el-table-column prop="resource" label="资源" width="120" />
        <el-table-column prop="action" label="操作" width="100" />
        <el-table-column prop="isEnabled" label="状态" width="80">
          <template #default="{ row }">
            <el-switch
              v-model="row.isEnabled"
              @change="handleStatusChange(row)"
              :disabled="row.isSystem"
            />
          </template>
        </el-table-column>
        <el-table-column prop="sortOrder" label="排序" width="80" />
        <el-table-column label="操作" width="200" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="handleEdit(row)">
              <el-icon><Edit /></el-icon>
              编辑
            </el-button>
            <el-button type="success" link @click="handleAddChild(row)">
              <el-icon><Plus /></el-icon>
              添加子权限
            </el-button>
            <el-button
              type="danger"
              link
              @click="handleDelete(row)"
              :disabled="row.isSystem || (row.children && row.children.length > 0)"
            >
              <el-icon><Delete /></el-icon>
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 权限编辑对话框 -->
    <PermissionDialog
      v-model:visible="dialogVisible"
      :permission="currentPermission"
      :parent-permission="parentPermission"
      :is-edit="isEdit"
      @success="handleDialogSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus, Refresh, Search, RefreshLeft, Edit, Delete } from '@element-plus/icons-vue'
import {
  getPermissionTree,
  enablePermission,
  disablePermission,
  deletePermission,
  refreshPermissionCache,
  type PermissionTreeResponse
} from '@/api/permission'
import PermissionDialog from './components/PermissionDialog.vue'

// 响应式数据
const loading = ref(false)
const dialogVisible = ref(false)
const isEdit = ref(false)
const currentPermission = ref<PermissionTreeResponse | null>(null)
const parentPermission = ref<PermissionTreeResponse | null>(null)
const permissionTree = ref<PermissionTreeResponse[]>([])

// 搜索表单
const searchForm = reactive({
  name: '',
  type: '',
  groupName: ''
})

// 获取权限树
const loadPermissionTree = async () => {
  try {
    loading.value = true
    const { data } = await getPermissionTree()
    permissionTree.value = data
  } catch (error) {
    console.error('Failed to load permission tree:', error)
    ElMessage.error('加载权限树失败')
  } finally {
    loading.value = false
  }
}

// 获取类型标签类型
const getTypeTagType = (type: string) => {
  const typeMap: Record<string, string> = {
    menu: 'primary',
    button: 'success',
    api: 'warning',
    data: 'info'
  }
  return typeMap[type] || 'info'
}

// 处理搜索
const handleSearch = () => {
  // TODO: 实现搜索逻辑
  loadPermissionTree()
}

// 处理重置
const handleReset = () => {
  Object.assign(searchForm, {
    name: '',
    type: '',
    groupName: ''
  })
  loadPermissionTree()
}

// 处理刷新
const handleRefresh = async () => {
  try {
    await refreshPermissionCache()
    await loadPermissionTree()
    ElMessage.success('刷新成功')
  } catch (error) {
    console.error('Failed to refresh:', error)
    ElMessage.error('刷新失败')
  }
}

// 处理创建
const handleCreate = () => {
  currentPermission.value = null
  parentPermission.value = null
  isEdit.value = false
  dialogVisible.value = true
}

// 处理编辑
const handleEdit = (permission: PermissionTreeResponse) => {
  currentPermission.value = permission
  parentPermission.value = null
  isEdit.value = true
  dialogVisible.value = true
}

// 处理添加子权限
const handleAddChild = (permission: PermissionTreeResponse) => {
  currentPermission.value = null
  parentPermission.value = permission
  isEdit.value = false
  dialogVisible.value = true
}

// 处理状态变更
const handleStatusChange = async (permission: PermissionTreeResponse) => {
  try {
    if (permission.isEnabled) {
      await enablePermission(permission.id)
      ElMessage.success('权限已启用')
    } else {
      await disablePermission(permission.id)
      ElMessage.success('权限已禁用')
    }
  } catch (error) {
    console.error('Failed to change permission status:', error)
    permission.isEnabled = !permission.isEnabled // 回滚状态
    ElMessage.error('状态变更失败')
  }
}

// 处理删除
const handleDelete = async (permission: PermissionTreeResponse) => {
  try {
    await ElMessageBox.confirm(
      `确定要删除权限"${permission.name}"吗？此操作不可恢复。`,
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await deletePermission(permission.id)
    ElMessage.success('删除成功')
    await loadPermissionTree()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('Failed to delete permission:', error)
      ElMessage.error('删除失败')
    }
  }
}

// 处理对话框成功
const handleDialogSuccess = () => {
  dialogVisible.value = false
  loadPermissionTree()
}

// 组件挂载时加载数据
onMounted(() => {
  loadPermissionTree()
})
</script>

<style scoped>
.permission-management {
  padding: 20px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.search-area {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 4px;
}

.permission-name {
  display: flex;
  align-items: center;
  gap: 8px;
}

.permission-name .el-tag {
  margin-right: 8px;
}
</style>
