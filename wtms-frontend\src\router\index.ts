import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import { useUserStore } from '@/stores/user'
import NProgress from 'nprogress'
import 'nprogress/nprogress.css'

// 配置NProgress
NProgress.configure({ showSpinner: false })

const routes: RouteRecordRaw[] = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/Login.vue'),
    meta: {
      title: '登录',
      requiresAuth: false,
      hideInMenu: true
    }
  },
  {
    path: '/',
    component: () => import('@/layouts/MainLayout.vue'),
    redirect: '/dashboard',
    meta: {
      requiresAuth: true
    },
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/dashboard/Dashboard.vue'),
        meta: {
          title: '工作台',
          icon: 'House',
          requiresAuth: true
        }
      },
      {
        path: 'tasks',
        name: 'Tasks',
        component: () => import('@/views/task/TaskList.vue'),
        meta: {
          title: '任务管理',
          icon: 'List',
          requiresAuth: true
        }
      },
      {
        path: 'tasks/:id',
        name: 'TaskDetail',
        component: () => import('@/views/task/TaskDetail.vue'),
        meta: {
          title: '任务详情',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: 'my-tasks',
        name: 'MyTasks',
        component: () => import('@/views/task/MyTasks.vue'),
        meta: {
          title: '我的任务',
          icon: 'User',
          requiresAuth: true
        }
      },
      {
        path: 'workflow',
        name: 'Workflow',
        component: () => import('@/views/workflow/WorkflowList.vue'),
        meta: {
          title: '工作流',
          icon: 'Share',
          requiresAuth: true
        }
      },
      {
        path: 'workflow/designer/:id?',
        name: 'WorkflowDesigner',
        component: () => import('@/views/workflow/WorkflowDesigner.vue'),
        meta: {
          title: '工作流设计器',
          requiresAuth: true,
          hideInMenu: true
        }
      },
      {
        path: 'evaluation',
        name: 'Evaluation',
        component: () => import('@/views/evaluation/EvaluationCenter.vue'),
        meta: {
          title: '质量评价',
          icon: 'Star',
          requiresAuth: true
        }
      },
      {
        path: 'team',
        name: 'Team',
        component: () => import('@/views/team/TeamManagement.vue'),
        meta: {
          title: '团队管理',
          icon: 'User',
          requiresAuth: true
        }
      },
      {
        path: 'analytics',
        name: 'Analytics',
        component: () => import('@/views/analytics/AnalyticsDashboard.vue'),
        meta: {
          title: '数据分析',
          icon: 'TrendCharts',
          requiresAuth: true
        }
      },
      {
        path: 'settings',
        name: 'Settings',
        component: () => import('@/views/settings/SystemSettings.vue'),
        meta: {
          title: '系统设置',
          icon: 'Setting',
          requiresAuth: true
        }
      }
    ]
  },
  {
    path: '/403',
    name: 'Forbidden',
    component: () => import('@/views/error/403.vue'),
    meta: {
      title: '权限不足',
      hideInMenu: true
    }
  },
  {
    path: '/404',
    name: 'NotFound',
    component: () => import('@/views/error/404.vue'),
    meta: {
      title: '页面不存在',
      hideInMenu: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    redirect: '/404'
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition
    } else {
      return { top: 0 }
    }
  }
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  NProgress.start()

  const userStore = useUserStore()

  // 设置页面标题
  if (to.meta.title) {
    document.title = `${to.meta.title} - WTMS工作任务管理平台`
  }

  // 检查是否需要认证
  if (to.meta.requiresAuth) {
    const hasToken = !!userStore.token
    const hasUser = !!userStore.currentUser

    console.log('Route guard check:', {
      path: to.path,
      hasToken,
      hasUser,
      isLoggedIn: userStore.isLoggedIn
    })

    // 如果没有token，直接跳转到登录页
    if (!hasToken) {
      console.log('No token found, redirecting to login')
      next({
        path: '/login',
        query: { redirect: to.fullPath }
      })
      return
    }

    // 如果有token但没有用户信息，尝试获取用户信息
    if (hasToken && !hasUser) {
      console.log('Has token but no user info, fetching user info...')
      try {
        await userStore.getCurrentUserInfo()
        console.log('User info fetched successfully')
      } catch (error) {
        console.error('Failed to get user info:', error)
        // 获取用户信息失败，清除token并跳转到登录页
        userStore.logout()
        next({
          path: '/login',
          query: { redirect: to.fullPath }
        })
        return
      }
    }

    // 检查权限
    if (to.meta.permission && !userStore.hasPermission(to.meta.permission as string)) {
      console.log('Permission denied for:', to.meta.permission)
      next('/403')
      return
    }
  }

  // 已登录用户访问登录页，重定向到首页
  if (to.path === '/login' && userStore.isLoggedIn) {
    next('/')
    return
  }

  next()
})

router.afterEach(() => {
  NProgress.done()
})

export default router
