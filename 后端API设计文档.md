# WTMS 后端API设计文档

## 1. API设计原则

### 1.1 RESTful设计规范
- 使用HTTP动词表示操作：GET（查询）、POST（创建）、PUT（更新）、DELETE（删除）
- 使用名词表示资源，避免动词
- 使用复数形式表示资源集合
- 使用嵌套路径表示资源关系

### 1.2 统一响应格式
```typescript
interface ApiResponse<T> {
  success: boolean;
  code: number;
  message: string;
  data?: T;
  error?: {
    code: string;
    message: string;
    details?: any;
  };
  pagination?: {
    page: number;
    pageSize: number;
    total: number;
    totalPages: number;
  };
  timestamp: number;
}
```

### 1.3 HTTP状态码规范
- 200 OK：请求成功
- 201 Created：资源创建成功
- 204 No Content：请求成功但无返回内容
- 400 Bad Request：请求参数错误
- 401 Unauthorized：未授权
- 403 Forbidden：权限不足
- 404 Not Found：资源不存在
- 409 Conflict：资源冲突
- 422 Unprocessable Entity：请求格式正确但语义错误
- 500 Internal Server Error：服务器内部错误

## 2. 认证和授权API

### 2.1 用户认证
```typescript
// 用户登录
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "string",
  "password": "string",
  "captcha": "string",
  "captchaId": "string"
}

Response:
{
  "success": true,
  "data": {
    "token": "jwt_token_string",
    "refreshToken": "refresh_token_string",
    "user": {
      "id": "uuid",
      "username": "string",
      "fullName": "string",
      "email": "string",
      "avatar": "string",
      "department": {
        "id": "uuid",
        "name": "string"
      },
      "role": {
        "id": "uuid",
        "name": "string",
        "permissions": ["string"]
      }
    },
    "expiresIn": 3600
  }
}

// 刷新Token
POST /api/v1/auth/refresh
Authorization: Bearer {refresh_token}

// 用户登出
POST /api/v1/auth/logout
Authorization: Bearer {access_token}

// 获取当前用户信息
GET /api/v1/auth/me
Authorization: Bearer {access_token}
```

### 2.2 密码管理
```typescript
// 修改密码
PUT /api/v1/auth/password
Authorization: Bearer {access_token}
{
  "oldPassword": "string",
  "newPassword": "string"
}

// 忘记密码
POST /api/v1/auth/forgot-password
{
  "email": "string"
}

// 重置密码
POST /api/v1/auth/reset-password
{
  "token": "string",
  "newPassword": "string"
}
```

## 3. 用户管理API

### 3.1 用户CRUD操作
```typescript
// 获取用户列表
GET /api/v1/users?page=1&pageSize=20&search=keyword&departmentId=uuid&status=active
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "username": "string",
      "fullName": "string",
      "email": "string",
      "phone": "string",
      "avatar": "string",
      "employeeId": "string",
      "department": {
        "id": "uuid",
        "name": "string"
      },
      "role": {
        "id": "uuid",
        "name": "string"
      },
      "status": "active",
      "lastLoginAt": "2024-01-01T00:00:00Z",
      "createdAt": "2024-01-01T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "total": 100,
    "totalPages": 5
  }
}

// 创建用户
POST /api/v1/users
Authorization: Bearer {access_token}
{
  "username": "string",
  "email": "string",
  "fullName": "string",
  "phone": "string",
  "departmentId": "uuid",
  "roleId": "uuid",
  "employeeId": "string"
}

// 获取用户详情
GET /api/v1/users/{userId}
Authorization: Bearer {access_token}

// 更新用户信息
PUT /api/v1/users/{userId}
Authorization: Bearer {access_token}
{
  "fullName": "string",
  "email": "string",
  "phone": "string",
  "departmentId": "uuid",
  "roleId": "uuid"
}

// 删除用户（软删除）
DELETE /api/v1/users/{userId}
Authorization: Bearer {access_token}
```

### 3.2 用户技能管理
```typescript
// 获取用户技能
GET /api/v1/users/{userId}/skills
Authorization: Bearer {access_token}

// 更新用户技能
PUT /api/v1/users/{userId}/skills
Authorization: Bearer {access_token}
{
  "skills": [
    {
      "skillId": "uuid",
      "level": 3,
      "certified": true,
      "notes": "string"
    }
  ]
}
```

## 4. 任务管理API

### 4.1 任务CRUD操作
```typescript
// 获取任务列表
GET /api/v1/tasks?page=1&pageSize=20&status=in_progress&assigneeId=uuid&categoryId=uuid&priority=high&search=keyword&sortBy=createdAt&sortOrder=desc
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": [
    {
      "id": "uuid",
      "title": "string",
      "taskCode": "string",
      "description": "string",
      "status": "in_progress",
      "priority": 3,
      "difficultyLevel": 2,
      "businessValue": 4,
      "progress": 65.5,
      "estimatedHours": 40.0,
      "actualHours": 26.0,
      "category": {
        "id": "uuid",
        "name": "string",
        "color": "#1890ff"
      },
      "assignee": {
        "id": "uuid",
        "fullName": "string",
        "avatar": "string"
      },
      "creator": {
        "id": "uuid",
        "fullName": "string"
      },
      "tags": ["frontend", "urgent"],
      "dueDate": "2024-01-15T00:00:00Z",
      "createdAt": "2024-01-01T00:00:00Z",
      "updatedAt": "2024-01-10T00:00:00Z"
    }
  ],
  "pagination": {
    "page": 1,
    "pageSize": 20,
    "total": 150,
    "totalPages": 8
  }
}

// 创建任务
POST /api/v1/tasks
Authorization: Bearer {access_token}
{
  "title": "string",
  "description": "string",
  "categoryId": "uuid",
  "priority": 3,
  "difficultyLevel": 2,
  "businessValue": 4,
  "estimatedHours": 40.0,
  "assigneeId": "uuid",
  "dueDate": "2024-01-15T00:00:00Z",
  "tags": ["string"],
  "skillRequirements": [
    {
      "skillId": "uuid",
      "requiredLevel": 3,
      "isMandatory": true
    }
  ]
}

// 获取任务详情
GET /api/v1/tasks/{taskId}
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "id": "uuid",
    "title": "string",
    "taskCode": "string",
    "description": "string",
    "status": "in_progress",
    "priority": 3,
    "difficultyLevel": 2,
    "businessValue": 4,
    "progress": 65.5,
    "estimatedHours": 40.0,
    "actualHours": 26.0,
    "category": {
      "id": "uuid",
      "name": "string",
      "color": "#1890ff"
    },
    "assignee": {
      "id": "uuid",
      "fullName": "string",
      "avatar": "string",
      "email": "string"
    },
    "creator": {
      "id": "uuid",
      "fullName": "string"
    },
    "reviewer": {
      "id": "uuid",
      "fullName": "string"
    },
    "tags": ["frontend", "urgent"],
    "customFields": {},
    "attachments": [
      {
        "id": "uuid",
        "name": "string",
        "url": "string",
        "size": 1024,
        "type": "image/png"
      }
    ],
    "skillRequirements": [
      {
        "skill": {
          "id": "uuid",
          "name": "string"
        },
        "requiredLevel": 3,
        "isMandatory": true
      }
    ],
    "dependencies": [
      {
        "id": "uuid",
        "predecessor": {
          "id": "uuid",
          "title": "string"
        },
        "type": "FS",
        "lagDays": 0
      }
    ],
    "subtasks": [
      {
        "id": "uuid",
        "title": "string",
        "status": "completed",
        "progress": 100
      }
    ],
    "plannedStartDate": "2024-01-01T00:00:00Z",
    "plannedEndDate": "2024-01-15T00:00:00Z",
    "actualStartDate": "2024-01-02T00:00:00Z",
    "actualEndDate": null,
    "dueDate": "2024-01-15T00:00:00Z",
    "createdAt": "2024-01-01T00:00:00Z",
    "updatedAt": "2024-01-10T00:00:00Z"
  }
}

// 更新任务
PUT /api/v1/tasks/{taskId}
Authorization: Bearer {access_token}
{
  "title": "string",
  "description": "string",
  "priority": 3,
  "assigneeId": "uuid",
  "dueDate": "2024-01-15T00:00:00Z",
  "tags": ["string"]
}

// 删除任务
DELETE /api/v1/tasks/{taskId}
Authorization: Bearer {access_token}
```

### 4.2 任务状态管理
```typescript
// 更新任务状态
PUT /api/v1/tasks/{taskId}/status
Authorization: Bearer {access_token}
{
  "status": "in_progress",
  "comment": "string"
}

// 更新任务进度
PUT /api/v1/tasks/{taskId}/progress
Authorization: Bearer {access_token}
{
  "progress": 75.5,
  "actualHours": 30.0,
  "comment": "string"
}

// 分配任务
POST /api/v1/tasks/{taskId}/assign
Authorization: Bearer {access_token}
{
  "assigneeId": "uuid",
  "comment": "string"
}

// 开始任务
POST /api/v1/tasks/{taskId}/start
Authorization: Bearer {access_token}

// 完成任务
POST /api/v1/tasks/{taskId}/complete
Authorization: Bearer {access_token}
{
  "actualHours": 35.0,
  "completionNotes": "string"
}
```

### 4.3 任务依赖管理
```typescript
// 获取任务依赖
GET /api/v1/tasks/{taskId}/dependencies
Authorization: Bearer {access_token}

// 添加任务依赖
POST /api/v1/tasks/{taskId}/dependencies
Authorization: Bearer {access_token}
{
  "predecessorId": "uuid",
  "type": "FS", // FS, SS, FF, SF
  "lagDays": 0,
  "description": "string"
}

// 删除任务依赖
DELETE /api/v1/tasks/{taskId}/dependencies/{dependencyId}
Authorization: Bearer {access_token}
```

### 4.4 任务评论
```typescript
// 获取任务评论
GET /api/v1/tasks/{taskId}/comments?page=1&pageSize=20
Authorization: Bearer {access_token}

// 添加任务评论
POST /api/v1/tasks/{taskId}/comments
Authorization: Bearer {access_token}
{
  "content": "string",
  "parentId": "uuid", // 可选，用于回复
  "isInternal": false,
  "attachments": [
    {
      "name": "string",
      "url": "string"
    }
  ]
}

// 更新评论
PUT /api/v1/tasks/{taskId}/comments/{commentId}
Authorization: Bearer {access_token}
{
  "content": "string"
}

// 删除评论
DELETE /api/v1/tasks/{taskId}/comments/{commentId}
Authorization: Bearer {access_token}
```

## 5. 工作流管理API

### 5.1 工作流定义管理
```typescript
// 获取工作流列表
GET /api/v1/workflows?page=1&pageSize=20&category=string&status=active
Authorization: Bearer {access_token}

// 创建工作流
POST /api/v1/workflows
Authorization: Bearer {access_token}
{
  "name": "string",
  "code": "string",
  "description": "string",
  "category": "string",
  "definition": {
    "nodes": [
      {
        "id": "start",
        "type": "start",
        "name": "开始",
        "position": { "x": 100, "y": 100 }
      },
      {
        "id": "task1",
        "type": "task",
        "name": "任务节点",
        "position": { "x": 300, "y": 100 },
        "config": {
          "assignee": "uuid",
          "dueDate": "3d",
          "autoAssign": true
        }
      }
    ],
    "edges": [
      {
        "id": "edge1",
        "source": "start",
        "target": "task1"
      }
    ]
  }
}

// 获取工作流详情
GET /api/v1/workflows/{workflowId}
Authorization: Bearer {access_token}

// 更新工作流
PUT /api/v1/workflows/{workflowId}
Authorization: Bearer {access_token}

// 发布工作流
POST /api/v1/workflows/{workflowId}/deploy
Authorization: Bearer {access_token}

// 删除工作流
DELETE /api/v1/workflows/{workflowId}
Authorization: Bearer {access_token}
```

### 5.2 工作流实例管理
```typescript
// 启动工作流实例
POST /api/v1/workflows/{workflowId}/instances
Authorization: Bearer {access_token}
{
  "taskId": "uuid",
  "variables": {
    "priority": "high",
    "department": "tech"
  }
}

// 获取工作流实例列表
GET /api/v1/workflow-instances?page=1&pageSize=20&status=running&workflowId=uuid
Authorization: Bearer {access_token}

// 获取工作流实例详情
GET /api/v1/workflow-instances/{instanceId}
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "id": "uuid",
    "workflow": {
      "id": "uuid",
      "name": "string",
      "version": 1
    },
    "task": {
      "id": "uuid",
      "title": "string"
    },
    "status": "running",
    "currentStep": "task1",
    "variables": {},
    "steps": [
      {
        "id": "uuid",
        "stepName": "task1",
        "status": "completed",
        "assignee": {
          "id": "uuid",
          "fullName": "string"
        },
        "startedAt": "2024-01-01T00:00:00Z",
        "completedAt": "2024-01-02T00:00:00Z"
      }
    ],
    "startedAt": "2024-01-01T00:00:00Z",
    "completedAt": null
  }
}
```

### 5.3 审批操作
```typescript
// 获取待审批任务
GET /api/v1/approvals/pending?page=1&pageSize=20
Authorization: Bearer {access_token}

// 执行审批操作
POST /api/v1/workflow-instances/{instanceId}/approve
Authorization: Bearer {access_token}
{
  "stepName": "string",
  "action": "approve", // approve, reject, delegate, request_change
  "comment": "string",
  "delegatedTo": "uuid" // 委托时必填
}

// 获取审批历史
GET /api/v1/workflow-instances/{instanceId}/approvals
Authorization: Bearer {access_token}
```

## 6. 质量评价API

### 6.1 评价模板管理
```typescript
// 获取评价模板列表
GET /api/v1/evaluation-templates?categoryId=uuid
Authorization: Bearer {access_token}

// 创建评价模板
POST /api/v1/evaluation-templates
Authorization: Bearer {access_token}
{
  "name": "string",
  "description": "string",
  "taskCategoryId": "uuid",
  "dimensions": [
    {
      "id": "quality",
      "name": "质量",
      "weight": 0.4,
      "metrics": [
        {
          "id": "accuracy",
          "name": "准确性",
          "type": "score",
          "scale": 5,
          "criteria": ["string"]
        }
      ]
    }
  ],
  "scoringMethod": "weighted",
  "passingScore": 60.0
}
```

### 6.2 任务评价
```typescript
// 创建任务评价
POST /api/v1/tasks/{taskId}/evaluations
Authorization: Bearer {access_token}
{
  "templateId": "uuid",
  "evaluationType": "supervisor", // self, peer, supervisor, expert
  "scores": {
    "quality": {
      "accuracy": 4,
      "completeness": 5
    },
    "efficiency": {
      "timeManagement": 3
    }
  },
  "comments": "string",
  "strengths": "string",
  "improvements": "string"
}

// 获取任务评价列表
GET /api/v1/tasks/{taskId}/evaluations
Authorization: Bearer {access_token}

// 提交评价
PUT /api/v1/evaluations/{evaluationId}/submit
Authorization: Bearer {access_token}
```

## 7. 数据分析API

### 7.1 统计数据
```typescript
// 获取仪表板数据
GET /api/v1/analytics/dashboard?period=30d&departmentId=uuid
Authorization: Bearer {access_token}

Response:
{
  "success": true,
  "data": {
    "summary": {
      "totalTasks": 150,
      "completedTasks": 120,
      "inProgressTasks": 25,
      "overdueTasks": 5,
      "completionRate": 80.0,
      "averageCompletionTime": 5.2
    },
    "trends": {
      "taskCompletion": [
        { "date": "2024-01-01", "completed": 5, "created": 8 }
      ],
      "efficiency": [
        { "date": "2024-01-01", "avgHours": 6.5 }
      ]
    },
    "distribution": {
      "byStatus": [
        { "status": "completed", "count": 120 },
        { "status": "in_progress", "count": 25 }
      ],
      "byPriority": [
        { "priority": "high", "count": 30 },
        { "priority": "medium", "count": 80 }
      ]
    }
  }
}

// 获取个人绩效数据
GET /api/v1/analytics/performance/{userId}?period=30d
Authorization: Bearer {access_token}

// 获取团队效率分析
GET /api/v1/analytics/team-efficiency?departmentId=uuid&period=30d
Authorization: Bearer {access_token}
```

## 8. 文件管理API

### 8.1 文件上传
```typescript
// 上传文件
POST /api/v1/files/upload
Authorization: Bearer {access_token}
Content-Type: multipart/form-data

FormData:
- file: File
- category: string (optional)
- description: string (optional)

Response:
{
  "success": true,
  "data": {
    "id": "uuid",
    "name": "string",
    "originalName": "string",
    "url": "string",
    "size": 1024,
    "mimeType": "image/png",
    "category": "string",
    "uploadedBy": "uuid",
    "uploadedAt": "2024-01-01T00:00:00Z"
  }
}

// 获取文件信息
GET /api/v1/files/{fileId}
Authorization: Bearer {access_token}

// 删除文件
DELETE /api/v1/files/{fileId}
Authorization: Bearer {access_token}
```

## 9. 通知管理API

### 9.1 通知操作
```typescript
// 获取通知列表
GET /api/v1/notifications?page=1&pageSize=20&status=unread&type=task_assigned
Authorization: Bearer {access_token}

// 标记通知为已读
PUT /api/v1/notifications/{notificationId}/read
Authorization: Bearer {access_token}

// 批量标记已读
PUT /api/v1/notifications/batch-read
Authorization: Bearer {access_token}
{
  "notificationIds": ["uuid"]
}

// 获取未读通知数量
GET /api/v1/notifications/unread-count
Authorization: Bearer {access_token}
```

---

**注：** 本API设计文档为后端接口的详细规范，实际开发时需要根据具体业务需求进行调整和扩展。所有接口都需要进行适当的参数验证、权限检查和错误处理。
