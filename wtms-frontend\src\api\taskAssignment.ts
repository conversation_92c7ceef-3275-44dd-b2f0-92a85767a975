import request from '@/utils/request'

/**
 * 任务分配请求
 */
export interface TaskAssignRequest {
  assignType: 'user' | 'department' | 'role'
  assignTargets: string[]
  comment?: string
  sendNotification?: boolean
  deadline?: string
  priorityAdjustment?: number
}

/**
 * 批量分配请求
 */
export interface BatchAssignRequest {
  taskIds: string[]
  assignRule?: 'round_robin' | 'workload_balance' | 'skill_match' | 'manual'
  assignTargets: AssignTarget[]
  comment?: string
  sendNotification?: boolean
}

/**
 * 分配目标
 */
export interface AssignTarget {
  targetType: 'user' | 'department' | 'role'
  targetId: string
  targetName: string
  weight?: number
  maxTasks?: number
}

/**
 * 任务分配响应
 */
export interface TaskAssignResponse {
  assignId: string
  taskId: string
  taskTitle: string
  assignType: string
  assigner: {
    id: string
    username: string
    fullName: string
    avatar: string
  }
  assignees: Array<{
    targetType: string
    targetId: string
    targetName: string
    avatar: string
    status: string
    acceptedAt?: string
    rejectReason?: string
  }>
  status: string
  comment?: string
  assignedAt: string
  acceptedAt?: string
  deadline?: string
}

/**
 * 推荐的分配目标
 */
export interface RecommendedAssignee {
  targetType: string
  targetId: string
  targetName: string
  avatar: string
  score: number
  reason: string
  currentWorkload: number
  skillMatch: number
}

/**
 * 用户工作负载信息
 */
export interface UserWorkloadInfo {
  userId: string
  userName: string
  currentTasks: number
  pendingAssignments: number
  completedTasks: number
  averageCompletionTime: number
  workloadScore: number
}

/**
 * 部门工作负载信息
 */
export interface DepartmentWorkloadInfo {
  departmentId: string
  departmentName: string
  memberCount: number
  totalCurrentTasks: number
  totalPendingAssignments: number
  totalCompletedTasks: number
  averageWorkloadPerMember: number
  departmentEfficiency: number
}

/**
 * 分页结果
 */
export interface PageResult<T> {
  records: T[]
  total: number
  size: number
  current: number
  pages: number
}

// API 接口函数

/**
 * 分配任务
 */
export function assignTask(taskId: string, data: TaskAssignRequest) {
  return request.post<TaskAssignResponse[]>(`/api/v1/task-assignments/tasks/${taskId}/assign`, data)
}

/**
 * 批量分配任务
 */
export function batchAssignTasks(data: BatchAssignRequest) {
  return request.post<TaskAssignResponse[]>('/api/v1/task-assignments/batch-assign', data)
}

/**
 * 接受任务分配
 */
export function acceptAssignment(assignmentId: string) {
  return request.put<TaskAssignResponse>(`/api/v1/task-assignments/${assignmentId}/accept`)
}

/**
 * 拒绝任务分配
 */
export function rejectAssignment(assignmentId: string, rejectReason?: string) {
  return request.put<TaskAssignResponse>(`/api/v1/task-assignments/${assignmentId}/reject`, null, {
    params: { rejectReason }
  })
}

/**
 * 取消任务分配
 */
export function cancelAssignment(assignmentId: string) {
  return request.delete(`/api/v1/task-assignments/${assignmentId}`)
}

/**
 * 重新分配任务
 */
export function reassignTask(assignmentId: string, data: TaskAssignRequest) {
  return request.post<TaskAssignResponse[]>(`/api/v1/task-assignments/${assignmentId}/reassign`, data)
}

/**
 * 获取任务分配记录
 */
export function getTaskAssignments(taskId: string) {
  return request.get<TaskAssignResponse[]>(`/api/v1/task-assignments/tasks/${taskId}`)
}

/**
 * 获取用户分配记录
 */
export function getUserAssignments(params: {
  userId: string
  status?: string
  page?: number
  size?: number
}) {
  return request.get<PageResult<TaskAssignResponse>>(`/api/v1/task-assignments/users/${params.userId}`, {
    params: {
      status: params.status,
      page: params.page,
      size: params.size
    }
  })
}

/**
 * 获取部门分配记录
 */
export function getDepartmentAssignments(params: {
  departmentId: string
  status?: string
  page?: number
  size?: number
}) {
  return request.get<PageResult<TaskAssignResponse>>(`/api/v1/task-assignments/departments/${params.departmentId}`, {
    params: {
      status: params.status,
      page: params.page,
      size: params.size
    }
  })
}

/**
 * 获取我分配的任务
 */
export function getMyAssignments(params: {
  status?: string
  page?: number
  size?: number
}) {
  return request.get<PageResult<TaskAssignResponse>>('/api/v1/task-assignments/my-assignments', { params })
}

/**
 * 自动分配任务
 */
export function autoAssignTask(taskId: string, assignRule: string = 'workload_balance') {
  return request.post<TaskAssignResponse[]>(`/api/v1/task-assignments/tasks/${taskId}/auto-assign`, null, {
    params: { assignRule }
  })
}

/**
 * 推荐分配目标
 */
export function recommendAssignees(taskId: string, limit: number = 5) {
  return request.get<RecommendedAssignee[]>(`/api/v1/task-assignments/tasks/${taskId}/recommend-assignees`, {
    params: { limit }
  })
}

/**
 * 获取用户工作负载
 */
export function getUserWorkload(userId: string) {
  return request.get<UserWorkloadInfo>(`/api/v1/task-assignments/workload/users/${userId}`)
}

/**
 * 获取部门工作负载
 */
export function getDepartmentWorkload(departmentId: string) {
  return request.get<DepartmentWorkloadInfo>(`/api/v1/task-assignments/workload/departments/${departmentId}`)
}

/**
 * 获取分配统计
 */
export function getAssignmentStatistics(params?: {
  userId?: string
  departmentId?: string
}) {
  return request.get('/api/v1/task-assignments/statistics', { params })
}

/**
 * 发送分配通知
 */
export function sendAssignmentNotification(assignmentId: string) {
  return request.post(`/api/v1/task-assignments/${assignmentId}/notify`)
}
