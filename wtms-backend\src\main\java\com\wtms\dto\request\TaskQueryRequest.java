package com.wtms.dto.request;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 任务查询请求DTO
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@Schema(description = "任务查询请求")
public class TaskQueryRequest {

    @Schema(description = "关键词搜索", example = "用户管理")
    private String search;

    @Schema(description = "任务状态", example = "in_progress")
    private String status;

    @Schema(description = "任务状态列表", example = "[\"pending\", \"in_progress\"]")
    private List<String> statusList;

    @Schema(description = "优先级", example = "3")
    @Min(value = 1, message = "优先级最小值为1")
    @Max(value = 5, message = "优先级最大值为5")
    private Integer priority;

    @Schema(description = "优先级列表", example = "[3, 4, 5]")
    private List<Integer> priorityList;

    @Schema(description = "难度等级", example = "3")
    @Min(value = 1, message = "难度等级最小值为1")
    @Max(value = 5, message = "难度等级最大值为5")
    private Integer difficultyLevel;

    @Schema(description = "任务分类ID", example = "uuid-string")
    private String categoryId;

    @Schema(description = "任务分类列表", example = "[\"uuid1\", \"uuid2\"]")
    private List<String> categoryIds;

    @Schema(description = "创建者ID", example = "uuid-string")
    private String creatorId;

    @Schema(description = "负责人ID", example = "uuid-string")
    private String assigneeId;

    @Schema(description = "负责人列表", example = "[\"uuid1\", \"uuid2\"]")
    private List<String> assigneeIds;

    @Schema(description = "审核人ID", example = "uuid-string")
    private String reviewerId;

    @Schema(description = "父任务ID", example = "uuid-string")
    private String parentId;

    @Schema(description = "项目ID", example = "uuid-string")
    private String projectId;

    @Schema(description = "标签", example = "前端")
    private String tag;

    @Schema(description = "标签列表", example = "[\"前端\", \"Vue.js\"]")
    private List<String> tags;

    @Schema(description = "创建时间开始", example = "2024-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAtStart;

    @Schema(description = "创建时间结束", example = "2024-01-31 23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAtEnd;

    @Schema(description = "计划开始时间开始", example = "2024-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedStartDateStart;

    @Schema(description = "计划开始时间结束", example = "2024-01-31 23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedStartDateEnd;

    @Schema(description = "计划结束时间开始", example = "2024-01-01 00:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedEndDateStart;

    @Schema(description = "计划结束时间结束", example = "2024-01-31 23:59:59")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime plannedEndDateEnd;

    @Schema(description = "是否包含子任务", example = "true")
    private Boolean includeSubTasks = false;

    @Schema(description = "是否只查询我的任务", example = "true")
    private Boolean onlyMyTasks = false;

    @Schema(description = "是否只查询我创建的任务", example = "true")
    private Boolean onlyMyCreated = false;

    @Schema(description = "是否包含已归档", example = "false")
    private Boolean includeArchived = false;

    @Schema(description = "排序字段", example = "created_at", allowableValues = {
            "created_at", "updated_at", "title", "priority", "difficulty_level", 
            "planned_start_date", "planned_end_date", "progress"
    })
    private String sortBy = "created_at";

    @Schema(description = "排序方向", example = "desc", allowableValues = {"asc", "desc"})
    private String sortOrder = "desc";
}
