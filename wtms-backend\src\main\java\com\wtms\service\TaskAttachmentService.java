package com.wtms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wtms.entity.TaskAttachment;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 任务附件服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface TaskAttachmentService {

    /**
     * 上传附件
     *
     * @param taskId 任务ID
     * @param commentId 评论ID
     * @param file 文件
     * @param isPublic 是否公开
     * @return 附件信息
     */
    TaskAttachment uploadAttachment(String taskId, String commentId, MultipartFile file, Boolean isPublic);

    /**
     * 批量上传附件
     *
     * @param taskId 任务ID
     * @param commentId 评论ID
     * @param files 文件列表
     * @param isPublic 是否公开
     * @return 附件列表
     */
    List<TaskAttachment> batchUploadAttachments(String taskId, String commentId, List<MultipartFile> files, Boolean isPublic);

    /**
     * 下载附件
     *
     * @param attachmentId 附件ID
     * @param response HTTP响应
     */
    void downloadAttachment(String attachmentId, HttpServletResponse response);

    /**
     * 预览附件
     *
     * @param attachmentId 附件ID
     * @param response HTTP响应
     */
    void previewAttachment(String attachmentId, HttpServletResponse response);

    /**
     * 删除附件
     *
     * @param attachmentId 附件ID
     */
    void deleteAttachment(String attachmentId);

    /**
     * 根据ID获取附件
     *
     * @param attachmentId 附件ID
     * @return 附件信息
     */
    TaskAttachment getAttachmentById(String attachmentId);

    /**
     * 获取任务附件
     *
     * @param taskId 任务ID
     * @return 附件列表
     */
    List<TaskAttachment> getTaskAttachments(String taskId);

    /**
     * 获取评论附件
     *
     * @param commentId 评论ID
     * @return 附件列表
     */
    List<TaskAttachment> getCommentAttachments(String commentId);

    /**
     * 根据文件类型获取附件
     *
     * @param taskId 任务ID
     * @param fileType 文件类型
     * @return 附件列表
     */
    List<TaskAttachment> getAttachmentsByFileType(String taskId, String fileType);

    /**
     * 获取用户上传的附件
     *
     * @param uploaderId 上传者ID
     * @param fileType 文件类型
     * @param status 状态
     * @param page 页码
     * @param size 每页大小
     * @return 附件分页结果
     */
    IPage<TaskAttachment> getUserAttachments(String uploaderId, String fileType, String status, Integer page, Integer size);

    /**
     * 获取公开附件
     *
     * @param taskId 任务ID
     * @return 公开附件列表
     */
    List<TaskAttachment> getPublicAttachments(String taskId);

    /**
     * 获取私有附件
     *
     * @param taskId 任务ID
     * @return 私有附件列表
     */
    List<TaskAttachment> getPrivateAttachments(String taskId);

    /**
     * 搜索附件
     *
     * @param taskId 任务ID
     * @param keyword 关键词
     * @param fileType 文件类型
     * @param uploaderId 上传者ID
     * @param status 状态
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    IPage<TaskAttachment> searchAttachments(String taskId, String keyword, String fileType, 
                                          String uploaderId, String status, Integer page, Integer size);

    /**
     * 批量删除附件
     *
     * @param attachmentIds 附件ID列表
     */
    void batchDeleteAttachments(List<String> attachmentIds);

    /**
     * 批量更新附件状态
     *
     * @param attachmentIds 附件ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<String> attachmentIds, String status);

    /**
     * 统计任务附件数量
     *
     * @param taskId 任务ID
     * @return 附件数量
     */
    int countTaskAttachments(String taskId);

    /**
     * 统计用户上传附件数量
     *
     * @param uploaderId 上传者ID
     * @return 附件数量
     */
    int countUserAttachments(String uploaderId);

    /**
     * 统计附件总大小
     *
     * @param taskId 任务ID
     * @return 总大小（字节）
     */
    long sumAttachmentSize(String taskId);

    /**
     * 统计用户上传附件总大小
     *
     * @param uploaderId 上传者ID
     * @return 总大小（字节）
     */
    long sumUserAttachmentSize(String uploaderId);

    /**
     * 检查文件是否已存在
     *
     * @param fileMd5 文件MD5
     * @return 是否存在
     */
    boolean isFileExists(String fileMd5);

    /**
     * 根据MD5获取文件
     *
     * @param fileMd5 文件MD5
     * @return 附件列表
     */
    List<TaskAttachment> getFilesByMd5(String fileMd5);

    /**
     * 检查用户是否可以下载附件
     *
     * @param attachmentId 附件ID
     * @param userId 用户ID
     * @return 是否可以下载
     */
    boolean canDownloadAttachment(String attachmentId, String userId);

    /**
     * 检查用户是否可以删除附件
     *
     * @param attachmentId 附件ID
     * @param userId 用户ID
     * @return 是否可以删除
     */
    boolean canDeleteAttachment(String attachmentId, String userId);

    /**
     * 处理过期附件
     */
    void handleExpiredAttachments();

    /**
     * 获取大文件附件
     *
     * @param minSize 最小大小
     * @param limit 数量限制
     * @return 大文件附件列表
     */
    List<TaskAttachment> getLargeFiles(Long minSize, Integer limit);

    /**
     * 获取重复文件
     *
     * @param fileMd5 文件MD5
     * @return 重复文件列表
     */
    List<TaskAttachment> getDuplicateFiles(String fileMd5);

    /**
     * 获取附件统计信息
     *
     * @param taskId 任务ID
     * @return 统计信息
     */
    Object getAttachmentStatistics(String taskId);

    /**
     * 获取文件类型统计
     *
     * @param taskId 任务ID
     * @return 文件类型统计
     */
    List<Object> getFileTypeStatistics(String taskId);

    /**
     * 获取上传者统计
     *
     * @param taskId 任务ID
     * @return 上传者统计
     */
    List<Object> getUploaderStatistics(String taskId);

    /**
     * 生成缩略图
     *
     * @param attachmentId 附件ID
     * @return 是否成功
     */
    boolean generateThumbnail(String attachmentId);

    /**
     * 清理临时文件
     */
    void cleanupTempFiles();
}
