package com.wtms.service;

import com.wtms.common.result.PageResult;
import com.wtms.dto.request.BatchAssignRequest;
import com.wtms.dto.request.TaskAssignRequest;
import com.wtms.dto.response.TaskAssignResponse;
import com.wtms.entity.TaskAssignment;
import com.wtms.mapper.TaskAssignmentMapper;

import java.util.List;

/**
 * 任务分配服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface TaskAssignmentService {

    /**
     * 分配任务给用户
     *
     * @param taskId 任务ID
     * @param request 分配请求
     * @return 分配结果
     */
    List<TaskAssignResponse> assignTask(String taskId, TaskAssignRequest request);

    /**
     * 批量分配任务
     *
     * @param request 批量分配请求
     * @return 分配结果
     */
    List<TaskAssignResponse> batchAssignTasks(BatchAssignRequest request);

    /**
     * 接受任务分配
     *
     * @param assignmentId 分配ID
     * @return 分配信息
     */
    TaskAssignResponse acceptAssignment(String assignmentId);

    /**
     * 拒绝任务分配
     *
     * @param assignmentId 分配ID
     * @param rejectReason 拒绝原因
     * @return 分配信息
     */
    TaskAssignResponse rejectAssignment(String assignmentId, String rejectReason);

    /**
     * 取消任务分配
     *
     * @param assignmentId 分配ID
     */
    void cancelAssignment(String assignmentId);

    /**
     * 重新分配任务
     *
     * @param assignmentId 原分配ID
     * @param request 新分配请求
     * @return 分配结果
     */
    List<TaskAssignResponse> reassignTask(String assignmentId, TaskAssignRequest request);

    /**
     * 获取任务的分配记录
     *
     * @param taskId 任务ID
     * @return 分配记录列表
     */
    List<TaskAssignResponse> getTaskAssignments(String taskId);

    /**
     * 获取用户的分配记录
     *
     * @param userId 用户ID
     * @param status 分配状态
     * @param page 页码
     * @param size 每页大小
     * @return 分配记录列表
     */
    PageResult<TaskAssignResponse> getUserAssignments(String userId, String status, Integer page, Integer size);

    /**
     * 获取部门的分配记录
     *
     * @param departmentId 部门ID
     * @param status 分配状态
     * @param page 页码
     * @param size 每页大小
     * @return 分配记录列表
     */
    PageResult<TaskAssignResponse> getDepartmentAssignments(String departmentId, String status, Integer page, Integer size);

    /**
     * 获取我分配的任务
     *
     * @param assignerId 分配者ID
     * @param status 分配状态
     * @param page 页码
     * @param size 每页大小
     * @return 分配记录列表
     */
    PageResult<TaskAssignResponse> getMyAssignments(String assignerId, String status, Integer page, Integer size);

    /**
     * 自动分配任务
     *
     * @param taskId 任务ID
     * @param assignRule 分配规则
     * @return 分配结果
     */
    List<TaskAssignResponse> autoAssignTask(String taskId, String assignRule);

    /**
     * 智能推荐分配目标
     *
     * @param taskId 任务ID
     * @param limit 推荐数量
     * @return 推荐的分配目标
     */
    List<RecommendedAssignee> recommendAssignees(String taskId, Integer limit);

    /**
     * 获取用户当前工作负载
     *
     * @param userId 用户ID
     * @return 工作负载统计
     */
    UserWorkloadInfo getUserWorkload(String userId);

    /**
     * 获取部门当前工作负载
     *
     * @param departmentId 部门ID
     * @return 工作负载统计
     */
    DepartmentWorkloadInfo getDepartmentWorkload(String departmentId);

    /**
     * 获取分配统计信息
     *
     * @param userId 用户ID（可选）
     * @param departmentId 部门ID（可选）
     * @return 统计信息
     */
    Object getAssignmentStatistics(String userId, String departmentId);

    /**
     * 处理过期的分配
     */
    void handleExpiredAssignments();

    /**
     * 发送分配通知
     *
     * @param assignmentId 分配ID
     */
    void sendAssignmentNotification(String assignmentId);

    /**
     * 推荐的分配目标
     */
    class RecommendedAssignee {
        private String targetType;
        private String targetId;
        private String targetName;
        private String avatar;
        private Double score;
        private String reason;
        private Integer currentWorkload;
        private Double skillMatch;

        // getters and setters
        public String getTargetType() { return targetType; }
        public void setTargetType(String targetType) { this.targetType = targetType; }
        public String getTargetId() { return targetId; }
        public void setTargetId(String targetId) { this.targetId = targetId; }
        public String getTargetName() { return targetName; }
        public void setTargetName(String targetName) { this.targetName = targetName; }
        public String getAvatar() { return avatar; }
        public void setAvatar(String avatar) { this.avatar = avatar; }
        public Double getScore() { return score; }
        public void setScore(Double score) { this.score = score; }
        public String getReason() { return reason; }
        public void setReason(String reason) { this.reason = reason; }
        public Integer getCurrentWorkload() { return currentWorkload; }
        public void setCurrentWorkload(Integer currentWorkload) { this.currentWorkload = currentWorkload; }
        public Double getSkillMatch() { return skillMatch; }
        public void setSkillMatch(Double skillMatch) { this.skillMatch = skillMatch; }
    }

    /**
     * 用户工作负载信息
     */
    class UserWorkloadInfo {
        private String userId;
        private String userName;
        private Integer currentTasks;
        private Integer pendingAssignments;
        private Integer completedTasks;
        private Double averageCompletionTime;
        private Double workloadScore;

        // getters and setters
        public String getUserId() { return userId; }
        public void setUserId(String userId) { this.userId = userId; }
        public String getUserName() { return userName; }
        public void setUserName(String userName) { this.userName = userName; }
        public Integer getCurrentTasks() { return currentTasks; }
        public void setCurrentTasks(Integer currentTasks) { this.currentTasks = currentTasks; }
        public Integer getPendingAssignments() { return pendingAssignments; }
        public void setPendingAssignments(Integer pendingAssignments) { this.pendingAssignments = pendingAssignments; }
        public Integer getCompletedTasks() { return completedTasks; }
        public void setCompletedTasks(Integer completedTasks) { this.completedTasks = completedTasks; }
        public Double getAverageCompletionTime() { return averageCompletionTime; }
        public void setAverageCompletionTime(Double averageCompletionTime) { this.averageCompletionTime = averageCompletionTime; }
        public Double getWorkloadScore() { return workloadScore; }
        public void setWorkloadScore(Double workloadScore) { this.workloadScore = workloadScore; }
    }

    /**
     * 部门工作负载信息
     */
    class DepartmentWorkloadInfo {
        private String departmentId;
        private String departmentName;
        private Integer memberCount;
        private Integer totalCurrentTasks;
        private Integer totalPendingAssignments;
        private Integer totalCompletedTasks;
        private Double averageWorkloadPerMember;
        private Double departmentEfficiency;

        // getters and setters
        public String getDepartmentId() { return departmentId; }
        public void setDepartmentId(String departmentId) { this.departmentId = departmentId; }
        public String getDepartmentName() { return departmentName; }
        public void setDepartmentName(String departmentName) { this.departmentName = departmentName; }
        public Integer getMemberCount() { return memberCount; }
        public void setMemberCount(Integer memberCount) { this.memberCount = memberCount; }
        public Integer getTotalCurrentTasks() { return totalCurrentTasks; }
        public void setTotalCurrentTasks(Integer totalCurrentTasks) { this.totalCurrentTasks = totalCurrentTasks; }
        public Integer getTotalPendingAssignments() { return totalPendingAssignments; }
        public void setTotalPendingAssignments(Integer totalPendingAssignments) { this.totalPendingAssignments = totalPendingAssignments; }
        public Integer getTotalCompletedTasks() { return totalCompletedTasks; }
        public void setTotalCompletedTasks(Integer totalCompletedTasks) { this.totalCompletedTasks = totalCompletedTasks; }
        public Double getAverageWorkloadPerMember() { return averageWorkloadPerMember; }
        public void setAverageWorkloadPerMember(Double averageWorkloadPerMember) { this.averageWorkloadPerMember = averageWorkloadPerMember; }
        public Double getDepartmentEfficiency() { return departmentEfficiency; }
        public void setDepartmentEfficiency(Double departmentEfficiency) { this.departmentEfficiency = departmentEfficiency; }
    }
}
