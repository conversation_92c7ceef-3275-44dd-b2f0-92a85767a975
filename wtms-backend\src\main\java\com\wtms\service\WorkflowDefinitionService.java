package com.wtms.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.wtms.entity.WorkflowDefinition;
import com.wtms.workflow.model.WorkflowExecutionResult;

import java.util.List;
import java.util.Map;

/**
 * 工作流定义服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface WorkflowDefinitionService {

    /**
     * 创建工作流定义
     *
     * @param definition 工作流定义
     * @return 工作流定义
     */
    WorkflowDefinition createDefinition(WorkflowDefinition definition);

    /**
     * 更新工作流定义
     *
     * @param definitionId 定义ID
     * @param definition 工作流定义
     * @return 工作流定义
     */
    WorkflowDefinition updateDefinition(String definitionId, WorkflowDefinition definition);

    /**
     * 删除工作流定义
     *
     * @param definitionId 定义ID
     */
    void deleteDefinition(String definitionId);

    /**
     * 根据ID获取工作流定义
     *
     * @param definitionId 定义ID
     * @return 工作流定义
     */
    WorkflowDefinition getDefinitionById(String definitionId);

    /**
     * 根据编码获取工作流定义
     *
     * @param code 编码
     * @return 工作流定义列表
     */
    List<WorkflowDefinition> getDefinitionsByCode(String code);

    /**
     * 根据编码和版本获取工作流定义
     *
     * @param code 编码
     * @param version 版本
     * @return 工作流定义
     */
    WorkflowDefinition getDefinitionByCodeAndVersion(String code, String version);

    /**
     * 根据编码获取最新版本的工作流定义
     *
     * @param code 编码
     * @return 工作流定义
     */
    WorkflowDefinition getLatestDefinitionByCode(String code);

    /**
     * 根据分类获取工作流定义
     *
     * @param category 分类
     * @return 工作流定义列表
     */
    List<WorkflowDefinition> getDefinitionsByCategory(String category);

    /**
     * 根据状态获取工作流定义
     *
     * @param status 状态
     * @return 工作流定义列表
     */
    List<WorkflowDefinition> getDefinitionsByStatus(String status);

    /**
     * 获取已发布的工作流定义
     *
     * @return 工作流定义列表
     */
    List<WorkflowDefinition> getPublishedDefinitions();

    /**
     * 获取启用的工作流定义
     *
     * @return 工作流定义列表
     */
    List<WorkflowDefinition> getEnabledDefinitions();

    /**
     * 获取默认工作流定义
     *
     * @return 工作流定义列表
     */
    List<WorkflowDefinition> getDefaultDefinitions();

    /**
     * 根据创建者获取工作流定义
     *
     * @param creatorId 创建者ID
     * @param status 状态
     * @param page 页码
     * @param size 每页大小
     * @return 工作流定义分页结果
     */
    IPage<WorkflowDefinition> getDefinitionsByCreator(String creatorId, String status, Integer page, Integer size);

    /**
     * 搜索工作流定义
     *
     * @param keyword 关键词
     * @param category 分类
     * @param status 状态
     * @param creatorId 创建者ID
     * @param page 页码
     * @param size 每页大小
     * @return 搜索结果
     */
    IPage<WorkflowDefinition> searchDefinitions(String keyword, String category, String status, 
                                               String creatorId, Integer page, Integer size);

    /**
     * 发布工作流定义
     *
     * @param definitionId 定义ID
     * @param publisherId 发布者ID
     * @return 是否成功
     */
    boolean publishDefinition(String definitionId, String publisherId);

    /**
     * 废弃工作流定义
     *
     * @param definitionId 定义ID
     * @return 是否成功
     */
    boolean deprecateDefinition(String definitionId);

    /**
     * 归档工作流定义
     *
     * @param definitionId 定义ID
     * @return 是否成功
     */
    boolean archiveDefinition(String definitionId);

    /**
     * 启用工作流定义
     *
     * @param definitionId 定义ID
     * @return 是否成功
     */
    boolean enableDefinition(String definitionId);

    /**
     * 禁用工作流定义
     *
     * @param definitionId 定义ID
     * @return 是否成功
     */
    boolean disableDefinition(String definitionId);

    /**
     * 设置为默认工作流定义
     *
     * @param definitionId 定义ID
     * @return 是否成功
     */
    boolean setAsDefault(String definitionId);

    /**
     * 取消默认工作流定义
     *
     * @param definitionId 定义ID
     * @return 是否成功
     */
    boolean unsetDefault(String definitionId);

    /**
     * 复制工作流定义
     *
     * @param definitionId 定义ID
     * @param newCode 新编码
     * @param newName 新名称
     * @return 新的工作流定义
     */
    WorkflowDefinition copyDefinition(String definitionId, String newCode, String newName);

    /**
     * 创建新版本
     *
     * @param definitionId 定义ID
     * @return 新版本的工作流定义
     */
    WorkflowDefinition createNewVersion(String definitionId);

    /**
     * 验证工作流定义
     *
     * @param definitionId 定义ID
     * @return 验证结果
     */
    WorkflowExecutionResult validateDefinition(String definitionId);

    /**
     * 导入工作流定义
     *
     * @param definitionJson 定义JSON
     * @param creatorId 创建者ID
     * @return 工作流定义
     */
    WorkflowDefinition importDefinition(String definitionJson, String creatorId);

    /**
     * 导出工作流定义
     *
     * @param definitionId 定义ID
     * @return 定义JSON
     */
    String exportDefinition(String definitionId);

    /**
     * 批量删除工作流定义
     *
     * @param definitionIds 定义ID列表
     */
    void batchDeleteDefinitions(List<String> definitionIds);

    /**
     * 批量更新工作流定义状态
     *
     * @param definitionIds 定义ID列表
     * @param status 状态
     */
    void batchUpdateStatus(List<String> definitionIds, String status);

    /**
     * 统计工作流定义数量
     *
     * @return 总数量
     */
    int countAllDefinitions();

    /**
     * 根据状态统计工作流定义数量
     *
     * @param status 状态
     * @return 数量
     */
    int countDefinitionsByStatus(String status);

    /**
     * 根据分类统计工作流定义数量
     *
     * @param category 分类
     * @return 数量
     */
    int countDefinitionsByCategory(String category);

    /**
     * 根据创建者统计工作流定义数量
     *
     * @param creatorId 创建者ID
     * @return 数量
     */
    int countDefinitionsByCreator(String creatorId);

    /**
     * 检查编码是否存在
     *
     * @param code 编码
     * @return 是否存在
     */
    boolean existsByCode(String code);

    /**
     * 检查编码和版本是否存在
     *
     * @param code 编码
     * @param version 版本
     * @return 是否存在
     */
    boolean existsByCodeAndVersion(String code, String version);

    /**
     * 获取下一个版本号
     *
     * @param code 编码
     * @return 下一个版本号
     */
    String getNextVersion(String code);

    /**
     * 获取热门工作流定义
     *
     * @param limit 数量限制
     * @return 热门工作流定义列表
     */
    List<WorkflowDefinition> getPopularDefinitions(Integer limit);

    /**
     * 获取最近创建的工作流定义
     *
     * @param limit 数量限制
     * @return 最近创建的工作流定义列表
     */
    List<WorkflowDefinition> getRecentDefinitions(Integer limit);

    /**
     * 获取最近更新的工作流定义
     *
     * @param limit 数量限制
     * @return 最近更新的工作流定义列表
     */
    List<WorkflowDefinition> getRecentlyUpdatedDefinitions(Integer limit);

    /**
     * 获取工作流定义统计信息
     *
     * @return 统计信息
     */
    Map<String, Object> getDefinitionStatistics();

    /**
     * 获取分类统计
     *
     * @return 分类统计
     */
    List<Map<String, Object>> getCategoryStatistics();

    /**
     * 获取创建者统计
     *
     * @return 创建者统计
     */
    List<Map<String, Object>> getCreatorStatistics();

    /**
     * 获取版本统计
     *
     * @param code 编码
     * @return 版本统计
     */
    List<Map<String, Object>> getVersionStatistics(String code);
}
