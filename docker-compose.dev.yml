version: '3.8'

# 开发环境Docker Compose配置
# 使用方法: docker-compose -f docker-compose.dev.yml up -d

services:
  # MySQL数据库
  mysql-dev:
    image: mysql:8.0
    container_name: wtms-mysql-dev
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ankaixin.docker.mysql
      MYSQL_DATABASE: wtms_db
      MYSQL_USER: wtms
      MYSQL_PASSWORD: wtms123
      TZ: Asia/Shanghai
    ports:
      - "3308:3306"
    volumes:
      - mysql_dev_data:/var/lib/mysql
      - ./MySQL数据库设计.sql:/docker-entrypoint-initdb.d/init.sql:ro
    command: --default-authentication-plugin=mysql_native_password --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci
    networks:
      - wtms-dev-network

  # Redis缓存
  redis-dev:
    image: redis:7.0-alpine
    container_name: wtms-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes --requirepass ""
    networks:
      - wtms-dev-network

  # phpMyAdmin (可选，用于数据库管理)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin
    container_name: wtms-phpmyadmin
    restart: unless-stopped
    environment:
      PMA_HOST: mysql-dev
      PMA_PORT: 3306
      PMA_USER: root
      PMA_PASSWORD: ankaixin.docker.mysql
      MYSQL_ROOT_PASSWORD: ankaixin.docker.mysql
    ports:
      - "8081:80"
    depends_on:
      - mysql-dev
    networks:
      - wtms-dev-network

  # Redis Commander (可选，用于Redis管理)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: wtms-redis-commander
    restart: unless-stopped
    environment:
      REDIS_HOSTS: local:redis-dev:6379
    ports:
      - "8082:8081"
    depends_on:
      - redis-dev
    networks:
      - wtms-dev-network

volumes:
  mysql_dev_data:
    driver: local
  redis_dev_data:
    driver: local

networks:
  wtms-dev-network:
    driver: bridge
