package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 质量统计实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("quality_statistics")
@Schema(description = "质量统计实体")
public class QualityStatistics implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "统计ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "统计对象ID")
    @TableField("target_id")
    private String targetId;

    @Schema(description = "统计对象类型")
    @TableField("target_type")
    private String targetType;

    @Schema(description = "统计周期")
    @TableField("period_type")
    private String periodType;

    @Schema(description = "统计开始时间")
    @TableField("period_start")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime periodStart;

    @Schema(description = "统计结束时间")
    @TableField("period_end")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime periodEnd;

    @Schema(description = "总评价数量")
    @TableField("total_evaluations")
    private Integer totalEvaluations;

    @Schema(description = "已完成评价数量")
    @TableField("completed_evaluations")
    private Integer completedEvaluations;

    @Schema(description = "平均总体评分")
    @TableField("avg_overall_score")
    private BigDecimal avgOverallScore;

    @Schema(description = "平均质量评分")
    @TableField("avg_quality_score")
    private BigDecimal avgQualityScore;

    @Schema(description = "平均效率评分")
    @TableField("avg_efficiency_score")
    private BigDecimal avgEfficiencyScore;

    @Schema(description = "平均沟通评分")
    @TableField("avg_communication_score")
    private BigDecimal avgCommunicationScore;

    @Schema(description = "平均创新评分")
    @TableField("avg_innovation_score")
    private BigDecimal avgInnovationScore;

    @Schema(description = "平均团队协作评分")
    @TableField("avg_teamwork_score")
    private BigDecimal avgTeamworkScore;

    @Schema(description = "最高评分")
    @TableField("max_score")
    private BigDecimal maxScore;

    @Schema(description = "最低评分")
    @TableField("min_score")
    private BigDecimal minScore;

    @Schema(description = "评分标准差")
    @TableField("score_std_dev")
    private BigDecimal scoreStdDev;

    @Schema(description = "优秀评价数量")
    @TableField("excellent_count")
    private Integer excellentCount;

    @Schema(description = "良好评价数量")
    @TableField("good_count")
    private Integer goodCount;

    @Schema(description = "中等评价数量")
    @TableField("average_count")
    private Integer averageCount;

    @Schema(description = "及格评价数量")
    @TableField("pass_count")
    private Integer passCount;

    @Schema(description = "不及格评价数量")
    @TableField("fail_count")
    private Integer failCount;

    @Schema(description = "质量趋势")
    @TableField("quality_trend")
    private String qualityTrend;

    @Schema(description = "改进建议数量")
    @TableField("improvement_suggestions")
    private Integer improvementSuggestions;

    @Schema(description = "统计状态")
    @TableField("status")
    private String status;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updatedAt;

    @Schema(description = "删除时间")
    @TableField("deleted_at")
    @TableLogic
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime deletedAt;

    // 非数据库字段
    @Schema(description = "统计对象信息")
    @TableField(exist = false)
    private Object target;

    /**
     * 统计对象类型枚举
     */
    public enum TargetType {
        USER("user", "用户"),
        TASK("task", "任务"),
        PROJECT("project", "项目"),
        DEPARTMENT("department", "部门"),
        TEAM("team", "团队"),
        ORGANIZATION("organization", "组织");

        private final String code;
        private final String description;

        TargetType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static TargetType fromCode(String code) {
            for (TargetType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return USER;
        }
    }

    /**
     * 统计周期枚举
     */
    public enum PeriodType {
        DAILY("daily", "日"),
        WEEKLY("weekly", "周"),
        MONTHLY("monthly", "月"),
        QUARTERLY("quarterly", "季度"),
        YEARLY("yearly", "年"),
        CUSTOM("custom", "自定义");

        private final String code;
        private final String description;

        PeriodType(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static PeriodType fromCode(String code) {
            for (PeriodType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return MONTHLY;
        }
    }

    /**
     * 质量趋势枚举
     */
    public enum QualityTrend {
        IMPROVING("improving", "改善"),
        STABLE("stable", "稳定"),
        DECLINING("declining", "下降"),
        UNKNOWN("unknown", "未知");

        private final String code;
        private final String description;

        QualityTrend(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static QualityTrend fromCode(String code) {
            for (QualityTrend trend : values()) {
                if (trend.code.equals(code)) {
                    return trend;
                }
            }
            return UNKNOWN;
        }
    }

    /**
     * 统计状态枚举
     */
    public enum Status {
        PENDING("pending", "待统计"),
        PROCESSING("processing", "统计中"),
        COMPLETED("completed", "已完成"),
        FAILED("failed", "失败");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return PENDING;
        }
    }

    /**
     * 计算完成率
     */
    public BigDecimal getCompletionRate() {
        if (totalEvaluations == null || totalEvaluations == 0) {
            return BigDecimal.ZERO;
        }
        
        int completed = completedEvaluations != null ? completedEvaluations : 0;
        return new BigDecimal(completed)
                .divide(new BigDecimal(totalEvaluations), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * 计算优秀率
     */
    public BigDecimal getExcellentRate() {
        if (completedEvaluations == null || completedEvaluations == 0) {
            return BigDecimal.ZERO;
        }
        
        int excellent = excellentCount != null ? excellentCount : 0;
        return new BigDecimal(excellent)
                .divide(new BigDecimal(completedEvaluations), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * 计算及格率
     */
    public BigDecimal getPassRate() {
        if (completedEvaluations == null || completedEvaluations == 0) {
            return BigDecimal.ZERO;
        }
        
        int pass = (excellentCount != null ? excellentCount : 0) +
                   (goodCount != null ? goodCount : 0) +
                   (averageCount != null ? averageCount : 0) +
                   (passCount != null ? passCount : 0);
        
        return new BigDecimal(pass)
                .divide(new BigDecimal(completedEvaluations), 4, BigDecimal.ROUND_HALF_UP)
                .multiply(new BigDecimal("100"));
    }

    /**
     * 获取统计对象类型描述
     */
    public String getTargetTypeText() {
        return TargetType.fromCode(this.targetType).getDescription();
    }

    /**
     * 获取统计周期描述
     */
    public String getPeriodTypeText() {
        return PeriodType.fromCode(this.periodType).getDescription();
    }

    /**
     * 获取质量趋势描述
     */
    public String getQualityTrendText() {
        return QualityTrend.fromCode(this.qualityTrend).getDescription();
    }

    /**
     * 获取状态描述
     */
    public String getStatusText() {
        return Status.fromCode(this.status).getDescription();
    }

    /**
     * 获取总体质量等级
     */
    public String getOverallQualityGrade() {
        if (avgOverallScore == null) {
            return "未评分";
        }
        
        if (avgOverallScore.compareTo(new BigDecimal("90")) >= 0) {
            return "优秀";
        } else if (avgOverallScore.compareTo(new BigDecimal("80")) >= 0) {
            return "良好";
        } else if (avgOverallScore.compareTo(new BigDecimal("70")) >= 0) {
            return "中等";
        } else if (avgOverallScore.compareTo(new BigDecimal("60")) >= 0) {
            return "及格";
        } else {
            return "不及格";
        }
    }

    /**
     * 检查是否有改进空间
     */
    public boolean hasImprovementSpace() {
        return avgOverallScore != null && avgOverallScore.compareTo(new BigDecimal("85")) < 0;
    }

    /**
     * 检查质量是否稳定
     */
    public boolean isQualityStable() {
        return QualityTrend.STABLE.getCode().equals(this.qualityTrend);
    }

    /**
     * 检查质量是否在改善
     */
    public boolean isQualityImproving() {
        return QualityTrend.IMPROVING.getCode().equals(this.qualityTrend);
    }
}
