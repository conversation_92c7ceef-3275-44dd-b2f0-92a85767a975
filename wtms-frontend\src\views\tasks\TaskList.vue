<template>
  <div class="task-list-page">
    <div class="page-header">
      <h1>任务管理</h1>
      <el-button type="primary" @click="createTask">
        <el-icon><Plus /></el-icon>
        新建任务
      </el-button>
    </div>

    <el-card>
      <div class="filter-bar">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select v-model="filters.status" placeholder="任务状态" clearable>
              <el-option label="全部" value="" />
              <el-option label="待开始" value="pending" />
              <el-option label="进行中" value="in_progress" />
              <el-option label="已完成" value="completed" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-select v-model="filters.priority" placeholder="优先级" clearable>
              <el-option label="高" value="high" />
              <el-option label="中" value="medium" />
              <el-option label="低" value="low" />
            </el-select>
          </el-col>
          <el-col :span="6">
            <el-input
              v-model="filters.search"
              placeholder="搜索任务..."
              prefix-icon="Search"
              clearable
            />
          </el-col>
          <el-col :span="6">
            <el-button @click="resetFilters">重置</el-button>
            <el-button type="primary" @click="searchTasks">搜索</el-button>
          </el-col>
        </el-row>
      </div>

      <div class="view-switcher">
        <el-radio-group v-model="viewMode">
          <el-radio-button label="table">列表视图</el-radio-button>
          <el-radio-button label="kanban">看板视图</el-radio-button>
          <el-radio-button label="gantt">甘特图</el-radio-button>
        </el-radio-group>
      </div>

      <div class="task-content">
        <el-empty description="任务列表功能开发中..." />
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'

const viewMode = ref('table')

const filters = reactive({
  status: '',
  priority: '',
  search: ''
})

const createTask = () => {
  ElMessage.info('新建任务功能开发中...')
}

const resetFilters = () => {
  filters.status = ''
  filters.priority = ''
  filters.search = ''
}

const searchTasks = () => {
  ElMessage.info('搜索功能开发中...')
}
</script>

<style lang="scss" scoped>
.task-list-page {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    
    h1 {
      margin: 0;
      font-size: 24px;
      font-weight: 600;
    }
  }
  
  .filter-bar {
    margin-bottom: 16px;
  }
  
  .view-switcher {
    margin-bottom: 16px;
  }
  
  .task-content {
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
</style>
