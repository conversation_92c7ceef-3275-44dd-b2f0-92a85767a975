package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 部门实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("departments")
@Schema(description = "部门实体")
public class Department implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "部门ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "部门名称")
    @TableField("name")
    private String name;

    @Schema(description = "部门编码")
    @TableField("code")
    private String code;

    @Schema(description = "部门描述")
    @TableField("description")
    private String description;

    @Schema(description = "父部门ID")
    @TableField("parent_id")
    private String parentId;

    @Schema(description = "部门经理ID")
    @TableField("manager_id")
    private String managerId;

    @Schema(description = "部门层级")
    @TableField("level")
    private Integer level;

    @Schema(description = "排序")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "状态")
    @TableField("status")
    private String status;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    // 非数据库字段
    @Schema(description = "父部门")
    @TableField(exist = false)
    private Department parent;

    @Schema(description = "部门经理")
    @TableField(exist = false)
    private User manager;

    @Schema(description = "子部门列表")
    @TableField(exist = false)
    private List<Department> children;

    /**
     * 部门状态枚举
     */
    public enum Status {
        ACTIVE("active", "活跃"),
        INACTIVE("inactive", "停用");

        private final String code;
        private final String description;

        Status(String code, String description) {
            this.code = code;
            this.description = description;
        }

        public String getCode() {
            return code;
        }

        public String getDescription() {
            return description;
        }

        public static Status fromCode(String code) {
            for (Status status : values()) {
                if (status.code.equals(code)) {
                    return status;
                }
            }
            return ACTIVE;
        }
    }

    /**
     * 检查部门是否活跃
     */
    public boolean isActive() {
        return Status.ACTIVE.getCode().equals(this.status);
    }

    /**
     * 检查是否为根部门
     */
    public boolean isRoot() {
        return this.parentId == null || this.parentId.isEmpty();
    }
}
