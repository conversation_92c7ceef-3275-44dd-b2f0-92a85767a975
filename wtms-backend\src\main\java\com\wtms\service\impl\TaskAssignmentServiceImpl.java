package com.wtms.service.impl;

import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.common.exception.BusinessException;
import com.wtms.common.result.PageResult;
import com.wtms.common.result.ResultCode;
import com.wtms.dto.request.BatchAssignRequest;
import com.wtms.dto.request.TaskAssignRequest;
import com.wtms.dto.response.TaskAssignResponse;
import com.wtms.entity.Task;
import com.wtms.entity.TaskAssignment;
import com.wtms.entity.User;
import com.wtms.mapper.TaskAssignmentMapper;
import com.wtms.mapper.TaskMapper;
import com.wtms.mapper.UserMapper;
import com.wtms.service.TaskAssignmentService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 任务分配服务实现
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Slf4j
@Service
@Transactional
public class TaskAssignmentServiceImpl implements TaskAssignmentService {

    @Autowired
    private TaskAssignmentMapper taskAssignmentMapper;

    @Autowired
    private TaskMapper taskMapper;

    @Autowired
    private UserMapper userMapper;

    @Override
    public List<TaskAssignResponse> assignTask(String taskId, TaskAssignRequest request) {
        log.info("Assigning task {} to targets: {}", taskId, request.getAssignTargets());

        // 验证任务是否存在
        Task task = taskMapper.selectById(taskId);
        if (task == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "任务不存在");
        }

        // 获取当前用户
        String currentUserId = getCurrentUserId();
        User currentUser = userMapper.selectById(currentUserId);
        if (currentUser == null) {
            throw new BusinessException(ResultCode.USER_NOT_FOUND);
        }

        // 检查分配权限
        if (!hasAssignPermission(taskId, currentUserId)) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED.getCode(), "没有权限分配此任务");
        }

        List<TaskAssignResponse> results = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();

        // 解析截止时间
        LocalDateTime deadline = null;
        if (StrUtil.isNotBlank(request.getDeadline())) {
            try {
                deadline = LocalDateTime.parse(request.getDeadline(), 
                    DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
            } catch (Exception e) {
                log.warn("Invalid deadline format: {}", request.getDeadline());
            }
        }

        // 为每个目标创建分配记录
        for (String targetId : request.getAssignTargets()) {
            TaskAssignment assignment = new TaskAssignment();
            assignment.setTaskId(taskId);
            assignment.setAssignType(TaskAssignment.AssignType.DIRECT.getCode());
            assignment.setTargetType(request.getAssignType());
            assignment.setTargetId(targetId);
            assignment.setAssignerId(currentUserId);
            assignment.setStatus(TaskAssignment.Status.PENDING.getCode());
            assignment.setComment(request.getComment());
            assignment.setAssignedAt(now);
            assignment.setDeadline(deadline);
            assignment.setWeight(1.0);
            assignment.setPriority(request.getPriorityAdjustment());
            assignment.setIsNotified(false);

            // 保存分配记录
            taskAssignmentMapper.insert(assignment);

            // 如果是直接分配给用户，自动接受
            if ("user".equals(request.getAssignType())) {
                // 验证用户是否存在
                User targetUser = userMapper.selectById(targetId);
                if (targetUser != null && targetUser.isActive()) {
                    // 自动接受分配
                    assignment.setStatus(TaskAssignment.Status.ACCEPTED.getCode());
                    assignment.setAcceptedAt(now);
                    taskAssignmentMapper.updateById(assignment);

                    // 更新任务的负责人
                    taskMapper.updateTaskAssignee(taskId, targetId);
                }
            }

            // 发送通知
            if (Boolean.TRUE.equals(request.getSendNotification())) {
                sendAssignmentNotification(assignment.getId());
            }

            // 构建响应
            TaskAssignResponse response = buildAssignResponse(assignment);
            results.add(response);

            log.info("Task assignment created: {} -> {}", taskId, targetId);
        }

        return results;
    }

    @Override
    public List<TaskAssignResponse> batchAssignTasks(BatchAssignRequest request) {
        log.info("Batch assigning {} tasks", request.getTaskIds().size());

        List<TaskAssignResponse> results = new ArrayList<>();
        String currentUserId = getCurrentUserId();

        // 根据分配规则处理
        switch (request.getAssignRule()) {
            case "round_robin":
                results = batchAssignRoundRobin(request, currentUserId);
                break;
            case "workload_balance":
                results = batchAssignWorkloadBalance(request, currentUserId);
                break;
            case "skill_match":
                results = batchAssignSkillMatch(request, currentUserId);
                break;
            case "manual":
            default:
                results = batchAssignManual(request, currentUserId);
                break;
        }

        log.info("Batch assignment completed: {} assignments created", results.size());
        return results;
    }

    @Override
    public TaskAssignResponse acceptAssignment(String assignmentId) {
        log.info("Accepting assignment: {}", assignmentId);

        TaskAssignment assignment = taskAssignmentMapper.selectById(assignmentId);
        if (assignment == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "分配记录不存在");
        }

        // 检查状态
        if (!TaskAssignment.Status.PENDING.getCode().equals(assignment.getStatus())) {
            throw new BusinessException(ResultCode.OPERATION_NOT_ALLOWED.getCode(), "分配状态不允许接受");
        }

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!canAcceptAssignment(assignment, currentUserId)) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED.getCode(), "没有权限接受此分配");
        }

        // 更新分配状态
        LocalDateTime now = LocalDateTime.now();
        taskAssignmentMapper.acceptAssignment(assignmentId, now);

        // 更新任务负责人
        if ("user".equals(assignment.getTargetType())) {
            taskMapper.updateTaskAssignee(assignment.getTaskId(), assignment.getTargetId());
        }

        log.info("Assignment accepted: {}", assignmentId);

        // 重新查询并返回
        assignment = taskAssignmentMapper.selectById(assignmentId);
        return buildAssignResponse(assignment);
    }

    @Override
    public TaskAssignResponse rejectAssignment(String assignmentId, String rejectReason) {
        log.info("Rejecting assignment: {} with reason: {}", assignmentId, rejectReason);

        TaskAssignment assignment = taskAssignmentMapper.selectById(assignmentId);
        if (assignment == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "分配记录不存在");
        }

        // 检查状态
        if (!TaskAssignment.Status.PENDING.getCode().equals(assignment.getStatus())) {
            throw new BusinessException(ResultCode.OPERATION_NOT_ALLOWED.getCode(), "分配状态不允许拒绝");
        }

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!canRejectAssignment(assignment, currentUserId)) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED.getCode(), "没有权限拒绝此分配");
        }

        // 更新分配状态
        LocalDateTime now = LocalDateTime.now();
        taskAssignmentMapper.rejectAssignment(assignmentId, now, rejectReason);

        log.info("Assignment rejected: {}", assignmentId);

        // 重新查询并返回
        assignment = taskAssignmentMapper.selectById(assignmentId);
        return buildAssignResponse(assignment);
    }

    @Override
    public void cancelAssignment(String assignmentId) {
        log.info("Cancelling assignment: {}", assignmentId);

        TaskAssignment assignment = taskAssignmentMapper.selectById(assignmentId);
        if (assignment == null) {
            throw new BusinessException(ResultCode.DATA_NOT_FOUND.getCode(), "分配记录不存在");
        }

        // 检查权限
        String currentUserId = getCurrentUserId();
        if (!assignment.getAssignerId().equals(currentUserId)) {
            throw new BusinessException(ResultCode.PERMISSION_DENIED.getCode(), "只有分配者可以取消分配");
        }

        // 更新状态
        taskAssignmentMapper.cancelAssignment(assignmentId);

        log.info("Assignment cancelled: {}", assignmentId);
    }

    @Override
    public List<TaskAssignResponse> getTaskAssignments(String taskId) {
        List<TaskAssignment> assignments = taskAssignmentMapper.selectAssignmentsByTaskId(taskId);
        return assignments.stream()
                .map(this::buildAssignResponse)
                .collect(Collectors.toList());
    }

    @Override
    public PageResult<TaskAssignResponse> getUserAssignments(String userId, String status, Integer page, Integer size) {
        Page<TaskAssignment> pageParam = new Page<>(page, size);
        IPage<TaskAssignment> assignmentPage = taskAssignmentMapper.selectAssignmentsByUserId(pageParam, userId, status);

        List<TaskAssignResponse> assignments = assignmentPage.getRecords().stream()
                .map(this::buildAssignResponse)
                .collect(Collectors.toList());

        return PageResult.of(assignmentPage, assignments);
    }

    // 其他方法的实现...

    /**
     * 获取当前用户ID
     */
    private String getCurrentUserId() {
        Object principal = SecurityContextHolder.getContext().getAuthentication().getPrincipal();
        if (principal instanceof UserDetails) {
            String username = ((UserDetails) principal).getUsername();
            User user = userMapper.findByUsernameWithRole(username);
            return user != null ? user.getId() : null;
        }
        return null;
    }

    /**
     * 检查分配权限
     */
    private boolean hasAssignPermission(String taskId, String userId) {
        Task task = taskMapper.selectById(taskId);
        if (task == null) return false;

        User user = userMapper.selectById(userId);
        if (user == null) return false;

        // 系统管理员和项目经理有分配权限
        if (user.getRole() != null && 
            ("ADMIN".equals(user.getRole().getCode()) || "PM".equals(user.getRole().getCode()))) {
            return true;
        }

        // 任务创建者有分配权限
        return task.getCreatorId().equals(userId);
    }

    /**
     * 检查是否可以接受分配
     */
    private boolean canAcceptAssignment(TaskAssignment assignment, String userId) {
        if ("user".equals(assignment.getTargetType())) {
            return assignment.getTargetId().equals(userId);
        }
        // TODO: 实现部门和角色的接受逻辑
        return false;
    }

    /**
     * 检查是否可以拒绝分配
     */
    private boolean canRejectAssignment(TaskAssignment assignment, String userId) {
        return canAcceptAssignment(assignment, userId);
    }

    /**
     * 构建分配响应
     */
    private TaskAssignResponse buildAssignResponse(TaskAssignment assignment) {
        // TODO: 实现完整的响应构建逻辑
        return TaskAssignResponse.builder()
                .assignId(assignment.getId())
                .taskId(assignment.getTaskId())
                .assignType(assignment.getAssignType())
                .status(assignment.getStatus())
                .comment(assignment.getComment())
                .assignedAt(assignment.getAssignedAt())
                .acceptedAt(assignment.getAcceptedAt())
                .deadline(assignment.getDeadline())
                .build();
    }

    // 批量分配的具体实现方法...
    private List<TaskAssignResponse> batchAssignRoundRobin(BatchAssignRequest request, String currentUserId) {
        // TODO: 实现轮询分配逻辑
        return new ArrayList<>();
    }

    private List<TaskAssignResponse> batchAssignWorkloadBalance(BatchAssignRequest request, String currentUserId) {
        // TODO: 实现负载均衡分配逻辑
        return new ArrayList<>();
    }

    private List<TaskAssignResponse> batchAssignSkillMatch(BatchAssignRequest request, String currentUserId) {
        // TODO: 实现技能匹配分配逻辑
        return new ArrayList<>();
    }

    private List<TaskAssignResponse> batchAssignManual(BatchAssignRequest request, String currentUserId) {
        // TODO: 实现手动分配逻辑
        return new ArrayList<>();
    }

    @Override
    public void sendAssignmentNotification(String assignmentId) {
        // TODO: 实现通知发送逻辑
        log.info("Sending assignment notification: {}", assignmentId);
    }

    // 其他未实现的方法...
    @Override
    public List<TaskAssignResponse> reassignTask(String assignmentId, TaskAssignRequest request) {
        // TODO: 实现重新分配逻辑
        return new ArrayList<>();
    }

    @Override
    public PageResult<TaskAssignResponse> getDepartmentAssignments(String departmentId, String status, Integer page, Integer size) {
        // TODO: 实现部门分配查询
        return new PageResult<>();
    }

    @Override
    public PageResult<TaskAssignResponse> getMyAssignments(String assignerId, String status, Integer page, Integer size) {
        // TODO: 实现我的分配查询
        return new PageResult<>();
    }

    @Override
    public List<TaskAssignResponse> autoAssignTask(String taskId, String assignRule) {
        // TODO: 实现自动分配
        return new ArrayList<>();
    }

    @Override
    public List<RecommendedAssignee> recommendAssignees(String taskId, Integer limit) {
        // TODO: 实现智能推荐
        return new ArrayList<>();
    }

    @Override
    public UserWorkloadInfo getUserWorkload(String userId) {
        // TODO: 实现用户工作负载查询
        return new UserWorkloadInfo();
    }

    @Override
    public DepartmentWorkloadInfo getDepartmentWorkload(String departmentId) {
        // TODO: 实现部门工作负载查询
        return new DepartmentWorkloadInfo();
    }

    @Override
    public Object getAssignmentStatistics(String userId, String departmentId) {
        // TODO: 实现分配统计
        return new Object();
    }

    @Override
    public void handleExpiredAssignments() {
        // TODO: 实现过期分配处理
        log.info("Handling expired assignments");
    }
}
