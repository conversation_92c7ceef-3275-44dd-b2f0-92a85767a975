package com.wtms.service;

import com.wtms.dto.request.CreatePermissionRequest;
import com.wtms.dto.request.UpdatePermissionRequest;
import com.wtms.dto.response.PermissionTreeResponse;
import com.wtms.dto.response.UserPermissionResponse;
import com.wtms.entity.Permission;

import java.util.List;
import java.util.Set;

/**
 * 权限管理服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface PermissionService {

    /**
     * 创建权限
     *
     * @param request 创建权限请求
     * @return 权限信息
     */
    Permission createPermission(CreatePermissionRequest request);

    /**
     * 更新权限
     *
     * @param permissionId 权限ID
     * @param request 更新权限请求
     * @return 权限信息
     */
    Permission updatePermission(String permissionId, UpdatePermissionRequest request);

    /**
     * 删除权限
     *
     * @param permissionId 权限ID
     */
    void deletePermission(String permissionId);

    /**
     * 根据ID获取权限
     *
     * @param permissionId 权限ID
     * @return 权限信息
     */
    Permission getPermissionById(String permissionId);

    /**
     * 根据编码获取权限
     *
     * @param code 权限编码
     * @return 权限信息
     */
    Permission getPermissionByCode(String code);

    /**
     * 获取权限树
     *
     * @return 权限树
     */
    List<PermissionTreeResponse> getPermissionTree();

    /**
     * 根据类型获取权限列表
     *
     * @param type 权限类型
     * @return 权限列表
     */
    List<Permission> getPermissionsByType(String type);

    /**
     * 根据分组获取权限列表
     *
     * @param groupName 权限分组
     * @return 权限列表
     */
    List<Permission> getPermissionsByGroup(String groupName);

    /**
     * 获取所有启用的权限
     *
     * @return 权限列表
     */
    List<Permission> getEnabledPermissions();

    /**
     * 获取系统权限
     *
     * @return 系统权限列表
     */
    List<Permission> getSystemPermissions();

    /**
     * 启用权限
     *
     * @param permissionId 权限ID
     */
    void enablePermission(String permissionId);

    /**
     * 禁用权限
     *
     * @param permissionId 权限ID
     */
    void disablePermission(String permissionId);

    /**
     * 批量启用权限
     *
     * @param permissionIds 权限ID列表
     */
    void batchEnablePermissions(List<String> permissionIds);

    /**
     * 批量禁用权限
     *
     * @param permissionIds 权限ID列表
     */
    void batchDisablePermissions(List<String> permissionIds);

    /**
     * 根据用户ID获取用户权限
     *
     * @param userId 用户ID
     * @return 用户权限信息
     */
    UserPermissionResponse getUserPermissions(String userId);

    /**
     * 根据用户ID获取用户权限编码
     *
     * @param userId 用户ID
     * @return 权限编码集合
     */
    Set<String> getUserPermissionCodes(String userId);

    /**
     * 根据角色ID获取角色权限
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permission> getRolePermissions(String roleId);

    /**
     * 根据角色ID获取角色权限编码
     *
     * @param roleId 角色ID
     * @return 权限编码集合
     */
    Set<String> getRolePermissionCodes(String roleId);

    /**
     * 检查用户是否有指定权限
     *
     * @param userId 用户ID
     * @param permissionCode 权限编码
     * @return 是否有权限
     */
    boolean hasPermission(String userId, String permissionCode);

    /**
     * 检查用户是否有任意一个权限
     *
     * @param userId 用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否有权限
     */
    boolean hasAnyPermission(String userId, String... permissionCodes);

    /**
     * 检查用户是否有所有权限
     *
     * @param userId 用户ID
     * @param permissionCodes 权限编码列表
     * @return 是否有权限
     */
    boolean hasAllPermissions(String userId, String... permissionCodes);

    /**
     * 检查权限编码是否存在
     *
     * @param code 权限编码
     * @return 是否存在
     */
    boolean existsByCode(String code);

    /**
     * 检查权限名称是否存在
     *
     * @param name 权限名称
     * @return 是否存在
     */
    boolean existsByName(String name);

    /**
     * 初始化系统权限
     */
    void initSystemPermissions();

    /**
     * 刷新权限缓存
     */
    void refreshPermissionCache();

    /**
     * 获取权限统计信息
     *
     * @return 统计信息
     */
    Object getPermissionStatistics();
}
