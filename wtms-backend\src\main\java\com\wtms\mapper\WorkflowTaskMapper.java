package com.wtms.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.wtms.entity.WorkflowTask;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 工作流任务Mapper接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Mapper
public interface WorkflowTaskMapper extends BaseMapper<WorkflowTask> {

    /**
     * 根据工作流实例ID查询任务
     */
    @Select("SELECT * FROM workflow_tasks WHERE workflow_instance_id = #{instanceId} AND deleted_at IS NULL ORDER BY created_at ASC")
    List<WorkflowTask> selectByInstanceId(@Param("instanceId") String instanceId);

    /**
     * 根据工作流节点ID查询任务
     */
    @Select("SELECT * FROM workflow_tasks WHERE workflow_node_id = #{nodeId} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<WorkflowTask> selectByNodeId(@Param("nodeId") String nodeId);

    /**
     * 根据分配者查询任务
     */
    IPage<WorkflowTask> selectByAssigneeId(Page<WorkflowTask> page, @Param("assigneeId") String assigneeId, 
                                          @Param("status") String status);

    /**
     * 根据候选用户查询任务
     */
    IPage<WorkflowTask> selectByCandidateUser(Page<WorkflowTask> page, @Param("userId") String userId, 
                                             @Param("status") String status);

    /**
     * 根据候选组查询任务
     */
    IPage<WorkflowTask> selectByCandidateGroup(Page<WorkflowTask> page, @Param("groupId") String groupId, 
                                              @Param("status") String status);

    /**
     * 根据状态查询任务
     */
    @Select("SELECT * FROM workflow_tasks WHERE status = #{status} AND deleted_at IS NULL ORDER BY created_at DESC")
    List<WorkflowTask> selectByStatus(@Param("status") String status);

    /**
     * 查询活跃任务
     */
    @Select("SELECT * FROM workflow_tasks WHERE status IN ('ready', 'reserved', 'in_progress') AND deleted_at IS NULL ORDER BY created_at DESC")
    List<WorkflowTask> selectActiveTasks();

    /**
     * 查询已完成任务
     */
    @Select("SELECT * FROM workflow_tasks WHERE status = 'completed' AND deleted_at IS NULL ORDER BY completed_at DESC")
    List<WorkflowTask> selectCompletedTasks();

    /**
     * 查询超期任务
     */
    @Select("SELECT * FROM workflow_tasks WHERE due_date IS NOT NULL AND due_date < NOW() AND status IN ('ready', 'reserved', 'in_progress') AND deleted_at IS NULL ORDER BY due_date ASC")
    List<WorkflowTask> selectOverdueTasks();

    /**
     * 查询用户的待办任务
     */
    List<WorkflowTask> selectUserTodoTasks(@Param("userId") String userId);

    /**
     * 查询用户的已办任务
     */
    IPage<WorkflowTask> selectUserDoneTasks(Page<WorkflowTask> page, @Param("userId") String userId);

    /**
     * 搜索工作流任务
     */
    IPage<WorkflowTask> searchWorkflowTasks(Page<WorkflowTask> page, @Param("keyword") String keyword,
                                           @Param("instanceId") String instanceId, @Param("status") String status,
                                           @Param("assigneeId") String assigneeId, @Param("taskType") String taskType);

    /**
     * 统计任务数量
     */
    @Select("SELECT COUNT(*) FROM workflow_tasks WHERE deleted_at IS NULL")
    int countAll();

    /**
     * 根据状态统计任务数量
     */
    @Select("SELECT COUNT(*) FROM workflow_tasks WHERE status = #{status} AND deleted_at IS NULL")
    int countByStatus(@Param("status") String status);

    /**
     * 根据分配者统计任务数量
     */
    @Select("SELECT COUNT(*) FROM workflow_tasks WHERE assignee_id = #{assigneeId} AND deleted_at IS NULL")
    int countByAssigneeId(@Param("assigneeId") String assigneeId);

    /**
     * 根据任务类型统计任务数量
     */
    @Select("SELECT COUNT(*) FROM workflow_tasks WHERE task_type = #{taskType} AND deleted_at IS NULL")
    int countByTaskType(@Param("taskType") String taskType);

    /**
     * 认领任务
     */
    @Update("UPDATE workflow_tasks SET assignee_id = #{assigneeId}, status = 'reserved', claimed_at = NOW(), updated_at = NOW() WHERE id = #{taskId}")
    int claimTask(@Param("taskId") String taskId, @Param("assigneeId") String assigneeId);

    /**
     * 取消认领任务
     */
    @Update("UPDATE workflow_tasks SET assignee_id = NULL, status = 'ready', claimed_at = NULL, updated_at = NOW() WHERE id = #{taskId}")
    int unclaimTask(@Param("taskId") String taskId);

    /**
     * 开始任务
     */
    @Update("UPDATE workflow_tasks SET status = 'in_progress', started_at = NOW(), updated_at = NOW() WHERE id = #{taskId}")
    int startTask(@Param("taskId") String taskId);

    /**
     * 完成任务
     */
    @Update("UPDATE workflow_tasks SET status = 'completed', completed_at = NOW(), updated_at = NOW() WHERE id = #{taskId}")
    int completeTask(@Param("taskId") String taskId);

    /**
     * 暂停任务
     */
    @Update("UPDATE workflow_tasks SET status = 'suspended', updated_at = NOW() WHERE id = #{taskId}")
    int suspendTask(@Param("taskId") String taskId);

    /**
     * 恢复任务
     */
    @Update("UPDATE workflow_tasks SET status = 'in_progress', updated_at = NOW() WHERE id = #{taskId}")
    int resumeTask(@Param("taskId") String taskId);

    /**
     * 委托任务
     */
    @Update("UPDATE workflow_tasks SET delegator_id = assignee_id, assignee_id = #{delegateId}, updated_at = NOW() WHERE id = #{taskId}")
    int delegateTask(@Param("taskId") String taskId, @Param("delegateId") String delegateId);

    /**
     * 解决委托任务
     */
    @Update("UPDATE workflow_tasks SET assignee_id = delegator_id, delegator_id = NULL, updated_at = NOW() WHERE id = #{taskId}")
    int resolveTask(@Param("taskId") String taskId);

    /**
     * 分配任务
     */
    @Update("UPDATE workflow_tasks SET assignee_id = #{assigneeId}, updated_at = NOW() WHERE id = #{taskId}")
    int assignTask(@Param("taskId") String taskId, @Param("assigneeId") String assigneeId);

    /**
     * 更新任务变量
     */
    @Update("UPDATE workflow_tasks SET variables_json = #{variablesJson}, updated_at = NOW() WHERE id = #{taskId}")
    int updateTaskVariables(@Param("taskId") String taskId, @Param("variablesJson") String variablesJson);

    /**
     * 批量删除任务
     */
    int batchDeleteTasks(@Param("taskIds") List<String> taskIds);

    /**
     * 批量更新任务状态
     */
    int batchUpdateStatus(@Param("taskIds") List<String> taskIds, @Param("status") String status);

    /**
     * 批量分配任务
     */
    int batchAssignTasks(@Param("taskIds") List<String> taskIds, @Param("assigneeId") String assigneeId);

    /**
     * 查询任务统计信息
     */
    Object selectTaskStatistics();

    /**
     * 查询状态统计
     */
    List<Object> selectStatusStatistics();

    /**
     * 查询任务类型统计
     */
    List<Object> selectTaskTypeStatistics();

    /**
     * 查询分配者统计
     */
    List<Object> selectAssigneeStatistics();

    /**
     * 查询执行时长统计
     */
    List<Object> selectDurationStatistics(@Param("days") Integer days);

    /**
     * 查询每日完成统计
     */
    List<Object> selectDailyCompletionStatistics(@Param("days") Integer days);

    /**
     * 查询超期任务统计
     */
    Object selectOverdueStatistics();

    /**
     * 查询用户工作负载统计
     */
    List<Object> selectUserWorkloadStatistics(@Param("limit") Integer limit);

    /**
     * 查询任务性能统计
     */
    Object selectTaskPerformanceStatistics(@Param("taskType") String taskType, @Param("days") Integer days);

    /**
     * 清理已完成的任务
     */
    @Update("UPDATE workflow_tasks SET deleted_at = NOW() WHERE status = 'completed' AND completed_at < #{beforeDate}")
    int cleanupCompletedTasks(@Param("beforeDate") LocalDateTime beforeDate);

    /**
     * 查询长时间运行的任务
     */
    @Select("SELECT * FROM workflow_tasks WHERE status = 'in_progress' AND started_at < #{beforeDate} AND deleted_at IS NULL ORDER BY started_at ASC")
    List<WorkflowTask> selectLongRunningTasks(@Param("beforeDate") LocalDateTime beforeDate);

    /**
     * 查询最近创建的任务
     */
    @Select("SELECT * FROM workflow_tasks WHERE deleted_at IS NULL ORDER BY created_at DESC LIMIT #{limit}")
    List<WorkflowTask> selectRecentCreatedTasks(@Param("limit") Integer limit);

    /**
     * 查询最近完成的任务
     */
    @Select("SELECT * FROM workflow_tasks WHERE status = 'completed' AND deleted_at IS NULL ORDER BY completed_at DESC LIMIT #{limit}")
    List<WorkflowTask> selectRecentCompletedTasks(@Param("limit") Integer limit);

    /**
     * 查询高优先级任务
     */
    @Select("SELECT * FROM workflow_tasks WHERE priority >= #{minPriority} AND status IN ('ready', 'reserved', 'in_progress') AND deleted_at IS NULL ORDER BY priority DESC, created_at ASC")
    List<WorkflowTask> selectHighPriorityTasks(@Param("minPriority") Integer minPriority);
}
