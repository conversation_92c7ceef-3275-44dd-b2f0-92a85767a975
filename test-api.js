const http = require('http');

// 测试登录API
function testLogin() {
  const postData = JSON.stringify({
    username: 'admin',
    password: 'admin123456'
  });

  const options = {
    hostname: 'localhost',
    port: 55557,
    path: '/api/v1/auth/login',
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Content-Length': Buffer.byteLength(postData)
    }
  };

  const req = http.request(options, (res) => {
    console.log(`登录API状态码: ${res.statusCode}`);
    console.log(`响应头:`, res.headers);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log('登录响应:', JSON.stringify(response, null, 2));
        
        if (response.success && response.data && response.data.token) {
          console.log('\n✅ 登录成功！');
          testUserProfile(response.data.token);
        } else {
          console.log('\n❌ 登录失败！');
        }
      } catch (error) {
        console.error('解析响应失败:', error);
        console.log('原始响应:', data);
      }
    });
  });

  req.on('error', (e) => {
    console.error(`登录请求失败: ${e.message}`);
  });

  req.write(postData);
  req.end();
}

// 测试用户信息API
function testUserProfile(token) {
  const options = {
    hostname: 'localhost',
    port: 55557,
    path: '/api/v1/user/profile',
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${token}`
    }
  };

  const req = http.request(options, (res) => {
    console.log(`\n用户信息API状态码: ${res.statusCode}`);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log('用户信息响应:', JSON.stringify(response, null, 2));
        
        if (response.success) {
          console.log('\n✅ 用户信息获取成功！');
        } else {
          console.log('\n❌ 用户信息获取失败！');
        }
      } catch (error) {
        console.error('解析用户信息响应失败:', error);
        console.log('原始响应:', data);
      }
    });
  });

  req.on('error', (e) => {
    console.error(`用户信息请求失败: ${e.message}`);
  });

  req.end();
}

// 测试健康检查
function testHealth() {
  const options = {
    hostname: 'localhost',
    port: 55557,
    path: '/api/v1/health',
    method: 'GET'
  };

  const req = http.request(options, (res) => {
    console.log(`健康检查状态码: ${res.statusCode}`);

    let data = '';
    res.on('data', (chunk) => {
      data += chunk;
    });

    res.on('end', () => {
      try {
        const response = JSON.parse(data);
        console.log('健康检查响应:', JSON.stringify(response, null, 2));
        
        if (response.success) {
          console.log('\n✅ 后端服务正常！');
          console.log('\n开始测试登录...');
          testLogin();
        } else {
          console.log('\n❌ 后端服务异常！');
        }
      } catch (error) {
        console.error('解析健康检查响应失败:', error);
        console.log('原始响应:', data);
      }
    });
  });

  req.on('error', (e) => {
    console.error(`健康检查请求失败: ${e.message}`);
  });

  req.end();
}

console.log('开始测试WTMS后端API...\n');
testHealth();
