-- =====================================================
-- WTMS 工作任务管理系统 - 数据表结构创建脚本
-- 版本: 1.0.0
-- 创建时间: 2025-07-24
-- 数据库: MySQL 8.0+
-- 字符集: utf8mb4
-- =====================================================

-- 设置字符集和时区
SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;
SET time_zone = '+08:00';

-- =====================================================
-- 1. 用户管理模块
-- =====================================================

-- 用户表
DROP TABLE IF EXISTS `users`;
CREATE TABLE `users` (
  `id` varchar(36) NOT NULL COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `password_hash` varchar(255) NOT NULL COMMENT '密码哈希',
  `salt` varchar(32) DEFAULT NULL COMMENT '密码盐值',
  `full_name` varchar(100) NOT NULL COMMENT '姓名',
  `avatar_url` varchar(255) DEFAULT NULL COMMENT '头像URL',
  `department_id` varchar(36) DEFAULT NULL COMMENT '部门ID',
  `role_id` varchar(36) DEFAULT NULL COMMENT '角色ID',
  `employee_id` varchar(50) DEFAULT NULL COMMENT '工号',
  `hire_date` date DEFAULT NULL COMMENT '入职日期',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态：active-活跃，inactive-停用，locked-锁定',
  `last_login_at` datetime DEFAULT NULL COMMENT '最后登录时间',
  `login_count` int DEFAULT '0' COMMENT '登录次数',
  `settings` json DEFAULT NULL COMMENT '用户设置',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_users_username` (`username`),
  UNIQUE KEY `uk_users_email` (`email`),
  UNIQUE KEY `uk_users_employee_id` (`employee_id`),
  KEY `idx_users_department_id` (`department_id`),
  KEY `idx_users_role_id` (`role_id`),
  KEY `idx_users_status` (`status`),
  KEY `idx_users_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 部门表
DROP TABLE IF EXISTS `departments`;
CREATE TABLE `departments` (
  `id` varchar(36) NOT NULL COMMENT '部门ID',
  `name` varchar(100) NOT NULL COMMENT '部门名称',
  `code` varchar(50) DEFAULT NULL COMMENT '部门编码',
  `description` text COMMENT '部门描述',
  `parent_id` varchar(36) DEFAULT NULL COMMENT '父部门ID',
  `manager_id` varchar(36) DEFAULT NULL COMMENT '部门经理ID',
  `level` int DEFAULT '1' COMMENT '部门层级',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_departments_code` (`code`),
  KEY `idx_departments_parent_id` (`parent_id`),
  KEY `idx_departments_manager_id` (`manager_id`),
  KEY `idx_departments_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部门表';

-- 角色表
DROP TABLE IF EXISTS `roles`;
CREATE TABLE `roles` (
  `id` varchar(36) NOT NULL COMMENT '角色ID',
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` text COMMENT '角色描述',
  `permissions` text COMMENT '权限列表（JSON格式）',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统角色',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_roles_code` (`code`),
  KEY `idx_roles_is_system` (`is_system`),
  KEY `idx_roles_is_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色表';

-- 权限表
DROP TABLE IF EXISTS `permissions`;
CREATE TABLE `permissions` (
  `id` varchar(36) NOT NULL COMMENT '权限ID',
  `code` varchar(100) NOT NULL COMMENT '权限编码',
  `name` varchar(100) NOT NULL COMMENT '权限名称',
  `description` text COMMENT '权限描述',
  `type` varchar(20) DEFAULT 'api' COMMENT '权限类型：menu-菜单，button-按钮，api-接口，data-数据',
  `group_name` varchar(50) DEFAULT NULL COMMENT '权限分组',
  `parent_id` varchar(36) DEFAULT NULL COMMENT '父权限ID',
  `path` varchar(500) DEFAULT NULL COMMENT '权限路径',
  `level` int DEFAULT '1' COMMENT '权限层级',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `resource` varchar(100) DEFAULT NULL COMMENT '资源标识',
  `action` varchar(50) DEFAULT NULL COMMENT '操作标识',
  `expression` varchar(200) DEFAULT NULL COMMENT '权限表达式',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `is_system` tinyint(1) DEFAULT '0' COMMENT '是否系统权限',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_permissions_code` (`code`),
  KEY `idx_permissions_type` (`type`),
  KEY `idx_permissions_group_name` (`group_name`),
  KEY `idx_permissions_parent_id` (`parent_id`),
  KEY `idx_permissions_level` (`level`),
  KEY `idx_permissions_is_enabled` (`is_enabled`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='权限表';

-- 用户角色关联表
DROP TABLE IF EXISTS `user_roles`;
CREATE TABLE `user_roles` (
  `id` varchar(36) NOT NULL COMMENT '关联ID',
  `user_id` varchar(36) NOT NULL COMMENT '用户ID',
  `role_id` varchar(36) NOT NULL COMMENT '角色ID',
  `assigned_by` varchar(36) DEFAULT NULL COMMENT '分配人ID',
  `assigned_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '分配时间',
  `expires_at` datetime DEFAULT NULL COMMENT '过期时间',
  `is_active` tinyint(1) DEFAULT '1' COMMENT '是否激活',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_user_roles` (`user_id`,`role_id`),
  KEY `idx_user_roles_user_id` (`user_id`),
  KEY `idx_user_roles_role_id` (`role_id`),
  KEY `idx_user_roles_is_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户角色关联表';

-- 角色权限关联表
DROP TABLE IF EXISTS `role_permissions`;
CREATE TABLE `role_permissions` (
  `id` varchar(36) NOT NULL COMMENT '关联ID',
  `role_id` varchar(36) NOT NULL COMMENT '角色ID',
  `permission_id` varchar(36) NOT NULL COMMENT '权限ID',
  `granted_by` varchar(36) DEFAULT NULL COMMENT '授权人ID',
  `granted_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '授权时间',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_role_permissions` (`role_id`,`permission_id`),
  KEY `idx_role_permissions_role_id` (`role_id`),
  KEY `idx_role_permissions_permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='角色权限关联表';

-- =====================================================
-- 2. 任务管理模块
-- =====================================================

-- 任务分类表
DROP TABLE IF EXISTS `task_categories`;
CREATE TABLE `task_categories` (
  `id` varchar(36) NOT NULL COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `code` varchar(50) DEFAULT NULL COMMENT '分类编码',
  `description` text COMMENT '分类描述',
  `parent_id` varchar(36) DEFAULT NULL COMMENT '父分类ID',
  `level` int DEFAULT '1' COMMENT '分类层级',
  `color` varchar(7) DEFAULT '#1890ff' COMMENT '颜色代码',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort_order` int DEFAULT '0' COMMENT '排序',
  `is_enabled` tinyint(1) DEFAULT '1' COMMENT '是否启用',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_task_categories_code` (`code`),
  KEY `idx_task_categories_parent_id` (`parent_id`),
  KEY `idx_task_categories_level` (`level`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务分类表';

-- 任务表
DROP TABLE IF EXISTS `tasks`;
CREATE TABLE `tasks` (
  `id` varchar(36) NOT NULL COMMENT '任务ID',
  `task_code` varchar(50) DEFAULT NULL COMMENT '任务编号',
  `title` varchar(200) NOT NULL COMMENT '任务标题',
  `description` text COMMENT '任务描述',
  `category_id` varchar(36) DEFAULT NULL COMMENT '任务分类ID',
  `status` varchar(20) DEFAULT 'draft' COMMENT '任务状态',
  `priority` int DEFAULT '3' COMMENT '优先级：1-最低，2-低，3-中，4-高，5-最高',
  `difficulty_level` int DEFAULT '1' COMMENT '难度等级：1-5',
  `estimated_hours` decimal(8,2) DEFAULT NULL COMMENT '预估工时',
  `actual_hours` decimal(8,2) DEFAULT NULL COMMENT '实际工时',
  `progress` decimal(5,2) DEFAULT '0.00' COMMENT '进度百分比',
  `creator_id` varchar(36) NOT NULL COMMENT '创建者ID',
  `assignee_id` varchar(36) DEFAULT NULL COMMENT '负责人ID',
  `reviewer_id` varchar(36) DEFAULT NULL COMMENT '审核人ID',
  `parent_id` varchar(36) DEFAULT NULL COMMENT '父任务ID',
  `project_id` varchar(36) DEFAULT NULL COMMENT '项目ID',
  `planned_start_date` datetime DEFAULT NULL COMMENT '计划开始时间',
  `planned_end_date` datetime DEFAULT NULL COMMENT '计划结束时间',
  `actual_start_date` datetime DEFAULT NULL COMMENT '实际开始时间',
  `actual_end_date` datetime DEFAULT NULL COMMENT '实际结束时间',
  `tags` json DEFAULT NULL COMMENT '标签',
  `attachments` json DEFAULT NULL COMMENT '附件信息',
  `custom_fields` json DEFAULT NULL COMMENT '自定义字段',
  `is_archived` tinyint(1) DEFAULT '0' COMMENT '是否已归档',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_tasks_task_code` (`task_code`),
  KEY `idx_tasks_category_id` (`category_id`),
  KEY `idx_tasks_status` (`status`),
  KEY `idx_tasks_priority` (`priority`),
  KEY `idx_tasks_creator_id` (`creator_id`),
  KEY `idx_tasks_assignee_id` (`assignee_id`),
  KEY `idx_tasks_parent_id` (`parent_id`),
  KEY `idx_tasks_planned_end_date` (`planned_end_date`),
  KEY `idx_tasks_is_archived` (`is_archived`),
  KEY `idx_tasks_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务表';

-- 任务评论表
DROP TABLE IF EXISTS `task_comments`;
CREATE TABLE `task_comments` (
  `id` varchar(36) NOT NULL COMMENT '评论ID',
  `task_id` varchar(36) NOT NULL COMMENT '任务ID',
  `user_id` varchar(36) NOT NULL COMMENT '评论用户ID',
  `parent_id` varchar(36) DEFAULT NULL COMMENT '父评论ID',
  `content` text NOT NULL COMMENT '评论内容',
  `comment_type` varchar(20) DEFAULT 'comment' COMMENT '评论类型：comment-普通评论，system-系统评论',
  `attachments` json DEFAULT NULL COMMENT '附件信息',
  `is_internal` tinyint(1) DEFAULT '0' COMMENT '是否内部评论',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted_at` datetime DEFAULT NULL COMMENT '删除时间',
  PRIMARY KEY (`id`),
  KEY `idx_task_comments_task_id` (`task_id`),
  KEY `idx_task_comments_user_id` (`user_id`),
  KEY `idx_task_comments_parent_id` (`parent_id`),
  KEY `idx_task_comments_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='任务评论表';

-- 设置外键约束
SET FOREIGN_KEY_CHECKS = 1;

-- 添加外键约束
ALTER TABLE `users` ADD CONSTRAINT `fk_users_department_id` FOREIGN KEY (`department_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL;
ALTER TABLE `users` ADD CONSTRAINT `fk_users_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE SET NULL;
ALTER TABLE `departments` ADD CONSTRAINT `fk_departments_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `departments` (`id`) ON DELETE SET NULL;
ALTER TABLE `departments` ADD CONSTRAINT `fk_departments_manager_id` FOREIGN KEY (`manager_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;
ALTER TABLE `permissions` ADD CONSTRAINT `fk_permissions_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;
ALTER TABLE `user_roles` ADD CONSTRAINT `fk_user_roles_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE CASCADE;
ALTER TABLE `user_roles` ADD CONSTRAINT `fk_user_roles_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;
ALTER TABLE `role_permissions` ADD CONSTRAINT `fk_role_permissions_role_id` FOREIGN KEY (`role_id`) REFERENCES `roles` (`id`) ON DELETE CASCADE;
ALTER TABLE `role_permissions` ADD CONSTRAINT `fk_role_permissions_permission_id` FOREIGN KEY (`permission_id`) REFERENCES `permissions` (`id`) ON DELETE CASCADE;
ALTER TABLE `task_categories` ADD CONSTRAINT `fk_task_categories_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `task_categories` (`id`) ON DELETE SET NULL;
ALTER TABLE `tasks` ADD CONSTRAINT `fk_tasks_category_id` FOREIGN KEY (`category_id`) REFERENCES `task_categories` (`id`) ON DELETE SET NULL;
ALTER TABLE `tasks` ADD CONSTRAINT `fk_tasks_creator_id` FOREIGN KEY (`creator_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT;
ALTER TABLE `tasks` ADD CONSTRAINT `fk_tasks_assignee_id` FOREIGN KEY (`assignee_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;
ALTER TABLE `tasks` ADD CONSTRAINT `fk_tasks_reviewer_id` FOREIGN KEY (`reviewer_id`) REFERENCES `users` (`id`) ON DELETE SET NULL;
ALTER TABLE `tasks` ADD CONSTRAINT `fk_tasks_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `tasks` (`id`) ON DELETE SET NULL;
ALTER TABLE `task_comments` ADD CONSTRAINT `fk_task_comments_task_id` FOREIGN KEY (`task_id`) REFERENCES `tasks` (`id`) ON DELETE CASCADE;
ALTER TABLE `task_comments` ADD CONSTRAINT `fk_task_comments_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`) ON DELETE RESTRICT;
ALTER TABLE `task_comments` ADD CONSTRAINT `fk_task_comments_parent_id` FOREIGN KEY (`parent_id`) REFERENCES `task_comments` (`id`) ON DELETE CASCADE;
