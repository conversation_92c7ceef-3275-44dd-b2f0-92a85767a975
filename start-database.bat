@echo off
echo ========================================
echo WTMS 数据库启动脚本
echo ========================================
echo.

echo 检查MySQL服务状态...
sc query mysql >nul 2>&1
if %errorlevel% equ 0 (
    echo MySQL服务已安装，尝试启动...
    net start mysql
    if %errorlevel% equ 0 (
        echo MySQL服务启动成功
    ) else (
        echo MySQL服务启动失败，请检查配置
    )
) else (
    echo MySQL服务未安装
    echo.
    echo 请选择以下选项：
    echo 1. 安装MySQL 8.0
    echo 2. 使用便携式MySQL
    echo 3. 手动配置数据库
    echo.
    echo 推荐下载MySQL 8.0安装包：
    echo https://dev.mysql.com/downloads/mysql/
    echo.
    echo 配置要求：
    echo - 端口: 3308
    echo - 用户: root
    echo - 密码: ankaixin.docker.mysql
    echo - 数据库: wtms
)

echo.
echo 检查端口3308占用情况...
netstat -an | findstr ":3308"
if %errorlevel% equ 0 (
    echo 端口3308已被占用，数据库可能已在运行
) else (
    echo 端口3308未被占用
)

echo.
echo ========================================
echo 数据库连接信息：
echo ========================================
echo 主机: localhost
echo 端口: 3308
echo 用户: root
echo 密码: ankaixin.docker.mysql
echo 数据库: wtms
echo ========================================

pause
