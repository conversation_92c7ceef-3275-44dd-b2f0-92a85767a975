package com.wtms.service;

import com.wtms.dto.request.AssignRolePermissionRequest;
import com.wtms.entity.Permission;
import com.wtms.entity.RolePermission;

import java.util.List;

/**
 * 角色权限管理服务接口
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
public interface RolePermissionService {

    /**
     * 为角色分配权限
     *
     * @param request 分配请求
     * @return 分配结果
     */
    List<RolePermission> assignPermissionsToRole(AssignRolePermissionRequest request);

    /**
     * 移除角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    void removePermissionsFromRole(String roleId, List<String> permissionIds);

    /**
     * 清空角色的所有权限
     *
     * @param roleId 角色ID
     */
    void clearRolePermissions(String roleId);

    /**
     * 获取角色的权限列表
     *
     * @param roleId 角色ID
     * @return 权限列表
     */
    List<Permission> getRolePermissions(String roleId);

    /**
     * 获取角色的权限关联列表
     *
     * @param roleId 角色ID
     * @return 权限关联列表
     */
    List<RolePermission> getRolePermissionAssignments(String roleId);

    /**
     * 获取权限的角色关联列表
     *
     * @param permissionId 权限ID
     * @return 角色关联列表
     */
    List<RolePermission> getPermissionRoleAssignments(String permissionId);

    /**
     * 检查角色是否有指定权限
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 是否有权限
     */
    boolean hasPermission(String roleId, String permissionId);

    /**
     * 检查角色权限关联是否存在
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     * @return 是否存在
     */
    boolean existsRolePermission(String roleId, String permissionId);

    /**
     * 启用角色权限关联
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     */
    void enableRolePermission(String roleId, String permissionId);

    /**
     * 禁用角色权限关联
     *
     * @param roleId 角色ID
     * @param permissionId 权限ID
     */
    void disableRolePermission(String roleId, String permissionId);

    /**
     * 复制角色权限
     *
     * @param sourceRoleId 源角色ID
     * @param targetRoleId 目标角色ID
     */
    void copyRolePermissions(String sourceRoleId, String targetRoleId);

    /**
     * 同步角色权限
     *
     * @param roleId 角色ID
     * @param permissionIds 权限ID列表
     */
    void syncRolePermissions(String roleId, List<String> permissionIds);

    /**
     * 处理过期的权限关联
     */
    void handleExpiredPermissions();

    /**
     * 获取角色权限统计
     *
     * @param roleId 角色ID
     * @return 统计信息
     */
    Object getRolePermissionStatistics(String roleId);
}
