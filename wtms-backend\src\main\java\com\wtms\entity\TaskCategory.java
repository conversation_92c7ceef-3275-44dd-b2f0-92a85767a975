package com.wtms.entity;

import com.baomidou.mybatisplus.annotation.*;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.io.Serializable;
import java.time.LocalDateTime;

/**
 * 任务分类实体
 *
 * <AUTHOR> Team
 * @since 1.0.0
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("task_categories")
@Schema(description = "任务分类实体")
public class TaskCategory implements Serializable {

    private static final long serialVersionUID = 1L;

    @Schema(description = "分类ID")
    @TableId(value = "id", type = IdType.ASSIGN_UUID)
    private String id;

    @Schema(description = "分类名称")
    @TableField("name")
    private String name;

    @Schema(description = "分类编码")
    @TableField("code")
    private String code;

    @Schema(description = "分类描述")
    @TableField("description")
    private String description;

    @Schema(description = "分类颜色")
    @TableField("color")
    private String color;

    @Schema(description = "分类图标")
    @TableField("icon")
    private String icon;

    @Schema(description = "命名模板")
    @TableField("naming_template")
    private String namingTemplate;

    @Schema(description = "排序")
    @TableField("sort_order")
    private Integer sortOrder;

    @Schema(description = "是否启用")
    @TableField("is_enabled")
    private Boolean isEnabled;

    @Schema(description = "创建时间")
    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @Schema(description = "更新时间")
    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    /**
     * 检查分类是否启用
     */
    public boolean isEnabled() {
        return Boolean.TRUE.equals(this.isEnabled);
    }
}
