<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WTMS 登录测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input {
            width: 100%;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 12px 24px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 20px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>WTMS 登录功能测试</h1>
        <form id="loginForm">
            <div class="form-group">
                <label for="username">用户名:</label>
                <input type="text" id="username" value="admin" required>
            </div>
            <div class="form-group">
                <label for="password">密码:</label>
                <input type="password" id="password" value="admin123456" required>
            </div>
            <button type="submit">测试登录</button>
        </form>
        <div id="result"></div>
    </div>

    <script>
        document.getElementById('loginForm').addEventListener('submit', async function(e) {
            e.preventDefault();

            const username = document.getElementById('username').value;
            const password = document.getElementById('password').value;
            const resultDiv = document.getElementById('result');

            try {
                resultDiv.innerHTML = '正在测试登录...';
                resultDiv.className = 'result';

                console.log('Testing login with:', { username, password });

                const response = await fetch('http://localhost:55557/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: username,
                        password: password
                    })
                });

                console.log('Response status:', response.status);
                console.log('Response headers:', response.headers);

                const data = await response.json();
                console.log('Response data:', data);

                if (response.ok && data.success) {
                    resultDiv.innerHTML = `✅ 登录成功！\n\n响应数据:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result success';

                    // 保存token到localStorage模拟前端行为
                    if (data.data && data.data.token) {
                        localStorage.setItem('wtms_token', data.data.token);
                        if (data.data.refreshToken) {
                            localStorage.setItem('refreshToken', data.data.refreshToken);
                        }
                    }

                    // 测试token验证
                    setTimeout(async () => {
                        try {
                            const userResponse = await fetch('http://localhost:55557/api/v1/user/profile', {
                                headers: {
                                    'Authorization': `Bearer ${data.data.token}`
                                }
                            });
                            const userData = await userResponse.json();

                            if (userResponse.ok && userData.success) {
                                resultDiv.innerHTML += `\n\n✅ 用户信息获取成功:\n${JSON.stringify(userData, null, 2)}`;
                            } else {
                                resultDiv.innerHTML += `\n\n❌ 用户信息获取失败:\n${JSON.stringify(userData, null, 2)}`;
                            }
                        } catch (error) {
                            resultDiv.innerHTML += `\n\n❌ 用户信息获取异常: ${error.message}`;
                        }
                    }, 1000);

                } else {
                    resultDiv.innerHTML = `❌ 登录失败！\n\n状态码: ${response.status}\n错误信息: ${data.message || '未知错误'}\n\n完整响应:\n${JSON.stringify(data, null, 2)}`;
                    resultDiv.className = 'result error';
                }
            } catch (error) {
                console.error('Login test error:', error);
                resultDiv.innerHTML = `❌ 请求失败！\n\n错误信息: ${error.message}\n\n请检查:\n1. 后端服务是否运行在 http://localhost:55557\n2. 网络连接是否正常\n3. CORS设置是否正确`;
                resultDiv.className = 'result error';
            }
        });

        // 页面加载时检查token状态
        window.addEventListener('load', function() {
            const token = localStorage.getItem('wtms_token');
            if (token) {
                document.getElementById('result').innerHTML = `发现已保存的token: ${token.substring(0, 50)}...`;
                document.getElementById('result').className = 'result';
            }
        });
    </script>
</body>
</html>
