<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WTMS 登录循环问题修复验证</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 20px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .step {
            margin: 10px 0;
            padding: 10px;
            background-color: #f8f9fa;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 WTMS 实际登录问题诊断</h1>

        <div class="test-section">
            <h3>🚨 紧急登录测试</h3>
            <p>直接测试登录功能，查看实际错误信息</p>
            <button onclick="emergencyLoginTest()" style="background-color: #dc3545; font-size: 18px; padding: 15px 30px;">🚨 立即测试登录</button>
        </div>
        
        <div class="test-section">
            <h3>📋 测试步骤</h3>
            <div class="step">1. 测试后端API健康状态</div>
            <div class="step">2. 测试登录API响应格式</div>
            <div class="step">3. 测试用户信息获取API</div>
            <div class="step">4. 模拟前端登录流程</div>
            <div class="step">5. 验证Token持久化</div>
            <button onclick="runAllTests()">🚀 开始完整测试</button>
        </div>

        <div class="test-section">
            <h3>🔍 单项测试</h3>
            <button onclick="testHealth()">健康检查</button>
            <button onclick="testLogin()">登录API</button>
            <button onclick="testUserProfile()">用户信息</button>
            <button onclick="testFrontendFlow()">前端流程</button>
            <button onclick="clearStorage()">清除存储</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        const API_BASE = 'http://localhost:55557/api/v1';
        let currentToken = null;

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `result ${type}`;
            resultDiv.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            resultsDiv.appendChild(resultDiv);
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function testHealth() {
            log('🔍 测试后端健康状态...');
            try {
                const response = await fetch(`${API_BASE}/health`);
                const data = await response.json();
                
                if (response.ok && data.success) {
                    log(`✅ 后端服务正常运行\n${JSON.stringify(data, null, 2)}`, 'success');
                    return true;
                } else {
                    log(`❌ 后端服务异常\n${JSON.stringify(data, null, 2)}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ 健康检查失败: ${error.message}`, 'error');
                return false;
            }
        }

        async function testLogin() {
            log('🔍 测试登录API...');
            try {
                const response = await fetch(`${API_BASE}/auth/login`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123456'
                    })
                });

                const data = await response.json();
                
                if (response.ok && data.success && data.data) {
                    currentToken = data.data.token;
                    localStorage.setItem('wtms_token', currentToken);
                    if (data.data.refreshToken) {
                        localStorage.setItem('refreshToken', data.data.refreshToken);
                    }
                    
                    log(`✅ 登录成功！\nToken: ${currentToken.substring(0, 50)}...\n用户: ${data.data.user.fullName}\n权限数量: ${data.data.user.role.permissions.length}`, 'success');
                    return true;
                } else {
                    log(`❌ 登录失败\n${JSON.stringify(data, null, 2)}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ 登录请求失败: ${error.message}`, 'error');
                return false;
            }
        }

        async function testUserProfile() {
            if (!currentToken) {
                log('⚠️ 请先执行登录测试获取Token', 'error');
                return false;
            }

            log('🔍 测试用户信息获取...');
            try {
                const response = await fetch(`${API_BASE}/user/profile`, {
                    headers: {
                        'Authorization': `Bearer ${currentToken}`
                    }
                });

                const data = await response.json();
                
                if (response.ok && data.success && data.data) {
                    log(`✅ 用户信息获取成功！\n用户: ${data.data.user.fullName}\n权限数量: ${data.data.permissions.length}\n菜单数量: ${data.data.menus.length}`, 'success');
                    return true;
                } else {
                    log(`❌ 用户信息获取失败\n${JSON.stringify(data, null, 2)}`, 'error');
                    return false;
                }
            } catch (error) {
                log(`❌ 用户信息请求失败: ${error.message}`, 'error');
                return false;
            }
        }

        async function testFrontendFlow() {
            log('🔍 模拟前端登录流程...');
            
            // 清除现有状态
            localStorage.clear();
            currentToken = null;
            
            // 步骤1: 登录
            log('步骤1: 执行登录...');
            const loginSuccess = await testLogin();
            if (!loginSuccess) {
                log('❌ 前端流程测试失败：登录失败', 'error');
                return false;
            }
            
            // 步骤2: 获取用户信息
            log('步骤2: 获取用户信息...');
            const profileSuccess = await testUserProfile();
            if (!profileSuccess) {
                log('❌ 前端流程测试失败：用户信息获取失败', 'error');
                return false;
            }
            
            // 步骤3: 验证Token持久化
            log('步骤3: 验证Token持久化...');
            const storedToken = localStorage.getItem('wtms_token');
            const storedRefreshToken = localStorage.getItem('refreshToken');
            
            if (storedToken && storedRefreshToken) {
                log(`✅ Token持久化成功！\nAccess Token: ${storedToken.substring(0, 30)}...\nRefresh Token: ${storedRefreshToken.substring(0, 30)}...`, 'success');
            } else {
                log('❌ Token持久化失败', 'error');
                return false;
            }
            
            log('🎉 前端登录流程测试完全成功！', 'success');
            return true;
        }

        async function runAllTests() {
            log('🚀 开始完整测试流程...');
            document.getElementById('results').innerHTML = '';
            
            const tests = [
                { name: '后端健康检查', fn: testHealth },
                { name: '登录API测试', fn: testLogin },
                { name: '用户信息API测试', fn: testUserProfile },
                { name: '前端流程测试', fn: testFrontendFlow }
            ];
            
            let passedTests = 0;
            
            for (const test of tests) {
                log(`\n=== ${test.name} ===`);
                const result = await test.fn();
                if (result) {
                    passedTests++;
                }
                await new Promise(resolve => setTimeout(resolve, 1000)); // 等待1秒
            }
            
            log(`\n📊 测试结果: ${passedTests}/${tests.length} 通过`);
            
            if (passedTests === tests.length) {
                log('🎉 所有测试通过！登录循环问题已修复！', 'success');
            } else {
                log('❌ 部分测试失败，需要进一步检查', 'error');
            }
        }

        function clearStorage() {
            localStorage.clear();
            currentToken = null;
            log('🧹 本地存储已清除', 'info');
        }

        async function emergencyLoginTest() {
            log('🚨 紧急登录测试开始...', 'info');
            document.getElementById('results').innerHTML = '';

            // 清除所有存储
            localStorage.clear();
            sessionStorage.clear();

            log('步骤1: 清除所有本地存储', 'info');

            // 测试API连接
            log('步骤2: 测试API连接...', 'info');
            try {
                const healthResponse = await fetch('http://localhost:55557/api/v1/health');
                const healthData = await healthResponse.json();
                log(`✅ API连接正常: ${JSON.stringify(healthData)}`, 'success');
            } catch (error) {
                log(`❌ API连接失败: ${error.message}`, 'error');
                return;
            }

            // 测试登录
            log('步骤3: 测试登录API...', 'info');
            try {
                const loginResponse = await fetch('http://localhost:55557/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        username: 'admin',
                        password: 'admin123456'
                    })
                });

                log(`登录响应状态: ${loginResponse.status}`, 'info');

                const loginData = await loginResponse.json();
                log(`登录响应数据: ${JSON.stringify(loginData, null, 2)}`, 'info');

                if (loginResponse.ok && loginData.success && loginData.data) {
                    const token = loginData.data.token;
                    log(`✅ 登录成功！Token: ${token.substring(0, 50)}...`, 'success');

                    // 保存token
                    localStorage.setItem('wtms_token', token);
                    if (loginData.data.refreshToken) {
                        localStorage.setItem('refreshToken', loginData.data.refreshToken);
                    }

                    // 测试用户信息获取
                    log('步骤4: 测试用户信息获取...', 'info');
                    const profileResponse = await fetch('http://localhost:55557/api/v1/user/profile', {
                        headers: {
                            'Authorization': `Bearer ${token}`
                        }
                    });

                    const profileData = await profileResponse.json();
                    log(`用户信息响应: ${JSON.stringify(profileData, null, 2)}`, 'info');

                    if (profileResponse.ok && profileData.success) {
                        log('🎉 完整登录流程测试成功！', 'success');
                        log('现在尝试在前端应用中登录...', 'info');

                        // 打开前端应用
                        window.open('http://localhost:33336', '_blank');
                    } else {
                        log('❌ 用户信息获取失败', 'error');
                    }
                } else {
                    log(`❌ 登录失败: ${loginData.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                log(`❌ 登录请求异常: ${error.message}`, 'error');
            }
        }

        // 页面加载时检查现有状态
        window.addEventListener('load', function() {
            const existingToken = localStorage.getItem('wtms_token');
            if (existingToken) {
                currentToken = existingToken;
                log(`ℹ️ 发现已保存的Token: ${existingToken.substring(0, 30)}...`, 'info');
            }
        });
    </script>
</body>
</html>
